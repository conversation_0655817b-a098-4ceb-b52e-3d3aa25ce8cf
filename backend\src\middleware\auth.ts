import { Request, Response, NextFunction } from 'express';
import { JwtService } from '../services/JwtService';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    username: string;
    role?: string;
  };
}

export class AuthMiddleware {
  private jwtService: JwtService;

  constructor() {
    this.jwtService = new JwtService();
  }

  /**
   * Verify JWT token and attach user to request
   */
  public authenticate = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          message: 'Authorization token required'
        });
        return;
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      
      // Verify token
      const decoded = this.jwtService.verifyAccessToken(token);
      
      if (!decoded) {
        res.status(401).json({
          success: false,
          message: 'Invalid or expired token'
        });
        return;
      }

      // Attach user info to request
      req.user = {
        id: decoded.userId,
        email: decoded.email,
        username: decoded.username
      };

      next();
    } catch (error) {
      console.error('Authentication error:', error);
      res.status(401).json({
        success: false,
        message: 'Authentication failed'
      });
    }
  };

  /**
   * Optional authentication - doesn't fail if no token
   */
  public optionalAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const decoded = this.jwtService.verifyAccessToken(token);
        
        if (decoded) {
          req.user = {
            id: decoded.userId,
            email: decoded.email,
            username: decoded.username
          };
        }
      }

      next();
    } catch (error) {
      console.error('Optional authentication error:', error);
      next();
    }
  };

  /**
   * Check if user has specific role
   */
  public requireRole = (role: string) => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      // Here you would check user role from database
      // For now, we'll assume role is attached to token
      if (req.user?.role !== role) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      next();
    };
  };

  /**
   * Check if user is creator
   */
  public requireCreator = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    try {
      // Check if user is creator from database
      const query = `
        SELECT is_creator FROM users 
        WHERE id = $1 AND is_active = true
      `;
      
      const result = await db.query(query, [req.user.id]);
      
      if (result.rows.length === 0 || !result.rows[0].is_creator) {
        res.status(403).json({
          success: false,
          message: 'Creator access required'
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Creator check error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  };
}