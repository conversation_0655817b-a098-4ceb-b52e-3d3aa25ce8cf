import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Search, Grid, List, Sparkles, Wand2 } from 'lucide-react';

interface MemorySearchBarProps {
  value: string;
  onChange: (value: string) => void;
  viewMode: 'grid' | 'timeline';
  onViewModeChange: (mode: 'grid' | 'timeline') => void;
  lang: string;
}

const MemorySearchBar: React.FC<MemorySearchBarProps> = ({
  value,
  onChange,
  viewMode,
  onViewModeChange,
  lang,
}) => {
  const { t: _ } = useTranslation(lang, 'translation');

  return (
    <div className="relative">
      {/* Magical Search Container */}
      <div className="flex gap-4 items-center">
        {/* Enhanced Search Input */}
        <div className="flex-1 relative group">
          {/* Background with Gradient Border */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-indigo-500/20 rounded-2xl blur-sm group-focus-within:blur-none transition-all duration-300"></div>
          
          <div className="relative backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-2xl border border-white/50 dark:border-gray-700/50 shadow-2xl group-focus-within:shadow-purple-500/25 transition-all duration-300">
            {/* Search Icon with Animation */}
            <div className="absolute left-5 top-1/2 transform -translate-y-1/2">
              <div className="relative">
                <Search className="w-6 h-6 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-300" />
                <div className="absolute inset-0 bg-purple-500/20 rounded-full scale-0 group-focus-within:scale-150 transition-transform duration-300 blur-md"></div>
              </div>
            </div>

            {/* Input Field */}
            <input
              type="text"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder={_('memory.search.placeholder', 'Search memories... try "birthday", "promise", "dream"')}
              className="w-full pl-16 pr-32 py-4 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none text-lg font-medium"
            />

            {/* AI Power Indicator */}
            <div className="absolute right-5 top-1/2 transform -translate-y-1/2">
              <div className="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-300/30 backdrop-blur-sm">
                <div className="relative">
                  <Sparkles className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                  <div className="absolute inset-0 animate-ping">
                    <Sparkles className="w-4 h-4 text-purple-600/30 dark:text-purple-400/30" />
                  </div>
                </div>
                <span className="text-sm font-medium text-purple-700 dark:text-purple-300 hidden sm:inline">
                  {_('memory.search.aiPowered', 'AI Semantic Search')}
                </span>
              </div>
            </div>

            {/* Search Suggestions */}
            {!value && (
              <div className="absolute top-full left-0 right-0 mt-2 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none">
                <div className="backdrop-blur-xl bg-white/90 dark:bg-gray-800/90 rounded-xl border border-white/50 dark:border-gray-700/50 shadow-xl p-3">
                  <div className="flex flex-wrap gap-2">
                    {['birthday promise', 'dream sharing', 'warm moments', 'important promise', 'beautiful memory'].map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => onChange(suggestion)}
                        className="px-3 py-1 bg-gradient-to-r from-purple-100/80 to-pink-100/80 dark:from-purple-900/30 dark:to-pink-900/30 text-purple-700 dark:text-purple-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200 pointer-events-auto"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced View Mode Toggle */}
        <div className="relative">
          <div className="backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-2xl border border-white/50 dark:border-gray-700/50 shadow-2xl p-1.5">
            <div className="flex bg-gray-100/50 dark:bg-gray-700/50 rounded-xl">
              {/* Grid View Button */}
              <button
                onClick={() => onViewModeChange('grid')}
                className={`relative px-4 py-3 rounded-lg transition-all duration-300 ${
                  viewMode === 'grid'
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg transform scale-105'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-600/50'
                }`}
                title={_('memory.view.grid', 'Grid View')}
              >
                <Grid size={20} />
                {viewMode === 'grid' && (
                  <div className="absolute inset-0 bg-white/20 rounded-lg animate-pulse"></div>
                )}
              </button>

              {/* Timeline View Button */}
              <button
                onClick={() => onViewModeChange('timeline')}
                className={`relative px-4 py-3 rounded-lg transition-all duration-300 ${
                  viewMode === 'timeline'
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg transform scale-105'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-gray-600/50'
                }`}
                title={_('memory.view.timeline', 'Timeline View')}
              >
                <List size={20} />
                {viewMode === 'timeline' && (
                  <div className="absolute inset-0 bg-white/20 rounded-lg animate-pulse"></div>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Magic Wand Button */}
        <div className="relative">
          <button className="group relative backdrop-blur-xl bg-gradient-to-r from-purple-500/80 to-pink-500/80 hover:from-purple-600/90 hover:to-pink-600/90 text-white rounded-2xl p-4 shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-105">
            <Wand2 size={20} className="transform group-hover:rotate-12 transition-transform duration-300" />
            <div className="absolute inset-0 bg-white/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            
            {/* Floating Sparkles */}
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping"></div>
            <div className="absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-pink-400 rounded-full animate-ping animation-delay-500"></div>
          </button>
          
          {/* Tooltip */}
          <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
            <div className="backdrop-blur-xl bg-gray-900/90 text-white px-3 py-1.5 rounded-lg text-sm whitespace-nowrap">
              AI Smart Suggestions
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900/90"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-4 left-1/4 w-1 h-1 bg-purple-400 rounded-full animate-bounce opacity-60"></div>
        <div className="absolute top-8 right-1/3 w-1.5 h-1.5 bg-pink-400 rounded-full animate-bounce animation-delay-300 opacity-40"></div>
        <div className="absolute bottom-4 left-1/3 w-1 h-1 bg-indigo-400 rounded-full animate-bounce animation-delay-700 opacity-50"></div>
      </div>

      <style jsx>{`
        .animation-delay-300 {
          animation-delay: 300ms;
        }
        .animation-delay-500 {
          animation-delay: 500ms;
        }
        .animation-delay-700 {
          animation-delay: 700ms;
        }
      `}</style>
    </div>
  );
};

export default MemorySearchBar; 