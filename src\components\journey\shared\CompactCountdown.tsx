'use client';

import React, { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface CompactCountdownProps {
  endTime: Date;
  label: string;
  type: 'daily' | 'weekly' | 'monthly';
  lang: string;
}

interface TimeLeft {
  hours: number;
  minutes: number;
  seconds: number;
}

export const CompactCountdown: React.FC<CompactCountdownProps> = ({
  endTime,
  label,
  type,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ hours: 0, minutes: 0, seconds: 0 });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const difference = endTime.getTime() - now;

      if (difference > 0) {
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ hours, minutes, seconds });
      } else {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
      }
    };

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timer);
  }, [endTime]);

  const getTypeColor = () => {
    switch (type) {
      case 'daily':
        return 'text-blue-400';
      case 'weekly':
        return 'text-purple-400';
      case 'monthly':
        return 'text-orange-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className="flex items-center gap-2 pl-0 pr-3 py-3">
      <div className="w-10 h-10 lg:w-10 lg:h-10 max-lg:w-8 max-lg:h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
        <Clock className="w-6 h-6 lg:w-6 lg:h-6 max-lg:w-5 max-lg:h-5 text-white" />
      </div>
      
      <div className="flex-1">
        <div className={`text-sm font-medium ${getTypeColor()}`}>
          {label}
        </div>
        <div className="text-xs text-gray-600 dark:text-gray-400">
          {timeLeft.hours.toString().padStart(2, '0')}:
          {timeLeft.minutes.toString().padStart(2, '0')}:
          {timeLeft.seconds.toString().padStart(2, '0')} {t('journey.messages.afterReset')}
        </div>
      </div>
    </div>
  );
}; 