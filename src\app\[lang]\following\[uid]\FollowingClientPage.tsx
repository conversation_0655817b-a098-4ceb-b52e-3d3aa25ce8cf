'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { <PERSON>Left, UserMinus, BadgeCheck, Search, Users, Clock } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Following {
  id: string;
  name: string;
  avatar: string;
  username: string;
  followerCount: number;
  bio: string;
  joinedDate: string;
  isVerified: boolean;
  lastActive: string;
  mutualFollowers: number;
}

interface UserInfo {
  name: string;
  avatar: string;
  followingCount: number;
}

interface FollowingClientPageProps {
  lang: string;
  uid: string;
  userInfo: UserInfo;
  following: Following[];
}

const FollowingClientPage: React.FC<FollowingClientPageProps> = ({
  lang,
  uid,
  userInfo,
  following
}) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [followingData, setFollowingData] = useState(following);

  const filteredFollowing = followingData.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUnfollow = (userId: string) => {
    setFollowingData(prev => prev.filter(user => user.id !== userId));
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div className="flex items-center gap-3">
            <Image
              src={userInfo.avatar}
              alt={userInfo.name}
              width={40}
              height={40}
              className="w-10 h-10 rounded-full object-cover"
              unoptimized
            />
            <div>
              <h1 className="text-xl font-bold">{userInfo.name}</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {userInfo.followingCount} following
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search following..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
      </div>

      {/* Following List */}
      <div className="bg-white dark:bg-gray-800">
        {filteredFollowing.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm ? 'No users found matching your search.' : 'Not following anyone yet.'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredFollowing.map((user) => (
              <div key={user.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1">
                    <Link href={`/${lang}/profile/${user.id}`}>
                      <Image
                        src={user.avatar}
                        alt={user.name}
                        width={48}
                        height={48}
                        className="w-12 h-12 rounded-full object-cover"
                        unoptimized
                      />
                    </Link>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <Link
                          href={`/${lang}/profile/${user.id}`}
                          className="font-semibold text-gray-900 dark:text-gray-100 hover:text-indigo-600 dark:hover:text-indigo-400 truncate"
                        >
                          {user.name}
                        </Link>
                        {user.isVerified && (
                          <BadgeCheck size={16} className="text-blue-500 flex-shrink-0" />
                        )}
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                        {user.username}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mt-1">
                        {user.bio}
                      </p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <span className="flex items-center gap-1">
                          <Users size={12} />
                          {user.followerCount.toLocaleString()} followers
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock size={12} />
                          Active {user.lastActive}
                        </span>
                        {user.mutualFollowers > 0 && (
                          <span>{user.mutualFollowers} mutual</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => handleUnfollow(user.id)}
                    className="ml-4 px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-600 dark:hover:text-red-400"
                  >
                    <UserMinus size={16} />
                    Unfollow
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FollowingClientPage;
