-- =====================================================
-- Multi-language Character Data Storage Schema
-- Supporting data_examples complex character structure
-- =====================================================

-- 1. Character Localizations Table
-- Stores multi-language character data (Chinese, Japanese, English)
CREATE TABLE character_localizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    language_code VARCHAR(10) NOT NULL CHECK (language_code IN ('zh-CN', 'ja-JP', 'en-US')),
    
    -- Basic Information (localized)
    name JSONB NOT NULL DEFAULT '{}', -- {full_name, name_origin}
    background TEXT,
    personality TEXT,
    
    -- Hierarchical Appearance Info (from data_examples)
    appear_hierarchical_info JSONB NOT NULL DEFAULT '{}',
    -- Structure: {
    --   first_layer_core_identity: {physique, stature, body_ratio, face, hair, eyes, base_species},
    --   second_layer_presentation_style: {wardrobe, garments, footwear, adornments, gear},
    --   third_layer_behavioral_dynamics: {expression_engine, kinesics, vocalization}
    -- }
    
    -- Extended Hierarchical Info
    ext_hierarchical_info JSONB NOT NULL DEFAULT '{}',
    -- Structure: {
    --   first_layer_core_identity: {core_identity_archetype, core_identity_physicality},
    --   second_layer_cognitive_model: {cognitive_model_personality, cognitive_model_emotional_spectrum},
    --   third_layer_meta_rules: {meta_rules_system_directives}
    -- }
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(character_id, language_code)
);

-- 2. Enhanced Characters Table Extensions
-- Add new columns to existing characters table
ALTER TABLE characters 
ADD COLUMN IF NOT EXISTS mbti_type VARCHAR(4) CHECK (mbti_type ~ '^[IE][NS][FT][JP]$'),
ADD COLUMN IF NOT EXISTS pov VARCHAR(20) DEFAULT 'neutral',
ADD COLUMN IF NOT EXISTS era VARCHAR(50),
ADD COLUMN IF NOT EXISTS region VARCHAR(100),
ADD COLUMN IF NOT EXISTS abo_ratio JSONB DEFAULT '{"alpha": 0.33, "beta": 0.34, "omega": 0.33}',
ADD COLUMN IF NOT EXISTS detailed_personality JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS cognitive_model JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS appearance_details JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS behavioral_patterns JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS voice_characteristics JSONB DEFAULT '{}';

-- 3. Character Appearance Details Table
-- Detailed storage for complex appearance hierarchies
CREATE TABLE character_appearance_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    
    -- Core Physical Identity
    physique JSONB DEFAULT '{}', -- {type, description}
    stature JSONB DEFAULT '{}',  -- {type, description}
    body_ratio JSONB DEFAULT '{}', -- {type, description}
    face JSONB DEFAULT '{}',     -- {type, description}
    hair JSONB DEFAULT '{}',     -- {type, description}
    eyes JSONB DEFAULT '{}',     -- {type, description}
    base_species JSONB DEFAULT '{}', -- {type, features[]}
    
    -- Presentation Style
    wardrobe JSONB DEFAULT '{}',     -- {core_style, description}
    garments JSONB DEFAULT '{}',     -- {type, description}
    footwear JSONB DEFAULT '{}',     -- {type, description}
    adornments JSONB DEFAULT '{}',   -- {accessories[]}
    gear JSONB DEFAULT '{}',         -- {type, description}
    handheld_items JSONB DEFAULT '{}', -- {type, description}
    
    -- Behavioral Dynamics
    expression_engine JSONB DEFAULT '{}', -- {default_expression, reactive_expressions, emotional_intensity}
    kinesics JSONB DEFAULT '{}',          -- {gestures, posture, microexpressions}
    vocalization JSONB DEFAULT '{}',      -- {tone, pacing, lexicon_and_quirks}
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(character_id)
);

-- 4. Character Personality Profiles Table
-- Cognitive models and emotional baselines
CREATE TABLE character_personality_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    
    -- Core Identity Archetype
    species VARCHAR(50) DEFAULT 'Human',
    vocation TEXT,
    social_role TEXT,
    archetypal_story TEXT,
    core_need TEXT,
    core_fear TEXT,
    core_virtue TEXT,
    
    -- Cognitive Model
    openness_score INTEGER CHECK (openness_score >= 1 AND openness_score <= 100),
    conscientiousness_score INTEGER CHECK (conscientiousness_score >= 1 AND conscientiousness_score <= 100),
    extraversion_score INTEGER CHECK (extraversion_score >= 1 AND extraversion_score <= 100),
    agreeableness_score INTEGER CHECK (agreeableness_score >= 1 AND agreeableness_score <= 100),
    neuroticism_score INTEGER CHECK (neuroticism_score >= 1 AND neuroticism_score <= 100),
    
    -- Emotional Spectrum
    emotional_baseline VARCHAR(50),
    emotional_range TEXT,
    emotional_triggers TEXT[],
    coping_mechanisms TEXT[],
    empathy_level VARCHAR(20),
    emotional_expression TEXT,
    
    -- Detailed Personality Data
    personality_details JSONB DEFAULT '{}',
    cognitive_details JSONB DEFAULT '{}',
    emotional_details JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(character_id)
);

-- 5. Character Statistics Materialized View
-- Aggregated stats for CharacterCard component
CREATE MATERIALIZED VIEW character_stats AS
SELECT 
    c.id,
    c.name,
    c.creator_id,
    COUNT(DISTINCT cl.user_id) as likes_count,
    COUNT(DISTINCT f.follower_id) as friends_count,
    COUNT(DISTINCT m.id) as moments_count,
    COUNT(DISTINCT ch.id) as chats_count,
    AVG(sr.rating) as avg_rating,
    COUNT(DISTINCT sr.id) as rating_count,
    c.created_at,
    c.updated_at
FROM characters c
LEFT JOIN character_likes cl ON c.id = cl.character_id
LEFT JOIN follows f ON c.creator_id = f.following_id  
LEFT JOIN moments m ON c.id = m.character_id
LEFT JOIN chats ch ON c.id = ch.character_id
LEFT JOIN story_ratings sr ON sr.story_id IN (
    SELECT s.id FROM stories s WHERE s.character_id = c.id
)
WHERE c.is_public = true
GROUP BY c.id, c.name, c.creator_id, c.created_at, c.updated_at;

-- 6. Indexes for Performance Optimization
CREATE INDEX idx_character_localizations_character_lang 
ON character_localizations(character_id, language_code);

CREATE INDEX idx_character_localizations_language 
ON character_localizations(language_code);

CREATE INDEX idx_character_appearance_character 
ON character_appearance_details(character_id);

CREATE INDEX idx_character_personality_character 
ON character_personality_profiles(character_id);

CREATE INDEX idx_characters_mbti ON characters(mbti_type) WHERE mbti_type IS NOT NULL;
CREATE INDEX idx_characters_era ON characters(era) WHERE era IS NOT NULL;
CREATE INDEX idx_characters_region ON characters(region) WHERE region IS NOT NULL;

-- GIN indexes for JSONB columns
CREATE INDEX idx_character_localizations_appear_gin 
ON character_localizations USING GIN (appear_hierarchical_info);

CREATE INDEX idx_character_localizations_ext_gin 
ON character_localizations USING GIN (ext_hierarchical_info);

CREATE INDEX idx_character_appearance_physique_gin 
ON character_appearance_details USING GIN (physique);

CREATE INDEX idx_character_personality_details_gin 
ON character_personality_profiles USING GIN (personality_details);

-- 7. Triggers for Updated Timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER character_localizations_updated_at 
BEFORE UPDATE ON character_localizations 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER character_appearance_updated_at 
BEFORE UPDATE ON character_appearance_details 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER character_personality_updated_at 
BEFORE UPDATE ON character_personality_profiles 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. Refresh Function for Materialized View
CREATE OR REPLACE FUNCTION refresh_character_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY character_stats;
END;
$$ LANGUAGE plpgsql;

-- Schedule refresh every hour (requires pg_cron extension)
-- SELECT cron.schedule('refresh-character-stats', '0 * * * *', 'SELECT refresh_character_stats();');

COMMENT ON TABLE character_localizations IS 'Multi-language character data supporting Chinese, Japanese, and English';
COMMENT ON TABLE character_appearance_details IS 'Detailed character appearance hierarchy from data_examples';
COMMENT ON TABLE character_personality_profiles IS 'Cognitive models and personality profiles for characters';
COMMENT ON MATERIALIZED VIEW character_stats IS 'Aggregated character statistics for frontend Card components';
