'use client';

import React, { useState, useMemo } from 'react';
import { Trophy, TrendingUp, Target, Users, Star, Compass } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';
import { EnhancedAchievement, AchievementFilters } from '@/types/achievements';
import EnhancedTabNavigation, { TabItem } from '@/components/common/EnhancedTabNavigation';
import { getTrophyTabColors } from '@/utils/tabColorSchemes';
import TrophyOverviewTab from '@/components/trophies/tabs/TrophyOverviewTab';
import TrophyAchievementsTab from '@/components/trophies/tabs/TrophyAchievementsTab';
import TrophyExplorationsTab from '@/components/trophies/tabs/TrophyExplorationsTab';
import TrophySocialTab from '@/components/trophies/tabs/TrophySocialTab';
import TrophyRankingTab from '@/components/trophies/tabs/TrophyRankingTab';
import TrophyModal from '@/components/trophies/TrophyModal';

import { generateAchievementsData } from '@/lib/trophy-data';

interface TrophiesClientPageProps {
  lang: string;
}

const TrophiesClientPage: React.FC<TrophiesClientPageProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');

  // State management
  const [selectedAchievement, setSelectedAchievement] = useState<EnhancedAchievement | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'achievements' | 'explorations' | 'social' | 'ranking'>('overview');

  // Generate achievements data
  const achievements = useMemo(() => generateAchievementsData(), []);

  // Tab configuration for enhanced navigation
  const trophyTabs: TabItem[] = [
    {
      id: 'overview',
      label: t('trophies.tabs.overview'),
      icon: Trophy,
      description: t('trophies.tabs.overviewDesc')
    },
    {
      id: 'achievements',
      label: t('trophies.tabs.achievements'),
      icon: Target,
      description: t('trophies.tabs.achievementsDesc')
    },
    {
      id: 'explorations',
      label: t('trophies.tabs.explorations'),
      icon: Compass,
      description: t('trophies.tabs.explorationsDesc')
    },
    {
      id: 'social',
      label: t('trophies.tabs.social'),
      icon: Users,
      description: t('trophies.tabs.socialDesc')
    },
    {
      id: 'ranking',
      label: t('trophies.tabs.ranking'),
      icon: TrendingUp,
      description: t('trophies.tabs.rankingDesc')
    }
  ];

  // Event handlers
  const handleAchievementClick = (achievement: EnhancedAchievement) => {
    setSelectedAchievement(achievement);
  };

  const handleClaimReward = (achievementId: string) => {
    // Implementation will be added with backend integration
    console.log(`Claiming reward for achievement: ${achievementId}`);
  };

  const handleTabChange = (tab: 'overview' | 'achievements' | 'explorations' | 'social' | 'ranking') => {
    setActiveTab(tab);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <TrophyOverviewTab
            achievements={achievements}
            lang={lang}
          />
        );
      case 'achievements':
        return (
          <TrophyAchievementsTab
            achievements={achievements}
            lang={lang}
            onAchievementClick={handleAchievementClick}
            onClaimReward={handleClaimReward}
          />
        );
      case 'explorations':
        return (
          <TrophyExplorationsTab
            achievements={achievements}
            lang={lang}
            onAchievementClick={handleAchievementClick}
            onClaimReward={handleClaimReward}
          />
        );
      case 'social':
        return (
          <TrophySocialTab
            achievements={achievements}
            lang={lang}
            onAchievementClick={handleAchievementClick}
            onClaimReward={handleClaimReward}
          />
        );
      case 'ranking':
        return (
          <TrophyRankingTab
            achievements={achievements}
            lang={lang}
            onAchievementClick={handleAchievementClick}
            onClaimReward={handleClaimReward}
          />
        );
      default:
        return null;
    }
  };

  return (
    <MainAppLayout lang={lang} title={t('trophies.title')}>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/20 to-purple-50/30 dark:from-slate-900 dark:via-blue-900/10 dark:to-purple-900/20">

        {/* Enhanced Tab Navigation */}
        <EnhancedTabNavigation
          tabs={trophyTabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          getTabColors={getTrophyTabColors}
          containerMaxWidth="max-w-6xl"
        />

        {/* Tab Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-10">
          {renderTabContent()}
        </div>
      </div>

      {/* Achievement Details Modal */}
      {selectedAchievement && (
        <TrophyModal
          achievement={selectedAchievement}
          onClose={() => setSelectedAchievement(null)}
          onClaimReward={handleClaimReward}
          lang={lang}
        />
      )}
    </MainAppLayout>
  );
};

export default TrophiesClientPage;
