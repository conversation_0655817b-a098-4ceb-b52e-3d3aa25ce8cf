'use client';

import React, { useCallback } from 'react';
import { Plus, GitBranch, X } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryChapter } from '@/types/story-creation';

export interface StoryFlowProps {
  chapters: StoryChapter[];
  selectedChapter: string | null;
  onChapterSelect: (chapterId: string) => void;
  onAddMainChapter?: () => void;
  onAddBranchChapter?: (parentChapterNumber: number) => void;
  onRemoveChapter?: (chapterId: string) => void;
  onChapterReorder?: (chapterId: string, newOrder: number, newParent?: string) => void;
  lang: string;
  title: string;
  icon: React.ReactNode;
  accentColor?: 'purple' | 'blue' | 'rose';
  showAddButton?: boolean;
  showBranchButtons?: boolean;
  showRemoveButtons?: boolean;
  className?: string;
}

const StoryFlow: React.FC<StoryFlowProps> = ({
  chapters,
  selectedChapter,
  onChapterSelect,
  onAddMainChapter,
  onAddBranchChapter,
  onRemoveChapter,
  onChapterReorder,
  lang,
  title,
  icon,
  accentColor = 'purple',
  showAddButton = true,
  showBranchButtons = true,
  showRemoveButtons = true,
  className = ''
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [draggedChapter, setDraggedChapter] = React.useState<string | null>(null);
  const [dragOverChapter, setDragOverChapter] = React.useState<string | null>(null);

  // Get chapters that will be affected by dragging (main chapter + its branches)
  const getAffectedChapters = useCallback((chapterId: string) => {
    const chapter = chapters.find(c => c.id === chapterId);
    if (!chapter) return [chapterId];

    if (chapter.chapterType === 'main') {
      // If dragging main chapter, include all its branches
      const branchChapters = chapters.filter(c =>
        c.chapterType === 'branch' && c.parentChapter === chapter.id
      );
      return [chapterId, ...branchChapters.map(c => c.id)];
    }

    // If dragging branch chapter, only affect itself
    return [chapterId];
  }, [chapters]);

  // Generate chapter display name
  const getChapterDisplayName = useCallback((chapter: StoryChapter) => {
    if (chapter.chapterType === 'main') {
      return `${chapter.order}`;
    } else {
      return `${chapter.order}${chapter.branchLetter || ''}`;
    }
  }, []);

  // Sort chapters by order and type
  const sortedChapters = [...chapters].sort((a, b) => {
    if (a.order !== b.order) return a.order - b.order;
    if (a.chapterType !== b.chapterType) {
      return a.chapterType === 'main' ? -1 : 1;
    }
    return (a.branchLetter || '').localeCompare(b.branchLetter || '');
  });

  // Color scheme based on accent color
  const colorSchemes = {
    purple: {
      border: 'border-purple-500',
      bg: 'bg-purple-50 dark:bg-purple-900/30',
      hover: 'hover:border-purple-300 dark:hover:border-purple-600',
      button: 'bg-purple-500 hover:bg-purple-600',
      mainBadge: 'bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300',
      branchBadge: 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300',
      icon: 'text-purple-500'
    },
    blue: {
      border: 'border-blue-500',
      bg: 'bg-blue-50 dark:bg-blue-900/30',
      hover: 'hover:border-blue-300 dark:hover:border-blue-600',
      button: 'bg-blue-500 hover:bg-blue-600',
      mainBadge: 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300',
      branchBadge: 'bg-cyan-100 dark:bg-cyan-900 text-cyan-700 dark:text-cyan-300',
      icon: 'text-blue-500'
    },
    rose: {
      border: 'border-rose-500',
      bg: 'bg-rose-50 dark:bg-rose-900/30',
      hover: 'hover:border-rose-300 dark:hover:border-rose-600',
      button: 'bg-rose-500 hover:bg-rose-600',
      mainBadge: 'bg-rose-100 dark:bg-rose-900 text-rose-700 dark:text-rose-300',
      branchBadge: 'bg-pink-100 dark:bg-pink-900 text-pink-700 dark:text-pink-300',
      icon: 'text-rose-500'
    }
  };

  const colors = colorSchemes[accentColor];

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, chapterId: string) => {
    setDraggedChapter(chapterId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', chapterId);
  };

  const handleDragOver = (e: React.DragEvent, chapterId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverChapter(chapterId);
  };

  const handleDragLeave = () => {
    setDragOverChapter(null);
  };

  const handleDrop = (e: React.DragEvent, targetChapterId: string) => {
    e.preventDefault();
    const draggedChapterId = e.dataTransfer.getData('text/plain');

    if (draggedChapterId === targetChapterId || !onChapterReorder) {
      setDraggedChapter(null);
      setDragOverChapter(null);
      return;
    }

    const draggedChapterData = chapters.find(c => c.id === draggedChapterId);
    const targetChapterData = chapters.find(c => c.id === targetChapterId);

    if (!draggedChapterData || !targetChapterData) {
      setDraggedChapter(null);
      setDragOverChapter(null);
      return;
    }

    // Only allow reordering within the same type (main with main, branch with branch)
    if (draggedChapterData.chapterType === targetChapterData.chapterType) {
      // For branch chapters, ensure they have the same parent
      if (draggedChapterData.chapterType === 'branch' &&
          draggedChapterData.parentChapter !== targetChapterData.parentChapter) {
        setDraggedChapter(null);
        setDragOverChapter(null);
        return;
      }

      onChapterReorder(draggedChapterId, targetChapterData.order, targetChapterData.parentChapter);
    }

    setDraggedChapter(null);
    setDragOverChapter(null);
  };

  const handleDragEnd = () => {
    setDraggedChapter(null);
    setDragOverChapter(null);
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-3 lg:p-4 border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-3 lg:mb-4">
        <h3 className="text-base lg:text-sm font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-1 lg:gap-2">
          <span className={colors.icon}>{icon}</span>
          <span className="truncate">{title}</span>
        </h3>
        {showAddButton && onAddMainChapter && (
          <button
            type="button"
            onClick={onAddMainChapter}
            className={`flex items-center justify-center w-8 h-8 lg:w-9 lg:h-9 ${colors.button} text-white rounded-lg transition-colors`}
            title={t('storyCreation.steps.chapters.addMainChapter')}
          >
            <Plus size={16} className="lg:w-5 lg:h-5" />
          </button>
        )}
      </div>

      {/* Chapter Flow List */}
      <div className="space-y-1.5 lg:space-y-2">
        {sortedChapters.map((chapter) => {
          const affectedChapters = draggedChapter ? getAffectedChapters(draggedChapter) : [];
          const isAffectedByDrag = affectedChapters.includes(chapter.id);

          return (
            <div key={chapter.id} className="space-y-1.5 lg:space-y-2">
              {/* Main or Branch Chapter */}
              <div
                draggable={onChapterReorder ? true : false}
                onDragStart={(e) => onChapterReorder && handleDragStart(e, chapter.id)}
                onDragOver={(e) => onChapterReorder && handleDragOver(e, chapter.id)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => onChapterReorder && handleDrop(e, chapter.id)}
                onDragEnd={handleDragEnd}
                className={`p-2 lg:p-3 rounded-lg border cursor-pointer transition-all ${
                  selectedChapter === chapter.id
                    ? `${colors.border} ${colors.bg}`
                    : `border-gray-200 dark:border-gray-600 ${colors.hover}`
                } ${chapter.chapterType === 'branch' ? 'ml-4 lg:ml-6' : ''} ${
                  isAffectedByDrag ? 'opacity-50 ring-2 ring-blue-300 ring-opacity-50' : ''
                } ${
                  dragOverChapter === chapter.id ? 'ring-2 ring-purple-400 ring-opacity-50' : ''
                }`}
                onClick={() => onChapterSelect(chapter.id)}
              >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 lg:gap-3 min-w-0 flex-1">
                  <div className={`w-6 h-6 lg:w-8 lg:h-8 rounded-full flex items-center justify-center text-xs lg:text-sm font-bold ${
                    chapter.chapterType === 'main'
                      ? colors.mainBadge
                      : colors.branchBadge
                  }`}>
                    {getChapterDisplayName(chapter)}
                  </div>
                  {/* Responsive text display */}
                  <div className="hidden lg:block min-w-0 flex-1">
                    <div className="font-medium text-gray-800 dark:text-gray-100 text-xs truncate">
                      {chapter.title}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {chapter.chapterType === 'main' ? t('storyCreation.storyFlow.chapters.mainChapter') : t('storyCreation.storyFlow.chapters.branchChapter')}
                    </div>
                  </div>
                  {/* Mobile: Show only truncated title */}
                  <div className="lg:hidden min-w-0 flex-1">
                    <div className="font-medium text-gray-800 dark:text-gray-100 text-sm truncate">
                      {chapter.title}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1 lg:gap-2 flex-shrink-0">
                  {showBranchButtons && chapter.chapterType === 'main' && onAddBranchChapter && (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onAddBranchChapter(chapter.order);
                      }}
                      className="flex items-center justify-center w-6 h-6 lg:w-7 lg:h-7 text-blue-500 hover:text-blue-700 dark:hover:text-blue-300 rounded transition-colors"
                      title={t('storyCreation.steps.chapters.addBranchChapter')}
                    >
                      <Plus size={12} className="lg:w-3.5 lg:h-3.5" />
                    </button>
                  )}
                  {showRemoveButtons && onRemoveChapter && (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onRemoveChapter(chapter.id);
                      }}
                      className="p-0.5 lg:p-1 text-red-500 hover:text-red-700 dark:hover:text-red-300"
                      title={t('storyCreation.steps.chapters.removeChapter')}
                    >
                      <X size={14} className="lg:w-4 lg:h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
        })}
      </div>

      {/* Empty state */}
      {chapters.length === 0 && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <p className="mb-4">{t('storyCreation.steps.chapters.noChapters')}</p>
          {showAddButton && onAddMainChapter && (
            <button
              type="button"
              onClick={onAddMainChapter}
              className={`inline-flex items-center justify-center w-12 h-12 ${colors.button} text-white rounded-lg transition-colors`}
              title={t('storyCreation.steps.chapters.addFirstChapter')}
            >
              <Plus size={20} />
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default StoryFlow;
