import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import CreatorProfileUidClientPage from './CreatorProfileUidClientPage';
import { characters, generateAllSampleMoments, getManagedCharacters } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';
import { getUserByUid } from '@/lib/auth-api';
import { notFound } from 'next/navigation';

// Mock story data
const generateCreatorStories = () => {
  const storyTitles = [
    "The Enchanted Forest Adventure",
    "Cyberpunk City Chronicles", 
    "Medieval Kingdom Quest",
    "Space Station Mystery",
    "Magical Academy Tales",
    "Post-Apocalyptic Survival",
    "Victorian Era Romance",
    "Underwater Kingdom Saga"
  ];

  return storyTitles.map((title, index) => ({
    id: `story-${index + 1}`,
    title,
    description: `An immersive story experience featuring ${title.toLowerCase()}`,
    coverImage: `https://picsum.photos/400/600?random=${index + 20}`,
    likes: Math.floor(Math.random() * 500) + 50,
    reads: Math.floor(Math.random() * 2000) + 100,
    chapters: Math.floor(Math.random() * 20) + 5,
    status: Math.random() > 0.3 ? 'published' : 'draft',
    createdAt: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
  }));
};

// Mock dashboard data
const generateDashboardData = () => {
  return {
    totalEarnings: 2456,
    monthlyEarnings: 456,
    totalFollowers: 12847,
    totalLikes: 45623,
    totalCharacters: 23,
    totalStories: 8,
    topCharacters: getManagedCharacters().slice(0, 5),
    topStories: generateCreatorStories().slice(0, 3),
    monthlyStats: [
      { month: 'Jan', earnings: 234, interactions: 1200 },
      { month: 'Feb', earnings: 345, interactions: 1450 },
      { month: 'Mar', earnings: 456, interactions: 1680 },
      { month: 'Apr', earnings: 567, interactions: 1920 },
      { month: 'May', earnings: 432, interactions: 1750 },
      { month: 'Jun', earnings: 456, interactions: 1890 },
    ]
  };
};

export default async function CreatorProfileUidPage({ 
  params 
}: { 
  params: Promise<{ lang: string; uid: string }> 
}) {
  const { lang, uid } = await params;
  
  // For now, we'll use mock data since server-side API calls need proper setup
  // In a real implementation, you would fetch user data here or use client-side fetching
  let userData = null;

  // Mock user data based on UID for demonstration
  userData = {
    _id: uid,
    uid: uid,
    name: `${uid.slice(-4)}`,
    email: `user${uid.slice(-4)}@example.com`,
    avatar: `https://i.pravatar.cc/160?u=${uid}`,
    bio: 'Passionate about crafting immersive AI characters and captivating stories. Building worlds where imagination meets technology.',
    subscriber_count: Math.floor(Math.random() * 50000) + 1000,
    follow_count: Math.floor(Math.random() * 2000) + 100,
    character_count: Math.floor(Math.random() * 50) + 5,
    story_count: Math.floor(Math.random() * 20) + 2,
  };
  
  // Generate mock data
  const createdCharacters = getManagedCharacters();
  const createdStories = generateCreatorStories();
  const dashboardData = generateDashboardData();

  return (
    <AuthGuard requireAuth={false}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <MainAppLayout lang={lang}>
          <CreatorProfileUidClientPage 
            lang={lang} 
            uid={uid}
            userData={userData}
            createdCharacters={createdCharacters}
            createdStories={createdStories}
            dashboardData={dashboardData}
          />
        </MainAppLayout>
      </Suspense>
    </AuthGuard>
  );
}
