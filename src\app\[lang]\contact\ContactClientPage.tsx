'use client';

import React, { useState } from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Mail, MessageSquare, Copy, Check } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { ContactForm, SuccessMessage, ContactFormData } from '@/components/contact';

interface ContactClientPageProps {
  lang: string;
}

const ContactClientPage: React.FC<ContactClientPageProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [emailCopied, setEmailCopied] = useState(false);

  const handleSubmit = async (formData: ContactFormData) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Here you would typically send the data to your backend
      console.log('Contact form submitted:', formData);
      
      setSubmitted(true);
      setError(null);
    } catch (err) {
      console.error('Failed to submit contact form:', err);
      setError(t('contact.error.message'));
    }
  };

  const handleSendAnother = () => {
    setSubmitted(false);
    setError(null);
  };

  const handleCopyEmail = async () => {
    try {
      await navigator.clipboard.writeText(t('contact.emailContact.email'));
      setEmailCopied(true);
      setTimeout(() => setEmailCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy email:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = t('contact.emailContact.email');
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setEmailCopied(true);
      setTimeout(() => setEmailCopied(false), 2000);
    }
  };

  if (submitted) {
    return (
      <MainAppLayout lang={lang}>
        <div className="min-h-screen bg-background theme-transition">
          <div className="max-w-2xl mx-auto p-6 pt-16">
            <SuccessMessage onSendAnother={handleSendAnother} lang={lang} />
          </div>
        </div>
      </MainAppLayout>
    );
  }

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-background theme-transition">
        <div className="max-w-2xl mx-auto p-6 pt-16">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-foreground mb-2">
              {t('contact.title')}
            </h1>
            <p className="text-foreground/70">
              {t('contact.subtitle')}
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <MessageSquare className="w-5 h-5 text-destructive" />
                <div>
                  <h3 className="font-medium text-destructive">
                    {t('contact.error.title')}
                  </h3>
                  <p className="text-sm text-destructive/80">
                    {error}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Contact Form */}
          <div className="bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 p-8 shadow-lg mb-6">
            <ContactForm onSubmit={handleSubmit} lang={lang} />
          </div>

          {/* Email Contact - Compact Version */}
          <div className="bg-muted/30 border border-border/30 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Mail className="w-4 h-4 text-primary" />
                <div>
                  <p className="text-sm text-foreground/80">
                    {t('contact.emailContact.title')}
                  </p>
                  <p className="text-sm font-mono text-foreground">
                    {t('contact.emailContact.email')}
                  </p>
                </div>
              </div>
              <button
                onClick={handleCopyEmail}
                className="flex items-center space-x-1 px-3 py-2 text-xs bg-secondary hover:bg-secondary/80 text-secondary-foreground rounded-md transition-colors"
                title={t('contact.emailContact.copyEmail')}
              >
                {emailCopied ? (
                  <>
                    <Check className="w-3 h-3" />
                    <span>{t('contact.emailContact.emailCopied')}</span>
                  </>
                ) : (
                  <>
                    <Copy className="w-3 h-3" />
                    <span>{t('contact.emailContact.copyEmail')}</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Additional Info */}
          <div className="text-center text-sm text-foreground/50">
            <p>
              {t('contact.additionalInfo')}
            </p>
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
};

export default ContactClientPage; 