import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format currency display
export function formatCurrency(value: number | undefined): string {
  if (value === undefined || value === null) return '0';

  // If the value is large, use K, M etc. for simplified display
  if (value >= 1000000) {
    return (value / 1000000).toFixed(1) + 'M';
  } else if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'K';
  }

  return value.toLocaleString();
}

// Calculate streak progress percentage for 25-day cycles
export function calculateStreakProgress(currentDays: number | undefined): number {
  if (!currentDays) return 0;

  // Calculate progress within current 25-day cycle
  const daysInCurrentCycle = currentDays - Math.floor(currentDays / 25) * 25;
  return (daysInCurrentCycle / 25) * 100;
}

// Calculate streak milestone multiplier
export function calculateStreakMultiplier(currentDays: number | undefined): number {
  if (!currentDays) return 1;

  // Formula: 1 + min(1, floor(max(0,currentDays-25)/5)/5)
  const multiplier = 1 + Math.min(1, Math.floor(Math.max(0, currentDays - 25) / 5) / 5);
  return multiplier;
}

// Get streak status text (with I18N support)
export function getStreakStatusText(
  currentDays: number | undefined,
  t: (key: string, options?: any) => string
): string {
  if (!currentDays) {
    return t('streak.start');
  }

  const multiplier = calculateStreakMultiplier(currentDays);

  return t('streak.milestonemultiplier', {
    multiplier: multiplier.toFixed(1)
  });
}
