'use client';

import React from 'react';
import Image from 'next/image';
import { Edit3 } from 'lucide-react';
import ProfileTags from './ProfileTags';
import QRShareButton from './QRShareButton';

interface StatItem {
  label: string;
  value: string | number;
  icon?: React.ComponentType<{ className?: string }>;
}

interface EnhancedHeroSectionProps {
  backgroundImage?: string;
  title: string;
  subtitle?: string;
  description?: string;
  avatar?: string;
  avatarSize?: 'sm' | 'md' | 'lg' | 'xl';
  stats?: StatItem[];
  actions?: React.ReactNode;
  children?: React.ReactNode;
  height?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'profile' | 'character';
  className?: string;
  // Enhanced props
  tags?: string[];
  creatorInfo?: string;
  showQRShare?: boolean;
  isOwner?: boolean;
  onEditDescription?: () => void;
  userBadge?: React.ReactNode;
}

const EnhancedHeroSection: React.FC<EnhancedHeroSectionProps> = ({
  backgroundImage,
  title,
  subtitle,
  description,
  avatar,
  avatarSize = 'lg',
  stats = [],
  actions,
  children,
  height = 'lg',
  variant = 'default',
  className = '',
  // Enhanced props
  tags,
  creatorInfo,
  showQRShare,
  isOwner,
  onEditDescription,
  userBadge
}) => {
  const getHeightClass = () => {
    switch (height) {
      case 'sm': return 'h-[300px]';
      case 'md': return 'h-[400px]';
      case 'lg': return 'h-[480px]';
      case 'xl': return 'h-[600px]';
      default: return 'h-[480px]';
    }
  };

  return (
    <section className={`relative w-full ${getHeightClass()} bg-romantic-gradient overflow-hidden flex items-end ${className}`}>
      {/* Background Image */}
      {backgroundImage && (
        <Image
          src={backgroundImage}
          alt="hero background"
          fill
          className="object-cover"
          unoptimized
        />
      )}
      
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />

      {/* SOTA Layout with Optimized Visual Padding */}
      <div className="relative z-10 px-8 py-6 w-full space-y-4">
        {/* Avatar Row - SOTA 37:63 Layout */}
        <div className="flex gap-4">
          {/* Avatar Section - 37% width with larger avatar */}
          {avatar && (
            <div className="w-[37%] flex justify-start">
              <div className="w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-36 lg:h-36 xl:w-44 xl:h-44 rounded-full border-4 border-white/60 relative shadow-lg bg-white/10 backdrop-blur-sm p-1 flex-shrink-0">
                <Image
                  src={avatar}
                  alt={title}
                  fill
                  className="rounded-full object-cover"
                  unoptimized
                />
                {/* Optional level badge for character variant */}
                {variant === 'character' && (
                  <div className="absolute -bottom-1 -right-1 bg-romantic-gradient text-white text-xs sm:text-sm px-2 py-1 rounded-full font-bold border-2 border-white shadow-lg">
                    Lv.5
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Info Section - 63% width with 50:50 height split */}
          <div className="flex-1 text-white flex flex-col justify-center h-24 sm:h-28 md:h-32 lg:h-36 xl:h-44">
            {/* Top Half: Name + User Badge - 50% of info height */}
            <div className="flex items-center gap-2 h-[50%] min-h-[36px]">
              <h1 className="text-base sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold truncate flex-1 leading-tight">{title}</h1>
              {userBadge && <div className="flex-shrink-0">{userBadge}</div>}
            </div>

            {/* Bottom Half: QR Row + Tags (2 rows) - 50% of info height */}
            <div className="h-[50%] flex flex-col justify-start space-y-1">
              {/* QR Row - 1/3 of bottom half */}
              {(subtitle || creatorInfo || showQRShare) && (
                <div className="flex items-center justify-between h-[33%] min-h-[18px]">
                  <p className="text-xs sm:text-sm opacity-90 flex-1 truncate">
                    {subtitle || creatorInfo}
                  </p>
                  {showQRShare && <QRShareButton size="sm" className="flex-shrink-0" />}
                </div>
              )}

              {/* Tags - 2/3 of bottom half (2 rows) */}
              <div className="h-[67%] flex items-start">
                {tags && tags.length > 0 && (
                  <ProfileTags
                    tags={tags}
                    maxTags={8}
                    variant="compact"
                    className="max-h-full overflow-hidden"
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Description Row - Consistent 2rem visual padding */}
        {description && (
          <div className="flex items-start gap-2">
            <p className="text-xs sm:text-sm text-white/90 line-clamp-2 md:line-clamp-3 flex-1">
              {description}
            </p>
            {isOwner && onEditDescription && (
              <button
                onClick={onEditDescription}
                className="flex-shrink-0 p-1 text-white/70 hover:text-white transition-colors"
                title="Edit Description"
              >
                <Edit3 size={14} />
              </button>
            )}
          </div>
        )}
        
        {/* Stats Grid - SOTA Compact Layout with Exact 2rem Visual Padding */}
        {stats.length > 0 && (
          <div className="-mx-4">
            <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 text-center px-4">
              {stats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <div key={index} className="min-w-0">
                    <div className="text-sm sm:text-lg md:text-xl font-bold text-white flex items-center justify-center gap-1">
                      {Icon && <Icon className="w-3 h-3 sm:w-4 sm:h-4" />}
                      <span className="truncate">{stat.value}</span>
                    </div>
                    <div className="text-xs text-white/80 truncate">{stat.label}</div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
        
        {/* Actions and Children */}
        {(actions || children) && (
          <div className="space-y-3">
            {actions}
            {children}
          </div>
        )}
      </div>
    </section>
  );
};

export default EnhancedHeroSection;
