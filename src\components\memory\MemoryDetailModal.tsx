import React, { useState } from 'react';
import { useTranslation } from '@/app/i18n/client';
import { X, Edit3, Trash2, Star, Tag, Image as ImageIcon, Sparkles, Download, Share2 } from 'lucide-react';
import type { Memory } from '@/app/[lang]/manage-memories/ManageMemoryClientPage';

interface MemoryDetailModalProps {
  memory: Memory;
  onClose: () => void;
  onUpdate: (memory: Memory) => void;
  onDelete: (memoryId: string) => void;
  lang: string;
}

const MemoryDetailModal: React.FC<MemoryDetailModalProps> = ({
  memory,
  onClose,
  onUpdate,
  onDelete,
  lang,
}) => {
  const { t: _ } = useTranslation(lang, 'translation');
  const [isEditing, setIsEditing] = useState(false);
  const [editedMemory, setEditedMemory] = useState(memory);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleSave = () => {
    onUpdate(editedMemory);
    setIsEditing(false);
  };

  const handleDelete = () => {
    onDelete(memory.memory_id_pk);
  };

  const formatFullDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(lang === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="relative h-48 bg-gradient-to-br from-purple-500 to-pink-500 p-6">
          {memory.artwork_url && (
            <img
              src={memory.artwork_url}
              alt=""
              className="absolute inset-0 w-full h-full object-cover opacity-30"
            />
          )}
          <div className="relative z-10 flex justify-between items-start">
            <div className="flex items-center gap-4">
              <img
                src={memory.source_character.avatar}
                alt={memory.source_character.name}
                className="w-16 h-16 rounded-full border-4 border-white/30 shadow-lg"
              />
              <div>
                <h2 className="text-2xl font-bold text-white">
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedMemory.capsule_name}
                      onChange={(e) => setEditedMemory({ ...editedMemory, capsule_name: e.target.value })}
                      className="bg-white/20 border border-white/30 rounded-lg px-3 py-1 text-white placeholder-white/70"
                    />
                  ) : (
                    memory.capsule_name
                  )}
                </h2>
                <p className="text-white/80">
                  {memory.source_character.name} · {formatFullDate(memory.created_at)}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 text-white" />
            </button>
          </div>

          {/* Action Buttons */}
          <div className="absolute bottom-4 right-4 flex gap-2">
            {!isEditing ? (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-3 py-1.5 bg-white/20 hover:bg-white/30 text-white rounded-lg flex items-center gap-2 transition-colors"
                >
                  <Edit3 size={16} />
                  {_('memory.action.edit', 'Edit')}
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="px-3 py-1.5 bg-red-500/20 hover:bg-red-500/30 text-white rounded-lg flex items-center gap-2 transition-colors"
                >
                  <Trash2 size={16} />
                  {_('memory.action.delete', 'Delete')}
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={handleSave}
                  className="px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
                >
                  {_('memory.action.save', 'Save')}
                </button>
                <button
                  onClick={() => {
                    setEditedMemory(memory);
                    setIsEditing(false);
                  }}
                  className="px-3 py-1.5 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
                >
                  {_('memory.action.cancel', 'Cancel')}
                </button>
              </>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-12rem)]">
          {/* Summary */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {_('memory.detail.summary', 'Memory Summary')}
            </h3>
            {isEditing ? (
              <textarea
                value={editedMemory.summary}
                onChange={(e) => setEditedMemory({ ...editedMemory, summary: e.target.value })}
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
                rows={3}
              />
            ) : (
              <p className="text-gray-600 dark:text-gray-300">{memory.summary}</p>
            )}
          </div>

          {/* Full Dialogue */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {_('memory.detail.fullDialogue', 'Full Dialogue')}
            </h3>
            <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-3">
              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {memory.dialogue_context}
              </p>
            </div>
          </div>

          {/* Metadata */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Importance */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {_('memory.detail.importance', 'Importance Score')}
              </h4>
              <div className="flex items-center gap-2">
                {[1, 2, 3, 4, 5].map((value) => (
                  <button
                    key={value}
                    onClick={() => isEditing && setEditedMemory({ ...editedMemory, importance_score: value })}
                    disabled={!isEditing}
                    className={`${isEditing ? 'cursor-pointer' : 'cursor-default'}`}
                  >
                    <Star
                      size={24}
                      className={`${
                        value <= (isEditing ? editedMemory : memory).importance_score
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300 dark:text-gray-600'
                      } transition-colors`}
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* Tags */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {_('memory.detail.tags', 'Tags')}
              </h4>
              <div className="flex flex-wrap gap-2">
                {(isEditing ? editedMemory : memory).tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-full text-sm flex items-center gap-1"
                  >
                    <Tag size={12} />
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* AI Insights */}
          {memory.reference_count !== undefined && memory.reference_count > 0 && (
            <div className="mt-6 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <h4 className="text-sm font-medium text-purple-900 dark:text-purple-200 mb-2">
                {_('memory.detail.aiInsights', 'AI Insights')}
              </h4>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                {_('memory.detail.referenceCount', `This memory has been referenced {{count}} times by AI, playing an important role in your conversations with {{character}}.`).replace('{{count}}', memory.reference_count.toString()).replace('{{character}}', memory.source_character.name)}
              </p>
            </div>
          )}

          {/* Additional Actions */}
          <div className="mt-6 flex flex-wrap gap-3">
            <button className="px-4 py-2 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-lg flex items-center gap-2 hover:bg-purple-200 dark:hover:bg-purple-900/70 transition-colors">
              <Sparkles size={16} />
              {_('memory.action.generateArt', 'Generate Memory Art')}
            </button>
            <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg flex items-center gap-2 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
              <Share2 size={16} />
              {_('memory.action.share', 'Share')}
            </button>
            <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg flex items-center gap-2 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
              <Download size={16} />
              {_('memory.action.export', 'Export')}
            </button>
          </div>
        </div>

        {/* Delete Confirmation */}
        {showDeleteConfirm && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm w-full">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {_('memory.confirm.deleteTitle', 'Confirm Delete')}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                {_('memory.confirm.deleteMessage', 'Are you sure you want to delete this memory? This action cannot be undone.')}
              </p>
              <div className="flex gap-3">
                <button
                  onClick={handleDelete}
                  className="flex-1 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                >
                  {_('memory.action.confirmDelete', 'Confirm Delete')}
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
                >
                  {_('memory.action.cancel', 'Cancel')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MemoryDetailModal; 