import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import CumulativeProgressReward from '../CumulativeProgressReward';

// Mock the translation hook
jest.mock('@/app/i18n/client', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      const translations: { [key: string]: string } = {
        'journey.progress.bigReward': 'Big Reward!',
        'journey.progress.claim': 'Claim',
        'journey.progress.nextReward': `Next reward at ${params?.count} tasks`
      };
      return translations[key] || key;
    }
  })
}));

describe('CumulativeProgressReward', () => {
  const defaultProps = {
    activeTab: 'daily' as const,
    completedTasks: 3,
    totalTasks: 9,
    lang: 'en',
    onClaimReward: jest.fn()
  };

  it('renders progress bar correctly', () => {
    render(<CumulativeProgressReward {...defaultProps} />);
    
    expect(screen.getByText('3/9 tasks completed')).toBeInTheDocument();
  });

  it('shows big reward when all tasks completed', () => {
    render(
      <CumulativeProgressReward 
        {...defaultProps} 
        completedTasks={9}
        totalTasks={9}
      />
    );
    
    expect(screen.getByText('Big Reward!')).toBeInTheDocument();
    expect(screen.getByText('Claim')).toBeInTheDocument();
  });

  it('displays correct milestones for daily tab', () => {
    render(<CumulativeProgressReward {...defaultProps} />);
    
    // Daily milestones: [0, 3, 5, 7]
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('7')).toBeInTheDocument();
  });

  it('displays correct milestones for weekly tab', () => {
    render(
      <CumulativeProgressReward 
        {...defaultProps} 
        activeTab="weekly"
        completedTasks={5}
        totalTasks={18}
      />
    );
    
    // Weekly milestones: [0, 5, 9, 12, 14, 15]
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('9')).toBeInTheDocument();
    expect(screen.getByText('12')).toBeInTheDocument();
  });

  it('displays correct milestones for monthly tab', () => {
    render(
      <CumulativeProgressReward 
        {...defaultProps} 
        activeTab="monthly"
        completedTasks={9}
        totalTasks={30}
      />
    );
    
    // Monthly milestones: [0, 9, 16, 22, 24, 25]
    expect(screen.getByText('9')).toBeInTheDocument();
    expect(screen.getByText('16')).toBeInTheDocument();
    expect(screen.getByText('22')).toBeInTheDocument();
  });

  it('calls onClaimReward when claim button is clicked', () => {
    const mockOnClaimReward = jest.fn();
    render(
      <CumulativeProgressReward 
        {...defaultProps} 
        completedTasks={9}
        totalTasks={9}
        onClaimReward={mockOnClaimReward}
      />
    );
    
    const claimButton = screen.getByText('Claim');
    claimButton.click();
    
    expect(mockOnClaimReward).toHaveBeenCalledWith(7); // Max milestone for daily
  });
});
