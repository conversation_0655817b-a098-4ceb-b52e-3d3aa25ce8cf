'use client';

import React from 'react';
import { 
  ArrowR<PERSON>, 
  Gift, 
  Check, 
  Zap, 
  Sparkles, 
  <PERSON>, 
  Brain, 
  ShoppingBag, 
  Target,
  Flame,
  Gem,
  Puzzle,
  Droplet,
  Star,
  Trophy
} from 'lucide-react';

// Types for mission data
export interface MissionReward {
  currency: 'star_diamonds' | 'glimmering_dust' | 'joy_crystals' | 'memory_puzzles' | 'bond_dew' | 'trophy_points';
  amount: number;
}

export interface MissionStatus {
  completed: boolean;
  rewardClaimed: boolean;
}

export interface JourneyMissionsRowProps {
  id: string;
  category: 'interaction' | 'creation' | 'social' | 'memory' | 'store';
  title: string;
  description: string;
  progress: {
    current: number;
    total: number;
  };
  rewards: MissionReward[];
  status: MissionStatus;
  onAction: (id: string, action: 'navigate' | 'claim') => void;
  isClaimingReward?: boolean;
  className?: string;
}

const JourneyMissionsRow: React.FC<JourneyMissionsRowProps> = ({
  id,
  category,
  title,
  description,
  progress,
  rewards,
  status,
  onAction,
  isClaimingReward = false,
  className = ''
}) => {
  // Get category icon
  const getCategoryIcon = () => {
    switch (category) {
      case 'interaction': return <Zap className="w-6 h-6 text-yellow-500" />;
      case 'creation': return <Sparkles className="w-6 h-6 text-purple-500" />;
      case 'social': return <Users className="w-6 h-6 text-blue-500" />;
      case 'memory': return <Brain className="w-6 h-6 text-green-500" />;
      case 'store': return <ShoppingBag className="w-6 h-6 text-indigo-500" />;
      default: return <Target className="w-6 h-6 text-gray-500" />;
    }
  };

  // Get currency icon and color
  const getCurrencyIcon = (currency: MissionReward['currency']) => {
    switch (currency) {
      case 'star_diamonds':
        return { icon: <Star className="w-4 h-4" />, color: 'text-yellow-500', bg: 'bg-yellow-100 dark:bg-yellow-900/20' };
      case 'glimmering_dust':
        return { icon: <Flame className="w-4 h-4" />, color: 'text-orange-500', bg: 'bg-orange-100 dark:bg-orange-900/20' };
      case 'joy_crystals':
        return { icon: <Gem className="w-4 h-4" />, color: 'text-blue-500', bg: 'bg-blue-100 dark:bg-blue-900/20' };
      case 'memory_puzzles':
        return { icon: <Puzzle className="w-4 h-4" />, color: 'text-purple-500', bg: 'bg-purple-100 dark:bg-purple-900/20' };
      case 'bond_dew':
        return { icon: <Droplet className="w-4 h-4" />, color: 'text-pink-500', bg: 'bg-pink-100 dark:bg-pink-900/20' };
      case 'trophy_points':
        return { icon: <Trophy className="w-4 h-4" />, color: 'text-orange-500', bg: 'bg-orange-100 dark:bg-orange-900/20' };
      default:
        return { icon: <Star className="w-4 h-4" />, color: 'text-gray-500', bg: 'bg-gray-100 dark:bg-gray-900/20' };
    }
  };

  // Get action button content and styling
  const getActionButton = () => {
    if (!status.completed) {
      // Incomplete state - show arrow button
      return {
        icon: <ArrowRight className="w-6 h-6" />,
        text: '',
        className: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-blue-500/25',
        action: 'navigate' as const
      };
    } else if (status.completed && !status.rewardClaimed) {
      // Completed but not claimed - show gift button
      if (isClaimingReward) {
        return {
          icon: <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />,
          text: '',
          className: 'bg-gradient-to-r from-green-500 to-green-600 text-white cursor-not-allowed shadow-green-500/25',
          action: null
        };
      }
      return {
        icon: <Gift className="w-6 h-6" />,
        text: '',
        className: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white animate-pulse shadow-green-500/25',
        action: 'claim' as const
      };
    } else {
      // Completed and claimed - show check mark (disabled)
      return {
        icon: <Check className="w-6 h-6" />,
        text: '',
        className: 'bg-gradient-to-r from-gray-400 to-gray-500 text-gray-600 cursor-not-allowed shadow-gray-400/25',
        action: null
      };
    }
  };

  const actionButton = getActionButton();
  const progressPercentage = (progress.current / progress.total) * 100;

  // Format progress display for better readability
  const formatProgress = (current: number, total: number) => {
    if (Number.isInteger(current) && Number.isInteger(total)) {
      return `${current}/${total}`;
    }
    // For decimal progress, show one decimal place if needed
    const formattedCurrent = current % 1 === 0 ? current.toString() : current.toFixed(1);
    const formattedTotal = total % 1 === 0 ? total.toString() : total.toFixed(1);
    return `${formattedCurrent}/${formattedTotal}`;
  };

  // Special styling for completed and claimed missions
  const isCompletedAndClaimed = status.completed && status.rewardClaimed;
  const isCompletedUnclaimed = status.completed && !status.rewardClaimed;

  const rowClassName = isCompletedAndClaimed
    ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-green-900/10 dark:via-emerald-900/10 dark:to-teal-900/10 border-green-200 dark:border-green-700/50 shadow-sm opacity-75'
    : isCompletedUnclaimed
    ? 'bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50 dark:from-yellow-900/10 dark:via-amber-900/10 dark:to-orange-900/10 border-yellow-200 dark:border-yellow-700/50 shadow-md hover:shadow-lg ring-2 ring-yellow-200/50 dark:ring-yellow-700/30'
    : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-500 hover:bg-gray-50/50 dark:hover:bg-gray-750';

  return (
    <div className={`
      p-5 rounded-xl border-2 transition-all duration-300 group
      ${rowClassName}
      ${className}
    `}>
      {/* Row 1: Logo + Title & Description */}
      <div className="flex items-center gap-4 mb-4">
        {/* 1. Task type logo (center-aligned with text content) */}
        <div className="flex-shrink-0">
          <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-200">
            {getCategoryIcon()}
          </div>
        </div>

        {/* 2. Two lines of text (right of logo) */}
        <div className="flex-1 min-w-0">
          <h3 className="font-bold text-gray-900 dark:text-gray-100 text-lg leading-tight mb-2 truncate">
            {title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed line-clamp-2">
            {description}
          </p>
        </div>
      </div>

      {/* Row 2: Stretched Progress + Right-aligned Rewards & Action */}
      <div className="flex items-center gap-3 sm:gap-4 md:gap-6 bg-transparent m-0">
        {/* 3. Pure Numerical Progress Bar (stretched to fill space) */}
        <div className="flex-1 min-w-0">
          <div className="mb-2">
            <div className="text-xs sm:text-sm font-bold text-gray-700 dark:text-gray-300">
              {formatProgress(progress.current, progress.total)}
            </div>
          </div>
          <div className="relative w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 shadow-inner overflow-hidden border border-gray-300/50 dark:border-gray-600/50">
            {/* Background pattern for visual interest */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>

            {/* Progress fill with enhanced gradient and effects */}
            <div
              className={`h-full rounded-full transition-all duration-700 ease-out relative overflow-hidden ${
                progressPercentage >= 100
                  ? 'bg-gradient-to-r from-green-400 via-green-500 to-green-600 shadow-green-500/25'
                  : progressPercentage >= 75
                  ? 'bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 shadow-blue-500/25'
                  : progressPercentage >= 50
                  ? 'bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 shadow-yellow-500/25'
                  : 'bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600 shadow-orange-500/25'
              }`}
              style={{ width: `${Math.max(progressPercentage, 0)}%` }}
            >
              {/* Animated shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-pulse"></div>

              {/* Moving highlight for active progress */}
              {progressPercentage > 0 && progressPercentage < 100 && (
                <div className="absolute right-0 top-0 w-1 h-full bg-white/60 animate-pulse"></div>
              )}
            </div>

            {/* Completion sparkle effect */}
            {progressPercentage >= 100 && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
              </div>
            )}
          </div>
        </div>

        {/* 4 & 5: Right-aligned Rewards + Action Button */}
        <div className="flex items-start gap-1 flex-shrink-0">
          {/* 4. Trophy reward (fixed position) */}
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg sm:rounded-xl mb-1 flex items-center justify-center shadow-sm bg-yellow-100 dark:bg-yellow-900/20 border border-white/50 dark:border-gray-600/50">
              <Trophy className="w-4 h-4 text-yellow-500" />
            </div>
            <div className="text-xs sm:text-sm font-bold text-yellow-500">
              {(() => {
                // Calculate trophy amount based on task category and total rewards
                const baseAmount = category === 'interaction' ? 15 : 
                                  category === 'creation' ? 25 :
                                  category === 'social' ? 20 :
                                  category === 'memory' ? 30 :
                                  category === 'store' ? 10 : 15;
                
                const progressMultiplier = Math.ceil(progress.total / 3);
                return baseAmount + (progressMultiplier * 5);
              })()}
            </div>
          </div>

          {/* 5. Currency rewards (right-aligned, precise 4px spacing) */}
          <div className="flex gap-1 justify-end">
            {Array(2).fill(null).map((_, index) => {
              const reward = rewards[index];
              if (!reward) {
                // Transparent placeholder to maintain consistent spacing
                return (
                  <div
                    key={`placeholder-${index}`}
                    className="w-8 h-8 sm:w-10 sm:h-10"
                  />
                );
              }

              const currencyInfo = getCurrencyIcon(reward.currency);
              return (
                <div
                  key={index}
                  className="flex flex-col items-center"
                >
                  {/* Currency logo above */}
                  <div className={`
                    w-8 h-8 sm:w-10 sm:h-10 rounded-lg sm:rounded-xl mb-1 flex items-center justify-center shadow-sm
                    ${currencyInfo.bg} border border-white/50 dark:border-gray-600/50
                  `}>
                    <span className={currencyInfo.color}>
                      {currencyInfo.icon}
                    </span>
                  </div>
                  {/* Amount value below */}
                  <div className={`text-xs sm:text-sm font-bold ${currencyInfo.color}`}>
                    {reward.amount}
                  </div>
                </div>
              );
            })}
          </div>

          {/* 6. Action button (y-aligned with currency icons) */}
          <div className="flex-shrink-0">
            <button
              onClick={() => actionButton.action && onAction(id, actionButton.action)}
              disabled={!actionButton.action}
              className={`
                w-12 h-8 sm:w-14 sm:h-10 rounded-lg sm:rounded-xl flex items-center justify-center
                transition-all duration-200 shadow-md hover:shadow-lg hover:scale-105 active:scale-95
                ${actionButton.className}
              `}
            >
              {actionButton.icon}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JourneyMissionsRow;
