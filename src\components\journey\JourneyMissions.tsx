'use client';

import React, { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import CumulativeProgressReward from './CumulativeProgressReward';
import { CompactCountdown } from './shared/CompactCountdown';
import JourneyMissionsRow from './JourneyMissionsRow';
import MockMissionService, { MockMissionData } from '@/services/mockMissionData';
import { useJourneyData } from '../../hooks/journey/useJourneyData';
import { useTranslation } from '@/app/i18n/client';

interface JourneyMissionsProps {
  lang: string;
}

const JourneyMissions: React.FC<JourneyMissionsProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [activeTab, setActiveTab] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [refreshKey, setRefreshKey] = useState(0);
  const [claimingMission, setClaimingMission] = useState<string | null>(null);
  const [claimedMilestones, setClaimedMilestones] = useState<Set<string>>(new Set());
  
  // 获取journey数据用于等级系统
  const { data, addTrophies } = useJourneyData();

  // Removed sticky offset calculation

  // Get missions from mock service (refreshKey is used to trigger re-renders)
  const missions = MockMissionService.getMissions(activeTab);

  // Use refreshKey to trigger re-renders when missions are updated
  useEffect(() => {
    // This effect runs when refreshKey changes, ensuring fresh data
  }, [refreshKey, activeTab]);

  // Handle milestone claim actions
  const handleMilestoneClaimReward = (milestone: number) => {
    const milestoneKey = `${activeTab}-${milestone}`;
    
    // Check if already claimed
    if (claimedMilestones.has(milestoneKey)) {
      return;
    }

    // Add to claimed milestones
    setClaimedMilestones(prev => new Set([...prev, milestoneKey]));
    
    // Show success feedback
    const rewards = {
      daily: { base: 50, multiplier: 25 },
      weekly: { base: 150, multiplier: 75 },
      monthly: { base: 500, multiplier: 250 }
    };
    
    const reward = rewards[activeTab];
    const amount = reward.base + (milestone * reward.multiplier);
    
    alert(t('journey.messages.milestoneReward', { amount }));
  };

  // Handle mission actions
  const handleMissionAction = async (missionId: string, action: 'navigate' | 'claim') => {
    if (action === 'claim') {
      setClaimingMission(missionId);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const success = MockMissionService.claimReward(missionId);
      if (success) {
        console.log(`Claimed reward for mission: ${missionId}`);
        // Trigger re-render by updating refresh key
        setRefreshKey(prev => prev + 1);

        // 处理奖励并更新等级
        const mission = missions.find(m => m.id === missionId);
        if (mission) {
          // 计算奖杯积分奖励
          const trophyReward = mission.rewards.find(r => r.currency === 'trophy_points');
          if (trophyReward) {
            addTrophies(trophyReward.amount);
          }
          
          alert(t('journey.messages.rewardClaimed') + ' ' + 
                mission.rewards.map(r => `${r.amount} ${r.currency.replace('_', ' ')}`).join(', '));
        }
      }

      setClaimingMission(null);
    } else if (action === 'navigate') {
      console.log(`Navigate to complete mission: ${missionId}`);
      // In a real app, you would navigate to the appropriate page
      // For demo purposes, let's simulate completing the mission
      const mission = missions.find(m => m.id === missionId);
      if (mission && !mission.status.completed) {
        MockMissionService.updateProgress(missionId, { current: mission.progress.total, total: mission.progress.total });
        setRefreshKey(prev => prev + 1);
        alert(t('journey.messages.missionCompleted'));
      }
    }
  };

  // Convert MockMissionData to JourneyMissionsRow props
  const convertToRowProps = (mission: MockMissionData) => ({
    id: mission.id,
    category: mission.category,
    title: mission.title,
    description: mission.description,
    progress: mission.progress,
    rewards: mission.rewards,
    status: mission.status,
    onAction: handleMissionAction,
    isClaimingReward: claimingMission === mission.id
  });

  const tabData = [
    { key: 'daily', label: t('journey.tabs.daily'), count: MockMissionService.getMissions('daily').length },
    { key: 'weekly', label: t('journey.tabs.weekly'), count: MockMissionService.getMissions('weekly').length },
    { key: 'monthly', label: t('journey.tabs.monthly'), count: MockMissionService.getMissions('monthly').length }
  ];

  return (
    <div className="-mt-4 -mx-4">
      {/* Section: Daily Tabs + Progress + Countdown with Glass Morphism */}
      <div className="mx-2 backdrop-blur-2xl bg-gradient-to-r from-white/20 via-white/30 to-white/20 dark:from-black/20 dark:via-black/30 dark:to-black/20 border border-white/40 dark:border-white/20 shadow-2xl shadow-purple-500/10 rounded-3xl mb-6">
        {/* Three-Module Container - Unified Glass Effect */}
        <div className="flex flex-row h-full gap-1.5">
          {/* Left: Daily/Weekly/Monthly Tab Navigation */}
          <div className="w-[30%] min-w-[120px] sm:min-w-[140px] md:min-w-[200px] max-w-[320px] rounded-l-3xl p-3 lg:p-3 max-lg:pb-3 flex flex-col">
            <div className="space-y-2 sm:space-y-3">
              {tabData.map((tab) => {
                const isActive = activeTab === tab.key;

                return (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key as 'daily' | 'weekly' | 'monthly')}
                    className={`relative w-full flex items-center gap-2 sm:gap-2 md:gap-3 lg:gap-4 p-2 sm:p-2 md:p-3 lg:p-4 rounded-lg sm:rounded-lg md:rounded-xl transition-all duration-500 group min-h-[44px] ${
                      isActive
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg backdrop-blur-xl transform scale-[1.02]'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-white/20 dark:hover:bg-white/10 backdrop-blur-sm border border-white/20 dark:border-white/10'
                    }`}
                  >
                    <div className="flex-1 text-left min-w-0">
                      <div className={`font-semibold text-sm sm:text-sm md:text-base transition-all duration-300 truncate ${
                        isActive ? 'text-white' : 'text-gray-900 dark:text-gray-100'
                      }`}>
                        {tab.label}
                      </div>
                    </div>
                    <div className={`flex-shrink-0 px-2 md:px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 min-w-[24px] text-center hidden sm:hidden md:block ${
                      isActive
                        ? 'bg-white/20 text-white backdrop-blur-sm'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}>
                      {tab.count}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Right: Progress Reward + Countdown Timer */}
          <div className="flex-1 min-w-0 p-3 flex flex-col justify-between">
            {/* Cumulative Progress Reward */}
            <div className="mb-3">
              <CumulativeProgressReward
                activeTab={activeTab}
                completedTasks={missions.filter(m => m.status.completed).length}
                totalTasks={missions.length}
                lang={lang}
                onClaimReward={handleMilestoneClaimReward}
                claimedMilestones={claimedMilestones}
              />
            </div>

            {/* Compact Countdown Timer */}
            <div className="mt-auto">
              <CompactCountdown
                endTime={new Date(Date.now() + (activeTab === 'daily' ? 24 : activeTab === 'weekly' ? 7 * 24 : 30 * 24) * 60 * 60 * 1000)}
                type={activeTab}
                label={`${t(`journey.tabs.${activeTab}`)}${t('journey.missions.title')}`}
                lang={lang}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Non-Sticky Content Area - Mission Cards */}
      <div className="mx-2">
        {/* Full Width Content Area - Mission Cards */}
        <div className="p-3 sm:p-4 lg:p-6">
          <div className="space-y-3 sm:space-y-4">
            {missions.map((mission) => (
              <JourneyMissionsRow key={mission.id} {...convertToRowProps(mission)} />
            ))}
          </div>

          {/* Empty State */}
          {missions.length === 0 && (
            <div className="text-center py-12">
              <Heart className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                {t('journey.missions.allCompleted')}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {t('journey.missions.checkBack', { type: activeTab })}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default JourneyMissions;
