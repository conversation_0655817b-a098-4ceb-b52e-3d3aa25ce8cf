'use client';

import React from 'react';
import { Trophy, Target, Compass, Users, TrendingUp, Star, Award, Crown, Zap } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { EnhancedAchievement } from '@/types/achievements';

interface TrophyOverviewTabProps {
  achievements: EnhancedAchievement[];
  lang: string;
}

const TrophyOverviewTab: React.FC<TrophyOverviewTabProps> = ({ achievements, lang }) => {
  const { t } = useTranslation(lang, 'translation');

  // Calculate statistics for each category
  const getStatsForCategory = (categoryFilter: (achievement: EnhancedAchievement) => boolean) => {
    const categoryAchievements = achievements.filter(categoryFilter);
    const completed = categoryAchievements.filter(a => a.status === 'completed').length;
    const total = categoryAchievements.length;
    const points = categoryAchievements
      .filter(a => a.status === 'completed')
      .reduce((sum, a) => sum + a.points, 0);
    
    return { completed, total, points, percentage: total > 0 ? (completed / total) * 100 : 0 };
  };

  const achievementsStats = getStatsForCategory(a => 
    a.category === 'interaction' || a.category === 'beginner'
  );
  
  const explorationsStats = getStatsForCategory(a => 
    a.category === 'collection' || a.category === 'creation'
  );
  
  const socialStats = getStatsForCategory(a => 
    a.category === 'social'
  );
  
  const rankingStats = getStatsForCategory(a => 
    a.category === 'special'
  );

  const totalStats = {
    completed: achievementsStats.completed + explorationsStats.completed + socialStats.completed + rankingStats.completed,
    total: achievementsStats.total + explorationsStats.total + socialStats.total + rankingStats.total,
    points: achievementsStats.points + explorationsStats.points + socialStats.points + rankingStats.points
  };

  const categoryCards = [
    {
      id: 'achievements',
      title: t('trophies.overview.achievements'),
      subtitle: t('trophies.overview.achievementsSubtitle'),
      icon: Target,
      stats: achievementsStats,
      gradient: 'from-amber-500 via-orange-500 to-red-500',
      bgGradient: 'from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20',
      description: t('trophies.overview.achievementsDesc')
    },
    {
      id: 'explorations',
      title: t('trophies.overview.explorations'),
      subtitle: t('trophies.overview.explorationsSubtitle'),
      icon: Compass,
      stats: explorationsStats,
      gradient: 'from-emerald-500 via-teal-500 to-cyan-500',
      bgGradient: 'from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20',
      description: t('trophies.overview.explorationsDesc')
    },
    {
      id: 'social',
      title: t('trophies.overview.social'),
      subtitle: t('trophies.overview.socialSubtitle'),
      icon: Users,
      stats: socialStats,
      gradient: 'from-blue-500 via-indigo-500 to-purple-500',
      bgGradient: 'from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20',
      description: t('trophies.overview.socialDesc')
    },
    {
      id: 'ranking',
      title: t('trophies.overview.ranking'),
      subtitle: t('trophies.overview.rankingSubtitle'),
      icon: TrendingUp,
      stats: rankingStats,
      gradient: 'from-rose-500 via-pink-500 to-fuchsia-500',
      bgGradient: 'from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20',
      description: t('trophies.overview.rankingDesc')
    }
  ];

  return (
    <div className="space-y-8">
      {/* Hero Stats Section */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-purple-600/10 border border-purple-200/20 dark:border-purple-800/20 p-8">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-pink-500/5 to-purple-600/5 backdrop-blur-3xl"></div>
        
        <div className="relative z-10">
          <div className="text-center mb-8">
            <div className="inline-flex items-center gap-3 mb-4">
              <div className="p-3 rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 text-white shadow-lg">
                <Trophy className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  {t('trophies.overview.title')}
                </h1>
                <p className="text-muted-foreground">{t('trophies.overview.subtitle')}</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                {totalStats.completed}
              </div>
              <div className="text-sm text-muted-foreground mb-1">{t('trophies.overview.totalTrophies')}</div>
              <div className="text-xs text-muted-foreground">
                {totalStats.completed} {t('trophies.overview.of')} {totalStats.total} {t('trophies.overview.unlocked')}
              </div>
            </div>

            <div className="text-center">
              <div className="text-4xl font-bold text-pink-600 dark:text-pink-400 mb-2">
                {totalStats.points.toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground mb-1">{t('trophies.overview.totalPoints')}</div>
              <div className="text-xs text-muted-foreground">
                {t('trophies.overview.earnedFromAll')}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-4xl font-bold text-indigo-600 dark:text-indigo-400 mb-2">
                {Math.round((totalStats.completed / totalStats.total) * 100)}%
              </div>
              <div className="text-sm text-muted-foreground mb-1">{t('trophies.stats.completionRate')}</div>
              <div className="text-xs text-muted-foreground">
                {t('trophies.stats.overallProgress')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {categoryCards.map((category) => {
          const Icon = category.icon;
          
          return (
            <div
              key={category.id}
              className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${category.bgGradient} border border-white/20 dark:border-gray-800/20 p-6 hover:shadow-xl transition-all duration-300 group cursor-pointer`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`p-3 rounded-xl bg-gradient-to-br ${category.gradient} text-white shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg">{category.title}</h3>
                    <p className="text-sm text-muted-foreground">{category.subtitle}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-2xl font-bold">
                    {category.stats.completed}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {t('trophies.overview.of')} {category.stats.total}
                  </div>
                </div>
              </div>

              <p className="text-sm text-muted-foreground mb-4">
                {category.description}
              </p>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{t('trophies.common.progress')}</span>
                  <span className="text-sm text-muted-foreground">
                    {Math.round(category.stats.percentage)}%
                  </span>
                </div>
                
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full bg-gradient-to-r ${category.gradient} transition-all duration-500`}
                    style={{ width: `${category.stats.percentage}%` }}
                  ></div>
                </div>
                
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">{t('trophies.common.pointsEarned')}</span>
                  <span className="font-semibold">{category.stats.points.toLocaleString()}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Recent Achievements */}
      <div className="rounded-2xl bg-card border border-border p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 rounded-lg bg-gradient-to-br from-yellow-500 to-orange-500 text-white">
            <Star className="w-5 h-5" />
          </div>
          <h2 className="text-xl font-bold">{t('trophies.overview.recentAchievements')}</h2>
        </div>
        
        <div className="space-y-3">
          {achievements
            .filter(a => a.status === 'completed' && a.earnedDate)
            .sort((a, b) => new Date(b.earnedDate!).getTime() - new Date(a.earnedDate!).getTime())
            .slice(0, 5)
            .map((achievement) => (
              <div key={achievement.id} className="flex items-center gap-4 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 text-white">
                  <Award className="w-4 h-4" />
                </div>
                <div className="flex-1">
                  <div className="font-medium">{achievement.name}</div>
                  <div className="text-sm text-muted-foreground">{achievement.description}</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">+{achievement.points}</div>
                  <div className="text-xs text-muted-foreground">
                    {achievement.earnedDate && new Date(achievement.earnedDate).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default TrophyOverviewTab;
