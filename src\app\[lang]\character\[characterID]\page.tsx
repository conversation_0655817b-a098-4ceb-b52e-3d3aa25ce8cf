import CharacterProfileClientPage from './CharacterProfileClientPage';
import { characters } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';

export default async function CharacterPage({ params }: { params: Promise<{ lang: string; characterID: string }> }) {
  const { lang, characterID } = await params;

  // Find character data; fallback to first if not found
  const character = characters.find((c) => c.id === characterID) ?? characters[0];

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-romantic-bg">
        <CharacterProfileClientPage character={character} lang={lang} />
      </div>
    </MainAppLayout>
  );
}