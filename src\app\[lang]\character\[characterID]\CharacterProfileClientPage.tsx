'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  MessageCircle,
  Heart,
  Users,
  MessageSquare,
  Sparkles,
  Gift,
  Clock,
  BookOpen,
  Trophy,
  Flame,
  Target,
  Play,
  Star
} from 'lucide-react';
import type { Character } from '@/lib/mock-data';
import MemoryCard from '@/components/MemoryCard';
import MemoryCapsule, { ChatRecord, ChatMessage } from '@/components/MemoryCapsule';
import BottomNavBar from '@/components/BottomNavBar';
import HeroSection from '@/components/HeroSection';
import StatCard from '@/components/StatCard';
import TabNavigation from '@/components/TabNavigation';
import { useTranslation } from '@/app/i18n/client';

// Icon wrapper to fix type compatibility
const iconWrapper = (IconComponent: any) => ({ className, size }: { className?: string; size?: number }) => (
  <IconComponent className={className} size={size} />
);

// Component Props
interface CharacterProfileProps {
  character: Character;
  lang: string;
}

const TABS = ['memories', 'stories', 'badges'] as const;
type Tab = (typeof TABS)[number];

// Mock data for memories, this would typically come from a service
// --- MOCK DATA ---
const mockMemoryMessages1: ChatMessage[] = [
    { id: 1, side: 'left', avatar: 'https://i.pinimg.com/564x/e7/7a/74/e77a7405232d2c1c107629577a83a48e.jpg', name: 'Seraphina', text: 'That time we explored the glowing caves and found the crystal that hums a forgotten melody.', timestamp: '3 days ago' },
    { id: 2, side: 'right', avatar: 'https://i.pravatar.cc/40?u=user', name: 'Me', text: "It was magical! I still hear the song in my dreams sometimes. ✨", timestamp: '3 days ago' },
];
const mockMemoryMessages2: ChatMessage[] = [
    { id: 1, side: 'left', avatar: 'https://i.pinimg.com/564x/e7/7a/74/e77a7405232d2c1c107629577a83a48e.jpg', name: 'Seraphina', text: 'You told me you were a little nervous about your big presentation, but I knew you would be amazing.', timestamp: 'Yesterday' },
    { id: 2, side: 'right', avatar: 'https://i.pravatar.cc/40?u=user', name: 'Me', text: "Thanks for the encouragement! It made all the difference.", timestamp: 'Yesterday' },
];
const characterMemories: ChatRecord[] = [
    {
        id: 'mem1',
        title: 'Glowing Caves Adventure',
        participants: [
            { id: 'seraphina', name: 'Seraphina', avatar: 'https://i.pinimg.com/564x/e7/7a/74/e77a7405232d2c1c107629577a83a48e.jpg' },
            { id: 'user', name: 'Me', avatar: 'https://i.pravatar.cc/40?u=user' }
        ],
        messages: mockMemoryMessages1,
        createdAt: '3 days ago',
        stats: { likes: 15, comments: 3, shares: 2 }
    },
    {
        id: 'mem2',
        title: 'Presentation Encouragement',
        participants: [
            { id: 'seraphina', name: 'Seraphina', avatar: 'https://i.pinimg.com/564x/e7/7a/74/e77a7405232d2c1c107629577a83a48e.jpg' },
            { id: 'user', name: 'Me', avatar: 'https://i.pravatar.cc/40?u=user' }
        ],
        messages: mockMemoryMessages2,
        createdAt: 'Yesterday',
        stats: { likes: 8, comments: 1, shares: 1 }
    },
];

// Mock data for stories
const characterStories = [
    {
        id: 'story1',
        title: 'Unexpected Encounter at School',
        description: 'On a spring afternoon with cherry blossoms dancing in the air, Aiko meets a mysterious transfer student in the library, beginning a surprising campus story...',
        coverImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
        status: 'completed',
        chapters: 12,
        readCount: '15.2K',
        rating: 4.8,
        tags: ['School', 'Youth', 'Romance'],
        lastUpdated: '2 days ago',
        isOfficial: true,
        type: 'chapters' as const
    },
    {
        id: 'story2',
        title: 'Warm Companionship on a Rainy Night',
        description: 'On a stormy night, Aiko uses her warmth and care to accompany you through the most difficult moment of your life...',
        coverImage: 'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?w=400&h=300&fit=crop',
        status: 'ongoing',
        chapters: 8,
        readCount: '8.7K',
        rating: 4.9,
        tags: ['Healing', 'Warmth', 'Companionship'],
        lastUpdated: '1 day ago',
        isOfficial: true,
        type: 'chapters' as const
    },
    {
        id: 'story3',
        title: 'Afternoon Coffee Time',
        description: 'In that cozy coffee shop on the corner, Aiko shares dreams and secrets with you, as if time stands still in this moment...',
        coverImage: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=300&fit=crop',
        status: 'completed',
        chapters: 6,
        readCount: '12.1K',
        rating: 4.7,
        tags: ['Daily', 'Cozy', 'Dialogue'],
        lastUpdated: '1 week ago',
        isOfficial: false,
        type: 'moments' as const
    }
];

// --- SUB-COMPONENTS ---

const FloatingMemory = ({ memory, onClose }: { memory: ChatRecord, onClose: () => void }) => {
    return (
        <MemoryCapsule
            chatRecord={memory}
            isOpen={true}
            onClose={onClose}
        />
    );
};





const CharacterHeader = ({ character, lang }: { character: Character; lang: string }) => {
  const { t } = useTranslation(lang, 'translation');

  const formatFollowers = (count: number) => {
      if (count >= 1000) {
          return `${(count / 1000).toFixed(1)}K`;
      }
      return count.toString();
  };

  const characterStats = [
    { label: t('characterProfile.header.followers'), value: formatFollowers(10000), icon: Users },
    { label: t('characterProfile.header.chats'), value: '500K', icon: MessageSquare },
    { label: t('characterProfile.header.weeklyRank'), value: '#3', icon: Trophy },
    { label: t('characterProfile.header.interactions'), value: '127', icon: Heart },
    { label: t('characterProfile.header.memories'), value: '10', icon: Sparkles },
    { label: t('characterProfile.header.relationshipLevel'), value: 'Lv.5', icon: Star }
  ];

  const subtitle = `${t('characterProfile.header.creatorLabel')}: ${character.creator_uid ? `@${character.creator_name || 'Sophia'}` : '@Sophia'}`;
  const description = t('characterProfile.description.default');

  return (
    <HeroSection
      backgroundImage={character.character_bg_image}
      title={character.name}
      subtitle={subtitle}
      description={description}
      avatar={character.character_avatar}
      avatarSize="lg"
      stats={characterStats}
      variant="character"
      height="lg"
          >
    </HeroSection>
  );
};

// Horizontal layout for the four interaction stats
const CharacterInteractionStats = ({ lang }: { lang: string }) => {
  const { t } = useTranslation(lang, 'translation');
  const streakDays = 7;
  const completedTasks = 2;
  const totalTasks = 3;
  const totalReward = 25;

  return (
    <section className="bg-background border-b border-border px-4 py-3">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <StatCard
          icon={iconWrapper(Gift)}
          label={t('characterProfile.stats.todaysRewards')}
          value="60"
          unit="🔥"
          gradient="from-orange-500 to-red-500"
          size="sm"
          variant="glass"
          trend="up"
          trendValue="+15"
        />

        <StatCard
          icon={iconWrapper(Flame)}
          label={t('characterProfile.stats.interactionStreak')}
          value={streakDays}
          unit={t('characterProfile.stats.days')}
          gradient="from-orange-500 to-yellow-500"
          size="sm"
          variant="glass"
          trend="up"
          trendValue="+1"
        />

        <StatCard
          icon={iconWrapper(Target)}
          label={t('characterProfile.stats.dailyTasks')}
          value={`${completedTasks}/${totalTasks}`}
          unit={t('characterProfile.stats.completed')}
          gradient="from-blue-500 to-purple-500"
          size="sm"
          variant="glass"
          trend="neutral"
          trendValue={`${totalReward}🔥`}
        />

        {/* Relationship Level - Unified Style with StatCard */}
        <div className="group relative rounded-2xl p-4 backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
          {/* Background gradient effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-pink-500 to-purple-600 opacity-5 group-hover:opacity-10 transition-opacity duration-300 rounded-2xl"></div>
          
          {/* Content */}
          <div className="relative z-10">
            {/* Icon and Trend Row */}
            <div className="flex items-start justify-between mb-4">
              {/* Icon */}
              <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Heart className="w-8 h-8 text-white" />
              </div>
            </div>

            {/* Value */}
            <div className="flex items-baseline gap-2 mb-2">
              <span className="text-2xl font-bold text-foreground group-hover:scale-110 transition-transform duration-300">
                Lv.5
              </span>
              <span className="text-xs bg-muted px-2 py-0.5 rounded-full font-medium text-muted-foreground">
                {t('characterProfile.stats.closeFriend')}
              </span>
            </div>

            {/* Label */}
            <div className="text-xs text-muted-foreground font-medium mb-3">
              {t('characterProfile.stats.relationship')}
            </div>

            {/* Progress bar with XP info */}
            <div className="space-y-1">
              <div className="w-full bg-white/20 dark:bg-gray-700/20 rounded-full h-1 overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-pink-500 to-purple-600 rounded-full transform transition-all duration-1000 delay-300"
                  style={{ width: '62.5%' }}
                ></div>
              </div>
              <p className="text-xs text-muted-foreground font-medium">1,250/2,000 XP</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// Action buttons section
const CharacterActions = ({ character, lang, isFollowed, onFollow }: {
  character: Character;
  lang: string;
  isFollowed: boolean;
  onFollow: () => void;
}) => {
  const { t } = useTranslation(lang, 'translation');
  
  return (
    <section className="bg-background border-b border-border p-4">
      <div className="flex gap-3">
        <Link
          href={`/${lang}/chats/${character.id}`}
          className="flex-1 bg-romantic-gradient hover:opacity-90 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center justify-center gap-2 shadow-romantic"
        >
          <MessageCircle size={16} />
          {t('characterProfile.actions.startChat')}
        </Link>
        <button
          onClick={onFollow}
          className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 border flex items-center gap-2 ${
            isFollowed
              ? 'bg-romantic-secondary text-system-primary border-romantic shadow-romantic'
              : 'bg-muted text-muted-foreground border-border hover:bg-romantic-secondary hover:text-system-primary hover:border-romantic'
          }`}
        >
          <Heart size={16} className={`${isFollowed ? 'fill-current' : ''}`} />
          {t('characterProfile.actions.follow')}
        </button>
      </div>
    </section>
  );
};

const CharacterTabs = ({ activeTab, setActiveTab, onMemoryClick, lang }: { activeTab: Tab; setActiveTab: (tab: Tab) => void, onMemoryClick: (mem: ChatRecord) => void, lang: string }) => {
    const { t } = useTranslation(lang, 'translation');
    const [activePeriod, setActivePeriod] = useState('Weekly'); // 'Daily', 'Weekly', 'Monthly'

    const statsData = {
        Daily: { chats: '15', duration: '35m', intimacy: '+50', memories: '2' },
        Weekly: { chats: '127', duration: '4.2h', intimacy: '+350', memories: '10' },
        Monthly: { chats: '500+', duration: '20h+', intimacy: '+1,200', memories: '38' },
    };
    
    const currentStats = statsData[activePeriod as keyof typeof statsData];

    const statsDisplay = [
        { icon: MessageSquare, label: t('characterProfile.badges.totalChats'), value: currentStats.chats, color: 'blue' },
        { icon: Clock, label: t('characterProfile.badges.interactionTime'), value: currentStats.duration, color: 'green' },
        { icon: Heart, label: t('characterProfile.badges.intimacyGrowth'), value: currentStats.intimacy, color: 'pink' },
        { icon: Sparkles, label: t('characterProfile.badges.memoriesCreated'), value: currentStats.memories, color: 'purple' },
    ];

    const colorClasses = {
        blue: {
            icon: 'text-primary',
            bg: 'bg-secondary/50',
        },
        green: {
            icon: 'text-success',
            bg: 'bg-secondary/50',
        },
        pink: {
            icon: 'text-primary-pink',
            bg: 'bg-secondary-pink/50',
        },
        purple: {
            icon: 'text-accent',
            bg: 'bg-secondary/50',
        }
    };
    
    return (
        <>
            {/* Tabs */}
            <TabNavigation
                tabs={[
                    { id: 'memories', label: t('characterProfile.tabs.memories'), icon: iconWrapper(Sparkles) },
                    { id: 'stories', label: t('characterProfile.tabs.stories'), icon: iconWrapper(BookOpen) },
                    { id: 'badges', label: t('characterProfile.tabs.badges'), icon: iconWrapper(Trophy) }
                ]}
                activeTab={activeTab}
                onTabChange={(tabId) => setActiveTab(tabId as Tab)}
                variant="underline"
                sticky={true}
                stickyTop="top-12 lg:top-16"
                className="bg-background border-b border-border"
            />

            {/* Content */}
            <div className="bg-background p-1.5 md:p-2">
                {activeTab === 'memories' && (
                    <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
                        {characterMemories.map((mem) => (
                            <div key={mem.id} className="break-inside-avoid">
                                <MemoryCard
                                    chatRecord={mem}
                                    onClick={() => onMemoryClick(mem)}
                                />
                            </div>
                        ))}
                    </div>
                )}

                {activeTab === 'stories' && (
                    <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
                        {characterStories.map((story) => (
                            <div key={story.id} className="break-inside-avoid">
                                <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 hover:shadow-lg transition-all duration-300 cursor-pointer group overflow-hidden">
                                    {/* Cover Image */}
                                    <div className="relative aspect-[4/3] overflow-hidden">
                                        <img
                                            src={story.coverImage}
                                            alt={story.title}
                                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                        />
                                        {/* Official/Unofficial Badge */}
                                        <div className="absolute top-2 left-2">
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                story.isOfficial
                                                    ? 'bg-blue-500 text-white'
                                                    : 'bg-gray-500 text-white'
                                            }`}>
                                                {story.isOfficial ? t('characterProfile.stories.official') : t('characterProfile.stories.community')}
                                            </span>
                                        </div>
                                        {/* Type Badge */}
                                        <div className="absolute top-2 right-2">
                                            <span className="bg-black/60 text-white px-2 py-1 rounded-full text-xs font-medium">
                                                {story.chapters} {story.type === 'chapters' ? t('characterProfile.stories.chapters') : t('characterProfile.stories.moments')}
                                            </span>
                                        </div>
                                        {/* Play Button */}
                                        <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors flex items-center justify-center">
                                            <Play className="w-6 h-6 text-white opacity-80" />
                                        </div>
                                    </div>

                                    {/* Content */}
                                    <div className="p-4">
                                        <div className="flex items-start justify-between gap-2 mb-2">
                                            <h4 className="font-bold text-gray-900 dark:text-gray-100 text-sm group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
                                                {story.title}
                                            </h4>
                                            <div className="flex items-center gap-1 text-yellow-500 flex-shrink-0">
                                                <Star className="w-3 h-3 fill-current" />
                                                <span className="text-xs font-medium">{story.rating}</span>
                                            </div>
                                        </div>

                                        <p className="text-gray-600 dark:text-gray-400 text-xs mb-3 line-clamp-3">
                                            {story.description}
                                        </p>

                                        <div className="flex flex-wrap gap-1 mb-3">
                                            {story.tags.slice(0, 2).map((tag, tagIndex) => (
                                                <span key={tagIndex} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full">
                                                    {tag}
                                                </span>
                                            ))}
                                            {story.tags.length > 2 && (
                                                <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full">
                                                    +{story.tags.length - 2}
                                                </span>
                                            )}
                                        </div>

                                        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                                            <span className="flex items-center gap-1">
                                                <Users className="w-3 h-3" />
                                                {story.readCount}
                                            </span>
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                story.status === 'completed'
                                                    ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400'
                                                    : 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                                            }`}>
                                                {story.status === 'completed' ? t('characterProfile.stories.completed') : t('characterProfile.stories.ongoing')}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}

                {activeTab === 'badges' && (
                    <div className="space-y-6">
                        <div className="flex justify-end items-center mb-6">
                            <div className="flex items-center bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                                {([t('characterProfile.badges.daily'), t('characterProfile.badges.weekly'), t('characterProfile.badges.monthly')] as const).map((period, index) => {
                                    const periodKey = ['Daily', 'Weekly', 'Monthly'][index];
                                    return (
                                        <button
                                            key={periodKey}
                                            onClick={() => setActivePeriod(periodKey)}
                                            className={`px-4 py-1.5 text-sm font-semibold rounded-md transition-colors ${
                                                activePeriod === periodKey
                                                    ? 'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400 shadow-sm border border-gray-200 dark:border-gray-600'
                                                    : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                                            }`}
                                        >
                                            {period}
                                        </button>
                                    );
                                })}
                            </div>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
                            {statsDisplay.map(stat => {
                                const Icon = stat.icon;
                                const colors = colorClasses[stat.color as keyof typeof colorClasses];
                                return (
                                    <div key={stat.label} className={`flex items-center p-4 rounded-xl ${colors.bg}`}>
                                        <div className="mr-4">
                                            <Icon size={24} className={colors.icon} />
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-600 dark:text-gray-400">{stat.label}</p>
                                            <p className="text-xl font-bold text-gray-900 dark:text-gray-100">{stat.value}</p>
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    </div>
                )}
            </div>
        </>
    )
};

// --- MAIN CLIENT PAGE ---
export default function CharacterProfileClientPage({ character, lang }: CharacterProfileProps) {
  const [activeTab, setActiveTab] = useState<Tab>('memories');
  const [activeMemory, setActiveMemory] = useState<ChatRecord | null>(null);
  const [isFollowed, setIsFollowed] = useState(false);

  const handleMemoryClick = (memory: ChatRecord) => {
      setActiveMemory(memory);
  };

  const handleCloseMemory = () => {
      setActiveMemory(null);
  };

  const handleFollow = () => {
    setIsFollowed(!isFollowed);
  };

  return (
    <>
      <main className="max-w-7xl mx-auto pb-24">
        {/* Character Header - Profile style cover section */}
        <CharacterHeader character={character} lang={lang} />

        {/* Action buttons */}
        <CharacterActions
          character={character}
          lang={lang}
          isFollowed={isFollowed}
          onFollow={handleFollow}
        />

        {/* Interaction Stats - horizontal layout */}
        <CharacterInteractionStats lang={lang} />

        {/* Character Tabs and Content */}
        <CharacterTabs
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          onMemoryClick={handleMemoryClick}
          lang={lang}
        />

        {activeMemory && (
          <FloatingMemory
              memory={activeMemory}
              onClose={handleCloseMemory}
          />
        )}
      </main>
      <BottomNavBar lang={lang} />
    </>
  );
}