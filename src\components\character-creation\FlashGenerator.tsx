'use client';

import React from 'react';
import { Sparkles } from 'lucide-react';
import type { FlashGenerationResult } from '@/types/character-creation';
import CharacterGenreFilter, { GenreFilters } from '@/components/CharacterGenreFilter';
import toast from 'react-hot-toast';
import { useTranslation } from '@/app/i18n/client';

interface FlashGeneratorProps {
  name: string;
  onNameChange: (name: string) => void;
  prompt: string;
  onPromptChange: (prompt: string) => void;
  result: FlashGenerationResult | null;
  regenerateCount: number;
  isGenerating: boolean;
  onGenerate: () => void;
  onRegenerate: () => void;
  onAccept: () => void;
  maxRegenerations?: number;
  filters: GenreFilters;
  onFiltersChange: (filters: GenreFilters) => void;
  lang?: string;
}

const FlashGenerator: React.FC<FlashGeneratorProps> = ({
  name,
  onNameChange,
  prompt,
  onPromptChange,
  result,
  regenerateCount,
  isGenerating,
  onGenerate,
  onRegenerate,
  onAccept,
  maxRegenerations = 2,
  filters,
  onFiltersChange,
  lang = 'en'
}) => {
  const { t } = useTranslation(lang, 'translation');
  const handleCopyText = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard!`);
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="text-center">
        <h3 className="text-2xl font-bold text-pink-800 dark:text-pink-200 mb-2">{t('characterCreation.functions.flash.subtitle')}</h3>
      </div>

      {/* Character Genre Filter */}
      <CharacterGenreFilter
        filters={filters}
        onFiltersChange={onFiltersChange}
        lang={lang}
      />

      {/* Character Name Input */}
      <div className="space-y-4">
        <h4 className="text-xl font-bold text-pink-800 dark:text-pink-200 flex items-center gap-2">
          ✨ {t('characterCreation.flash.characterName')}
        </h4>
        <input
          type="text"
          value={name}
          onChange={(e) => onNameChange(e.target.value)}
          maxLength={50}
          className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-pink-300 dark:border-pink-700 rounded-xl text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-pink-500 focus:ring-2 focus:ring-pink-500/20 transition-all"
          placeholder={t('characterCreation.flash.namePlaceholder')}
        />
        <div className="flex justify-end items-center">
          <span className={`text-sm font-medium ${name.length > 40 ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'}`}>
            {name.length}/50
          </span>
        </div>
      </div>

      {/* Character Description Input */}
      <div className="space-y-4">
        <h4 className="text-xl font-bold text-pink-800 dark:text-pink-200 flex items-center gap-2">
          🎯 {t('characterCreation.flash.characterConcept')}
        </h4>
        <textarea
          value={prompt}
          onChange={(e) => onPromptChange(e.target.value)}
          rows={5}
          maxLength={10000}
          className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-pink-300 dark:border-pink-700 rounded-xl text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-pink-500 focus:ring-2 focus:ring-pink-500/20 transition-all resize-none"
          placeholder={t('characterCreation.flash.conceptPlaceholder')}
        />
        <div className="flex justify-end items-center">
          <span className={`text-sm font-medium ${prompt.length > 5000 ? 'text-true-red' : 'text-true-green'}`}>
            {prompt.length}/5000
          </span>
        </div>

        {/* Generate Button */}
        <button
          onClick={onGenerate}
          disabled={isGenerating}
          className="w-full px-6 py-4 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-xl hover:from-pink-600 hover:to-rose-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg hover:shadow-xl text-lg font-semibold flex items-center justify-center gap-3 transform hover:scale-[1.02]"
        >
          {isGenerating ? (
            <>
              <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full" />
              <span>{t('characterCreation.flash.generatingText')}</span>
            </>
          ) : (
            <>
              <Sparkles size={20} />
              <span>{t('characterCreation.flash.generateButton')}</span>
            </>
          )}
        </button>

        {/* Feature Tags */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-pink-200 dark:border-pink-700">
          <p className="text-sm text-pink-600 dark:text-pink-400 text-center mb-3 font-medium">
            {t('characterCreation.flash.aiFeatures')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-center gap-2 flex-wrap">
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 text-pink-700 dark:text-pink-300 rounded-full text-sm font-medium border border-pink-200 dark:border-pink-700">{t('characterCreation.flash.nameTag')}</span>
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 text-pink-700 dark:text-pink-300 rounded-full text-sm font-medium border border-pink-200 dark:border-pink-700">{t('characterCreation.flash.appearanceTag')}</span>
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 text-pink-700 dark:text-pink-300 rounded-full text-sm font-medium border border-pink-200 dark:border-pink-700">{t('characterCreation.flash.personalityTag')}</span>
            </div>
            <div className="flex justify-center gap-2 flex-wrap">
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 text-pink-700 dark:text-pink-300 rounded-full text-sm font-medium border border-pink-200 dark:border-pink-700">{t('characterCreation.flash.behaviorTag')}</span>
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 text-pink-700 dark:text-pink-300 rounded-full text-sm font-medium border border-pink-200 dark:border-pink-700">{t('characterCreation.flash.knowledgeTag')}</span>
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 text-pink-700 dark:text-pink-300 rounded-full text-sm font-medium border border-pink-200 dark:border-pink-700">{t('characterCreation.flash.storyTag')}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Generation Result Preview */}
      {result && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h4 className="text-xl font-bold text-purple-800 dark:text-purple-200 flex items-center gap-2">
              {t('characterCreation.flash.previewTitle')}
            </h4>
            <div className="bg-white dark:bg-gray-800 px-4 py-2 rounded-full border border-purple-200 dark:border-purple-700">
              <span className="text-sm text-purple-600 dark:text-purple-400 font-medium">
                {t('characterCreation.flash.regenerationsLeft')} {maxRegenerations - regenerateCount}
              </span>
            </div>
          </div>

          {/* Character Name */}
          <div className="text-center">
            <h5 className="text-3xl font-bold text-purple-800 dark:text-purple-200">
              ✨ {result.name || 'Generated Character'}
            </h5>
          </div>

          {/* Character Details Grid */}
          <div className="space-y-4">
            {/* Appearance and Personality Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-purple-200 dark:border-purple-700 shadow-sm">
                <div className="flex items-center justify-between mb-3">
                  <h6 className="font-semibold text-purple-700 dark:text-purple-300 flex items-center gap-2">
                    👤 Appearance
                  </h6>
                  <button
                    onClick={() => handleCopyText(result.appearance || '', 'Appearance description')}
                    className="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 p-2 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/50 transition-all"
                    title="Copy appearance description"
                  >
                    📋
                  </button>
                </div>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed select-text cursor-text">
                  {result.appearance || 'Generated appearance description...'}
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-purple-200 dark:border-purple-700 shadow-sm">
                <h6 className="font-semibold text-purple-700 dark:text-purple-300 mb-3 flex items-center gap-2">
                  🎭 Personality
                </h6>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{result.personality || 'Generated personality traits...'}</p>
              </div>
            </div>

            {/* Behavior and Knowledge Row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-purple-200 dark:border-purple-700 shadow-sm">
                <h6 className="font-semibold text-purple-700 dark:text-purple-300 mb-3 flex items-center gap-2">
                  🎯 Behavior
                </h6>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{result.behavior || 'Generated behavior patterns...'}</p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-purple-200 dark:border-purple-700 shadow-sm">
                <h6 className="font-semibold text-purple-700 dark:text-purple-300 mb-3 flex items-center gap-2">
                  🧠 Knowledge
                </h6>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{result.knowledge || 'Generated knowledge base...'}</p>
              </div>
            </div>

            {/* Storyboard - Full Width */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-purple-200 dark:border-purple-700 shadow-sm">
              <h6 className="font-semibold text-purple-700 dark:text-purple-300 mb-3 flex items-center gap-2">
                📖 Story Background
              </h6>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{result.storyboard || 'Generated story background...'}</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 mt-6">
            <button
              onClick={onAccept}
              className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all shadow-lg hover:shadow-xl font-semibold flex items-center justify-center gap-2 transform hover:scale-[1.02]"
            >
              ✅ Accept & Create Character
            </button>

            {regenerateCount < maxRegenerations && (
              <button
                onClick={onRegenerate}
                disabled={isGenerating}
                className="px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl hover:from-orange-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg hover:shadow-xl font-semibold flex items-center justify-center gap-2 transform hover:scale-[1.02]"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                    <span>Regenerating...</span>
                  </>
                ) : (
                  <>
                    🔄 Regenerate ({maxRegenerations - regenerateCount} left)
                  </>
                )}
              </button>
            )}
          </div>

          <p className="text-sm text-purple-600 dark:text-purple-400 text-center font-medium">
            💡 You can still edit any details before your AI character goes live
          </p>
        </div>
      )}
    </div>
  );
};

export default FlashGenerator; 