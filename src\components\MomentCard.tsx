'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Heart, Share2, Clock } from 'lucide-react';
import Link from 'next/link';
import { Character } from '@/lib/mock-data';

interface MomentCardProps {
  lang: string;
  character: Character;
  moment: {
    id: string;
    title: string;
    image: string;
  };
  storyTemplateId: string;
  stats: {
    likes: number;
    shares: number;
  };
  aspectRatio: number;
  publishedAt: string;
}

const MomentCard: React.FC<MomentCardProps> = ({
  lang,
  character,
  moment,
  stats,
  aspectRatio,
  publishedAt
}) => {
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(stats.likes);
  const [imageSrc, setImageSrc] = useState(moment.image);
  const [avatarSrc, setAvatarSrc] = useState(character.character_avatar);

  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
  };

  return (
    <Link href={`/${lang}/moments/${moment.id}`} className="block group">
      <div className="relative overflow-hidden rounded-lg shadow-sm group-hover:shadow-md transition-shadow duration-200">
        {/* Placeholder to maintain aspect ratio */}
        <div style={{ paddingBottom: `${(1 / aspectRatio) * 100}%` }} />

        {/* Background Image */}
        <Image
          src={imageSrc}
          alt={moment.title}
          fill
          className="absolute top-0 left-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          unoptimized
          onError={() => setImageSrc('https://picsum.photos/500/800')}
        />

        {/* Gradient overlay covering bottom 60% */}
        <div className="absolute bottom-0 left-0 right-0 h-[60%] bg-gradient-to-t from-black/80 via-black/40 to-transparent" />

        {/* Content overlay */}
        <div className="absolute inset-0 p-4 flex flex-col justify-end">
          {/* Moment description (complete text) */}
          <h3 className="font-medium text-xs text-white mb-3">
            {moment.title}
          </h3>

          {/* Character info and actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Image
                src={avatarSrc}
                alt={character.name}
                width={32}
                height={32}
                className="w-8 h-8 rounded-full border-2 border-white/60 flex-shrink-0 object-cover"
                unoptimized
                onError={() => setAvatarSrc('https://picsum.photos/400/400')}
              />
              <div className="flex-1 min-w-0">
                <p className="font-semibold text-sm text-white truncate">
                  {character.name}
                </p>
                <div className="flex items-center gap-1 text-xs text-white/80">
                  <Clock size={12} />
                  <span>{publishedAt}</span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-3 text-white/90">
              <button
                onClick={handleLike}
                className="flex items-center gap-1 hover:text-red-400 transition-colors"
              >
                <Heart size={16} className={isLiked ? 'text-red-400 fill-current' : ''} />
                <span className="text-xs">{likeCount}</span>
              </button>
              <div className="flex items-center gap-1">
                <Share2 size={16} />
                <span className="text-xs">{stats.shares}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default MomentCard;