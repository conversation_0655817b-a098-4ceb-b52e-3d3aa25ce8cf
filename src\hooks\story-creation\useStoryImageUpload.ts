'use client';

import { useState, useCallback, useRef } from 'react';
import toast from 'react-hot-toast';
import type { StoryFormData, StoryChapter } from '@/types/story-creation';

export const useStoryImageUpload = (
  formData: StoryFormData,
  setFormData: (data: StoryFormData | ((prev: StoryFormData) => StoryFormData)) => void
) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [worldSettingImagePreview, setWorldSettingImagePreview] = useState<string>('');
  const [chapterImagePreviews, setChapterImagePreviews] = useState<Record<string, string>>({});

  // Refs for file inputs
  const worldSettingImageInputRef = useRef<HTMLInputElement>(null);
  const chapterImageInputRefs = useRef<Record<string, HTMLInputElement | null>>({});

  // Validate image file
  const validateImageFile = useCallback((file: File): boolean => {
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return false;
    }

    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      toast.error('Image file size must be less than 10MB');
      return false;
    }

    return true;
  }, []);

  // Handle world setting cover image upload
  const handleWorldSettingImageUpload = useCallback((file: File) => {
    if (!validateImageFile(file)) return;

    setIsUploading(true);
    setUploadError(null);

    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setWorldSettingImagePreview(result);
        
        // Update form data
        setFormData(prev => ({
          ...prev,
          worldSetting: {
            ...prev.worldSetting,
            coverImage: file,
            coverImageUrl: undefined
          }
        }));

        toast.success('World setting cover image uploaded successfully!');
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      setUploadError('Failed to upload image');
      toast.error('Failed to upload image');
      setIsUploading(false);
    }
  }, [validateImageFile, setFormData]);

  // Handle chapter background image upload
  const handleChapterImageUpload = useCallback((chapterId: string, file: File) => {
    if (!validateImageFile(file)) return;

    setIsUploading(true);
    setUploadError(null);

    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setChapterImagePreviews(prev => ({
          ...prev,
          [chapterId]: result
        }));
        
        // Update form data
        setFormData(prev => ({
          ...prev,
          chapters: prev.chapters.map(chapter => 
            chapter.id === chapterId 
              ? { 
                  ...chapter, 
                  backgroundImage: file,
                  backgroundImageUrl: undefined,
                  useWorldSettingImage: false
                }
              : chapter
          )
        }));

        toast.success('Chapter background image uploaded successfully!');
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      setUploadError('Failed to upload chapter image');
      toast.error('Failed to upload chapter image');
      setIsUploading(false);
    }
  }, [validateImageFile, setFormData]);

  // Toggle use world setting image for chapter
  const toggleUseWorldSettingImage = useCallback((chapterId: string, useWorldSetting: boolean) => {
    setFormData(prev => ({
      ...prev,
      chapters: prev.chapters.map(chapter => 
        chapter.id === chapterId 
          ? { 
              ...chapter, 
              useWorldSettingImage: useWorldSetting,
              backgroundImage: useWorldSetting ? undefined : chapter.backgroundImage,
              backgroundImageUrl: useWorldSetting ? undefined : chapter.backgroundImageUrl
            }
          : chapter
      )
    }));

    if (useWorldSetting) {
      setChapterImagePreviews(prev => {
        const newPreviews = { ...prev };
        delete newPreviews[chapterId];
        return newPreviews;
      });
      toast.success('Chapter will use world setting image');
    }
  }, [setFormData]);

  // Trigger file input clicks
  const triggerWorldSettingImageInput = useCallback(() => {
    worldSettingImageInputRef.current?.click();
  }, []);

  const triggerChapterImageInput = useCallback((chapterId: string) => {
    chapterImageInputRefs.current[chapterId]?.click();
  }, []);

  // Get chapter image preview (either chapter-specific or world setting)
  const getChapterImagePreview = useCallback((chapterId: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter?.useWorldSettingImage) {
      return worldSettingImagePreview || formData.worldSetting.coverImageUrl;
    }
    return chapterImagePreviews[chapterId] || chapter?.backgroundImageUrl;
  }, [formData.chapters, formData.worldSetting.coverImageUrl, worldSettingImagePreview, chapterImagePreviews]);

  return {
    // State
    isUploading,
    uploadError,
    worldSettingImagePreview: worldSettingImagePreview || formData.worldSetting.coverImageUrl,
    chapterImagePreviews,
    
    // Refs
    worldSettingImageInputRef,
    chapterImageInputRefs,
    
    // Actions
    handleWorldSettingImageUpload,
    handleChapterImageUpload,
    toggleUseWorldSettingImage,
    triggerWorldSettingImageInput,
    triggerChapterImageInput,
    getChapterImagePreview,
    validateImageFile
  };
};
