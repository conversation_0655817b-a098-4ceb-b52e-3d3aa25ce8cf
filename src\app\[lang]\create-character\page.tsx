import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import CreateCharacterClientPage from './CreateCharacterClientPage';

interface CreateCharacterPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function CreateCharacterPage({ params }: CreateCharacterPageProps) {
  const { lang } = await params;

  return (
    <AuthGuard requireAuth={true}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <CreateCharacterClientPage lang={lang} />
      </Suspense>
    </AuthGuard>
  );
} 