'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Gift, Zap, <PERSON>, Compass, <PERSON>, Heart, Sparkles } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { Task } from '../../app/[lang]/journey/JourneyClientPage';

interface TaskCardProps {
  task: Task;
  lang: string;
  onClaimReward: (taskId: string) => void;
  isClaimingReward: boolean;
  isSpecial?: boolean;
}

const TaskCard: React.FC<TaskCardProps> = ({ 
  task, 
  lang, 
  onClaimReward, 
  isClaimingReward,
  isSpecial = false 
}) => {
  const { t } = useTranslation(lang, 'translation');

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'interaction': return <Zap className="w-4 h-4" />;
      case 'creation': return <Sparkles className="w-4 h-4" />;
      case 'social': return <Users className="w-4 h-4" />;
      case 'exploration': return <Compass className="w-4 h-4" />;
      case 'memory': return <Brain className="w-4 h-4" />;
      case 'bonding': return <Heart className="w-4 h-4" />;
      default: return <Zap className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'interaction': return 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200';
      case 'creation': return 'bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200';
      case 'social': return 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200';
      case 'exploration': return 'bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-200';
      case 'memory': return 'bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-200';
      case 'bonding': return 'bg-red-100 dark:bg-red-900/50 text-red-800 dark:text-red-200';
      default: return 'bg-gray-100 dark:bg-gray-900/50 text-gray-800 dark:text-gray-200';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200';
      case 'medium': return 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200';
      case 'hard': return 'bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-200';
      case 'epic': return 'bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200';
      default: return 'bg-gray-100 dark:bg-gray-900/50 text-gray-800 dark:text-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'claimed': return <Gift className="w-5 h-5 text-purple-500" />;
      case 'locked': return <Lock className="w-5 h-5 text-gray-400" />;
      default: return <Clock className="w-5 h-5 text-blue-500" />;
    }
  };

  const getRewardIcon = (rewardType: string) => {
    switch (rewardType) {
      case 'alphane': return '✨';
      case 'endora': return '💎';
      case 'serotile': return '🧩';
      case 'oxytol': return '💧';
      case 'experience': return '⭐';
      case 'badge': return '🏆';
      case 'streakFreeze': return '❄️';
      default: return '🎁';
    }
  };

  const progressPercentage = (task.progress.current / task.progress.required) * 100;

  return (
    <div className={`p-6 border rounded-lg theme-transition ${
      isSpecial 
        ? 'border-yellow-300 dark:border-yellow-700 bg-yellow-50/50 dark:bg-yellow-900/10' 
        : task.status === 'locked' 
          ? 'border-border opacity-60 bg-muted/50' 
          : task.status === 'completed' && !isClaimingReward
            ? 'border-green-300 dark:border-green-700 bg-green-50/50 dark:bg-green-900/10'
            : task.status === 'claimed'
              ? 'border-purple-300 dark:border-purple-700 bg-purple-50/50 dark:bg-purple-900/10'
              : 'border-border bg-card hover:border-primary/20'
    }`}>
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            {getStatusIcon(task.status)}
            <h3 className="text-lg font-semibold text-foreground">{task.name}</h3>
            {isSpecial && (
              <div className="bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full text-xs font-medium">
                Special
              </div>
            )}
          </div>
          <p className="text-foreground/70 mb-3">{task.description}</p>
          
          {/* Tags */}
          <div className="flex items-center gap-2 mb-3">
            <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 ${getCategoryColor(task.category)}`}>
              {getCategoryIcon(task.category)}
              {t(`tasks.categories.${task.category}`)}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(task.difficulty)}`}>
              {t(`tasks.difficulty.${task.difficulty}`)}
            </span>
          </div>

          {/* Progress */}
          {task.progress.required > 1 && (
            <div className="mb-3">
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm text-foreground/70">Progress</span>
                <span className="text-sm font-medium text-foreground">
                  {task.progress.current}/{task.progress.required}
                </span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* Unlock Condition */}
          {task.status === 'locked' && task.unlockCondition && (
            <div className="bg-muted rounded-lg p-3 mb-3">
              <p className="text-sm text-foreground/70">
                <Lock className="w-4 h-4 inline mr-1" />
                {task.unlockCondition}
              </p>
            </div>
          )}

          {/* Time Remaining (for special tasks) */}
          {task.timeRemaining && (
            <div className="bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200 rounded-lg p-3 mb-3">
              <p className="text-sm font-medium">
                <Clock className="w-4 h-4 inline mr-1" />
                {t('tasks.surpriseTask.timeRemaining', { time: task.timeRemaining })}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Rewards */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-foreground mb-2 flex items-center gap-1">
          <Gift className="w-4 h-4" />
          Rewards
        </h4>
        <div className="flex flex-wrap gap-2">
          {task.rewards.map((reward, index) => (
            <div key={index} className="bg-muted rounded-lg px-3 py-2 text-sm flex items-center gap-1">
              <span>{getRewardIcon(reward.type)}</span>
              <span className="text-foreground/80">{reward.description}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Action Button */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-foreground/60">
          {task.status === 'completed' && 'Task completed!'}
          {task.status === 'claimed' && 'Reward claimed'}
          {task.status === 'locked' && 'Locked'}
          {task.status === 'available' && `${task.progress.current}/${task.progress.required} completed`}
        </div>
        
        {task.status === 'completed' && (
          <button
            onClick={() => onClaimReward(task.id)}
            disabled={isClaimingReward}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-1.5"
          >
            {isClaimingReward ? (
              <>
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                Claiming...
              </>
            ) : (
              <>
                <Gift className="w-4 h-4" />
                {t('tasks.claimReward')}
              </>
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default TaskCard; 