import { Suspense } from 'react';
import FollowersClientPage from './FollowersClientPage';
import { characters } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';
import { getUserByUid, getUserFollowers } from '@/lib/auth-api';
import { notFound } from 'next/navigation';

// Mock followers data
const generateFollowersData = () => {
  return characters.map((character, index) => ({
    id: character.id,
    name: character.name,
    avatar: character.character_avatar,
    username: `@${character.name.toLowerCase().replace(' ', '_')}`,
    isFollowing: Math.random() > 0.5, // Random following status
    followerCount: Math.floor(Math.random() * 10000) + 100,
    bio: `Fan of AI characters and interactive stories. Love exploring new worlds and meeting interesting personalities.`,
    joinedDate: `${Math.floor(Math.random() * 365) + 1} days ago`,
    isVerified: Math.random() > 0.7, // 30% chance of being verified
  }));
};

export default async function FollowersPage({
  params
}: {
  params: Promise<{ lang: string; uid: string }>
}) {
  const { lang, uid } = await params;

  // For now, we'll use mock data since server-side API calls need proper setup
  let userData = {
    _id: uid,
    uid: uid,
    name: `User ${uid.slice(-4)}`,
    email: `user${uid.slice(-4)}@example.com`,
    avatar: `https://i.pravatar.cc/160?u=${uid}`,
    subscriber_count: Math.floor(Math.random() * 10000) + 100,
  };

  let followersList = generateFollowersData();

  const userInfo = {
    name: userData?.name || 'Unknown User',
    avatar: userData?.avatar || 'https://i.pravatar.cc/160?u=profile',
    followerCount: userData?.subscriber_count || followersList.length,
  };

  return (
    <Suspense fallback={
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    }>
      <MainAppLayout lang={lang} title={`${userInfo.name}'s Followers`}>
        <FollowersClientPage
          lang={lang}
          uid={uid}
          userInfo={userInfo}
          followers={followersList}
        />
      </MainAppLayout>
    </Suspense>
  );
}
