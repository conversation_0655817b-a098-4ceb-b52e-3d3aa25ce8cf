'use client';

import { useState, useEffect, useCallback } from 'react';

interface StickyOffsetResult {
  offset: number;
  headerHeight: number;
  overviewHeight: number;
}

/**
 * Custom hook to dynamically calculate sticky offset based on header and overview heights
 * Handles responsive layouts and real-time updates
 */
export const useStickyOffset = (): StickyOffsetResult => {
  const [offset, setOffset] = useState(0);
  const [headerHeight, setHeaderHeight] = useState(0);
  const [overviewHeight, setOverviewHeight] = useState(0);

  const calculateHeights = useCallback(() => {
    // Calculate header height - try multiple selectors
    const headerElement = document.querySelector('header') ||
                          document.querySelector('[data-header]') ||
                          document.querySelector('.header') ||
                          document.querySelector('nav[class*="h-14"], nav[class*="h-16"]') ||
                          document.querySelector('[class*="sticky"][class*="top-0"]');

    let calculatedHeaderHeight = 0;
    if (headerElement) {
      const rect = headerElement.getBoundingClientRect();
      calculatedHeaderHeight = rect.height;
    } else {
      // Fallback to responsive values if header element not found
      const isMobile = window.innerWidth < 1024;
      calculatedHeaderHeight = isMobile ? 56 : 64; // h-14 : h-16
    }

    // Calculate overview tab navigation height
    const overviewElement = document.querySelector('[data-journey-tabs]') ||
                           document.querySelector('.journey-tab-navigation') ||
                           document.querySelector('[class*="sticky"][class*="top-14"]') ||
                           document.querySelector('[class*="sticky"][class*="top-16"]');

    let calculatedOverviewHeight = 0;
    if (overviewElement) {
      const rect = overviewElement.getBoundingClientRect();
      calculatedOverviewHeight = rect.height;
    } else {
      // Fallback estimation for overview tabs
      calculatedOverviewHeight = 64; // Approximate height for tab navigation
    }

    const totalOffset = calculatedHeaderHeight + calculatedOverviewHeight;

    setHeaderHeight(calculatedHeaderHeight);
    setOverviewHeight(calculatedOverviewHeight);
    setOffset(totalOffset);

    return {
      headerHeight: calculatedHeaderHeight,
      overviewHeight: calculatedOverviewHeight,
      offset: totalOffset
    };
  }, []);

  // Debounced resize handler
  const debouncedCalculateHeights = useCallback(() => {
    let timeoutId: NodeJS.Timeout;
    
    const debounced = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        calculateHeights();
      }, 100); // 100ms debounce
    };

    return debounced;
  }, [calculateHeights]);

  useEffect(() => {
    // Initial calculation
    const initialCalculation = () => {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', calculateHeights);
      } else {
        calculateHeights();
      }
    };

    initialCalculation();

    // Set up resize observer for more accurate tracking
    let resizeObserver: ResizeObserver | null = null;
    
    if (typeof window !== 'undefined' && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(() => {
        debouncedCalculateHeights()();
      });

      // Observe header and overview elements
      const headerElement = document.querySelector('header') || 
                            document.querySelector('[data-header]');
      const overviewElement = document.querySelector('[data-journey-tabs]') ||
                             document.querySelector('.journey-tab-navigation');

      if (headerElement) {
        resizeObserver.observe(headerElement);
      }
      if (overviewElement) {
        resizeObserver.observe(overviewElement);
      }
    }

    // Fallback to window resize listener
    const handleResize = debouncedCalculateHeights();
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    // Recalculate when fonts are loaded (affects text height)
    if (document.fonts) {
      document.fonts.ready.then(calculateHeights);
    }

    // Cleanup
    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      document.removeEventListener('DOMContentLoaded', calculateHeights);
    };
  }, [calculateHeights, debouncedCalculateHeights]);

  return {
    offset,
    headerHeight,
    overviewHeight
  };
};

/**
 * Utility function to convert pixel value to CSS custom property
 */
export const createStickyStyle = (offset: number): React.CSSProperties => {
  return {
    position: 'sticky',
    top: `${offset}px`,
    zIndex: 20
  };
};

export default useStickyOffset;
