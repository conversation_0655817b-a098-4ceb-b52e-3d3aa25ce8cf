'use client';

import React from 'react';
import { Gift, ExternalLink, Clock, Calendar, Target, Sparkles } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface RewardPreviewProps {
  onGoToGifts: () => void;
  lang: string;
}

const RewardPreview: React.FC<RewardPreviewProps> = ({ onGoToGifts, lang }) => {
  const { t } = useTranslation(lang, 'translation');

  // Mock reward data - in a real app, this would come from props or API
  const rewardProgress = {
    daily: {
      current: 85,
      total: 100,
      available: true,
      timeRemaining: '3h 24m'
    },
    weekly: {
      current: 60,
      total: 100,
      available: false,
      timeRemaining: '2d 15h'
    },
    monthly: {
      current: 35,
      total: 100,
      available: false,
      timeRemaining: '18d 7h'
    }
  };

  const mockRewards = [
    {
      type: 'daily',
      icon: <Clock className="w-5 h-5 text-blue-500" />,
      title: t('tasks.dailyGifts'),
      progress: rewardProgress.daily,
      rewards: ['50 ✨ Glimmering Dust', '10 💎 Joy Crystal'],
      color: 'blue'
    },
    {
      type: 'weekly',
      icon: <Calendar className="w-5 h-5 text-green-500" />,
      title: t('tasks.weeklyGifts'),
      progress: rewardProgress.weekly,
      rewards: ['150 ✨ Glimmering Dust', '50 💎 Joy Crystal', '5 🧩 Memory Puzzle'],
      color: 'green'
    },
    {
      type: 'monthly',
      icon: <Target className="w-5 h-5 text-purple-500" />,
      title: t('tasks.monthlyGifts'),
      progress: rewardProgress.monthly,
      rewards: ['500 ✨ Glimmering Dust', '200 💎 Joy Crystal', '20 🧩 Memory Puzzle', '🏆 Special Badge'],
      color: 'purple'
    }
  ];

  const getProgressColor = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-500';
      case 'green': return 'bg-green-500';
      case 'purple': return 'bg-purple-500';
      default: return 'bg-primary';
    }
  };

  const getBorderColor = (color: string, available: boolean) => {
    if (available) {
      switch (color) {
        case 'blue': return 'border-blue-300 dark:border-blue-700 bg-blue-50/50 dark:bg-blue-900/10';
        case 'green': return 'border-green-300 dark:border-green-700 bg-green-50/50 dark:bg-green-900/10';
        case 'purple': return 'border-purple-300 dark:border-purple-700 bg-purple-50/50 dark:bg-purple-900/10';
        default: return 'border-primary';
      }
    }
    return 'border-border bg-card';
  };

  return (
    <div className="bg-card border border-border rounded-lg p-6 theme-transition">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <Gift className="w-5 h-5 text-primary" />
          Available Rewards
        </h2>
        <button
          onClick={onGoToGifts}
          className="flex items-center gap-1.5 px-4 py-2 text-sm bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
        >
          {t('tasks.goToGifts')}
          <ExternalLink className="w-4 h-4" />
        </button>
      </div>

      {/* Reward Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {mockRewards.map((reward, index) => {
          const progressPercentage = (reward.progress.current / reward.progress.total) * 100;
          
          return (
            <div 
              key={index} 
              className={`p-4 border rounded-lg theme-transition ${getBorderColor(reward.color, reward.progress.available)}`}
            >
              <div className="flex items-center gap-2 mb-3">
                {reward.icon}
                <span className="font-medium text-foreground">{reward.title}</span>
                {reward.progress.available && (
                  <div className="bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
                    Ready!
                  </div>
                )}
              </div>

              {/* Progress Ring Placeholder */}
              <div className="flex items-center justify-center mb-4">
                <div className="relative w-20 h-20">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      className="text-muted"
                      stroke="currentColor"
                      strokeWidth="3"
                      fill="none"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <path
                      className={`${reward.color === 'blue' ? 'text-blue-500' : reward.color === 'green' ? 'text-green-500' : 'text-purple-500'}`}
                      stroke="currentColor"
                      strokeWidth="3"
                      strokeLinecap="round"
                      fill="none"
                      strokeDasharray={`${progressPercentage}, 100`}
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs font-medium text-foreground">
                      {progressPercentage.toFixed(0)}%
                    </span>
                  </div>
                </div>
              </div>

              {/* Progress Info */}
              <div className="text-center mb-3">
                <div className="text-sm text-foreground/70 mb-1">
                  {reward.progress.current}/{reward.progress.total} completed
                </div>
                {!reward.progress.available && (
                  <div className="text-xs text-foreground/50">
                    Next in {reward.progress.timeRemaining}
                  </div>
                )}
              </div>

              {/* Rewards List */}
              <div className="space-y-1">
                <div className="text-xs font-medium text-foreground mb-1">Rewards:</div>
                {reward.rewards.map((rewardItem, rewardIndex) => (
                  <div key={rewardIndex} className="text-xs text-foreground/70 bg-muted rounded px-2 py-1">
                    {rewardItem}
                  </div>
                ))}
              </div>

              {/* Special Indicator */}
              {reward.progress.available && (
                <div className="mt-3 flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-yellow-500 animate-pulse" />
                  <span className="text-xs font-medium text-yellow-600 dark:text-yellow-400 ml-1">
                    Claim in Gift Center
                  </span>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Quick Info */}
      <div className="mt-6 bg-muted rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Gift className="w-5 h-5 text-primary mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-foreground mb-1">
              💡 How to earn rewards:
            </h4>
            <ul className="text-sm text-foreground/70 space-y-1">
              <li>• Complete daily tasks to fill the daily gift progress</li>
              <li>• Maintain your interaction streak for bonus multipliers</li>
              <li>• Check the Gift Center to claim available rewards</li>
              <li>• Premium subscribers get enhanced rewards</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RewardPreview; 