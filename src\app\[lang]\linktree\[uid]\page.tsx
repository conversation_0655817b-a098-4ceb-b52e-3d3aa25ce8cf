import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import LinktreeClientPage from './LinktreeClientPage';
import MainAppLayout from '@/components/MainAppLayout';
import { getUserByUid, getUserLinks } from '@/lib/auth-api';
import { notFound } from 'next/navigation';

// Mock links data
const generateLinksData = () => {
  return [
    {
      id: '1',
      title: 'My Website',
      url: 'https://example.com',
      description: 'Check out my personal website',
      icon: '🌐',
      clicks: 1234,
      isActive: true,
    },
    {
      id: '2',
      title: 'Twitter',
      url: 'https://twitter.com/username',
      description: 'Follow me on Twitter',
      icon: '🐦',
      clicks: 856,
      isActive: true,
    },
    {
      id: '3',
      title: 'Instagram',
      url: 'https://instagram.com/username',
      description: 'See my latest photos',
      icon: '📸',
      clicks: 2341,
      isActive: true,
    },
    {
      id: '4',
      title: 'YouTube Channel',
      url: 'https://youtube.com/channel/username',
      description: 'Subscribe to my channel',
      icon: '🎥',
      clicks: 567,
      isActive: true,
    },
    {
      id: '5',
      title: 'Discord Server',
      url: 'https://discord.gg/invite',
      description: 'Join our community',
      icon: '💬',
      clicks: 789,
      isActive: true,
    },
    {
      id: '6',
      title: 'Patreon',
      url: 'https://patreon.com/username',
      description: 'Support my work',
      icon: '💖',
      clicks: 234,
      isActive: false,
    },
  ];
};

export default async function LinktreePage({ 
  params 
}: { 
  params: Promise<{ lang: string; uid: string }> 
}) {
  const { lang, uid } = await params;
  
  // For now, we'll use mock data since server-side API calls need proper setup
  let userData = {
    _id: uid,
    uid: uid,
    name: `User ${uid.slice(-4)}`,
    email: `user${uid.slice(-4)}@example.com`,
    avatar: `https://i.pravatar.cc/160?u=${uid}`,
    bio: 'Welcome to my link tree!',
  };

  let linksData = generateLinksData();

  return (
    <AuthGuard requireAuth={false}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <MainAppLayout lang={lang}>
          <LinktreeClientPage 
            lang={lang} 
            uid={uid}
            userData={userData}
            linksData={linksData}
          />
        </MainAppLayout>
      </Suspense>
    </AuthGuard>
  );
}
