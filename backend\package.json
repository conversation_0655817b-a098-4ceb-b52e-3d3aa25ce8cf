{"name": "alphane-backend", "version": "1.0.0", "description": "Backend API for Alphane AI character platform", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "node src/database/seeders/index.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "socket.io": "^4.7.4", "redis": "^4.6.11", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "axios": "^1.6.2", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.56.0", "prettier": "^3.1.1"}, "keywords": ["api", "nodejs", "express", "postgresql", "ai", "character", "chat"], "author": "Alphane Team", "license": "MIT"}