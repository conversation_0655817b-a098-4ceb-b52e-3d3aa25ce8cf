import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import ProfileUidClientPage from './ProfileUidClientPage';
import { generateAllSampleMoments, characters, generateRandomAspectRatio } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';
import { getUserByUid } from '@/lib/auth-api';
import { notFound } from 'next/navigation';

export default async function ProfileUidPage({ 
  params 
}: { 
  params: Promise<{ lang: string; uid: string }> 
}) {
  const { lang, uid } = await params;
  
  // For now, we'll use mock data since server-side API calls need proper setup
  // In a real implementation, you would fetch user data here or use client-side fetching
  let userData = null;

  // Mock user data based on UID for demonstration
  userData = {
    _id: uid,
    uid: uid,
    name: `${uid.slice(-4)}`,
    email: `user${uid.slice(-4)}@example.com`,
    avatar: `https://i.pravatar.cc/160?u=${uid}`,
    bio: 'Passionate about crafting immersive AI characters and captivating stories. Building worlds where imagination meets technology.',
    subscriber_count: Math.floor(Math.random() * 10000) + 100,
    follow_count: Math.floor(Math.random() * 1000) + 50,
  };

  // Generate mock data for the profile
  const moments = generateAllSampleMoments(12);
  const liked = generateAllSampleMoments(10);
  const friendsCards = characters.map((c) => ({
    character: c,
    stats: {
      likes: 0,
      friends: Math.floor(Math.random()*500)+10,
      shares: 0,
    },
    aspectRatio: generateRandomAspectRatio(),
  }));

  return (
    <AuthGuard requireAuth={false}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <MainAppLayout lang={lang}>
          <ProfileUidClientPage 
            lang={lang} 
            uid={uid}
            userData={userData}
            moments={moments} 
            liked={liked} 
            friendsCards={friendsCards} 
          />
        </MainAppLayout>
      </Suspense>
    </AuthGuard>
  );
}
