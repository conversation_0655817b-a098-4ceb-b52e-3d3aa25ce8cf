-- =====================================================
-- Social Features, Analytics, and Final Optimizations
-- Version: 2.0.0 (Part 3)
-- Description: Social interactions, analytics, and performance optimizations
-- =====================================================

-- =====================================================
-- PHASE 6: SOCIAL AND UGC FEATURES
-- =====================================================

-- Extend moments table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'moment_type') THEN
        ALTER TABLE moments ADD COLUMN moment_type VARCHAR(30) DEFAULT 'general' CHECK (moment_type IN (
            'general', 'milestone', 'achievement', 'story_highlight', 'character_showcase', 'memory_share'
        ));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'mood') THEN
        ALTER TABLE moments ADD COLUMN mood VARCHAR(50);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'visibility') THEN
        ALTER TABLE moments ADD COLUMN visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'followers', 'private'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'media_urls') THEN
        ALTER TABLE moments ADD COLUMN media_urls JSONB DEFAULT '[]';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'tags') THEN
        ALTER TABLE moments ADD COLUMN tags TEXT[] DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'location_data') THEN
        ALTER TABLE moments ADD COLUMN location_data JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'interaction_stats') THEN
        ALTER TABLE moments ADD COLUMN interaction_stats JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'content_metadata') THEN
        ALTER TABLE moments ADD COLUMN content_metadata JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'is_featured') THEN
        ALTER TABLE moments ADD COLUMN is_featured BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'moments' AND column_name = 'featured_until') THEN
        ALTER TABLE moments ADD COLUMN featured_until TIMESTAMP;
    END IF;
END $$;

-- Create social_interactions table
CREATE TABLE IF NOT EXISTS social_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    target_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Interaction Type and Target
    interaction_type VARCHAR(30) NOT NULL CHECK (interaction_type IN (
        'like', 'unlike', 'share', 'comment', 'follow', 'unfollow', 
        'bookmark', 'report', 'block', 'mention', 'react'
    )),
    
    target_type VARCHAR(30) NOT NULL CHECK (target_type IN (
        'moment', 'character', 'story', 'comment', 'user', 'memory_capsule'
    )),
    target_id UUID NOT NULL,
    
    -- Interaction Data
    interaction_data JSONB DEFAULT '{}',
    context_data JSONB DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create comments table
CREATE TABLE IF NOT EXISTS comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Comment Target
    target_type VARCHAR(30) NOT NULL CHECK (target_type IN (
        'moment', 'character', 'story', 'memory_capsule', 'comment'
    )),
    target_id UUID NOT NULL,
    
    -- Comment Content
    content TEXT NOT NULL,
    content_type VARCHAR(20) DEFAULT 'text' CHECK (content_type IN ('text', 'rich_text', 'media')),
    
    -- Threading
    parent_comment_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    thread_depth INTEGER DEFAULT 0,
    
    -- Interaction Stats
    likes_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    
    -- Moderation
    is_edited BOOLEAN DEFAULT false,
    edited_at TIMESTAMP,
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP,
    moderation_status VARCHAR(20) DEFAULT 'approved' CHECK (moderation_status IN (
        'pending', 'approved', 'rejected', 'flagged'
    )),
    
    -- Metadata
    comment_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_statistics table
CREATE TABLE IF NOT EXISTS user_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Activity Statistics
    total_login_days INTEGER DEFAULT 0,
    consecutive_login_days INTEGER DEFAULT 0,
    max_consecutive_login_streak INTEGER DEFAULT 0,
    last_login_date DATE,
    
    -- Interaction Statistics
    total_conversations INTEGER DEFAULT 0,
    total_messages_sent INTEGER DEFAULT 0,
    total_messages_received INTEGER DEFAULT 0,
    avg_conversation_length DECIMAL(8,2) DEFAULT 0.00,
    total_conversation_time INTEGER DEFAULT 0,
    
    -- Character and Story Statistics
    characters_created INTEGER DEFAULT 0,
    characters_liked INTEGER DEFAULT 0,
    stories_completed INTEGER DEFAULT 0,
    stories_started INTEGER DEFAULT 0,
    avg_story_completion_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- Social Statistics
    moments_shared INTEGER DEFAULT 0,
    moments_liked INTEGER DEFAULT 0,
    comments_made INTEGER DEFAULT 0,
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    
    -- Achievement Statistics
    achievements_unlocked INTEGER DEFAULT 0,
    total_achievement_points INTEGER DEFAULT 0,
    rare_achievements_count INTEGER DEFAULT 0,
    
    -- Currency and Purchase Statistics
    total_currency_earned JSONB DEFAULT '{}',
    total_currency_spent JSONB DEFAULT '{}',
    total_purchases_made INTEGER DEFAULT 0,
    total_money_spent DECIMAL(10,2) DEFAULT 0.00,
    
    -- Engagement Metrics
    session_count INTEGER DEFAULT 0,
    avg_session_duration INTEGER DEFAULT 0,
    total_time_spent INTEGER DEFAULT 0,
    feature_usage_stats JSONB DEFAULT '{}',
    
    -- Metadata
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id)
);

-- Create daily_missions table
CREATE TABLE IF NOT EXISTS daily_missions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Mission Information
    mission_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN (
        'conversation', 'social', 'creation', 'exploration', 'achievement'
    )),
    
    -- Mission Configuration
    mission_type VARCHAR(30) NOT NULL CHECK (mission_type IN (
        'daily', 'weekly', 'monthly', 'seasonal', 'special'
    )),
    
    -- Requirements and Progress
    requirements JSONB NOT NULL DEFAULT '{}',
    rewards JSONB NOT NULL DEFAULT '[]',
    
    -- Difficulty and Rarity
    difficulty VARCHAR(20) DEFAULT 'easy' CHECK (difficulty IN ('easy', 'medium', 'hard', 'expert')),
    rarity VARCHAR(20) DEFAULT 'common' CHECK (rarity IN ('common', 'uncommon', 'rare', 'epic', 'legendary')),
    
    -- Availability
    is_active BOOLEAN DEFAULT true,
    available_from TIME DEFAULT '00:00:00',
    available_until TIME DEFAULT '23:59:59',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_daily_mission_progress table
CREATE TABLE IF NOT EXISTS user_daily_mission_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    daily_mission_id UUID NOT NULL REFERENCES daily_missions(id) ON DELETE CASCADE,
    
    -- Progress Tracking
    current_progress INTEGER DEFAULT 0,
    target_progress INTEGER NOT NULL,
    is_completed BOOLEAN DEFAULT false,
    is_claimed BOOLEAN DEFAULT false,
    
    -- Timing
    assigned_date DATE DEFAULT CURRENT_DATE,
    completed_at TIMESTAMP,
    claimed_at TIMESTAMP,
    expires_at TIMESTAMP,
    
    -- Progress Details
    progress_data JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, daily_mission_id, assigned_date)
);

-- Create memorial_events table
CREATE TABLE IF NOT EXISTS memorial_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    character_id UUID REFERENCES characters(id) ON DELETE CASCADE,
    
    -- Event Information
    event_type VARCHAR(30) NOT NULL CHECK (event_type IN (
        'first_meeting', 'perfect_intimacy', 'perfect_storyline', 
        'anniversary', 'milestone', 'special_moment'
    )),
    
    event_name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Timing
    original_date TIMESTAMP NOT NULL,
    anniversary_date DATE NOT NULL,
    next_anniversary DATE,
    
    -- Rewards and Recognition
    rewards JSONB DEFAULT '[]',
    is_claimed BOOLEAN DEFAULT false,
    claimed_at TIMESTAMP,
    
    -- Event Metadata
    event_data JSONB DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- PHASE 7: PERFORMANCE INDEXES
-- =====================================================

-- Character system indexes
CREATE INDEX IF NOT EXISTS idx_characters_mbti ON characters(mbti_type) WHERE mbti_type IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_characters_era ON characters(era) WHERE era IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_characters_region ON characters(region) WHERE region IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_character_localizations_character_lang ON character_localizations(character_id, language_code);
CREATE INDEX IF NOT EXISTS idx_character_localizations_language ON character_localizations(language_code);

CREATE INDEX IF NOT EXISTS idx_character_appearance_character ON character_appearance_details(character_id);
CREATE INDEX IF NOT EXISTS idx_character_personality_character ON character_personality_profiles(character_id);

-- Story system indexes
CREATE INDEX IF NOT EXISTS idx_story_localizations_story_lang ON story_localizations(story_id, language_code);
CREATE INDEX IF NOT EXISTS idx_scene_hierarchical_story_scene ON scene_hierarchical_info(story_id, scene_index);
CREATE INDEX IF NOT EXISTS idx_story_progression_user_story ON story_progression(user_id, story_id);
CREATE INDEX IF NOT EXISTS idx_story_progression_status ON story_progression(status);

-- Store system indexes
CREATE INDEX IF NOT EXISTS idx_store_products_category ON store_products(category);
CREATE INDEX IF NOT EXISTS idx_store_products_type ON store_products(product_type);
CREATE INDEX IF NOT EXISTS idx_store_products_featured ON store_products(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_store_products_available ON store_products(is_available) WHERE is_available = true;

CREATE INDEX IF NOT EXISTS idx_subscription_plans_tier ON subscription_plans(tier);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_purchase_history_user ON purchase_history(user_id);
CREATE INDEX IF NOT EXISTS idx_purchase_history_date ON purchase_history(created_at);

-- Social system indexes
CREATE INDEX IF NOT EXISTS idx_social_interactions_user ON social_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_social_interactions_target ON social_interactions(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_social_interactions_type ON social_interactions(interaction_type);

CREATE INDEX IF NOT EXISTS idx_comments_target ON comments(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_comments_user ON comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent ON comments(parent_comment_id);

-- Gamification indexes
CREATE INDEX IF NOT EXISTS idx_user_statistics_user ON user_statistics(user_id);
CREATE INDEX IF NOT EXISTS idx_daily_missions_type ON daily_missions(mission_type);
CREATE INDEX IF NOT EXISTS idx_user_daily_mission_progress_user_date ON user_daily_mission_progress(user_id, assigned_date);
CREATE INDEX IF NOT EXISTS idx_memorial_events_user ON memorial_events(user_id);
CREATE INDEX IF NOT EXISTS idx_memorial_events_anniversary ON memorial_events(anniversary_date);

-- GIN indexes for JSONB columns
CREATE INDEX IF NOT EXISTS idx_character_localizations_appear_gin ON character_localizations USING GIN (appear_hierarchical_info);
CREATE INDEX IF NOT EXISTS idx_character_localizations_ext_gin ON character_localizations USING GIN (ext_hierarchical_info);
CREATE INDEX IF NOT EXISTS idx_story_localizations_core_world_gin ON story_localizations USING GIN (story_core_world);
CREATE INDEX IF NOT EXISTS idx_store_products_price_gin ON store_products USING GIN (price_data);
CREATE INDEX IF NOT EXISTS idx_user_currencies_stats_gin ON user_currencies USING GIN (currency_stats);
CREATE INDEX IF NOT EXISTS idx_social_interactions_data_gin ON social_interactions USING GIN (interaction_data);
CREATE INDEX IF NOT EXISTS idx_user_statistics_feature_usage_gin ON user_statistics USING GIN (feature_usage_stats);

-- =====================================================
-- PHASE 8: TRIGGERS AND FUNCTIONS
-- =====================================================

-- Add triggers for updated_at columns
DO $$
DECLARE
    table_name TEXT;
    tables_with_updated_at TEXT[] := ARRAY[
        'character_localizations', 'character_appearance_details', 'character_personality_profiles',
        'story_localizations', 'scene_hierarchical_info', 'story_progression',
        'store_products', 'subscription_plans', 'user_subscriptions', 'purchase_history',
        'promotional_offers', 'social_interactions', 'comments', 'user_statistics',
        'daily_missions', 'user_daily_mission_progress', 'memorial_events'
    ];
BEGIN
    FOREACH table_name IN ARRAY tables_with_updated_at
    LOOP
        -- Check if trigger already exists
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE trigger_name = table_name || '_updated_at' 
            AND event_object_table = table_name
        ) THEN
            EXECUTE format('
                CREATE TRIGGER %I_updated_at 
                BEFORE UPDATE ON %I 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            ', table_name, table_name);
        END IF;
    END LOOP;
END $$;

-- =====================================================
-- PHASE 9: DATA MIGRATION AND CLEANUP
-- =====================================================

-- Update existing data to match new schema requirements
UPDATE characters SET mbti_type = NULL WHERE mbti_type IS NOT NULL AND mbti_type !~ '^[IE][NS][FT][JP]$';

-- Initialize currency stats for existing users
UPDATE user_currencies 
SET currency_stats = '{
    "star_diamonds": {"spent_24h": 0, "earned_24h": 0, "total_earned": 0, "total_spent": 0},
    "joy_crystals": {"spent_24h": 0, "earned_24h": 0, "total_earned": 0, "total_spent": 0},
    "glimmering_dust": {"spent_24h": 0, "earned_24h": 0, "total_earned": 0, "total_spent": 0},
    "memory_puzzles": {"spent_24h": 0, "earned_24h": 0, "total_earned": 0, "total_spent": 0}
}'::jsonb
WHERE currency_stats = '{}'::jsonb OR currency_stats IS NULL;

-- Initialize user statistics for existing users
INSERT INTO user_statistics (user_id)
SELECT u.id 
FROM users u 
LEFT JOIN user_statistics us ON u.id = us.user_id 
WHERE us.user_id IS NULL;

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

-- Update migration record
UPDATE schema_migrations 
SET completed_at = CURRENT_TIMESTAMP, 
    status = 'completed',
    notes = 'Comprehensive schema upgrade completed successfully. Added multi-language support, enhanced gamification, store system, and social features.'
WHERE version = '2.0.0';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Schema migration 2.0.0 completed successfully at %', CURRENT_TIMESTAMP;
    RAISE NOTICE 'New features: Multi-language characters/stories, Enhanced store system, Advanced gamification, Social interactions';
    RAISE NOTICE 'Performance optimizations: Materialized views, Comprehensive indexing, JSONB optimization';
END $$;
