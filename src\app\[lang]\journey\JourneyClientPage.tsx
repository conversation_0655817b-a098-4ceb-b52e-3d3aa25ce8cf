'use client';

import React, { useState } from 'react';
import { Crown, Trophy, Calendar, ClipboardList } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';
import EnhancedTabNavigation, { TabItem } from '@/components/common/EnhancedTabNavigation';
import { getJourneyTabColors } from '@/utils/tabColorSchemes';
import JourneyOverviewRefactored from '@/components/journey/JourneyOverviewRefactored';
import JourneySeasonRefactored from '@/components/journey/JourneySeasonRefactored';
import MonthlySignInReward from '@/components/journey/MonthlySignInReward';
import JourneyMissions from '@/components/journey/JourneyMissions';

interface JourneyClientPageProps {
  lang: string;
}

const JourneyClientPage: React.FC<JourneyClientPageProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');

  // Journey state
  const [currentLevel, setCurrentLevel] = useState(8);
  const [currentTrophies, setCurrentTrophies] = useState(1247);
  const [seasonEndTime] = useState(new Date('2024-03-15T23:59:59'));
  const [hasHeartTrack, setHasHeartTrack] = useState(false);
  const [hasDiamondTrack, setHasDiamondTrack] = useState(false);
  const [hasMetaverseTrack, setHasMetaverseTrack] = useState(false);

  // Tab state
  const [activeTab, setActiveTab] = useState<'overview' | 'season' | 'monthly' | 'missions'>('overview');

  // Tab configuration for enhanced navigation
  const journeyTabs: TabItem[] = [
    {
      id: 'overview',
      label: t('journey.tabs.overview'),
      icon: Crown,
      description: t('journey.tabs.overviewDesc')
    },
    {
      id: 'season',
      label: t('journey.tabs.season'),
      icon: Trophy,
      description: t('journey.tabs.seasonDesc')
    },
    {
      id: 'monthly',
      label: t('journey.tabs.monthly'),
      icon: Calendar,
      description: t('journey.tabs.monthlyDesc')
    },
    {
      id: 'missions',
      label: t('journey.tabs.missions'),
      icon: ClipboardList,
      description: t('journey.tabs.missionsDesc')
    }
  ];

  const handleTrackPurchase = (trackType: 'heart' | 'diamond' | 'metaverse') => {
    if (trackType === 'heart') {
      setHasHeartTrack(true);
    } else if (trackType === 'diamond') {
      setHasDiamondTrack(true);
    } else {
      setHasMetaverseTrack(true);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <JourneyOverviewRefactored
            lang={lang}
          />
        );
      case 'season':
        return (
          <JourneySeasonRefactored
            lang={lang}
          />
        );
      case 'monthly':
        return (
          <MonthlySignInReward
            lang={lang}
          />
        );
      case 'missions':
        return <JourneyMissions lang={lang} />;
      default:
        return null;
    }
  };

  return (
    <MainAppLayout lang={lang} title={t('journey.title')}>
      <div className="min-h-screen bg-gradient-to-br from-purple-50/30 via-pink-50/30 to-amber-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10">
        {/* Enhanced Tab Navigation */}
        <EnhancedTabNavigation
          tabs={journeyTabs}
          activeTab={activeTab}
          onTabChange={(tabId) => setActiveTab(tabId as 'overview' | 'season' | 'monthly' | 'missions')}
          getTabColors={getJourneyTabColors}
          containerMaxWidth="max-w-4xl"
        />

        {/* Tab Content */}
        <div className="max-w-7xl mx-auto px-4 pt-6 pb-8">
          {renderTabContent()}
        </div>
      </div>
    </MainAppLayout>
  );
};

export default JourneyClientPage; 