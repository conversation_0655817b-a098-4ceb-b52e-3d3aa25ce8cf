// 功能选择类型
export type SelectedFunction = 'flash' | 'customize' | 'import';

// 创建步骤类型
export type Step = 'essentials' | 'behavior' | 'finalize';

// 头像裁剪信息
export interface AvatarCropData {
  x: number;
  y: number;
  width: number;
  height: number;
}

// 故事板章节
export interface StoryboardChapter {
  id: number;
  title: string;
  content: string;
  greetingAfter: string;
  updateGreeting: boolean;
}

// 表单数据类型
export interface CharacterFormData {
  // 基础信息
  name: string;
  description: string; // 内部性格描述
  publicDescription: string; // 对外展示的详情描述
  appearance: string; // 外观描述
  setting: string; // 世界设定
  
  // 图像资源
  characterImage: File | null; // 角色立绘
  avatar: File | null; // 头像（从立绘截取）
  avatarCrop: AvatarCropData | null; // 头像裁剪信息
  
  // 对话设置
  greetingMessage: string;
  personality: string[];
  
  // 分类信息
  gender: string;
  faction: 'anime' | 'realistic' | 'fantasy' | 'scifi';
  
  // 发布设置
  visibility: 'public' | 'unlisted' | 'private';
  
  // 高级设置 (对应API参数)
  backgroundStory: string;
  interactionStyleTags: string;
  voiceIds: string;
  initialMemoriesText: string;
  customPromptPrefix: string;
  
  // 世界书文件
  knowledgeFiles: File[];
}

// Flash生成结果类型
export interface FlashGenerationResult {
  name: string;
  personality: string;
  appearance: string;
  behavior: string;
  knowledge: string;
  greetings: string;
  storyboard: string;
}

// 导入的角色数据类型
export interface ImportedCharacterData extends CharacterFormData {
  // 可以扩展其他导入相关的字段
}

// 图片比例类型
export type AspectRatio = '1:1' | '5:6' | '5:8';

// 步骤配置
export interface StepConfig {
  id: Step;
  label: string;
  icon: any; // React component type
  description: string;
} 