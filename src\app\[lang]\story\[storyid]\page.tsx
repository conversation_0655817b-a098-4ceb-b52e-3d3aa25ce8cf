import StoryDetailClientPage from './StoryDetailClientPage';
import { generateStoryData } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';

export default async function StoryPage({ params }: { params: Promise<{ lang: string; storyid: string }> }) {
  const { lang, storyid } = await params;

  // Get story data using the mock data generator
  const storyData = generateStoryData(storyid);

  return (
    <MainAppLayout lang={lang} title={storyData.story.title}>
      <div className="min-h-screen" style={{ background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 30%, #e2e8f0 70%, #f8fafc 100%)' }}>
        <StoryDetailClientPage storyData={storyData} lang={lang} />
      </div>
    </MainAppLayout>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ lang: string; storyid: string }> }) {
  const { storyid } = await params;
  const storyData = generateStoryData(storyid);
  
  return {
    title: `${storyData.story.title} - Alphane`,
    description: storyData.story.description,
  };
}
