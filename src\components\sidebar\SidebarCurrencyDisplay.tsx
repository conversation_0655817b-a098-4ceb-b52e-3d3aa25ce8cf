'use client';

import React from 'react';
import { Flame, Gem, Puzzle, Droplet } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface User {
  alphane_dust_balance?: number;
  endora_crystal_balance?: number;
  serotile_fragment_balance?: number;
  oxytol_dew_balance?: number;
}

interface SidebarCurrencyDisplayProps {
  user: User | null;
  lang?: string;
}

const SidebarCurrencyDisplay: React.FC<SidebarCurrencyDisplayProps> = ({ user, lang = 'en' }) => {
  const currencies = [
    {
      icon: Flame,
      value: user?.alphane_dust_balance || 1250,
      bgColor: 'bg-orange-500'
    },
    {
      icon: Gem,
      value: user?.endora_crystal_balance || 89,
      bgColor: 'bg-blue-500'
    },
    {
      icon: Puzzle,
      value: user?.serotile_fragment_balance || 156,
      bgColor: 'bg-purple-500'
    },
    {
      icon: Droplet,
      value: user?.oxytol_dew_balance || 42,
      bgColor: 'bg-pink-500'
    }
  ];

  return (
    <div className="w-80 px-4">
      {/* 货币显示 - 与mobile header保持一致的风格 */}
      <div className="flex items-start gap-4">
        {currencies.map((currency, index) => {
          const Icon = currency.icon;
          return (
            <div key={index} className="flex flex-col items-center">
              <div className={`h-11 ${currency.bgColor} rounded-lg flex items-center justify-center mb-0.5`} style={{width: '3.625rem'}}>
                <Icon className="text-white" size={20}/>
              </div>
              <p className="text-lg font-bold text-gray-800 dark:text-gray-100">
                {formatCurrency(currency.value)}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SidebarCurrencyDisplay;
