'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Gamepad2, Trophy, Star, Sparkles, Target, TrendingUp, Medal, Zap, Palette, Crown } from 'lucide-react';
import SettingItem from './SettingItem';
import ToggleSwitch from './ToggleSwitch';
import <PERSON>Field from './SelectField';
import type { SettingsComponentProps } from '@/types/settings';

const GamificationSettings: React.FC<SettingsComponentProps> = ({
  settings,
  updateSetting,
  lang,
  user,
  hasUnsavedChanges,
  isPremiumUser
}) => {
  const { t } = useTranslation(lang, 'translation');

  const taskReminderIntensityOptions = [
    { value: 'low', label: t('settings.categories.gamification.taskReminderIntensityOptions.low') },
    { value: 'moderate', label: t('settings.categories.gamification.taskReminderIntensityOptions.moderate') },
    { value: 'high', label: t('settings.categories.gamification.taskReminderIntensityOptions.high') }
  ];

  const memoryArtStyleOptions = [
    { value: 'anime', label: t('settings.categories.gamification.memoryArtStyleOptions.anime') },
    { value: 'realistic', label: t('settings.categories.gamification.memoryArtStyleOptions.realistic') },
    { value: 'abstract', label: t('settings.categories.gamification.memoryArtStyleOptions.abstract') },
    { value: 'custom', label: t('settings.categories.gamification.memoryArtStyleOptions.custom'), isPremium: true }
  ];

  const leaderboardVisibilityOptions = [
    { value: 'public', label: t('settings.categories.gamification.leaderboardVisibilityOptions.public') },
    { value: 'friends', label: t('settings.categories.gamification.leaderboardVisibilityOptions.friends') },
    { value: 'private', label: t('settings.categories.gamification.leaderboardVisibilityOptions.private') }
  ];

  const experienceDisplayOptions = [
    { value: 'detailed', label: t('settings.categories.gamification.experienceDisplayModeOptions.detailed') },
    { value: 'simplified', label: t('settings.categories.gamification.experienceDisplayModeOptions.simplified') },
    { value: 'minimal', label: t('settings.categories.gamification.experienceDisplayModeOptions.minimal') }
  ];

  const badgeDisplayOptions = [
    { value: 'all', label: t('settings.categories.gamification.badgeDisplayModeOptions.all') },
    { value: 'favorites', label: t('settings.categories.gamification.badgeDisplayModeOptions.favorites') },
    { value: 'recent', label: t('settings.categories.gamification.badgeDisplayModeOptions.recent') }
  ];

  return (
    <div className="space-y-6">
      {/* Visual Effects */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Sparkles className="w-4 h-4" />
          Visual Effects
        </h4>

        <SettingItem
          label={t('settings.categories.gamification.achievementAnimations')}
          description={t('settings.categories.gamification.achievementAnimationsDesc')}
        >
          <ToggleSwitch
            checked={settings.gamification.achievementAnimations}
            onChange={(checked) => updateSetting('gamification.achievementAnimations', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.gamification.currencyGainNotifications')}
          description={t('settings.categories.gamification.currencyGainNotificationsDesc')}
        >
          <ToggleSwitch
            checked={settings.gamification.currencyGainNotifications}
            onChange={(checked) => updateSetting('gamification.currencyGainNotifications', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.gamification.progressCelebrations')}
          description={t('settings.categories.gamification.progressCelebrationsDesc')}
        >
          <ToggleSwitch
            checked={settings.gamification.progressCelebrations}
            onChange={(checked) => updateSetting('gamification.progressCelebrations', checked)}
          />
        </SettingItem>
      </div>

      {/* Task & Reminder Management */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Target className="w-4 h-4" />
          Task & Reminder Management
        </h4>

        <SettingItem
          label={t('settings.categories.gamification.taskReminderIntensity')}
          description={t('settings.categories.gamification.taskReminderIntensityDesc')}
        >
          <SelectField
            value={settings.gamification.taskReminderIntensity}
            onChange={(value) => updateSetting('gamification.taskReminderIntensity', value)}
            options={taskReminderIntensityOptions}
            className="w-40"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.gamification.autoClaimRewards')}
          description={t('settings.categories.gamification.autoClaimRewardsDesc')}
        >
          <ToggleSwitch
            checked={settings.gamification.autoClaimRewards}
            onChange={(checked) => updateSetting('gamification.autoClaimRewards', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.gamification.streakMotivation')}
          description={t('settings.categories.gamification.streakMotivationDesc')}
        >
          <ToggleSwitch
            checked={settings.gamification.streakMotivation}
            onChange={(checked) => updateSetting('gamification.streakMotivation', checked)}
          />
        </SettingItem>
      </div>

      {/* Display Preferences */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <TrendingUp className="w-4 h-4" />
          Display Preferences
        </h4>

        <SettingItem
          label={t('settings.categories.gamification.experienceDisplayMode')}
          description={t('settings.categories.gamification.experienceDisplayModeDesc')}
        >
          <SelectField
            value={settings.gamification.experienceDisplayMode}
            onChange={(value) => updateSetting('gamification.experienceDisplayMode', value)}
            options={experienceDisplayOptions}
            className="w-48"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.gamification.badgeDisplayMode')}
          description={t('settings.categories.gamification.badgeDisplayModeDesc')}
        >
          <SelectField
            value={settings.gamification.badgeDisplayMode}
            onChange={(value) => updateSetting('gamification.badgeDisplayMode', value)}
            options={badgeDisplayOptions}
            className="w-48"
          />
        </SettingItem>
      </div>

      {/* Memory Art */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Palette className="w-4 h-4" />
          Memory Art
        </h4>

        <SettingItem
          label={t('settings.categories.gamification.memoryArtStyle')}
          description={t('settings.categories.gamification.memoryArtStyleDesc')}
        >
          <SelectField
            value={settings.gamification.memoryArtStyle}
            onChange={(value) => updateSetting('gamification.memoryArtStyle', value)}
            options={memoryArtStyleOptions}
            className="w-48"
          />
        </SettingItem>
      </div>

      {/* Competitive Features */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Medal className="w-4 h-4" />
          Competitive Features
        </h4>

        <SettingItem
          label={t('settings.categories.gamification.competitiveMode')}
          description={t('settings.categories.gamification.competitiveModeDesc')}
        >
          <ToggleSwitch
            checked={settings.gamification.competitiveMode}
            onChange={(checked) => updateSetting('gamification.competitiveMode', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.gamification.leaderboardVisibility')}
          description={t('settings.categories.gamification.leaderboardVisibilityDesc')}
        >
          <SelectField
            value={settings.gamification.leaderboardVisibility}
            onChange={(value) => updateSetting('gamification.leaderboardVisibility', value)}
            options={leaderboardVisibilityOptions}
            className="w-48"
          />
        </SettingItem>
      </div>

      {/* Reward Preferences */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Star className="w-4 h-4" />
          Reward Preferences
        </h4>

        <div className="p-4 bg-card/50 border border-border rounded-lg space-y-3">
          <SettingItem
            label="Auto-Open Rewards"
            description="Automatically open reward chests when received"
            className="!py-2"
          >
            <ToggleSwitch
              checked={settings.gamification.rewardPreferences.autoOpen}
              onChange={(checked) => updateSetting('gamification.rewardPreferences.autoOpen', checked)}
            />
          </SettingItem>

          <SettingItem
            label="Show Rarity"
            description="Display rarity indicators on rewards"
            className="!py-2"
          >
            <ToggleSwitch
              checked={settings.gamification.rewardPreferences.showRarity}
              onChange={(checked) => updateSetting('gamification.rewardPreferences.showRarity', checked)}
            />
          </SettingItem>

          <SettingItem
            label="Celebration Style"
            description="Style of reward celebration animations"
            className="!py-2"
          >
            <SelectField
              value={settings.gamification.rewardPreferences.celebrationStyle}
              onChange={(value) => updateSetting('gamification.rewardPreferences.celebrationStyle', value)}
              options={[
                { value: 'minimal', label: 'Minimal' },
                { value: 'standard', label: 'Standard' },
                { value: 'festive', label: 'Festive' }
              ]}
              className="w-32"
            />
          </SettingItem>
        </div>
      </div>

      {/* Gamification Summary */}
      <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <h5 className="font-medium text-blue-800 dark:text-blue-200 flex items-center gap-2 mb-3">
          <Trophy className="w-4 h-4" />
          Your Gamification Profile
        </h5>
        
        <div className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
          <p>• Animations: <span className="font-medium">{settings.gamification.achievementAnimations ? 'Enabled' : 'Disabled'}</span></p>
          <p>• Task Reminders: <span className="font-medium">{taskReminderIntensityOptions.find(opt => opt.value === settings.gamification.taskReminderIntensity)?.label}</span></p>
          <p>• Experience Display: <span className="font-medium">{experienceDisplayOptions.find(opt => opt.value === settings.gamification.experienceDisplayMode)?.label}</span></p>
          <p>• Competitive Mode: <span className="font-medium">{settings.gamification.competitiveMode ? 'Enabled' : 'Disabled'}</span></p>
        </div>
      </div>

      {/* Premium Features Preview */}
      {!isPremiumUser && (
        <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
          <h5 className="font-medium text-yellow-800 dark:text-yellow-200 flex items-center gap-2 mb-3">
            <Crown className="w-4 h-4" />
            Premium Gamification Features
          </h5>

          <div className="space-y-2 text-sm text-yellow-700 dark:text-yellow-300">
            <p>• Custom memory art styles</p>
            <p>• Advanced progress tracking</p>
            <p>• Exclusive achievement categories</p>
            <p>• Priority leaderboard placement</p>
            <p>• Custom celebration effects</p>
          </div>

          <button
            onClick={() => {
              // TODO: Navigate to upgrade page
              alert('Upgrade to Diamond Pass for premium gamification features!');
            }}
            className="mt-3 px-4 py-2 bg-gradient-to-r from-yellow-600 to-orange-600 text-white rounded-lg hover:from-yellow-700 hover:to-orange-700 transition-colors text-sm font-medium"
          >
            Upgrade to Diamond Pass
          </button>
        </div>
      )}
    </div>
  );
};

export default GamificationSettings; 