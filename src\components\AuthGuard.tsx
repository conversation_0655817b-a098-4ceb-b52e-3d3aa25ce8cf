'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthContext } from './AuthProvider';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean; // 是否需要认证
  redirectTo?: string; // 重定向路径
}

const AuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  requireAuth = true, 
  redirectTo 
}) => {
  const { isAuthenticated, isLoading } = useAuthContext();
  const router = useRouter();
  const pathname = usePathname();

  // 判断是否处于开发环境以跳过认证逻辑
  const isDevBypass = process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_SKIP_AUTH === 'true';

  useEffect(() => {
    if (isDevBypass) return; // 开发环境不做任何跳转

    if (!isLoading && requireAuth && !isAuthenticated) {
      // 需要认证但用户未登录，跳转到登录页
      const lang = pathname?.split('/')[1] || 'en';
      const authPath = `/${lang}/auth`;
      
      if (redirectTo) {
        router.push(redirectTo);
      } else {
        router.push(authPath);
      }
    }
  }, [isAuthenticated, isLoading, requireAuth, router, pathname, redirectTo]);

  // 加载中显示加载状态（开发环境直接忽略）
  if (!isDevBypass && isLoading) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // 需要认证但用户未登录，不渲染内容（将重定向）
  if (!isDevBypass && requireAuth && !isAuthenticated) {
    return null;
  }

  // 开发环境或已认证情况下直接渲染子组件
  // 渲染子组件
  return <>{children}</>;
};

export default AuthGuard; 