'use client';

import { useState, useCallback, useEffect } from 'react';
import type { StoryFormData, StoryStep, StoryValidationR<PERSON>ult, StoryChapter } from '@/types/story-creation';

// Create default chapters for story creation
const createDefaultChapters = (): StoryChapter[] => {
  return [
    {
      id: `chapter-${Date.now()}-1`,
      title: 'First Meeting',
      description: 'The initial encounter that sets the story in motion',
      content: '',
      order: 1,
      chapterType: 'main',
      backgroundSetting: '',
      completionEffects: {
        bondPointsChange: 0,
        greetingChange: '',
        characterMoodChange: '',
        customEffects: ''
      },
      choices: [],
      nextChapters: [],
      // Background Image Settings
      useWorldSettingImage: false,
      // Enhanced fields for objectives
      environment: '',
      timeElements: {
        season: '',
        timeOfDay: '',
        duration: '',
        specialDate: ''
      },
      spatialElements: {
        location: '',
        atmosphere: '',
        keyObjects: ''
      },
      environmentalElements: {
        weather: '',
        lighting: '',
        sounds: '',
        scents: '',
        temperature: ''
      },
      macroHistory: '',
      characterPast: '',
      immediateTrigger: '',
      // Enhanced fields for subjectives
      mentalModel: {
        coreValues: '',
        thinkingMode: '',
        decisionLogic: ''
      },
      emotionalBaseline: {
        displayedEmotion: '',
        hiddenEmotion: '',
        emotionalIntensity: 50,
        emotionalStability: 50
      },
      memorySystem: {
        triggeredMemories: '',
        emotionalMemories: '',
        knowledgePriority: ''
      },
      dialogueStrategy: {
        initiative: 50,
        listeningRatio: 50,
        questioningStyle: '',
        responseSpeed: ''
      },
      relationshipDynamics: {
        initialGoodwill: 50,
        trustLevel: 50,
        intimacyLevel: '',
        powerRelation: ''
      },
      goalOrientation: {
        sceneGoal: '',
        displayedIntent: '',
        hiddenIntent: '',
        successCriteria: ''
      }
    },
    {
      id: `chapter-${Date.now()}-2`,
      title: 'Growing Connection',
      description: 'The relationship begins to develop and deepen',
      content: '',
      order: 2,
      chapterType: 'main',
      backgroundSetting: '',
      completionEffects: {
        bondPointsChange: 0,
        greetingChange: '',
        characterMoodChange: '',
        customEffects: ''
      },
      choices: [],
      nextChapters: [],
      // Background Image Settings
      useWorldSettingImage: false,
      // Enhanced fields for objectives
      environment: '',
      timeElements: {
        season: '',
        timeOfDay: '',
        duration: '',
        specialDate: ''
      },
      spatialElements: {
        location: '',
        atmosphere: '',
        keyObjects: ''
      },
      environmentalElements: {
        weather: '',
        lighting: '',
        sounds: '',
        scents: '',
        temperature: ''
      },
      macroHistory: '',
      characterPast: '',
      immediateTrigger: '',
      // Enhanced fields for subjectives
      mentalModel: {
        coreValues: '',
        thinkingMode: '',
        decisionLogic: ''
      },
      emotionalBaseline: {
        displayedEmotion: '',
        hiddenEmotion: '',
        emotionalIntensity: 50,
        emotionalStability: 50
      },
      memorySystem: {
        triggeredMemories: '',
        emotionalMemories: '',
        knowledgePriority: ''
      },
      dialogueStrategy: {
        initiative: 50,
        listeningRatio: 50,
        questioningStyle: '',
        responseSpeed: ''
      },
      relationshipDynamics: {
        initialGoodwill: 50,
        trustLevel: 50,
        intimacyLevel: '',
        powerRelation: ''
      },
      goalOrientation: {
        sceneGoal: '',
        displayedIntent: '',
        hiddenIntent: '',
        successCriteria: ''
      }
    },
    {
      id: `chapter-${Date.now()}-3`,
      title: 'Deeper Understanding',
      description: 'A meaningful moment that brings characters closer together',
      content: '',
      order: 3,
      chapterType: 'main',
      backgroundSetting: '',
      completionEffects: {
        bondPointsChange: 0,
        greetingChange: '',
        characterMoodChange: '',
        customEffects: ''
      },
      choices: [],
      nextChapters: [],
      // Background Image Settings
      useWorldSettingImage: false,
      // Enhanced fields for objectives
      environment: '',
      timeElements: {
        season: '',
        timeOfDay: '',
        duration: '',
        specialDate: ''
      },
      spatialElements: {
        location: '',
        atmosphere: '',
        keyObjects: ''
      },
      environmentalElements: {
        weather: '',
        lighting: '',
        sounds: '',
        scents: '',
        temperature: ''
      },
      macroHistory: '',
      characterPast: '',
      immediateTrigger: '',
      // Enhanced fields for subjectives
      mentalModel: {
        coreValues: '',
        thinkingMode: '',
        decisionLogic: ''
      },
      emotionalBaseline: {
        displayedEmotion: '',
        hiddenEmotion: '',
        emotionalIntensity: 50,
        emotionalStability: 50
      },
      memorySystem: {
        triggeredMemories: '',
        emotionalMemories: '',
        knowledgePriority: ''
      },
      dialogueStrategy: {
        initiative: 50,
        listeningRatio: 50,
        questioningStyle: '',
        responseSpeed: ''
      },
      relationshipDynamics: {
        initialGoodwill: 50,
        trustLevel: 50,
        intimacyLevel: '',
        powerRelation: ''
      },
      goalOrientation: {
        sceneGoal: '',
        displayedIntent: '',
        hiddenIntent: '',
        successCriteria: ''
      }
    }
  ];
};

const defaultFormData: StoryFormData = {
  title: '',
  description: '',
  genre: '',
  tags: [],
  openingMessage: '',
  estimatedDuration: '',
  backgroundSetting: '',
  worldSetting: {
    // Basic Info
    storyName: '',

    // Quick Setup - 通用设置
    worldOverview: '',
    storyBackground: '',

    // Basic Settings - 基础设置
    historicalEra: '',
    geographicEnvironment: '',
    mainRaces: '',
    coreConflict: '',

    // Advanced Settings - 高级设置
    physicsRules: '',
    physicsRulesCustom: '',
    supernaturalElements: '',
    socialPoliticalSystem: '', // 合并后的字段
    economicFoundation: '',
    techLevel: '',
    techLevelCustom: '',
    timeBackground: '',

    // 保持向后兼容
    socialForm: '',
    politicalStructure: ''
  },
  chapters: createDefaultChapters(),
  branches: [],
  unlockConditions: '',
  rewards: [],
  rewardAllocation: {
    totalBudget: 500,
    usedBudget: 0,
    allocations: []
  },
  achievements: []
};

export const useStoryForm = (lang: string, storyId?: string, existingStory?: any) => {
  // Initialize form data with existing story data if in edit mode
  const [formData, setFormData] = useState<StoryFormData>(() => {
    if (existingStory && storyId) {
      // Convert existing story data to StoryFormData format
      return {
        ...defaultFormData,
        title: existingStory.title || '',
        description: existingStory.description || '',
        genre: existingStory.tags?.[0] || '',
        tags: existingStory.tags || [],
        openingMessage: existingStory.openingMessage || '',
        estimatedDuration: existingStory.estimatedDuration || 'medium',
        backgroundSetting: existingStory.backgroundSetting || '',
        coverImageUrl: existingStory.coverImage || '',
        // Expand other fields as needed based on your story data structure
      };
    }
    return defaultFormData;
  });
  const [isLoading, setIsLoading] = useState(false);
  const isEditMode = Boolean(storyId && existingStory);

  // Update form data when existing story changes (for async loading)
  useEffect(() => {
    if (existingStory && storyId && isEditMode) {
      setFormData(prev => ({
        ...prev,
        title: existingStory.title || '',
        description: existingStory.description || '',
        genre: existingStory.tags?.[0] || '',
        tags: existingStory.tags || [],
        openingMessage: existingStory.openingMessage || '',
        estimatedDuration: existingStory.estimatedDuration || 'medium',
        backgroundSetting: existingStory.backgroundSetting || '',
        coverImageUrl: existingStory.coverImage || '',
        // Expand other fields as needed based on your story data structure
      }));
    }
  }, [existingStory, storyId, isEditMode]);

  // Validation logic
  const validateStep = useCallback((step: StoryStep): StoryValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    switch (step) {
      case 'worldSetting':
        // Validate world setting fields
        if (!formData.worldSetting.physicsRules.trim()) {
          warnings.push('Physics rules are not defined');
        }
        if (!formData.worldSetting.timeBackground.trim()) {
          warnings.push('Time background is not defined');
        }
        if (!formData.worldSetting.geographicEnvironment.trim()) {
          warnings.push('Geographic environment is not defined');
        }
        break;

      case 'storyFlow':
        if (formData.chapters.length === 0) {
          errors.push('At least one chapter is required');
        }
        if (formData.chapters.length < 3) {
          warnings.push('At least 3 chapters are recommended for a complete story');
        }
        if (formData.chapters.length > 15) {
          errors.push('Maximum 15 chapters allowed');
        }
        formData.chapters.forEach((chapter, index) => {
          if (!chapter.title.trim()) {
            errors.push(`Chapter ${index + 1} title is required`);
          }
        });
        break;

      case 'objectivesSubjectives':
        // Validate that chapters have objective settings
        formData.chapters.forEach((chapter, index) => {
          if (!chapter.environment?.trim()) {
            warnings.push(`Chapter ${index + 1} environment description is recommended`);
          }
          // Validate that chapters have subjective settings
          if (!chapter.mentalModel?.coreValues?.trim()) {
            warnings.push(`Chapter ${index + 1} core values are recommended`);
          }
        });
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }, [formData]);

  const isStepComplete = useCallback((step: StoryStep): boolean => {
    const validation = validateStep(step);
    return validation.isValid;
  }, [validateStep]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    setIsLoading(true);
    try {
      // Validate all steps
      const steps: StoryStep[] = ['worldSetting', 'storyFlow', 'objectivesSubjectives'];
      const allValidations = steps.map(step => validateStep(step));
      const hasErrors = allValidations.some(validation => !validation.isValid);

      if (hasErrors) {
        const allErrors = allValidations.flatMap(validation => validation.errors);
        throw new Error(`Validation failed: ${allErrors.join(', ')}`);
      }

      if (isEditMode) {
        // TODO: Replace with actual API call for updating story
        console.log('Updating story:', storyId, formData);
        
        // Simulate API call for update
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        return { success: true, storyId: storyId };
      } else {
        // TODO: Replace with actual API call for creating story
        console.log('Creating story:', formData);
        
        // Simulate API call for creation
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        return { success: true, storyId: `story-${Date.now()}` };
      }
    } catch (error) {
      console.error(`Failed to ${isEditMode ? 'update' : 'create'} story:`, error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [formData, validateStep, isEditMode, storyId]);

  // Handle flash story generation
  const generateFlashStory = useCallback(async (concept: string) => {
    setIsLoading(true);
    try {
      // TODO: Replace with actual AI generation API call
      console.log('Generating flash story from concept:', concept);
      
      // Simulate AI generation
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Generate story data based on concept
      const generatedStory: Partial<StoryFormData> = {
        title: `AI Generated: ${concept.slice(0, 50)}...`,
        description: `An engaging story based on: ${concept}`,
        genre: 'adventure',
        tags: ['ai-generated', 'adventure'],
        openingMessage: `Welcome to a story about ${concept}. Are you ready to begin this adventure?`,
        estimatedDuration: 'medium',
        backgroundSetting: `A world where ${concept} unfolds`,
        unlockConditions: 'Complete character introduction',
        chapters: [
          {
            id: 'chapter-1',
            title: 'The Beginning',
            description: 'Where our story starts',
            content: `The story begins with ${concept}. What will you do?`,
            order: 1,
            chapterType: 'main',
            backgroundSetting: `A mysterious world where ${concept} takes place`,
            completionEffects: {
              bondPointsChange: 10,
              greetingChange: 'Welcome back! How did your adventure go?',
              characterMoodChange: 'Excited and curious',
              customEffects: 'Character becomes more trusting'
            },
            choices: [
              {
                id: 'choice-1',
                text: 'Explore further',
                description: 'Dive deeper into the story',
                consequences: 'Bond level ≥ 2',
                nextChapter: 'chapter-2a',
                effectPreview: 'Leads to adventure path'
              },
              {
                id: 'choice-2',
                text: 'Proceed carefully',
                description: 'Take a cautious approach',
                consequences: 'Always available',
                nextChapter: 'chapter-2b',
                effectPreview: 'Leads to careful path'
              }
            ],
            nextChapters: ['chapter-2a', 'chapter-2b']
          }
        ],
        branches: [],
        rewards: [
          {
            id: 'reward-1',
            name: 'Story Completion',
            type: 'currency',
            amount: 50,
            icon: '💎',
            description: 'Reward for completing the story'
          }
        ],
        rewardAllocation: {
          totalBudget: 500,
          usedBudget: 0,
          allocations: []
        }
      };

      setFormData(prev => ({ ...prev, ...generatedStory }));
      return { success: true, generatedStory };
    } catch (error) {
      console.error('Failed to generate flash story:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Reset form
  const resetForm = useCallback(() => {
    setFormData(defaultFormData);
  }, []);

  // Update specific field
  const updateField = useCallback((field: keyof StoryFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  return {
    formData,
    setFormData,
    isLoading,
    validateStep,
    isStepComplete,
    handleSubmit,
    generateFlashStory,
    resetForm,
    updateField
  };
}; 