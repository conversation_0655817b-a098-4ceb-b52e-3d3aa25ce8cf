import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { body, query } from 'express-validator';
import { AuthMiddleware } from '../middleware/auth';

const router = Router();
const userController = new UserController();
const authMiddleware = new AuthMiddleware();

// Validation rules
const updateUserValidation = [
  body('display_name').optional().isLength({ min: 1, max: 100 }),
  body('bio').optional().isLength({ max: 500 }),
  body('location').optional().isLength({ max: 100 }),
  body('website').optional().isURL(),
  body('birth_date').optional().isISO8601(),
  body('gender').optional().isIn(['male', 'female', 'other', 'prefer_not_to_say']),
  body('interests').optional().isArray(),
  body('language_preference').optional().isIn(['en', 'zh-CN', 'ja-JP']),
  body('timezone').optional().isLength({ max: 50 })
];

const followUserValidation = [
  body('userId').isUUID().withMessage('Valid user ID required')
];

/**
 * @swagger
 * /api/v1/users/profile:
 *   get:
 *     summary: Get current user's profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.get('/users/profile', 
  authMiddleware.authenticate,
  userController.getCurrentUserProfile
);

/**
 * @swagger
 * /api/v1/users/profile:
 *   put:
 *     summary: Update current user's profile
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               display_name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *               bio:
 *                 type: string
 *                 maxLength: 500
 *               location:
 *                 type: string
 *                 maxLength: 100
 *               website:
 *                 type: string
 *                 format: uri
 *               birth_date:
 *                 type: string
 *                 format: date
 *               gender:
 *                 type: string
 *                 enum: [male, female, other, prefer_not_to_say]
 *               interests:
 *                 type: array
 *                 items:
 *                   type: string
 *               language_preference:
 *                 type: string
 *                 enum: [en, zh-CN, ja-JP]
 *               timezone:
 *                 type: string
 *                 maxLength: 50
 *               privacy_settings:
 *                 type: object
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Authentication required
 *       500:
 *         description: Internal server error
 */
router.put('/users/profile', 
  authMiddleware.authenticate,
  updateUserValidation,
  userController.updateUserProfile
);

/**
 * @swagger
 * /api/v1/users/{uid}:
 *   get:
 *     summary: Get user by UID
 *     tags: [Users]
 *     parameters:
 *       - in: path
 *         name: uid
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: User ID
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.get('/users/:uid', 
  authMiddleware.optionalAuth,
  userController.getUserByUid
);

/**
 * @swagger
 * /api/v1/users/{uid}/following:
 *   get:
 *     summary: Get user's following list
 *     tags: [Users]
 *     parameters:
 *       - in: path
 *         name: uid
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: User ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Following list retrieved successfully
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.get('/users/:uid/following', 
  authMiddleware.optionalAuth,
  [query('page').optional().isInt({ min: 1 }),
   query('limit').optional().isInt({ min: 1, max: 100 })],
  userController.getUserFollowing
);

/**
 * @swagger
 * /api/v1/users/{uid}/followers:
 *   get:
 *     summary: Get user's followers list
 *     tags: [Users]
 *     parameters:
 *       - in: path
 *         name: uid
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: User ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Followers list retrieved successfully
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.get('/users/:uid/followers', 
  authMiddleware.optionalAuth,
  [query('page').optional().isInt({ min: 1 }),
   query('limit').optional().isInt({ min: 1, max: 100 })],
  userController.getUserFollowers
);

/**
 * @swagger
 * /api/v1/users/follow:
 *   post:
 *     summary: Follow a user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of user to follow
 *     responses:
 *       200:
 *         description: User followed successfully
 *       400:
 *         description: Validation error or already following
 *       401:
 *         description: Authentication required
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.post('/users/follow', 
  authMiddleware.authenticate,
  followUserValidation,
  userController.followUser
);

/**
 * @swagger
 * /api/v1/users/unfollow:
 *   post:
 *     summary: Unfollow a user
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *             properties:
 *               userId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of user to unfollow
 *     responses:
 *       200:
 *         description: User unfollowed successfully
 *       400:
 *         description: Validation error or not following
 *       401:
 *         description: Authentication required
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.post('/users/unfollow', 
  authMiddleware.authenticate,
  followUserValidation,
  userController.unfollowUser
);

/**
 * @swagger
 * /api/v1/users/currencies:
 *   get:
 *     summary: Get user's currencies
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User currencies retrieved successfully
 *       401:
 *         description: Authentication required
 *       404:
 *         description: User currencies not found
 *       500:
 *         description: Internal server error
 */
router.get('/users/currencies', 
  authMiddleware.authenticate,
  userController.getUserCurrencies
);

/**
 * @swagger
 * /api/v1/users/currencies/daily-bonus:
 *   post:
 *     summary: Claim daily bonus
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Daily bonus claimed successfully
 *       400:
 *         description: Daily bonus not available
 *       401:
 *         description: Authentication required
 *       500:
 *         description: Internal server error
 */
router.post('/users/currencies/daily-bonus', 
  authMiddleware.authenticate,
  userController.claimDailyBonus
);

export default router;