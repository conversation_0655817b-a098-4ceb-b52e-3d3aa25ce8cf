'use client';

import React from 'react';
import Link from 'next/link';
import { Camera, Users, Heart } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface SidebarNavigationProps {
  lang: string;
}

const SidebarNavigation: React.FC<SidebarNavigationProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  
  const discoverLinks = [
    {
      href: `/${lang}`,
      label: t('sidebar.shiningMoments'),
      icon: Camera,
      description: 'Discover moments'
    },
    {
      href: `/${lang}/discover`,
      label: t('sidebar.discoverCharacters'),
      icon: Users,
      description: 'Find characters'
    },
    {
      href: `/${lang}/square`,
      label: t('sidebar.memoriesSquare'),
      icon: Heart,
      description: 'Your memories'
    },
  ];

  return (
    <div className="px-4 py-2">
      <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
        {t('sidebar.discoverMore')}
      </h3>
      <div className="space-y-2">
        {discoverLinks.map(link => (
          <Link
            key={link.href}
            href={link.href}
            className="flex items-center gap-3 py-0 px-0 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors group"
          >
            <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform">
              <link.icon className="text-white" size={20} />
            </div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
              {link.label}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default SidebarNavigation;
