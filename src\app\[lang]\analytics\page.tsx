import { Metadata } from 'next';
import AnalyticsClientPage from './AnalyticsClientPage';

export const metadata: Metadata = {
  title: 'Analytics Dashboard - Alphane',
  description: 'Comprehensive analytics and insights for user engagement, purchases, and progression',
};

interface AnalyticsPageProps {
  params: {
    lang: string;
  };
}

export default function AnalyticsPage({ params: { lang } }: AnalyticsPageProps) {
  return <AnalyticsClientPage lang={lang} />;
}
