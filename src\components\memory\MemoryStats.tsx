import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { <PERSON>, Users, Heart, TrendingUp, Sparkles } from 'lucide-react';
import type { Memory } from '@/app/[lang]/manage-memories/ManageMemoryClientPage';

interface MemoryStatsProps {
  memories: Memory[];
  lang: string;
}

const MemoryStats: React.FC<MemoryStatsProps> = ({ memories, lang }) => {
  const { t: _ } = useTranslation(lang, 'translation');

  // Calculate statistics
  const totalMemories = memories.length;
  const totalCharacters = new Set(memories.map(m => m.source_character_id)).size;
  const avgImportance = memories.length > 0 
    ? memories.reduce((sum, m) => sum + m.importance_score, 0) / memories.length 
    : 0;
  const totalReferences = memories.reduce((sum, m) => sum + (m.reference_count || 0), 0);

  const stats = [
    {
      label: _('memory.stats.total', 'Total Memories'),
      value: totalMemories,
      unit: '',
      icon: Brain,
      gradient: 'from-blue-500 to-cyan-500',
      bgGradient: 'from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20',
      shadowColor: 'shadow-blue-500/25',
      iconBg: 'from-blue-500/20 to-cyan-500/20',
      textColor: 'text-blue-700 dark:text-blue-300',
      valueColor: 'text-blue-900 dark:text-blue-100',
    },
    {
      label: _('memory.stats.characters', 'Characters Involved'),
      value: totalCharacters,
      unit: '',
      icon: Users,
      gradient: 'from-emerald-500 to-green-500',
      bgGradient: 'from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20',
      shadowColor: 'shadow-emerald-500/25',
      iconBg: 'from-emerald-500/20 to-green-500/20',
      textColor: 'text-emerald-700 dark:text-emerald-300',
      valueColor: 'text-emerald-900 dark:text-emerald-100',
    },
    {
      label: _('memory.stats.importance', 'Avg Importance'),
      value: avgImportance.toFixed(1),
      unit: '⭐',
      icon: Heart,
      gradient: 'from-pink-500 to-rose-500',
      bgGradient: 'from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20',
      shadowColor: 'shadow-pink-500/25',
      iconBg: 'from-pink-500/20 to-rose-500/20',
      textColor: 'text-pink-700 dark:text-pink-300',
      valueColor: 'text-pink-900 dark:text-pink-100',
    },
    {
      label: _('memory.stats.references', 'AI References'),
      value: totalReferences,
      unit: '',
      icon: TrendingUp,
      gradient: 'from-purple-500 to-violet-500',
      bgGradient: 'from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20',
      shadowColor: 'shadow-purple-500/25',
      iconBg: 'from-purple-500/20 to-violet-500/20',
      textColor: 'text-purple-700 dark:text-purple-300',
      valueColor: 'text-purple-900 dark:text-purple-100',
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <div
            key={index}
            className="group relative"
            style={{
              animationDelay: `${index * 150}ms`,
            }}
          >
            {/* Card Container with Glass Morphism */}
            <div className={`
              relative backdrop-blur-xl rounded-2xl overflow-hidden
              bg-gradient-to-br ${stat.bgGradient}
              border border-white/50 dark:border-gray-700/50
              shadow-2xl ${stat.shadowColor}
              hover:shadow-3xl hover:${stat.shadowColor}
              transform hover:scale-105 hover:-translate-y-2
              transition-all duration-500
              animate-slideInUp
            `}>
              {/* Gradient Border Effect */}
              <div className={`absolute inset-0 bg-gradient-to-r ${stat.gradient} opacity-20 group-hover:opacity-30 transition-opacity duration-300`}></div>
              
              {/* Floating Icon */}
              <div className="absolute -top-6 -right-6">
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${stat.iconBg} backdrop-blur-xl border border-white/30 flex items-center justify-center shadow-xl transform group-hover:rotate-12 group-hover:scale-110 transition-all duration-500`}>
                  <Icon className={`w-8 h-8 ${stat.textColor}`} />
                </div>
              </div>

              {/* Main Content */}
              <div className="relative z-10 p-6 pt-8">
                {/* Label */}
                <p className={`text-sm font-semibold mb-3 ${stat.textColor}`}>
                  {stat.label}
                </p>

                {/* Value with Animation */}
                <div className="flex items-baseline gap-2 mb-4">
                  <span className={`text-4xl font-bold ${stat.valueColor} group-hover:scale-110 transition-transform duration-300`}>
                    {stat.value}
                  </span>
                  {stat.unit && (
                    <span className={`text-lg ${stat.textColor}`}>
                      {stat.unit}
                    </span>
                  )}
                </div>

                                 {/* Progress Bar */}
                 <div className="w-full bg-white/30 dark:bg-gray-700/30 rounded-full h-2 overflow-hidden">
                   <div 
                     className={`h-full bg-gradient-to-r ${stat.gradient} rounded-full transform transition-all duration-1000 delay-300`}
                     style={{
                       width: `${Math.min(100, (Number(stat.value) / Math.max(...stats.map(s => Number(s.value)))) * 100)}%`
                     }}
                   ></div>
                 </div>

                {/* Sparkle Effects */}
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <Sparkles className="w-4 h-4 text-yellow-400 animate-pulse" />
                </div>
              </div>

              {/* Hover Glow Effect */}
              <div className={`absolute inset-0 bg-gradient-to-r ${stat.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500 pointer-events-none`}></div>
            </div>

            {/* Floating Particles */}
            <div className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-700">
              <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-white rounded-full animate-ping"></div>
              <div className="absolute top-3/4 right-1/4 w-1.5 h-1.5 bg-white rounded-full animate-ping animation-delay-300"></div>
              <div className="absolute bottom-1/4 left-3/4 w-1 h-1 bg-white rounded-full animate-ping animation-delay-600"></div>
            </div>
          </div>
        );
      })}

      <style jsx>{`
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(50px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-slideInUp {
          animation: slideInUp 0.8s ease-out forwards;
        }
        .animation-delay-300 {
          animation-delay: 300ms;
        }
        .animation-delay-600 {
          animation-delay: 600ms;
        }
      `}</style>
    </div>
  );
};

export default MemoryStats; 