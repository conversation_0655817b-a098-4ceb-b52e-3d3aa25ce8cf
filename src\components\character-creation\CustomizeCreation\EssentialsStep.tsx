'use client';

import React, { useState } from 'react';
import { User, Palette, MessageCircle, Plus } from 'lucide-react';
import type { CharacterFormData } from '@/types/character-creation';
import ImageUploadSection from '../ImageUpload/ImageUploadSection';
import { useImageUpload } from '@/hooks/character-creation/useImageUpload';

interface EssentialsStepProps {
  formData: CharacterFormData;
  setFormData: (data: CharacterFormData | ((prev: CharacterFormData) => CharacterFormData)) => void;
}

const EssentialsStep: React.FC<EssentialsStepProps> = ({
  formData,
  setFormData,
}) => {
  const imageUpload = useImageUpload(formData, setFormData);
  const [personalityInput, setPersonalityInput] = useState('');

  const handleAddPersonalityTrait = () => {
    const trait = personalityInput.trim();
    if (trait && !formData.personality.includes(trait)) {
      setFormData(prev => ({
        ...prev,
        personality: [...prev.personality, trait]
      }));
      setPersonalityInput('');
    }
  };

  const handlePersonalityKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddPersonalityTrait();
    }
  };

  return (
    <div className="space-y-1.5">
      <div className="text-center">
        <h3 className="text-lg font-bold text-purple-800 dark:text-purple-200 mb-1.5">Step 1: Essentials</h3>
        <p className="text-muted-foreground text-sm">Define your character's core identity</p>
      </div>

      {/* Name Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-1.5 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-1.5 flex items-center gap-1.5">
          <User size={18} />
          Character Name
        </h4>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
          placeholder="Enter character name..."
          maxLength={50}
        />
        <p className="text-xs text-purple-600 dark:text-purple-400 mt-1.5">
          Choose a memorable name for your character
        </p>
      </div>

      {/* Image Upload Section */}
      <ImageUploadSection {...imageUpload} />

      {/* Appearance Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-1.5 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-1.5 flex items-center gap-1.5">
          <Palette size={18} />
          Appearance Description
        </h4>
        <div className="relative">
          <textarea
            value={formData.appearance}
            onChange={(e) => setFormData(prev => ({ ...prev, appearance: e.target.value }))}
            rows={4}
            className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
            placeholder="Describe your character's physical appearance..."
            maxLength={500}
            style={{ userSelect: 'text' }}
          />
          {imageUpload.isAiGenerated && (
            <div className="absolute top-2 right-2">
              <span className="inline-flex items-center px-2 py-1 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 rounded text-xs">
                ✨ AI Generated
              </span>
            </div>
          )}
        </div>
        <div className="flex justify-end items-center mt-1.5">
          <span className={`character-counter ${formData.appearance.length > 100 ? '' : 'over-limit'}`}>
            {formData.appearance.length}/500
          </span>
        </div>
      </div>

      {/* Character Description Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-1.5 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-1.5 flex items-center gap-1.5">
          <MessageCircle size={18} />
          Character Description
        </h4>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          rows={4}
          className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
          placeholder="Describe your character's personality, background, and story..."
          maxLength={1000}
        />
        <div className="flex justify-end items-center mt-2">
          <span className={`character-counter ${formData.description.length > 200 ? '' : 'over-limit'}`}>
            {formData.description.length}/1000
          </span>
        </div>
      </div>

      {/* Public Description Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-1.5 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-1.5 flex items-center gap-1.5">
          <MessageCircle size={18} />
          Public Description
        </h4>
        <textarea
          value={formData.publicDescription}
          onChange={(e) => setFormData(prev => ({ ...prev, publicDescription: e.target.value }))}
          rows={3}
          className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
          placeholder="Write a public description that will be shown to other users..."
          maxLength={800}
        />
        <div className="flex justify-end items-center mt-2">
          <span className={`character-counter ${formData.publicDescription.length > 100 ? '' : 'over-limit'}`}>
            {formData.publicDescription.length}/800
          </span>
        </div>
      </div>

      {/* Gender Selection */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-3">Gender</h4>
        <div className="grid grid-cols-3 gap-2">
          {['Male', 'Female', 'Other'].map((gender) => (
            <button
              key={gender}
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, gender }))}
              className={`px-3 py-2 rounded-lg border-2 transition-all text-sm font-medium ${
                formData.gender === gender
                  ? 'border-purple-500 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200'
                  : 'border-purple-200 dark:border-purple-700 hover:border-purple-400 dark:hover:border-purple-500 text-purple-600 dark:text-purple-400'
              }`}
            >
              {gender}
            </button>
          ))}
        </div>
      </div>

      {/* Personality Tags */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-3">Personality Traits</h4>
        <div className="space-y-2">
          <div className="flex gap-2">
            <input
              type="text"
              value={personalityInput}
              onChange={(e) => setPersonalityInput(e.target.value)}
              onKeyDown={handlePersonalityKeyDown}
              placeholder="Add personality trait (e.g., kind, brave, mysterious)"
              className="flex-1 px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
            />
            <button
              type="button"
              onClick={handleAddPersonalityTrait}
              disabled={!personalityInput.trim() || formData.personality.includes(personalityInput.trim())}
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all text-sm font-medium flex items-center gap-1"
            >
              <Plus size={14} />
              Add Trait
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {formData.personality.map((trait, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-full text-sm"
              >
                {trait}
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({
                    ...prev,
                    personality: prev.personality.filter((_, i) => i !== index)
                  }))}
                  className="ml-1 text-purple-500 hover:text-purple-700 dark:hover:text-purple-200"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EssentialsStep;
