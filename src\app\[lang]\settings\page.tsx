import { Suspense } from 'react';
import SettingsClientPage from './SettingsClientPage';

interface SettingsPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function SettingsPage({ params }: SettingsPageProps) {
  const { lang } = await params;

  return (
    <Suspense fallback={<div>Loading settings...</div>}>
      <SettingsClientPage lang={lang} />
    </Suspense>
  );
} 