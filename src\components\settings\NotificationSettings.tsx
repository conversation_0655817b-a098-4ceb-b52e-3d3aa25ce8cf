'use client';

import React, { useState } from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Bell, Clock, Mail, Smartphone, Trophy, Heart, Star, MessageCircle, Settings, Moon } from 'lucide-react';
import SettingItem from './SettingItem';
import ToggleSwitch from './ToggleSwitch';
import type { SettingsComponentProps } from '@/types/settings';

const NotificationSettings: React.FC<SettingsComponentProps> = ({
  settings,
  updateSetting,
  lang,
  user,
  hasUnsavedChanges,
  isPremiumUser
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [showTimeModal, setShowTimeModal] = useState(false);
  const [timeType, setTimeType] = useState<'start' | 'end'>('start');

  const handleTimeChange = (type: 'start' | 'end', time: string) => {
    updateSetting(`notifications.doNotDisturb${type === 'start' ? 'Start' : 'End'}`, time);
  };

  const formatTime = (time?: string) => {
    if (!time) return t('settings.categories.notifications.notSet');
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const TimeModal = ({ type, currentTime, onSave, onClose }: {
    type: 'start' | 'end';
    currentTime?: string;
    onSave: (time: string) => void;
    onClose: () => void;
  }) => {
    const [selectedTime, setSelectedTime] = useState(currentTime || '22:00');

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
        <div className="bg-card rounded-lg p-6 max-w-sm w-full space-y-4">
          <h3 className="text-lg font-semibold text-foreground">
            {t(`settings.categories.notifications.doNotDisturb${type === 'start' ? 'Start' : 'End'}`)}
          </h3>
          
          <div className="space-y-4">
            <input
              type="time"
              value={selectedTime}
              onChange={(e) => setSelectedTime(e.target.value)}
              className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
            />
          </div>

          <div className="flex gap-2 justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-foreground/70 hover:text-foreground transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                onSave(selectedTime);
                onClose();
              }}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              {t('settings.categories.notifications.setTime')}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Notification Types */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Bell className="w-4 h-4" />
          Notification Types
        </h4>

        <SettingItem
          label={t('settings.categories.notifications.pushNotifications')}
          description={t('settings.categories.notifications.pushNotificationsDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.pushNotifications}
            onChange={(checked) => updateSetting('notifications.pushNotifications', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.emailNotifications')}
          description={t('settings.categories.notifications.emailNotificationsDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.emailNotifications}
            onChange={(checked) => updateSetting('notifications.emailNotifications', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.inAppNotifications')}
          description={t('settings.categories.notifications.inAppNotificationsDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.inAppNotifications}
            onChange={(checked) => updateSetting('notifications.inAppNotifications', checked)}
          />
        </SettingItem>
      </div>

      {/* Game & Activity */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Trophy className="w-4 h-4" />
          Game & Activity
        </h4>

        <SettingItem
          label={t('settings.categories.notifications.streakReminders')}
          description={t('settings.categories.notifications.streakRemindersDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.streakReminders}
            onChange={(checked) => updateSetting('notifications.streakReminders', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.battlePassProgress')}
          description={t('settings.categories.notifications.battlePassProgressDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.battlePassProgress}
            onChange={(checked) => updateSetting('notifications.battlePassProgress', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.achievementNotifications')}
          description={t('settings.categories.notifications.achievementNotificationsDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.achievementNotifications}
            onChange={(checked) => updateSetting('notifications.achievementNotifications', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.taskReminders')}
          description={t('settings.categories.notifications.taskRemindersDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.taskReminders}
            onChange={(checked) => updateSetting('notifications.taskReminders', checked)}
          />
        </SettingItem>
      </div>

      {/* Character & Relationships */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Heart className="w-4 h-4" />
          Character & Relationships
        </h4>

        <SettingItem
          label={t('settings.categories.notifications.bondingLevelUps')}
          description={t('settings.categories.notifications.bondingLevelUpsDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.bondingLevelUps}
            onChange={(checked) => updateSetting('notifications.bondingLevelUps', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.memoryMilestones')}
          description={t('settings.categories.notifications.memoryMilestonesDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.memoryMilestones}
            onChange={(checked) => updateSetting('notifications.memoryMilestones', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.newCharacterReleases')}
          description={t('settings.categories.notifications.newCharacterReleasesDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.newCharacterReleases}
            onChange={(checked) => updateSetting('notifications.newCharacterReleases', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.followedCharacterUpdates')}
          description={t('settings.categories.notifications.followedCharacterUpdatesDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.followedCharacterUpdates}
            onChange={(checked) => updateSetting('notifications.followedCharacterUpdates', checked)}
          />
        </SettingItem>
      </div>

      {/* Social & Updates */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <MessageCircle className="w-4 h-4" />
          Social & Updates
        </h4>

        <SettingItem
          label={t('settings.categories.notifications.friendActivityNotifications')}
          description={t('settings.categories.notifications.friendActivityNotificationsDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.friendActivityNotifications}
            onChange={(checked) => updateSetting('notifications.friendActivityNotifications', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.weeklyDigest')}
          description={t('settings.categories.notifications.weeklyDigestDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.weeklyDigest}
            onChange={(checked) => updateSetting('notifications.weeklyDigest', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.maintenanceNotifications')}
          description={t('settings.categories.notifications.maintenanceNotificationsDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.maintenanceNotifications}
            onChange={(checked) => updateSetting('notifications.maintenanceNotifications', checked)}
          />
        </SettingItem>
      </div>

      {/* Marketing & Premium */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Star className="w-4 h-4" />
          Marketing & Premium
        </h4>

        <SettingItem
          label={t('settings.categories.notifications.promotionalOffers')}
          description={t('settings.categories.notifications.promotionalOffersDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.promotionalOffers}
            onChange={(checked) => updateSetting('notifications.promotionalOffers', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.notifications.premiumExpiryReminders')}
          description={t('settings.categories.notifications.premiumExpiryRemindersDesc')}
        >
          <ToggleSwitch
            checked={settings.notifications.premiumExpiryReminders}
            onChange={(checked) => updateSetting('notifications.premiumExpiryReminders', checked)}
          />
        </SettingItem>
      </div>

      {/* Do Not Disturb */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Moon className="w-4 h-4" />
          {t('settings.categories.notifications.doNotDisturb')}
        </h4>

        <div className="p-4 bg-card/50 border border-border rounded-lg">
          <p className="text-sm text-foreground/70 mb-4">
            {t('settings.categories.notifications.doNotDisturbDesc')}
          </p>

          <div className="space-y-3">
            <SettingItem
              label={t('settings.categories.notifications.doNotDisturbStart')}
              onClick={() => {
                setTimeType('start');
                setShowTimeModal(true);
              }}
              showArrow
              className="!py-2"
            >
              <span className="text-sm font-medium">
                {formatTime(settings.notifications.doNotDisturbStart)}
              </span>
            </SettingItem>

            <SettingItem
              label={t('settings.categories.notifications.doNotDisturbEnd')}
              onClick={() => {
                setTimeType('end');
                setShowTimeModal(true);
              }}
              showArrow
              className="!py-2"
            >
              <span className="text-sm font-medium">
                {formatTime(settings.notifications.doNotDisturbEnd)}
              </span>
            </SettingItem>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-6 p-4 bg-card/50 border border-border rounded-lg">
        <h5 className="font-medium text-foreground flex items-center gap-2 mb-3">
          <Settings className="w-4 h-4" />
          Quick Actions
        </h5>
        
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => {
              // Enable all important notifications
              const importantNotifications = [
                'streakReminders',
                'battlePassProgress',
                'achievementNotifications',
                'bondingLevelUps',
                'memoryMilestones'
              ];
              
              importantNotifications.forEach(key => {
                updateSetting(`notifications.${key}`, true);
              });
            }}
            className="px-3 py-1.5 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm"
          >
            Enable Important
          </button>
          
          <button
            onClick={() => {
              // Disable all promotional notifications
              const promotionalNotifications = [
                'promotionalOffers',
                'newCharacterReleases',
                'weeklyDigest'
              ];
              
              promotionalNotifications.forEach(key => {
                updateSetting(`notifications.${key}`, false);
              });
            }}
            className="px-3 py-1.5 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/90 transition-colors text-sm"
          >
            Disable Promotional
          </button>
        </div>
      </div>

      {/* Time Modal */}
      {showTimeModal && (
        <TimeModal
          type={timeType}
          currentTime={timeType === 'start' ? settings.notifications.doNotDisturbStart : settings.notifications.doNotDisturbEnd}
          onSave={(time) => handleTimeChange(timeType, time)}
          onClose={() => setShowTimeModal(false)}
        />
      )}
    </div>
  );
};

export default NotificationSettings; 