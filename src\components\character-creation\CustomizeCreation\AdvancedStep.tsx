'use client';

import React from 'react';
import { Play, Settings as SettingsIcon } from 'lucide-react';
import type { CharacterFormData } from '@/types/character-creation';
import { useTranslation } from '@/app/i18n/client';
import { useRouter } from 'next/navigation';

interface AdvancedStepProps {
  formData: CharacterFormData;
  setFormData: (data: CharacterFormData | ((prev: CharacterFormData) => CharacterFormData)) => void;
  characterId?: string;
  lang?: string;
}

const AdvancedStep: React.FC<AdvancedStepProps> = ({
  formData,
  setFormData,
  characterId,
  lang = 'en'
}) => {
  const { t } = useTranslation(lang, 'translation');
  const router = useRouter();

  // 处理创建故事的跳转
  const handleCreateStory = () => {
    if (characterId) {
      router.push(`/${lang}/create-story/${characterId}`);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="text-center">
        <h3 className="text-2xl font-bold text-purple-800 dark:text-purple-200 mb-2">{t('characterCreation.advanced.title')}</h3>
        <p className="text-gray-600 dark:text-gray-400">{t('characterCreation.advanced.subtitle')}</p>
      </div>

      {/* Section 1: Start Chatting */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-6 border border-green-200 dark:border-green-700 shadow-lg">
        <div className="text-center mb-4">
          <h4 className="text-xl font-bold text-green-800 dark:text-green-200 mb-2">{t('characterCreation.advanced.readyToChat')}</h4>
          <p className="text-green-700 dark:text-green-300 text-sm">{t('characterCreation.advanced.readyToChatDesc')}</p>
        </div>
        <button
          type="button"
          onClick={() => {/* Handle start chat */}}
          className="w-full flex items-center justify-center gap-3 p-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all shadow-lg text-lg font-semibold hover:shadow-xl transform hover:scale-[1.02]"
        >
          <Play size={24} />
          {t('characterCreation.advanced.readyToChatButton')}
        </button>
      </div>

      {/* Section 2: Advanced Story Building */}
      <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl p-6 border border-purple-200 dark:border-purple-700 shadow-lg">
        <div className="text-center mb-4">
          <h4 className="text-xl font-bold text-purple-800 dark:text-purple-200 mb-2">{t('characterCreation.advanced.advancedStoryBuilding')}</h4>
          <p className="text-purple-700 dark:text-purple-300 text-sm">{t('characterCreation.advanced.advancedStoryDesc')}</p>
        </div>

        <button
          type="button"
          onClick={handleCreateStory}
          disabled={!characterId}
          className="w-full flex items-center justify-center gap-3 p-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all shadow-lg text-lg font-semibold hover:shadow-xl transform hover:scale-[1.02]"
        >
          <SettingsIcon size={24} />
          {t('characterCreation.advanced.createStoryButton') || '创建故事'}
        </button>

        {!characterId && (
          <div className="mt-3 text-center text-amber-600 dark:text-amber-400 text-sm">
            {t('characterCreation.advanced.finishCreatingCharacter')}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedStep;
