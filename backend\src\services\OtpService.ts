import { v4 as uuidv4 } from 'uuid';

export class OtpService {
  private otpStore: Map<string, { otp: string; expiry: Date }>;

  constructor() {
    this.otpStore = new Map();
  }

  /**
   * Generate a 6-digit OTP
   */
  public generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Store OTP with expiry
   */
  public async storeOTP(email: string, otp: string, expiry: Date): Promise<void> {
    this.otpStore.set(email, { otp, expiry });
    
    // In production, store in database or Redis
    // await OtpModel.create({ email, otp, expiry });
  }

  /**
   * Verify OTP
   */
  public async verifyOTP(email: string, otp: string): Promise<boolean> {
    const stored = this.otpStore.get(email);
    
    if (!stored) {
      return false;
    }

    // Check if OTP is expired
    if (new Date() > stored.expiry) {
      this.otpStore.delete(email);
      return false;
    }

    // Check if OTP matches
    if (stored.otp === otp) {
      // Remove used OTP
      this.otpStore.delete(email);
      return true;
    }

    return false;
  }

  /**
   * Clean up expired OTPs
   */
  public cleanupExpiredOTPs(): void {
    const now = new Date();
    for (const [email, stored] of this.otpStore.entries()) {
      if (now > stored.expiry) {
        this.otpStore.delete(email);
      }
    }
  }
}