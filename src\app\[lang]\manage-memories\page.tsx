import { Metadata } from 'next';
import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import ManageMemoryClientPage from './ManageMemoryClientPage';

interface ManageMemoryPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export const metadata: Metadata = {
  title: 'Memory Capsules - Alphane',
  description: 'Manage your AI memory capsules and cherished moments',
};

export default async function ManageMemoryPage({ params }: ManageMemoryPageProps) {
  const { lang } = await params;
  
  return (
    <AuthGuard requireAuth={true}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <ManageMemoryClientPage lang={lang} />
      </Suspense>
    </AuthGuard>
  );
} 