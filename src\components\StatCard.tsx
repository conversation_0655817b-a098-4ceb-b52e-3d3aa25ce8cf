'use client';

import React from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface StatCardProps {
  icon?: React.ComponentType<{ className?: string; size?: number }>;
  label: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  gradient?: string;
  size?: 'sm' | 'md' | 'lg';
  clickable?: boolean;
  onClick?: () => void;
  className?: string;
  variant?: 'default' | 'glass' | 'solid';
}

const StatCard: React.FC<StatCardProps> = ({
  icon: Icon,
  label,
  value,
  unit,
  trend,
  trendValue,
  gradient = 'from-purple-500 to-pink-500',
  size = 'md',
  clickable = false,
  onClick,
  className = '',
  variant = 'glass'
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'p-4',
          icon: 'w-8 h-8',
          iconContainer: 'w-12 h-12',
          value: 'text-2xl',
          label: 'text-xs',
          trend: 'text-xs'
        };
      case 'md':
        return {
          container: 'p-6',
          icon: 'w-10 h-10',
          iconContainer: 'w-16 h-16',
          value: 'text-3xl',
          label: 'text-sm',
          trend: 'text-sm'
        };
      case 'lg':
        return {
          container: 'p-8',
          icon: 'w-12 h-12',
          iconContainer: 'w-20 h-20',
          value: 'text-4xl',
          label: 'text-base',
          trend: 'text-base'
        };
      default:
        return {
          container: 'p-6',
          icon: 'w-10 h-10',
          iconContainer: 'w-16 h-16',
          value: 'text-3xl',
          label: 'text-sm',
          trend: 'text-sm'
        };
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'glass':
        return 'backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10';
      case 'solid':
        return 'bg-card border border-border';
      default:
        return 'backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      case 'neutral':
        return <Minus className="w-4 h-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'up':
        return 'text-green-500';
      case 'down':
        return 'text-red-500';
      case 'neutral':
        return 'text-gray-500';
      default:
        return 'text-gray-500';
    }
  };

  const sizeClasses = getSizeClasses();
  const variantClasses = getVariantClasses();

  return (
    <div
      className={`
        group relative rounded-2xl ${sizeClasses.container} ${variantClasses}
        shadow-lg hover:shadow-xl transition-all duration-300
        ${clickable ? 'cursor-pointer hover:scale-105 hover:-translate-y-1' : ''}
        ${className}
      `}
      onClick={clickable ? onClick : undefined}
    >
      {/* Background gradient effect */}
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-300 rounded-2xl`}></div>
      
      {/* Content */}
      <div className="relative z-10">
        {/* Icon and Value Row */}
        <div className="flex items-start justify-between mb-4">
          {/* Icon */}
          {Icon && (
            <div className={`${sizeClasses.iconContainer} bg-gradient-to-br ${gradient} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
              <Icon className={`${sizeClasses.icon} text-white`} />
            </div>
          )}
          
          {/* Trend indicator */}
          {trend && (
            <div className="flex items-center gap-1">
              {getTrendIcon()}
              {trendValue && (
                <span className={`${sizeClasses.trend} font-medium ${getTrendColor()}`}>
                  {trendValue}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Value */}
        <div className="flex items-baseline gap-2 mb-2">
          <span className={`${sizeClasses.value} font-bold text-foreground group-hover:scale-110 transition-transform duration-300`}>
            {value}
          </span>
          {unit && (
            <span className={`${sizeClasses.label} text-muted-foreground`}>
              {unit}
            </span>
          )}
        </div>

        {/* Label */}
        <div className={`${sizeClasses.label} text-muted-foreground font-medium`}>
          {label}
        </div>

        {/* Progress bar (optional visual enhancement) */}
        <div className="mt-3 w-full bg-white/20 dark:bg-gray-700/20 rounded-full h-1 overflow-hidden">
          <div 
            className={`h-full bg-gradient-to-r ${gradient} rounded-full transform transition-all duration-1000 delay-300`}
            style={{
              width: `${Math.min(100, Math.max(10, typeof value === 'number' ? (value / 100) * 100 : 50))}%`
            }}
          ></div>
        </div>
      </div>

      {/* Hover glow effect */}
      {clickable && (
        <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-300 pointer-events-none`}></div>
      )}
    </div>
  );
};

export default StatCard;
