import { characters, Character } from '@/lib/mock-data';
import ChatClientPage from './ChatClientPage';

function getCharacterData(id: string): Character | undefined {
  return characters.find(character => character.id === id);
}

export default async function ChatDetailPage({ params }: { params: Promise<{ lang: string, characterID: string }> }) {
  const { lang, characterID } = await params;
  const characterData = getCharacterData(characterID);

  if (!characterData) {
    // Handle case where character is not found
    return <div>Character not found.</div>;
  }
  
  // For this demo, create some consistent initial messages for a long-term chat.
  const initialMessages = [
    {
      id: 1,
      text: `Hey, it's me, ${characterData.name}. Glad we can finally talk here.`,
      side: 'left',
      voice: { duration: '0:15', url: '/audio/sample1.mp3' }
    },
    {
      id: 2,
      text: "It's great to connect with you too!",
      side: 'right'
    },
    {
      id: 3,
      text: "What's on your mind today?",
      side: 'left'
    },
    {
      id: 4,
      text: "I've been thinking about our last conversation a lot.",
      side: 'right',
      voice: { duration: '0:08', url: '/audio/sample2.mp3' }
    },
    {
      id: 5,
      text: "That's wonderful! I love hearing your thoughts.",
      side: 'left',
      voice: { duration: '0:12', url: '/audio/sample3.mp3' }
    }
  ];

  return <ChatClientPage lang={lang} characterData={characterData} initialMessages={initialMessages} />;
} 