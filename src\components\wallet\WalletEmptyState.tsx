import React from 'react';
import { Wallet, CreditCard, History, TrendingUp } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface WalletEmptyStateProps {
  lang: string;
  type: 'transactions' | 'balance' | 'paymentMethods' | 'general';
  message?: string;
  actionText?: string;
  onAction?: () => void;
  className?: string;
}

const WalletEmptyState: React.FC<WalletEmptyStateProps> = ({ 
  lang, 
  type, 
  message,
  actionText,
  onAction,
  className = ''
}) => {
  const { t } = useTranslation(lang, 'translation');

  const getIcon = () => {
    switch (type) {
      case 'transactions':
        return <History className="w-16 h-16 text-gray-300 dark:text-gray-600" />;
      case 'balance':
        return <TrendingUp className="w-16 h-16 text-gray-300 dark:text-gray-600" />;
      case 'paymentMethods':
        return <CreditCard className="w-16 h-16 text-gray-300 dark:text-gray-600" />;
      default:
        return <Wallet className="w-16 h-16 text-gray-300 dark:text-gray-600" />;
    }
  };

  const getDefaultMessage = () => {
    switch (type) {
      case 'transactions':
        return t('wallet.empty.transactions');
      case 'balance':
        return t('wallet.empty.balance');
      case 'paymentMethods':
        return t('wallet.empty.paymentMethods');
      default:
        return t('wallet.empty.general');
    }
  };

  const getDefaultActionText = () => {
    switch (type) {
      case 'transactions':
        return t('wallet.addFunds');
      case 'balance':
        return t('wallet.addFunds');
      case 'paymentMethods':
        return t('wallet.addPaymentMethod');
      default:
        return t('wallet.getStarted');
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center py-12 px-6 text-center ${className}`}>
      <div className="mb-6">
        {getIcon()}
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        {message || getDefaultMessage()}
      </h3>
      
      <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-sm">
        {type === 'transactions' && t('wallet.empty.transactionsDescription')}
        {type === 'balance' && t('wallet.empty.balanceDescription')}
        {type === 'paymentMethods' && t('wallet.empty.paymentMethodsDescription')}
        {type === 'general' && t('wallet.empty.generalDescription')}
      </p>

      {onAction && (
        <button
          onClick={onAction}
          className="px-6 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-xl font-semibold hover:shadow-lg transition-all"
        >
          {actionText || getDefaultActionText()}
        </button>
      )}
    </div>
  );
};

export default WalletEmptyState; 