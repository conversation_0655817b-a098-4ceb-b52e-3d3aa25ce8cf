'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Globe, MapPin, Crown, Zap, Upload, Type, ChevronDown, ChevronUp, BookOpen, Settings, Users2 } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { useStoryImageUpload } from '@/hooks/story-creation/useStoryImageUpload';

export interface WorldSettingData {
  // Basic Info
  storyName: string;
  coverImage?: File;
  coverImageUrl?: string;

  // Quick Setup - 新增通用设置
  worldOverview: string;
  storyBackground: string;

  // Core Settings - 核心设置 (在基础设置中显示)
  coreConflict: string; // 故事主线（核心任务目标）

  // Background Details - 背景细节 (在世界背景细节中显示)
  historicalEra: string;
  geographicEnvironment: string;
  mainRaces: string;

  // Advanced Settings - 高级设置
  physicsRules: string;
  physicsRulesCustom?: string;
  supernaturalElements: string;
  socialPoliticalSystem: string; // 合并后的字段
  economicFoundation: string;
  techLevel: string;
  techLevelCustom?: string;
  timeBackground: string;

  // 保持向后兼容（废弃字段，但保留以防数据丢失）
  socialForm?: string;
  politicalStructure?: string;
}

export interface WorldSettingProps {
  worldSetting: WorldSettingData;
  onWorldSettingChange: (updates: Partial<WorldSettingData>) => void;
  formData: any; // StoryFormData
  setFormData: (data: any) => void;
  lang: string;
  className?: string;
}

const WorldSetting: React.FC<WorldSettingProps> = ({
  worldSetting,
  onWorldSettingChange,
  formData,
  setFormData,
  lang,
  className = ''
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Use the story image upload hook
  const {
    worldSettingImagePreview,
    worldSettingImageInputRef,
    handleWorldSettingImageUpload,
    triggerWorldSettingImageInput
  } = useStoryImageUpload(formData, setFormData);

  const handleFieldChange = (field: keyof WorldSettingData, value: string) => {
    onWorldSettingChange({ [field]: value });
  };

  // 数据迁移逻辑：如果存在旧字段但没有新字段，自动合并
  React.useEffect(() => {
    if (!worldSetting.socialPoliticalSystem && (worldSetting.socialForm || worldSetting.politicalStructure)) {
      const combinedValue = [worldSetting.socialForm, worldSetting.politicalStructure]
        .filter(Boolean)
        .join('，');
      if (combinedValue) {
        onWorldSettingChange({ socialPoliticalSystem: combinedValue });
      }
    }
  }, [worldSetting.socialForm, worldSetting.politicalStructure, worldSetting.socialPoliticalSystem, onWorldSettingChange]);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
          <Globe className="w-5 h-5 text-purple-500" />
          {t('storyCreation.worldSetting.title')}
        </h3>
      </div>

      <div className="space-y-8">
        {/* Basic Info Section */}
        <div>
          <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center gap-2">
            <Type className="w-4 h-4 text-purple-400" />
            {t('storyCreation.worldSetting.basicInfo.title')}
          </h4>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Story Name */}
            <div>
              <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                {t('storyCreation.worldSetting.basicInfo.storyName')}
              </label>
              <input
                type="text"
                value={worldSetting.storyName}
                onChange={(e) => handleFieldChange('storyName', e.target.value)}
                placeholder={t('storyCreation.worldSetting.basicInfo.storyNamePlaceholder')}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
              />
            </div>

            {/* Cover Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                {t('storyCreation.worldSetting.basicInfo.coverImage')}
              </label>
              <button
                type="button"
                onClick={triggerWorldSettingImageInput}
                className="w-full p-4 border-2 border-dashed border-purple-300 dark:border-purple-700 rounded-lg hover:border-purple-400 dark:hover:border-purple-600 transition-all duration-300 bg-white/50 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800 hover:scale-105"
              >
                {worldSettingImagePreview ? (
                  <div className="text-purple-700 dark:text-purple-300">
                    <Image
                      src={worldSettingImagePreview}
                      alt="World Setting Cover"
                      width={120}
                      height={80}
                      className="mx-auto mb-2 rounded-lg object-cover"
                    />
                    <p className="font-semibold text-sm">{t('storyCreation.worldSetting.basicInfo.coverImageUploaded')}</p>
                    <p className="text-xs">{t('storyCreation.worldSetting.basicInfo.clickToChangeImage')}</p>
                  </div>
                ) : (
                  <div className="text-purple-600 dark:text-purple-400">
                    <Upload className="mx-auto mb-2" size={24} />
                    <p className="font-semibold text-sm">{t('storyCreation.worldSetting.basicInfo.uploadCoverImage')}</p>
                    <p className="text-xs">{t('storyCreation.worldSetting.basicInfo.clickOrDragToUpload')}</p>
                  </div>
                )}
              </button>

              <input
                ref={worldSettingImageInputRef}
                type="file"
                accept="image/*"
                onChange={(e) => e.target.files?.[0] && handleWorldSettingImageUpload(e.target.files[0])}
                className="hidden"
              />
            </div>
          </div>

          {/* Story Display Information */}
          <div className="grid grid-cols-1 gap-6 mt-6">
            {/* Story Description */}
            <div>
              <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                {t('storyCreation.worldSetting.basicInfo.storyDescription')}
              </label>
              <textarea
                value={formData.description || ''}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, description: e.target.value }))}
                placeholder={t('storyCreation.worldSetting.basicInfo.storyDescriptionPlaceholder')}
                rows={3}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
              />
            </div>

            {/* Opening Message */}
            <div>
              <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                {t('storyCreation.worldSetting.basicInfo.openingMessage')}
              </label>
              <textarea
                value={formData.openingMessage || ''}
                onChange={(e) => setFormData((prev: any) => ({ ...prev, openingMessage: e.target.value }))}
                placeholder={t('storyCreation.worldSetting.basicInfo.openingMessagePlaceholder')}
                rows={3}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
              />
            </div>

            {/* Story Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                {t('storyCreation.worldSetting.basicInfo.storyTags')}
              </label>
              <div className="space-y-3">
                {/* Tag Input */}
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder={t('storyCreation.worldSetting.basicInfo.tagPlaceholder')}
                    className="flex-1 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        const input = e.target as HTMLInputElement;
                        const tagValue = input.value.trim();
                        if (tagValue && !formData.tags?.includes(tagValue)) {
                          setFormData((prev: any) => ({
                            ...prev,
                            tags: [...(prev.tags || []), tagValue]
                          }));
                          input.value = '';
                        }
                      }
                    }}
                  />
                  <button
                    type="button"
                    onClick={(e) => {
                      const input = (e.target as HTMLButtonElement).previousElementSibling as HTMLInputElement;
                      const tagValue = input.value.trim();
                      if (tagValue && !formData.tags?.includes(tagValue)) {
                        setFormData((prev: any) => ({
                          ...prev,
                          tags: [...(prev.tags || []), tagValue]
                        }));
                        input.value = '';
                      }
                    }}
                    className="px-4 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors duration-200 text-sm font-medium"
                  >
                    {t('storyCreation.worldSetting.basicInfo.addTag')}
                  </button>
                </div>

                {/* Existing Tags */}
                {formData.tags && formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag: string, index: number) => (
                      <span
                        key={index}
                        className="inline-flex items-center gap-1 px-3 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200 text-sm rounded-full"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => {
                            setFormData((prev: any) => ({
                              ...prev,
                              tags: prev.tags?.filter((_: any, i: number) => i !== index) || []
                            }));
                          }}
                          className="ml-1 text-purple-600 dark:text-purple-300 hover:text-purple-800 dark:hover:text-purple-100 transition-colors"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Basic Settings Section - 基础设置 (原Quick Setup) */}
        <div>
          <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center gap-2">
            <BookOpen className="w-4 h-4 text-purple-400" />
            {t('storyCreation.worldSetting.basicSettings.title')}
          </h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                {t('storyCreation.worldSetting.quickSetup.worldOverview')}
              </label>
              <textarea
                value={worldSetting.worldOverview || ''}
                onChange={(e) => handleFieldChange('worldOverview', e.target.value)}
                placeholder={t('storyCreation.worldSetting.quickSetup.worldOverviewPlaceholder')}
                rows={3}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                {t('storyCreation.worldSetting.quickSetup.storyBackground')}
              </label>
              <textarea
                value={worldSetting.storyBackground || ''}
                onChange={(e) => handleFieldChange('storyBackground', e.target.value)}
                placeholder={t('storyCreation.worldSetting.quickSetup.storyBackgroundPlaceholder')}
                rows={3}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 flex items-center gap-2">
                <Crown className="w-4 h-4 text-purple-500" />
                {t('storyCreation.worldSetting.basicSettings.storyMainline')}
                <span className="text-xs text-gray-500">({t('storyCreation.worldSetting.basicSettings.coreObjective')})</span>
              </label>
              <textarea
                value={worldSetting.coreConflict}
                onChange={(e) => handleFieldChange('coreConflict', e.target.value)}
                placeholder={t('storyCreation.worldSetting.basicSettings.storyMainlinePlaceholderNew')}
                rows={3}
                className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
              />
            </div>
          </div>
        </div>

        {/* Advanced Settings Section - 简化为两层 */}
        <div>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="w-full flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200"
          >
            <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
              <Settings className="w-4 h-4 text-purple-400" />
              {t('storyCreation.worldSetting.advancedSettings.title')}
              <span className="text-xs text-gray-500 ml-2">({t('storyCreation.worldSetting.advancedSettings.optional')})</span>
            </h4>
            {showAdvanced ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </button>

          {showAdvanced && (
            <div className="mt-4 space-y-6">
              {/* 核心世界规则 */}
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50/30 dark:bg-gray-700/20">
                <h5 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-4 flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  {t('storyCreation.worldSetting.advancedSettings.coreWorldRules')}
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Physics Rules - Select + 自定义 */}
                                      <div>
                      <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                        {t('storyCreation.worldSetting.advancedSettings.physicsRules')}
                      </label>
                      <select
                        value={worldSetting.physicsRules === 'custom' ? 'custom' : worldSetting.physicsRules}
                        onChange={(e) => {
                          handleFieldChange('physicsRules', e.target.value);
                          if (e.target.value !== 'custom') {
                            handleFieldChange('physicsRulesCustom', '');
                          }
                        }}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                      >
                        <option value="">{t('storyCreation.worldSetting.advancedSettings.physicsRulesPlaceholder')}</option>
                        <option value="realistic">{t('storyCreation.worldSetting.advancedSettings.physicsRulesOptions.realistic')}</option>
                        <option value="soft-scifi">{t('storyCreation.worldSetting.advancedSettings.physicsRulesOptions.softScifi')}</option>
                        <option value="high-fantasy">{t('storyCreation.worldSetting.advancedSettings.physicsRulesOptions.highFantasy')}</option>
                        <option value="cosmic-horror">{t('storyCreation.worldSetting.advancedSettings.physicsRulesOptions.cosmicHorror')}</option>
                        <option value="custom">{t('storyCreation.worldSetting.advancedSettings.customOption')}</option>
                      </select>
                      {worldSetting.physicsRules === 'custom' && (
                        <textarea
                          value={worldSetting.physicsRulesCustom || ''}
                          onChange={(e) => handleFieldChange('physicsRulesCustom', e.target.value)}
                          placeholder={t('storyCreation.worldSetting.advancedSettings.physicsRulesCustomPlaceholder')}
                          rows={2}
                          className="w-full mt-2 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                        />
                      )}
                    </div>

                  {/* Tech Level - Select + 自定义 */}
                                    <div>
                      <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                        {t('storyCreation.worldSetting.advancedSettings.techLevel')}
                      </label>
                      <select
                        value={worldSetting.techLevel === 'custom' ? 'custom' : worldSetting.techLevel}
                        onChange={(e) => {
                          handleFieldChange('techLevel', e.target.value);
                          if (e.target.value !== 'custom') {
                            handleFieldChange('techLevelCustom', '');
                          }
                        }}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                      >
                        <option value="">{t('storyCreation.worldSetting.advancedSettings.techLevelPlaceholder')}</option>
                        <option value="industrial-revolution">{t('storyCreation.worldSetting.advancedSettings.techLevelOptions.industrialRevolution')}</option>
                        <option value="information-age">{t('storyCreation.worldSetting.advancedSettings.techLevelOptions.informationAge')}</option>
                        <option value="cyberpunk-near-future">{t('storyCreation.worldSetting.advancedSettings.techLevelOptions.cyberpunkNearFuture')}</option>
                        <option value="interstellar-civilization">{t('storyCreation.worldSetting.advancedSettings.techLevelOptions.interstellarCivilization')}</option>
                        <option value="magic-tech-fusion">{t('storyCreation.worldSetting.advancedSettings.techLevelOptions.magicTechFusion')}</option>
                        <option value="custom">{t('storyCreation.worldSetting.advancedSettings.customOption')}</option>
                      </select>
                      {worldSetting.techLevel === 'custom' && (
                        <textarea
                          value={worldSetting.techLevelCustom || ''}
                          onChange={(e) => handleFieldChange('techLevelCustom', e.target.value)}
                          placeholder={t('storyCreation.worldSetting.advancedSettings.techLevelCustomPlaceholder')}
                          rows={2}
                          className="w-full mt-2 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                        />
                      )}
                    </div>

                  {/* Supernatural Elements - 跨两列 */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      {t('storyCreation.worldSetting.advancedSettings.supernaturalElements')}
                    </label>
                    <textarea
                      value={worldSetting.supernaturalElements}
                      onChange={(e) => handleFieldChange('supernaturalElements', e.target.value)}
                      placeholder={t('storyCreation.worldSetting.advancedSettings.supernaturalElementsNewPlaceholder')}
                      rows={3}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                    />
                  </div>
                </div>
              </div>

                            {/* 世界背景设定 */}
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50/30 dark:bg-gray-700/20">
                <h5 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-4 flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  {t('storyCreation.worldSetting.advancedSettings.worldBackgroundSettings')}
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      {t('storyCreation.worldSetting.basicSettings.historicalEra')}
                    </label>
                    <input
                      type="text"
                      value={worldSetting.historicalEra}
                      onChange={(e) => handleFieldChange('historicalEra', e.target.value)}
                      placeholder={t('storyCreation.worldSetting.basicSettings.historicalEraPlaceholder')}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      {t('storyCreation.worldSetting.basicSettings.geographicEnvironment')}
                    </label>
                    <input
                      type="text"
                      value={worldSetting.geographicEnvironment}
                      onChange={(e) => handleFieldChange('geographicEnvironment', e.target.value)}
                      placeholder={t('storyCreation.worldSetting.basicSettings.geographicEnvironmentPlaceholder')}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      {t('storyCreation.worldSetting.basicSettings.mainRaces')}
                    </label>
                    <input
                      type="text"
                      value={worldSetting.mainRaces}
                      onChange={(e) => handleFieldChange('mainRaces', e.target.value)}
                      placeholder={t('storyCreation.worldSetting.basicSettings.mainRacesPlaceholder')}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                    {t('storyCreation.worldSetting.advancedSettings.timeBackground')}
                  </label>
                  <textarea
                    value={worldSetting.timeBackground}
                    onChange={(e) => handleFieldChange('timeBackground', e.target.value)}
                    placeholder={t('storyCreation.worldSetting.advancedSettings.timeBackgroundNewPlaceholder')}
                    rows={3}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                  />
                </div>
              </div>

              {/* 社会经济设定 */}
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50/30 dark:bg-gray-700/20">
                <h5 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-4 flex items-center gap-2">
                  <Users2 className="w-4 h-4" />
                  {t('storyCreation.worldSetting.advancedSettings.socialEconomicSettings')}
                </h5>
                <div className="space-y-4">
                  {/* 合并后的社会政治制度 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      {t('storyCreation.worldSetting.advancedSettings.socialPoliticalSystem')}
                      <span className="text-xs text-gray-500 ml-1">({t('storyCreation.worldSetting.advancedSettings.socialFormPlusPolitical')})</span>
                    </label>
                    <textarea
                      value={worldSetting.socialPoliticalSystem || ''}
                      onChange={(e) => handleFieldChange('socialPoliticalSystem', e.target.value)}
                      placeholder={t('storyCreation.worldSetting.advancedSettings.socialPoliticalSystemPlaceholder')}
                      rows={3}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                    />
                  </div>

                  {/* 经济基础 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      {t('storyCreation.worldSetting.advancedSettings.economicFoundation')}
                    </label>
                    <textarea
                      value={worldSetting.economicFoundation}
                      onChange={(e) => handleFieldChange('economicFoundation', e.target.value)}
                      placeholder={t('storyCreation.worldSetting.advancedSettings.economicFoundationNewPlaceholder')}
                      rows={3}
                      className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                    />
                  </div>
                </div>
              </div>


            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WorldSetting;
