import React from 'react';
import { Spark<PERSON>, Heart, Coins, TrendingUp, ArrowUp, ArrowDown } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface CurrencyBalance {
  type: 'alphane' | 'endora' | 'serotile' | 'oxytol';
  amount: number;
  dailyEarned: number;
  weeklyEarned: number;
  totalEarned: number;
}

interface CurrencyCardProps {
  lang: string;
  currency: CurrencyBalance;
  onClick?: () => void;
}

const CurrencyCard: React.FC<CurrencyCardProps> = ({ 
  lang, 
  currency, 
  onClick 
}) => {
  const { t } = useTranslation(lang, 'translation');

  const getCurrencyConfig = () => {
    switch (currency.type) {
      case 'alphane':
        return {
          icon: <Sparkles className="w-6 h-6" />,
          gradient: 'from-yellow-400 to-orange-500',
          bgGradient: 'from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20',
          borderColor: 'border-yellow-200 dark:border-yellow-800',
          textColor: 'text-yellow-700 dark:text-yellow-300'
        };
      case 'endora':
        return {
          icon: <Heart className="w-6 h-6" />,
          gradient: 'from-pink-400 to-rose-500',
          bgGradient: 'from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20',
          borderColor: 'border-pink-200 dark:border-pink-800',
          textColor: 'text-pink-700 dark:text-pink-300'
        };
      case 'serotile':
        return {
          icon: <Coins className="w-6 h-6" />,
          gradient: 'from-blue-400 to-cyan-500',
          bgGradient: 'from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20',
          borderColor: 'border-blue-200 dark:border-blue-800',
          textColor: 'text-blue-700 dark:text-blue-300'
        };
      case 'oxytol':
        return {
          icon: <TrendingUp className="w-6 h-6" />,
          gradient: 'from-green-400 to-emerald-500',
          bgGradient: 'from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20',
          borderColor: 'border-green-200 dark:border-green-800',
          textColor: 'text-green-700 dark:text-green-300'
        };
      default:
        return {
          icon: <Coins className="w-6 h-6" />,
          gradient: 'from-gray-400 to-gray-500',
          bgGradient: 'from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20',
          borderColor: 'border-gray-200 dark:border-gray-800',
          textColor: 'text-gray-700 dark:text-gray-300'
        };
    }
  };

  const config = getCurrencyConfig();
  const weeklyChange = ((currency.weeklyEarned / (currency.amount - currency.weeklyEarned)) * 100);
  const isPositiveChange = weeklyChange > 0;

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
  };

  return (
    <div 
      className={`bg-gradient-to-br ${config.bgGradient} border ${config.borderColor} rounded-2xl p-6 cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105`}
      onClick={onClick}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className={`w-12 h-12 bg-gradient-to-r ${config.gradient} rounded-xl flex items-center justify-center text-white shadow-lg`}>
          {config.icon}
        </div>
        <div className="text-right">
          <div className="text-xs text-gray-500 dark:text-gray-400">{t('wallet.balance')}</div>
          <div className={`text-sm font-medium ${config.textColor}`}>
            {formatNumber(currency.amount)}
          </div>
        </div>
      </div>

      {/* Currency Name */}
      <div className="mb-4">
        <h3 className={`text-lg font-bold ${config.textColor}`}>
          {t(`tokens.${currency.type}`)}
        </h3>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          {t(`wallet.${currency.type}Desc`)}
        </div>
      </div>

      {/* Stats */}
      <div className="space-y-3">
        {/* Daily Earned */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.todayEarned')}</span>
          <div className="flex items-center gap-1">
            <ArrowUp className="w-3 h-3 text-green-500" />
            <span className="text-sm font-semibold text-green-600 dark:text-green-400">
              +{currency.dailyEarned.toLocaleString()}
            </span>
          </div>
        </div>

        {/* Weekly Trend */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.weeklyTrend')}</span>
          <div className="flex items-center gap-1">
            {isPositiveChange ? (
              <ArrowUp className="w-3 h-3 text-green-500" />
            ) : (
              <ArrowDown className="w-3 h-3 text-red-500" />
            )}
            <span className={`text-sm font-semibold ${isPositiveChange ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {isPositiveChange ? '+' : ''}{weeklyChange.toFixed(1)}%
            </span>
          </div>
        </div>

        {/* Total Earned */}
        <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600 dark:text-gray-400">{t('wallet.totalEarned')}</span>
            <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
              {formatNumber(currency.totalEarned)}
            </span>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mt-4">
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className={`h-2 bg-gradient-to-r ${config.gradient} rounded-full transition-all duration-500`}
            style={{ width: `${Math.min((currency.amount / (currency.totalEarned || 1)) * 100, 100)}%` }}
          ></div>
        </div>
        <div className="flex justify-between mt-1">
          <span className="text-xs text-gray-500 dark:text-gray-400">{t('wallet.current')}</span>
          <span className="text-xs text-gray-500 dark:text-gray-400">{t('wallet.allTime')}</span>
        </div>
      </div>
    </div>
  );
};

export default CurrencyCard; 