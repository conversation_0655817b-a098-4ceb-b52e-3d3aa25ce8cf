'use client';

import React, { useRef, DragEvent } from 'react';
import Image from 'next/image';
import { Upload } from 'lucide-react';
import toast from 'react-hot-toast';

interface ImageUploadProps {
  id: string;
  label: string;
  description?: string;
  preview?: string;
  required?: boolean;
  accept?: string;
  maxSizeMB?: number;
  aspectRatio?: string;
  className?: string;
  onUpload: (file: File) => void;
  disabled?: boolean;
  uploadButton?: React.ReactNode;
  children?: React.ReactNode;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  id,
  label,
  description,
  preview,
  required = false,
  accept = "image/*",
  maxSizeMB = 10,
  aspectRatio,
  className = "",
  onUpload,
  disabled = false,
  uploadButton,
  children
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): boolean => {
    // Check file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return false;
    }

    // Check file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      toast.error(`File size cannot exceed ${maxSizeMB}MB`);
      return false;
    }

    return true;
  };

  const handleFileSelect = (file: File) => {
    if (validateFile(file)) {
      onUpload(file);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (disabled) return;

    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleClick = () => {
    if (!disabled) {
      inputRef.current?.click();
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Label */}
      <label className="block text-sm font-bold text-gray-800 dark:text-gray-200 flex items-center gap-2">
        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
        {label}
        {required && <span className="text-red-500">*</span>}
        {description && (
          <span className="text-xs text-gray-500 dark:text-gray-400 font-normal ml-1">
            {description}
          </span>
        )}
      </label>

      {/* Upload Area */}
      <div
        className={`relative group rounded-2xl overflow-hidden transition-all duration-300 cursor-pointer ${
          disabled ? 'opacity-50 cursor-not-allowed' : ''
        } ${
          preview ? 
            'bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 border-2 border-emerald-200 dark:border-emerald-800' : 
            'bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 border-2 border-dashed border-purple-300 dark:border-purple-700 hover:border-purple-400 dark:hover:border-purple-600'
        }`}
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        {preview ? (
          // Preview Mode
          <div className="p-6">
            <div className="relative mx-auto max-w-sm">
              <Image
                src={preview}
                alt="Preview"
                width={300}
                height={aspectRatio === '5:8' ? 480 : aspectRatio === '5:6' ? 360 : 300}
                className="w-full h-auto rounded-xl shadow-lg object-contain border border-white/50 dark:border-gray-700/50"
              />
              <div className="absolute top-3 right-3">
                <div className="bg-emerald-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-lg">
                  ✓ Completed
                </div>
              </div>
            </div>
            
            <div className="mt-4 text-center space-y-2">
              <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 rounded-full text-sm font-medium">
                <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full animate-pulse"></div>
                Image upload completed
              </div>
              {aspectRatio && (
                <p className="text-xs text-emerald-600 dark:text-emerald-400">
                  Ratio: {aspectRatio} • Click to re-upload
                </p>
              )}
              
              {uploadButton || (
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClick();
                  }}
                  className="mt-3 inline-flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-emerald-200 dark:border-emerald-700 text-emerald-600 dark:text-emerald-400 rounded-lg hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-all text-sm font-medium shadow-sm"
                >
                  <Upload size={14} />
                  Re-upload
                </button>
              )}
            </div>
            {children}
          </div>
        ) : (
          // Upload Mode
          <div className="p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-violet-500 rounded-2xl flex items-center justify-center shadow-lg">
              <Upload className="text-white" size={28} />
            </div>
            <h3 className="text-lg font-bold text-purple-800 dark:text-purple-200 mb-2">Upload Image</h3>
            <p className="text-sm text-purple-600 dark:text-purple-400 mb-4">
              Supports JPG, PNG formats, max {maxSizeMB}MB
              {aspectRatio && ` • Recommended ratio ${aspectRatio}`}
            </p>
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-purple-100 dark:bg-purple-900/40 text-purple-700 dark:text-purple-300 rounded-lg text-sm font-medium">
              🎨 Click to select image
            </div>
            <p className="text-xs text-purple-500 dark:text-purple-400 mt-3">
              Supports drag & drop upload
            </p>
          </div>
        )}
      </div>

      {/* Hidden Input */}
      <input
        ref={inputRef}
        id={id}
        type="file"
        accept={accept}
        onChange={handleInputChange}
        className="hidden"
        disabled={disabled}
      />
    </div>
  );
};

export default ImageUpload; 