'use client';

import React, { useState } from 'react';
import { Gift, Send, Copy, Check, Plus, Clock, User, Star, History } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import EnhancedTabNavigation, { TabItem } from '@/components/common/EnhancedTabNavigation';
import { getGiftTabColors } from '@/utils/tabColorSchemes';

interface GiftClientPageProps {
  lang: string;
}

interface CouponCode {
  id: string;
  code: string;
  type: 'premium' | 'tokens' | 'character' | 'special';
  value: string;
  description: string;
  expiresAt: string;
  isUsed: boolean;
  fromUser?: string;
  createdAt: string;
}

const GiftClientPage: React.FC<GiftClientPageProps> = ({ lang }) => {

  const [activeTab, setActiveTab] = useState<'receive' | 'send' | 'history'>('receive');
  const [couponInput, setCouponInput] = useState('');
  const [sendCode, setSendCode] = useState('');
  const [sendToUser, setSendToUser] = useState('');
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  // Mock data for received coupons
  const [receivedCoupons] = useState<CouponCode[]>([
    {
      id: '1',
      code: 'WELCOME2024',
      type: 'premium',
      value: '7 Days Premium',
      description: 'Welcome gift for new users',
      expiresAt: '2024-12-31',
      isUsed: false,
      fromUser: 'System',
      createdAt: '2024-01-01'
    },
    {
      id: '2',
      code: 'FRIEND50',
      type: 'tokens',
      value: '500 Tokens',
      description: 'Gift from a friend',
      expiresAt: '2024-06-30',
      isUsed: true,
      fromUser: 'Alice',
      createdAt: '2024-01-15'
    }
  ]);

  // Mock data for available coupons to send
  const [availableCoupons] = useState<CouponCode[]>([
    {
      id: '3',
      code: 'SHARE100',
      type: 'tokens',
      value: '100 Tokens',
      description: 'Token gift for sharing',
      expiresAt: '2024-08-31',
      isUsed: false,
      createdAt: '2024-02-01'
    },
    {
      id: '4',
      code: 'PREMIUM3D',
      type: 'premium',
      value: '3 Days Premium',
      description: 'Premium access gift',
      expiresAt: '2024-09-30',
      isUsed: false,
      createdAt: '2024-02-15'
    }
  ]);



  // Mock data for transaction history (往来账)
  const [transactionHistory] = useState([
    {
      id: '1',
      type: 'received',
      code: 'WELCOME2024',
      fromUser: 'System',
      value: '7 Days Premium',
      date: '2024-01-01',
      status: 'redeemed'
    },
    {
      id: '2',
      type: 'sent',
      code: 'SHARE123',
      toUser: 'Bob',
      value: 'Special Character',
      date: '2024-01-20',
      status: 'redeemed'
    },
    {
      id: '3',
      type: 'received',
      code: 'FRIEND50',
      fromUser: 'Alice',
      value: '500 Tokens',
      date: '2024-01-15',
      status: 'redeemed'
    }
  ]);

  const handleRedeemCoupon = async () => {
    if (!couponInput.trim()) return;
    
    setIsRedeeming(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert(`Successfully redeemed coupon: ${couponInput}`);
      setCouponInput('');
    } catch (error) {
      alert('Failed to redeem coupon. Please check the code and try again.');
    } finally {
      setIsRedeeming(false);
    }
  };

  const handleSendGift = async () => {
    if (!sendCode.trim() || !sendToUser.trim()) return;
    
    setIsSending(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert(`Gift sent successfully to ${sendToUser}!`);
      setSendCode('');
      setSendToUser('');
    } catch (error) {
      alert('Failed to send gift. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  const copyToClipboard = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };



  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'premium': return <Star className="w-5 h-5 text-yellow-500" />;
      case 'tokens': return <Gift className="w-5 h-5 text-blue-500" />;
      case 'character': return <User className="w-5 h-5 text-purple-500" />;
      default: return <Gift className="w-5 h-5 text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'premium': return 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200';
      case 'tokens': return 'bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200';
      case 'character': return 'bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200';
      default: return 'bg-gray-100 dark:bg-gray-900/50 text-gray-800 dark:text-gray-200';
    }
  };

  // Tab configuration for enhanced navigation
  const giftTabs: TabItem[] = [
    {
      id: 'receive',
      label: 'Receive Gifts',
      icon: Gift,
      description: 'Receive and redeem gifts'
    },
    {
      id: 'send',
      label: 'Send Gifts',
      icon: Send,
      description: 'Send gifts to friends'
    },
    {
      id: 'history',
      label: 'Gifts History',
      icon: History,
      description: 'View gift history'
    }
  ];

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-blue-900/10 dark:to-purple-900/10">
        {/* Enhanced Tab Navigation */}
        <EnhancedTabNavigation
          tabs={giftTabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          getTabColors={getGiftTabColors}
          containerMaxWidth="max-w-4xl"
        />

        {/* Tab Content */}
        <div className="max-w-4xl mx-auto space-y-6 p-4 sm:p-6">
            {activeTab === 'receive' && (
              <div className="space-y-6">
                {/* Redeem Coupon Section */}
                <div className="backdrop-blur-xl bg-white/40 dark:bg-black/20 border border-white/30 dark:border-white/10 rounded-lg p-6 theme-transition shadow-lg">
                  <h2 className="text-xl font-semibold mb-4 flex items-center gap-1.5">
                    <Plus className="w-5 h-5" />
                    Redeem Coupon Code
                  </h2>
                  <div className="flex gap-3">
                    <input
                      type="text"
                      value={couponInput}
                      onChange={(e) => setCouponInput(e.target.value.toUpperCase())}
                      placeholder="Enter coupon code"
                      className="flex-1 px-4 py-2 border border-white/30 dark:border-white/20 rounded-lg backdrop-blur-sm bg-white/30 dark:bg-black/20 text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white/40 dark:focus:bg-black/30 transition-colors"
                    />
                    <button
                      onClick={handleRedeemCoupon}
                      disabled={!couponInput.trim() || isRedeeming}
                      className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-1.5"
                    >
                      {isRedeeming ? (
                        <>
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                          Redeeming...
                        </>
                      ) : (
                        <>
                          <Gift className="w-4 h-4" />
                          Redeem
                        </>
                      )}
                    </button>
                  </div>
                </div>

                {/* Received Coupons */}
                <div className="backdrop-blur-xl bg-white/40 dark:bg-black/20 border border-white/30 dark:border-white/10 rounded-lg p-6 theme-transition shadow-lg">
                  <h2 className="text-xl font-semibold mb-4">Received Coupons</h2>
                  {receivedCoupons.length > 0 ? (
                    <div className="space-y-3">
                      {receivedCoupons.map((coupon) => (
                        <div
                          key={coupon.id}
                          className={`p-4 border rounded-lg backdrop-blur-sm ${
                            coupon.isUsed
                              ? 'opacity-50 border-white/20 dark:border-white/10 bg-white/20 dark:bg-black/10'
                              : 'border-primary/30 bg-primary/10 dark:bg-primary/5'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              {getTypeIcon(coupon.type)}
                              <div>
                                <div className="flex items-center gap-1.5">
                                  <span className="font-mono font-semibold">{coupon.code}</span>
                                  <span className={`px-2 py-1 rounded-full text-xs ${getTypeColor(coupon.type)}`}>
                                    {coupon.value}
                                  </span>
                                  {coupon.isUsed && (
                                    <span className="px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400">
                                      Used
                                    </span>
                                  )}
                                </div>
                                <p className="text-sm text-foreground/70">{coupon.description}</p>
                                <p className="text-xs text-foreground/50">
                                  From: {coupon.fromUser} • Expires: {coupon.expiresAt}
                                </p>
                              </div>
                            </div>
                            <button
                              onClick={() => copyToClipboard(coupon.code)}
                              className="p-1.5 text-foreground/70 hover:text-foreground transition-colors"
                            >
                              {copiedCode === coupon.code ? (
                                <Check className="w-4 h-4 text-green-500" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-foreground/50">
                      <Gift className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>No coupons received yet</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'send' && (
              <div className="space-y-6">
                {/* Available Coupons to Send */}
                <div className="backdrop-blur-xl bg-white/40 dark:bg-black/20 border border-white/30 dark:border-white/10 rounded-lg p-6 theme-transition shadow-lg">
                  <h2 className="text-xl font-semibold mb-4 flex items-center gap-1.5">
                    <Gift className="w-5 h-5" />
                    Available Coupons to Send
                  </h2>
                  {availableCoupons.length > 0 ? (
                    <div className="space-y-3">
                      {availableCoupons.map((coupon) => (
                        <div
                          key={coupon.id}
                          className="p-4 border border-white/30 dark:border-white/10 rounded-lg backdrop-blur-sm bg-white/20 dark:bg-black/10 hover:bg-white/30 dark:hover:bg-black/20 transition-colors"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              {getTypeIcon(coupon.type)}
                              <div>
                                <div className="flex items-center gap-1.5">
                                  <span className="font-mono font-semibold">{coupon.code}</span>
                                  <span className={`px-2 py-1 rounded-full text-xs ${getTypeColor(coupon.type)}`}>
                                    {coupon.value}
                                  </span>
                                </div>
                                <p className="text-sm text-foreground/70">{coupon.description}</p>
                                <p className="text-xs text-foreground/50">Expires: {coupon.expiresAt}</p>
                              </div>
                            </div>
                            <button
                              onClick={() => setSendCode(coupon.code)}
                              className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                            >
                              Select
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-foreground/50">
                      <Gift className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>No coupons available to send</p>
                    </div>
                  )}
                </div>

                {/* Send Gift Form */}
                <div className="backdrop-blur-xl bg-white/40 dark:bg-black/20 border border-white/30 dark:border-white/10 rounded-lg p-6 theme-transition shadow-lg">
                  <h2 className="text-xl font-semibold mb-4 flex items-center gap-1.5">
                    <Send className="w-5 h-5" />
                    Send Gift to Friend
                  </h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Coupon Code</label>
                    <input
                      type="text"
                      value={sendCode}
                      onChange={(e) => setSendCode(e.target.value.toUpperCase())}
                      placeholder="Enter coupon code to send"
                      className="w-full px-4 py-2 border border-white/30 dark:border-white/20 rounded-lg backdrop-blur-sm bg-white/30 dark:bg-black/20 text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white/40 dark:focus:bg-black/30 transition-colors"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Recipient Username</label>
                    <input
                      type="text"
                      value={sendToUser}
                      onChange={(e) => setSendToUser(e.target.value)}
                      placeholder="Enter username"
                      className="w-full px-4 py-2 border border-white/30 dark:border-white/20 rounded-lg backdrop-blur-sm bg-white/30 dark:bg-black/20 text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white/40 dark:focus:bg-black/30 transition-colors"
                    />
                  </div>
                  <button
                    onClick={handleSendGift}
                    disabled={!sendCode.trim() || !sendToUser.trim() || isSending}
                    className="w-full px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-1.5"
                  >
                    {isSending ? (
                      <>
                        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        Sending Gift...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        Send Gift
                      </>
                    )}
                  </button>
                </div>
                </div>
              </div>
            )}

            {activeTab === 'history' && (
              <div className="backdrop-blur-xl bg-white/40 dark:bg-black/20 border border-white/30 dark:border-white/10 rounded-lg p-6 theme-transition shadow-lg">
                <h2 className="text-xl font-semibold mb-4 flex items-center gap-1.5">
                  <Clock className="w-5 h-5" />
                  Gifts History
                </h2>
                {transactionHistory.length > 0 ? (
                  <div className="space-y-3">
                    {transactionHistory.map((transaction) => (
                      <div key={transaction.id} className="p-4 border border-white/30 dark:border-white/10 rounded-lg backdrop-blur-sm bg-white/20 dark:bg-black/10">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {transaction.type === 'received' ? (
                              <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center">
                                <Gift className="w-4 h-4 text-green-600 dark:text-green-400" />
                              </div>
                            ) : (
                              <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
                                <Send className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                              </div>
                            )}
                            <div>
                              <div className="flex items-center gap-1.5">
                                <span className="font-mono font-semibold">{transaction.code}</span>
                                <span className="px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200">
                                  {transaction.value}
                                </span>
                                <span className="px-2 py-1 rounded-full text-xs bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200">
                                  {transaction.status}
                                </span>
                              </div>
                              <p className="text-sm text-foreground/70">
                                {transaction.type === 'received'
                                  ? `Received from: ${transaction.fromUser}`
                                  : `Sent to: ${transaction.toUser}`}
                              </p>
                              <p className="text-xs text-foreground/50">{transaction.date}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-foreground/50">
                    <Clock className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>No gifts history yet</p>
                  </div>
                )}
              </div>
            )}
        </div>
      </div>
    </MainAppLayout>
  );
};

export default GiftClientPage;
