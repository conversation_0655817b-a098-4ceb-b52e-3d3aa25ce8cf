'use client';

import { useState, useRef, useEffect, useCallback } from 'react';

export interface UseDropdownOptions {
  closeOnOutsideClick?: boolean;
  closeOnEscape?: boolean;
  initialOpen?: boolean;
}

export interface UseDropdownReturn {
  isOpen: boolean;
  toggle: () => void;
  open: () => void;
  close: () => void;
  dropdownRef: React.RefObject<HTMLDivElement>;
  activeSubmenu: string | null;
  setActiveSubmenu: (submenu: string | null) => void;
}

export const useDropdown = (options: UseDropdownOptions = {}): UseDropdownReturn => {
  const {
    closeOnOutsideClick = true,
    closeOnEscape = true,
    initialOpen = false,
  } = options;

  const [isOpen, setIsOpen] = useState(initialOpen);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const close = useCallback(() => {
    setIsOpen(false);
    setActiveSubmenu(null);
  }, []);

  const open = useCallback(() => {
    setIsOpen(true);
  }, []);

  const toggle = useCallback(() => {
    setIsOpen(prev => !prev);
    if (isOpen) {
      setActiveSubmenu(null);
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        closeOnOutsideClick &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        close();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (closeOnEscape && event.key === 'Escape') {
        close();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, close, closeOnOutsideClick, closeOnEscape]);

  // Close dropdown when component unmounts
  useEffect(() => {
    return () => {
      close();
    };
  }, [close]);

  return {
    isOpen,
    toggle,
    open,
    close,
    dropdownRef,
    activeSubmenu,
    setActiveSubmenu,
  };
};
