'use client';

import { useState } from 'react';
import Link from 'next/link';
import MomentCard from '@/components/MomentCard';
import CharacterCard from '@/components/CharacterCard';
import ProfileModeToggle from '@/components/ProfileModeToggle';
import OtherUserModeToggle from '@/components/OtherUserModeToggle';
import HeroSection from '@/components/HeroSection';
import TabNavigation from '@/components/TabNavigation';
import { Character } from '@/lib/mock-data';
import { User } from '@/lib/api';
import { ExternalLink, UserPlus, UserCheck, Edit3, Users, Heart, FileText, Sparkles, Eye, Link as LinkIcon } from 'lucide-react';
import Image from 'next/image';
import { useAuthContext } from '@/components/AuthProvider';
import { useTranslation } from '@/app/i18n/client';

// Icon wrapper to fix type compatibility
const iconWrapper = (IconComponent: any) => ({ className, size }: { className?: string; size?: number }) => (
  <IconComponent className={className} size={size} />
);

interface ProfileUidClientPageProps {
  lang: string;
  uid: string;
  userData: User | null;
  moments: any[];
  liked: any[];
  friendsCards: {
    character: Character;
    stats: { likes: number; friends: number; shares: number };
    aspectRatio: number;
  }[];
}

const TABS = ['moments', 'likes', 'friends'] as const;

const ProfileUidClientPage: React.FC<ProfileUidClientPageProps> = ({ 
  lang, 
  uid, 
  userData, 
  moments, 
  liked, 
  friendsCards 
}) => {
  const [activeTab, setActiveTab] = useState<typeof TABS[number]>('moments');
  const { user: currentUser, isAuthenticated } = useAuthContext();
  const { t } = useTranslation(lang, 'translation');
  
  // Check if this is the current user's own profile
  const isOwnProfile = isAuthenticated && currentUser?.uid === uid;
  
  // Use userData if available, otherwise use currentUser for own profile
  const displayUser = userData || (isOwnProfile ? currentUser : null);

  if (!displayUser) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{t('profile.errors.userNotFound')}</h2>
          <p className="text-gray-600 dark:text-gray-400">{t('profile.errors.userNotFoundDesc')}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Top profile cover - Now using optimized HeroSection */}
      <HeroSection
        backgroundImage="https://picsum.photos/1200/800?blur=3"
        title={displayUser.name || t('profile.anonymous')}
        description={displayUser.bio || displayUser.sign || t('profile.description.default')}
        avatar={displayUser.avatar || "https://i.pravatar.cc/160?u=profile"}
        avatarSize="lg"
        variant="profile"
        height="lg"
        stats={[
          { label: t('profile.header.followers'), value: displayUser.subscriber_count || 0, icon: iconWrapper(Users) },
          { label: t('profile.header.following'), value: displayUser.follow_count || 0, icon: iconWrapper(Heart) },
          { label: t('profile.header.moments'), value: moments.length, icon: iconWrapper(FileText) },
          { label: t('profile.header.memories'), value: liked.length, icon: iconWrapper(Sparkles) },
          { label: t('profile.header.liked'), value: '1.2K', icon: iconWrapper(Eye) },
          { label: t('profile.header.links'), value: '7', icon: iconWrapper(LinkIcon) }
        ]}
      >
        <div className="flex items-center gap-2">
          {isOwnProfile ? (
            <ProfileModeToggle lang={lang} currentPage="profile" uid={uid} size="md" className="flex-shrink-0" />
          ) : (
            <OtherUserModeToggle lang={lang} currentPage="profile" uid={uid} size="md" className="flex-shrink-0" />
          )}
        </div>
      </HeroSection>

      {/* Action buttons */}
      {!isOwnProfile && isAuthenticated && (
        <section className="bg-background border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="flex gap-3">
            <button className="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
              <UserPlus size={16} />
              {t('profile.actions.follow')}
            </button>
            <button className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              {t('profile.actions.message')}
            </button>
          </div>
        </section>
      )}

      {/* Tabs */}
      <TabNavigation
        tabs={[
          { id: 'moments', label: t('profile.tabs.moments'), icon: iconWrapper(FileText) },
          { id: 'likes', label: t('profile.tabs.likes'), icon: iconWrapper(Heart) },
          { id: 'friends', label: t('profile.tabs.friends'), icon: iconWrapper(Users) }
        ]}
        activeTab={activeTab}
        onTabChange={(tabId) => setActiveTab(tabId as typeof TABS[number])}
        variant="underline"
        sticky={true}
        stickyTop="top-12 lg:top-16"
        className="bg-background border-b border-border"
      />

      {/* Content */}
      <div className="bg-background p-1.5 md:p-2">
        {activeTab === 'moments' && (
          <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
            {moments.map((momentData, idx) => (
              <div key={`moment-${idx}`} className="break-inside-avoid">
                <MomentCard
                  lang={lang}
                  character={momentData.character}
                  moment={momentData.moment}
                  storyTemplateId={momentData.storyTemplateId}
                  stats={momentData.stats}
                  aspectRatio={momentData.aspectRatio}
                  publishedAt={momentData.publishedAt}
                />
              </div>
            ))}
          </div>
        )}

        {activeTab === 'likes' && (
          <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
            {liked.map((momentData, idx) => (
              <div key={`liked-${idx}`} className="break-inside-avoid">
                <MomentCard
                  lang={lang}
                  character={momentData.character}
                  moment={momentData.moment}
                  storyTemplateId={momentData.storyTemplateId}
                  stats={momentData.stats}
                  aspectRatio={momentData.aspectRatio}
                  publishedAt={momentData.publishedAt}
                />
              </div>
            ))}
          </div>
        )}

        {activeTab === 'friends' && (
          <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
            {friendsCards.map((card, idx) => (
              <div key={`friend-${idx}`} className="break-inside-avoid">
                <CharacterCard
                  lang={lang}
                  character={card.character}
                  stats={card.stats}
                  aspectRatio={card.aspectRatio}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default ProfileUidClientPage;
