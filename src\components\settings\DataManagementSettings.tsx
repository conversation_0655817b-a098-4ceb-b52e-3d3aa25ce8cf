'use client';

import React, { useState } from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Database, HardDrive, Cloud, Download, Trash2, Archive, RefreshCw, Settings, Shield, AlertTriangle } from 'lucide-react';
import SettingItem from './SettingItem';
import ToggleSwitch from './ToggleSwitch';
import <PERSON>Field from './SelectField';
import type { SettingsComponentProps } from '@/types/settings';

const DataManagementSettings: React.FC<SettingsComponentProps> = ({
  settings,
  updateSetting,
  lang,
  user,
  hasUnsavedChanges,
  isPremiumUser
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [showExportModal, setShowExportModal] = useState(false);
  const [showClearCacheModal, setShowClearCacheModal] = useState(false);
  const [exportOptions, setExportOptions] = useState({
    includeChats: true,
    includeCharacters: true,
    includeMemories: true,
    includeAchievements: true,
    includeSettings: true,
    format: 'json' as 'json' | 'csv' | 'pdf',
    dateRange: 'all' as 'all' | 'last_month' | 'last_year'
  });

  const backupFrequencyOptions = [
    { value: 'daily', label: t('settings.categories.dataManagement.backupFrequencyOptions.daily') },
    { value: 'weekly', label: t('settings.categories.dataManagement.backupFrequencyOptions.weekly') },
    { value: 'monthly', label: t('settings.categories.dataManagement.backupFrequencyOptions.monthly') }
  ];

  const handleExport = () => {
    // TODO: Implement actual export functionality
    console.log('Export requested with options:', exportOptions);
    setShowExportModal(false);
    // Show success message
    alert('Data export has been requested. You will receive an email when it\'s ready.');
  };

  const handleClearCache = () => {
    // TODO: Implement cache clearing
    console.log('Cache cleared');
    setShowClearCacheModal(false);
    updateSetting('dataManagement.cacheSize', 0);
    alert('Cache cleared successfully!');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 MB';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const ExportModal = () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-card rounded-lg p-6 max-w-md w-full space-y-4">
        <h3 className="text-lg font-semibold text-foreground">Export Personal Data</h3>
        
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-foreground mb-2">Include Data Types:</h4>
            <div className="space-y-2">
              {[
                { key: 'includeChats', label: 'Chat History' },
                { key: 'includeCharacters', label: 'Created Characters' },
                { key: 'includeMemories', label: 'Memory Capsules' },
                { key: 'includeAchievements', label: 'Achievements & Progress' },
                { key: 'includeSettings', label: 'Settings & Preferences' }
              ].map(({ key, label }) => (
                <label key={key} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={exportOptions[key as keyof typeof exportOptions]}
                    onChange={(e) => setExportOptions(prev => ({ 
                      ...prev, 
                      [key]: e.target.checked 
                    }))}
                    className="rounded border-border"
                  />
                  <span className="text-sm">{label}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-medium text-foreground mb-2">Format:</h4>
            <select
              value={exportOptions.format}
              onChange={(e) => setExportOptions(prev => ({ 
                ...prev, 
                format: e.target.value as 'json' | 'csv' | 'pdf'
              }))}
              className="w-full px-3 py-2 bg-background border border-border rounded-lg"
            >
              <option value="json">JSON</option>
              <option value="csv">CSV</option>
              <option value="pdf">PDF</option>
            </select>
          </div>

          <div>
            <h4 className="font-medium text-foreground mb-2">Date Range:</h4>
            <select
              value={exportOptions.dateRange}
              onChange={(e) => setExportOptions(prev => ({ 
                ...prev, 
                dateRange: e.target.value as 'all' | 'last_month' | 'last_year'
              }))}
              className="w-full px-3 py-2 bg-background border border-border rounded-lg"
            >
              <option value="all">All Time</option>
              <option value="last_year">Last Year</option>
              <option value="last_month">Last Month</option>
            </select>
          </div>
        </div>

        <div className="flex gap-2 justify-end">
          <button
            onClick={() => setShowExportModal(false)}
            className="px-4 py-2 text-foreground/70 hover:text-foreground transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleExport}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
          >
            Request Export
          </button>
        </div>
      </div>
    </div>
  );

  const ClearCacheModal = () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-card rounded-lg p-6 max-w-md w-full space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-yellow-500" />
          Clear Cache
        </h3>
        
        <div className="space-y-3">
          <p className="text-sm text-foreground/70">
            {t('settings.messages.confirmClearCache')}
          </p>
          
          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              Current cache size: {formatFileSize(settings.dataManagement.cacheSize * 1024 * 1024)}
            </p>
          </div>
        </div>

        <div className="flex gap-2 justify-end">
          <button
            onClick={() => setShowClearCacheModal(false)}
            className="px-4 py-2 text-foreground/70 hover:text-foreground transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleClearCache}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Clear Cache
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Storage Management */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <HardDrive className="w-4 h-4" />
          Storage Management
        </h4>

        <SettingItem
          label={t('settings.categories.dataManagement.clearCache')}
          description={t('settings.categories.dataManagement.clearCacheDesc')}
          showArrow
          onClick={() => setShowClearCacheModal(true)}
        >
          <span className="text-sm text-foreground/60">
            {formatFileSize(settings.dataManagement.cacheSize * 1024 * 1024)}
          </span>
        </SettingItem>

        <SettingItem
          label={t('settings.categories.dataManagement.storageOptimization')}
          description={t('settings.categories.dataManagement.storageOptimizationDesc')}
        >
          <ToggleSwitch
            checked={settings.dataManagement.storageOptimization}
            onChange={(checked) => updateSetting('dataManagement.storageOptimization', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.dataManagement.compressionEnabled')}
          description={t('settings.categories.dataManagement.compressionEnabledDesc')}
        >
          <ToggleSwitch
            checked={settings.dataManagement.compressionEnabled}
            onChange={(checked) => updateSetting('dataManagement.compressionEnabled', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.dataManagement.autoCleanup')}
          description={t('settings.categories.dataManagement.autoCleanupDesc')}
        >
          <ToggleSwitch
            checked={settings.dataManagement.autoCleanup}
            onChange={(checked) => updateSetting('dataManagement.autoCleanup', checked)}
          />
        </SettingItem>
      </div>

      {/* Backup & Sync */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Cloud className="w-4 h-4" />
          Backup & Sync
        </h4>

        <SettingItem
          label={t('settings.categories.dataManagement.autoMemoryBackup')}
          description={t('settings.categories.dataManagement.autoMemoryBackupDesc')}
        >
          <ToggleSwitch
            checked={settings.dataManagement.autoMemoryBackup}
            onChange={(checked) => updateSetting('dataManagement.autoMemoryBackup', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.dataManagement.backupFrequency')}
          description={t('settings.categories.dataManagement.backupFrequencyDesc')}
        >
          <SelectField
            value={settings.dataManagement.backupFrequency}
            onChange={(value) => updateSetting('dataManagement.backupFrequency', value)}
            options={backupFrequencyOptions}
            className="w-32"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.dataManagement.cloudSyncEnabled')}
          description={t('settings.categories.dataManagement.cloudSyncEnabledDesc')}
        >
          <ToggleSwitch
            checked={settings.dataManagement.cloudSyncEnabled}
            onChange={(checked) => updateSetting('dataManagement.cloudSyncEnabled', checked)}
          />
        </SettingItem>
      </div>

      {/* Data Limits */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Settings className="w-4 h-4" />
          Data Limits
        </h4>

        <SettingItem
          label={t('settings.categories.dataManagement.localStorageLimit')}
          description={t('settings.categories.dataManagement.localStorageLimitDesc')}
        >
          <div className="flex items-center gap-2">
            <input
              type="number"
              value={settings.dataManagement.localStorageLimit}
              onChange={(e) => updateSetting('dataManagement.localStorageLimit', parseInt(e.target.value))}
              min="100"
              max="10000"
              className="w-20 px-2 py-1 bg-background border border-border rounded text-sm"
            />
            <span className="text-sm text-foreground/60">MB</span>
          </div>
        </SettingItem>

        <SettingItem
          label={t('settings.categories.dataManagement.chatHistoryLimit')}
          description={t('settings.categories.dataManagement.chatHistoryLimitDesc')}
        >
          <div className="flex items-center gap-2">
            <input
              type="number"
              value={settings.dataManagement.chatHistoryLimit}
              onChange={(e) => updateSetting('dataManagement.chatHistoryLimit', parseInt(e.target.value))}
              min="1000"
              max="100000"
              step="1000"
              className="w-24 px-2 py-1 bg-background border border-border rounded text-sm"
            />
            <span className="text-sm text-foreground/60">messages</span>
          </div>
        </SettingItem>

        <SettingItem
          label={t('settings.categories.dataManagement.dataRetentionPeriod')}
          description={t('settings.categories.dataManagement.dataRetentionPeriodDesc')}
        >
          <div className="flex items-center gap-2">
            <input
              type="number"
              value={settings.dataManagement.dataRetentionPeriod}
              onChange={(e) => updateSetting('dataManagement.dataRetentionPeriod', parseInt(e.target.value))}
              min="30"
              max="1095"
              className="w-20 px-2 py-1 bg-background border border-border rounded text-sm"
            />
            <span className="text-sm text-foreground/60">days</span>
          </div>
        </SettingItem>
      </div>

      {/* Data Export */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Download className="w-4 h-4" />
          Data Export
        </h4>

        <SettingItem
          label={t('settings.categories.dataManagement.exportPersonalData')}
          description={t('settings.categories.dataManagement.exportPersonalDataDesc')}
          showArrow
          onClick={() => setShowExportModal(true)}
        >
          <Download className="w-4 h-4 text-foreground/60" />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.dataManagement.downloadHistory')}
          description={t('settings.categories.dataManagement.downloadHistoryDesc')}
        >
          <ToggleSwitch
            checked={settings.dataManagement.downloadHistory}
            onChange={(checked) => updateSetting('dataManagement.downloadHistory', checked)}
          />
        </SettingItem>

        <SettingItem
          label="Export Data Consent"
          description="Consent to export personal data for backup purposes"
        >
          <ToggleSwitch
            checked={settings.dataManagement.exportDataConsent}
            onChange={(checked) => updateSetting('dataManagement.exportDataConsent', checked)}
          />
        </SettingItem>
      </div>

      {/* Storage Statistics */}
      <div className="mt-6 p-4 bg-card/50 border border-border rounded-lg">
        <h5 className="font-medium text-foreground flex items-center gap-2 mb-3">
          <Database className="w-4 h-4" />
          Storage Statistics
        </h5>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-foreground/70">Cache Size</p>
            <p className="font-medium">{formatFileSize(settings.dataManagement.cacheSize * 1024 * 1024)}</p>
          </div>
          <div>
            <p className="text-foreground/70">Storage Limit</p>
            <p className="font-medium">{settings.dataManagement.localStorageLimit} MB</p>
          </div>
          <div>
            <p className="text-foreground/70">Chat History</p>
            <p className="font-medium">{settings.dataManagement.chatHistoryLimit.toLocaleString()} messages</p>
          </div>
          <div>
            <p className="text-foreground/70">Data Retention</p>
            <p className="font-medium">{settings.dataManagement.dataRetentionPeriod} days</p>
          </div>
        </div>
      </div>

      {/* Privacy Notice */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <h5 className="font-medium text-blue-800 dark:text-blue-200 flex items-center gap-2 mb-2">
          <Shield className="w-4 h-4" />
          Privacy & Data Protection
        </h5>
        <p className="text-sm text-blue-700 dark:text-blue-300">
          All your data is encrypted and stored securely. We never share your personal information with third parties without your explicit consent. You have full control over your data and can export or delete it at any time.
        </p>
      </div>

      {/* Modals */}
      {showExportModal && <ExportModal />}
      {showClearCacheModal && <ClearCacheModal />}
    </div>
  );
};

export default DataManagementSettings; 