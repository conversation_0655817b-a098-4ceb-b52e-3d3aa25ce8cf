{"api_version": "2.0.0", "description": "API specification updates for enhanced PostgreSQL schema support", "base_url": "/api/v2", "new_endpoints": {"/characters/{id}/localizations": {"get": {"summary": "Get character data in specific language", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "language", "in": "query", "required": false, "schema": {"type": "string", "enum": ["zh-CN", "ja-<PERSON>", "en-US"], "default": "en-US"}}], "responses": {"200": {"description": "Character localization data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterLocalization"}}}}}}, "put": {"summary": "Update character localization data", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterLocalizationUpdate"}}}}, "responses": {"200": {"description": "Character localization updated successfully"}}}}, "/characters/{id}/appearance": {"get": {"summary": "Get detailed character appearance data", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Character appearance details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterAppearanceDetails"}}}}}}}, "/characters/{id}/personality": {"get": {"summary": "Get character personality profile", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Character personality profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterPersonalityProfile"}}}}}}}, "/characters/{id}/stats": {"get": {"summary": "Get character statistics for CharacterCard", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Character statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterStats"}}}}}}}, "/stories/{id}/localizations": {"get": {"summary": "Get story data in specific language", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "language", "in": "query", "required": false, "schema": {"type": "string", "enum": ["zh-CN", "ja-<PERSON>", "en-US"], "default": "en-US"}}], "responses": {"200": {"description": "Story localization data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoryLocalization"}}}}}}}, "/stories/{id}/scenes": {"get": {"summary": "Get story scenes with hierarchical information", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "language", "in": "query", "required": false, "schema": {"type": "string", "enum": ["zh-CN", "ja-<PERSON>", "en-US"], "default": "en-US"}}], "responses": {"200": {"description": "Story scenes with hierarchical data", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SceneHierarchicalInfo"}}}}}}}}, "/users/{id}/dashboard": {"get": {"summary": "Get comprehensive user dashboard data", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User dashboard data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDashboardData"}}}}}}}, "/users/{id}/currencies": {"get": {"summary": "Get user currency balances for CurrencyCard", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User currency balances", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCurrencies"}}}}}}, "post": {"summary": "Update user currency balance", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyTransaction"}}}}, "responses": {"200": {"description": "Currency updated successfully"}}}}, "/users/{id}/achievements": {"get": {"summary": "Get user achievements for TrophyCard", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["unlocked", "ready_to_unlock", "in_progress", "not_started"]}}, {"name": "rarity", "in": "query", "required": false, "schema": {"type": "string", "enum": ["bronze", "silver", "gold", "platinum", "diamond"]}}], "responses": {"200": {"description": "User achievements", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AchievementProgress"}}}}}}}}, "/users/{id}/memorial-events": {"get": {"summary": "Get user memorial events for MemorialCard", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["today", "this_week", "this_month", "future"]}}], "responses": {"200": {"description": "User memorial events", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MemorialEvent"}}}}}}}}, "/users/{id}/daily-missions": {"get": {"summary": "Get user daily missions progress", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "date", "in": "query", "required": false, "schema": {"type": "string", "format": "date", "default": "today"}}], "responses": {"200": {"description": "Daily missions progress", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DailyMissionProgress"}}}}}}}}, "/store/products": {"get": {"summary": "Get store products for FeaturedCard and store display", "parameters": [{"name": "category", "in": "query", "required": false, "schema": {"type": "string", "enum": ["featured", "memberships", "welcome", "arts", "memorial", "currency", "mindfuel"]}}, {"name": "featured_only", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}, {"name": "first_time_only", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Store products", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StoreProduct"}}}}}}}}, "/store/products/{id}/stats": {"get": {"summary": "Get store product statistics", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Product statistics", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreProductStats"}}}}}}}, "/moments/stats": {"get": {"summary": "Get moments with statistics for MomentCard", "parameters": [{"name": "user_id", "in": "query", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "character_id", "in": "query", "required": false, "schema": {"type": "string", "format": "uuid"}}, {"name": "visibility", "in": "query", "required": false, "schema": {"type": "string", "enum": ["public", "followers", "private"]}}, {"name": "featured_only", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "Moments with statistics", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MomentStats"}}}}}}}}}, "components": {"schemas": {"CharacterLocalization": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "character_id": {"type": "string", "format": "uuid"}, "language_code": {"type": "string", "enum": ["zh-CN", "ja-<PERSON>", "en-US"]}, "name": {"type": "object", "properties": {"full_name": {"type": "string"}, "name_origin": {"type": "string"}}}, "background": {"type": "string"}, "personality": {"type": "string"}, "appear_hierarchical_info": {"type": "object", "properties": {"first_layer_core_identity": {"type": "object"}, "second_layer_presentation_style": {"type": "object"}, "third_layer_behavioral_dynamics": {"type": "object"}}}, "ext_hierarchical_info": {"type": "object"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "CharacterAppearanceDetails": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "character_id": {"type": "string", "format": "uuid"}, "physique": {"type": "object"}, "stature": {"type": "object"}, "body_ratio": {"type": "object"}, "face": {"type": "object"}, "hair": {"type": "object"}, "eyes": {"type": "object"}, "base_species": {"type": "object"}, "wardrobe": {"type": "object"}, "garments": {"type": "object"}, "footwear": {"type": "object"}, "adornments": {"type": "object"}, "gear": {"type": "object"}, "handheld_items": {"type": "object"}, "expression_engine": {"type": "object"}, "kinesics": {"type": "object"}, "vocalization": {"type": "object"}}}, "CharacterPersonalityProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "character_id": {"type": "string", "format": "uuid"}, "species": {"type": "string", "default": "Human"}, "vocation": {"type": "string"}, "social_role": {"type": "string"}, "archetypal_story": {"type": "string"}, "core_need": {"type": "string"}, "core_fear": {"type": "string"}, "core_virtue": {"type": "string"}, "openness_score": {"type": "integer", "minimum": 1, "maximum": 100}, "conscientiousness_score": {"type": "integer", "minimum": 1, "maximum": 100}, "extraversion_score": {"type": "integer", "minimum": 1, "maximum": 100}, "agreeableness_score": {"type": "integer", "minimum": 1, "maximum": 100}, "neuroticism_score": {"type": "integer", "minimum": 1, "maximum": 100}, "emotional_baseline": {"type": "string"}, "emotional_range": {"type": "string"}, "emotional_triggers": {"type": "array", "items": {"type": "string"}}, "coping_mechanisms": {"type": "array", "items": {"type": "string"}}, "empathy_level": {"type": "string"}, "emotional_expression": {"type": "string"}, "personality_details": {"type": "object"}, "cognitive_details": {"type": "object"}, "emotional_details": {"type": "object"}}}, "CharacterStats": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "creator_id": {"type": "string", "format": "uuid"}, "avatar_url": {"type": "string"}, "mbti_type": {"type": "string"}, "era": {"type": "string"}, "region": {"type": "string"}, "chats_count": {"type": "integer"}, "unique_chatters": {"type": "integer"}, "likes_count": {"type": "integer"}, "friends_count": {"type": "integer"}, "moments_count": {"type": "integer"}, "stories_count": {"type": "integer"}, "total_engagement": {"type": "integer"}, "avg_rating": {"type": "number", "format": "float"}, "rating_count": {"type": "integer"}, "creator_username": {"type": "string"}, "creator_display_name": {"type": "string"}, "creator_avatar": {"type": "string"}, "last_chat_activity": {"type": "string", "format": "date-time"}, "last_moment_created": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "StoryLocalization": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "story_id": {"type": "string", "format": "uuid"}, "language_code": {"type": "string", "enum": ["zh-CN", "ja-<PERSON>", "en-US"]}, "story_name": {"type": "string"}, "story_rating": {"type": "string"}, "story_core_world": {"type": "object", "properties": {"core_concept": {"type": "string"}, "synopsis": {"type": "string"}, "core_constraints": {"type": "string"}, "core_goal": {"type": "string"}, "character_chronicle": {"type": "string"}}}, "story_narrative_style": {"type": "object", "properties": {"narrative_structure": {"type": "string"}, "information_disclosure": {"type": "string"}, "narrative_style": {"type": "string"}, "character_arc": {"type": "string"}, "plot_pacing": {"type": "string"}}}, "story_beats": {"type": "array", "items": {"type": "object", "properties": {"scene_index": {"type": "integer"}, "scene_name": {"type": "string"}, "scene_description": {"type": "string"}, "scene_start": {"type": "string"}, "scene_conflict": {"type": "string"}, "scene_turning_point": {"type": "string"}, "scene_character_arc": {"type": "string"}, "scene_relationship_development": {"type": "string"}, "scene_target": {"type": "string"}, "scene_ending": {"type": "string"}}}}}}, "SceneHierarchicalInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "story_id": {"type": "string", "format": "uuid"}, "scene_index": {"type": "integer"}, "language_code": {"type": "string", "enum": ["zh-CN", "ja-<PERSON>", "en-US"]}, "scene_name": {"type": "string"}, "scene_description": {"type": "string"}, "first_layer_worldview": {"type": "object", "properties": {"time_period": {"type": "string"}, "geographical_location": {"type": "string"}, "cultural_context": {"type": "string"}, "social_environment": {"type": "string"}}}, "second_layer_scene": {"type": "object", "properties": {"physical_setting": {"type": "string"}, "atmosphere": {"type": "string"}, "sensory_details": {"type": "string"}, "environmental_factors": {"type": "string"}}}, "third_layer_antecedent": {"type": "object", "properties": {"prior_events": {"type": "string"}, "character_state": {"type": "string"}, "emotional_context": {"type": "string"}, "relationship_dynamics": {"type": "string"}}}, "fourth_layer_character": {"type": "object", "properties": {"character_goals": {"type": "string"}, "internal_conflicts": {"type": "string"}, "emotional_arc": {"type": "string"}, "behavioral_patterns": {"type": "string"}}}}}}}}