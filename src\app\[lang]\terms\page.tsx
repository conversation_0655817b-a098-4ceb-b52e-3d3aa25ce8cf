'use client';

import { useParams } from 'next/navigation';
import MainAppLayout from '@/components/MainAppLayout';

export default function TermsPage() {
  const { lang } = useParams() as { lang: string };

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-white dark:bg-black text-black dark:text-white">
        <div className="max-w-4xl mx-auto p-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-2">Terms of Use</h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">Last updated: December 2024</p>
          </div>

          <div className="space-y-8">
            <section>
              <h2 className="text-xl font-bold mb-4">1. Acceptance of Terms</h2>
              <p className="mb-4">
                By accessing or using Alphane AI ("Service"), you agree to be bound by these Terms of Use.
                If you disagree with any part of these terms, you may not access the Service.
              </p>
              <p>
                These Terms constitute a legally binding agreement between you and Alphane AI regarding your use of our AI virtual character and story platform.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-bold mb-4">2. User Eligibility and Accounts</h2>
              <p className="mb-4">
                You must be at least 13 years old to use this Service. Users under 18 require parental consent.
              </p>
              <p className="mb-4">
                You are responsible for maintaining the security of your account and all activities that occur under your account.
                You must provide accurate registration information and notify us immediately of any unauthorized use.
              </p>
              <p>
                We reserve the right to suspend or terminate accounts that violate these Terms.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-bold mb-4">3. AI Virtual Characters and Content Policy</h2>
              <p className="mb-4">
                Our Service allows you to create, interact with, and share AI virtual characters and stories. You may use the Service for personal entertainment and creative expression.
              </p>
              <p className="mb-4">
                <strong>Prohibited Content:</strong> You may not create or share content that is illegal, harmful, harassing, abusive, sexually explicit, violent, hateful, or infringes on intellectual property rights.
                Characters and stories must comply with applicable laws and community standards.
              </p>
              <p className="mb-4">
                <strong>Content Moderation:</strong> We reserve the right to review, remove, or modify any content that violates these Terms.
                Repeated violations may result in account suspension or termination.
              </p>
              <p>
                <strong>AI-Generated Content:</strong> All AI-generated content is provided for entertainment purposes only.
                We do not guarantee the accuracy, appropriateness, or safety of AI-generated responses.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-bold mb-4">4. Intellectual Property</h2>
              <p className="mb-4">
                The Service and its original content, features, and functionality are owned by Alphane AI and protected by intellectual property laws.
              </p>
              <p className="mb-4">
                You retain ownership of content you create, but grant us a license to use, display, and distribute your content through the Service.
                You represent that you own or have rights to submit your content.
              </p>
              <p>
                We respect intellectual property rights and comply with the Digital Millennium Copyright Act.
                If you believe your work has been infringed, please contact <NAME_EMAIL>.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-bold mb-4">5. Service Limitations and Disclaimers</h2>
              <p className="mb-4">
                The Service is provided "as is" without warranties of any kind. We do not guarantee continuous availability and may modify or discontinue features at any time.
              </p>
              <p className="mb-4">
                AI-generated content is for entertainment purposes only. We do not endorse or guarantee the accuracy of AI responses or user-generated content.
              </p>
              <p>
                TO THE MAXIMUM EXTENT PERMITTED BY LAW, ALPHANE AI SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-bold mb-4">6. Subscription and Payment</h2>
              <p className="mb-4">
                Free and paid subscription plans are available. Subscription fees are billed in advance and are generally non-refundable except as required by law.
              </p>
              <p>
                You may cancel your subscription at any time. We may change subscription fees with 30 days notice.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-bold mb-4">7. Governing Law</h2>
              <p className="mb-4">
                These Terms are governed by the laws of the State of Delaware. Any disputes shall be resolved through binding arbitration.
              </p>
              <p>
                You waive the right to participate in class action lawsuits.
              </p>
            </section>

            <section>
              <h2 className="text-xl font-bold mb-4">8. Contact Information</h2>
              <p className="mb-4">If you have questions about these Terms, contact us at:</p>
              <p><strong>Email:</strong> <EMAIL></p>
            </section>

            <section>
              <h2 className="text-xl font-bold mb-4">9. Changes to Terms</h2>
              <p>
                We may modify these Terms at any time. Continued use of the Service after changes constitutes acceptance of the new Terms.
              </p>
            </section>
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
} 