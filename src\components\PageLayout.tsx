'use client';

import React from 'react';

interface PageLayoutProps {
  title: string;
  children: React.ReactNode;
  headerContent?: React.ReactNode; // Optional: For buttons or other content next to the title
}

const PageLayout: React.FC<PageLayoutProps> = ({ title, children, headerContent }) => {
  return (
    <div className='min-h-screen py-8 px-4 theme-transition'>
      <div className='max-w-4xl mx-auto'> {/* This provides the consistent centered column */}
        <header className='mb-8 flex flex-col sm:flex-row justify-between items-center'>
          <h1 className='text-3xl md:text-4xl font-semibold text-foreground'>{title}</h1>
          {headerContent && <div className='mt-4 sm:mt-0'>{headerContent}</div>}
        </header>
        <main>{children}</main>
      </div>
    </div>
  );
};

export default PageLayout;
