'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/components/AuthProvider';
import { useTranslation } from '@/app/i18n/client';

interface CreatorProfileRedirectPageProps {
  lang: string;
}

const CreatorProfileRedirectPage: React.FC<CreatorProfileRedirectPageProps> = ({ lang }) => {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useAuthContext();
  const { t } = useTranslation(lang, 'translation');

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated && user?.uid) {
        // Redirect to the user's UID-based creator profile
        router.replace(`/${lang}/creator-profile/${user.uid}`);
      } else {
        // Redirect to auth page if not authenticated
        router.replace(`/${lang}/auth`);
      }
    }
  }, [isAuthenticated, user, isLoading, lang, router]);

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">{t('creatorProfile.redirect.message')}</p>
      </div>
    </div>
  );
};

export default CreatorProfileRedirectPage;
