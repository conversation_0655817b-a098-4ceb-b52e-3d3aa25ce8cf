# 角色创建页面组件拆分方案

## 概述

原始的 `CreateCharacterClientPage.tsx` 文件包含近3000行代码，功能复杂，难以维护。通过系统性的拆分，我们将其重构为多个独立、可复用的组件，提高代码的可维护性和复用性。

## 拆分原则

1. **单一职责原则**：每个组件只负责一个特定功能
2. **可复用性**：组件设计通用化，可在其他地方使用
3. **关注点分离**：UI、逻辑、数据处理分离
4. **类型安全**：使用TypeScript确保类型安全

## 拆分结构

### 1. 类型定义 (`src/types/character.ts`)

将所有角色创建相关的类型定义集中管理：

- `SelectedFunction`: 功能选择类型
- `Step`: 创建步骤类型  
- `CharacterFormData`: 表单数据类型
- `FlashGenerationResult`: Flash生成结果类型
- `StoryboardChapter`: 故事板章节类型
- `AvatarCropData`: 头像裁剪数据类型

**优势**：
- 类型定义集中管理，避免重复
- 便于维护和更新
- 提供更好的代码提示

### 2. 自定义Hook (`src/hooks/useCharacterCreation.ts`)

将所有状态管理和业务逻辑抽取到自定义Hook中：

**管理的状态**：
- 功能选择状态
- 表单数据
- 图片相关状态
- Flash生成状态
- 导入状态

**提供的方法**：
- 步骤导航（nextStep, prevStep）
- 步骤验证（isStepComplete）
- Flash生成（handleOneClickGenerate）
- 图片处理（handleAvatarCropComplete, handleCharacterImageCropComplete）
- 表单提交（handleSubmit）

**优势**：
- 逻辑复用：可在其他组件中使用
- 易于测试：逻辑与UI分离
- 代码组织：相关逻辑集中管理

### 3. UI组件拆分

#### 3.1 功能选择组件 (`src/components/character-creation/FunctionSelector.tsx`)

负责显示Flash、Customize、Import三个功能选择卡。

**Props**：
- `selectedFunction`: 当前选中的功能
- `onFunctionSelect`: 功能选择回调

**复用场景**：
- 角色创建页面
- 其他需要功能选择的场景

#### 3.2 步骤指示器组件 (`src/components/character-creation/StepIndicator.tsx`)

通用的步骤指示器，支持桌面端和移动端不同展示。

**Props**：
- `steps`: 步骤配置数组
- `currentStep`: 当前步骤
- `onStepChange`: 步骤切换回调
- `isStepComplete`: 步骤完成状态检查

**复用场景**：
- 角色创建流程
- 其他多步骤流程
- 向导式界面

#### 3.3 图片上传组件 (`src/components/character-creation/ImageUpload.tsx`)

通用的图片上传组件，支持拖拽、预览、验证。

**特性**：
- 拖拽上传支持
- 文件类型和大小验证
- 实时预览
- 自定义比例支持
- 错误处理

**Props**：
- `id`, `label`, `description`: 基础属性
- `preview`: 预览图片URL
- `onUpload`: 上传回调
- `aspectRatio`: 期望比例
- `maxSizeMB`: 最大文件大小

**复用场景**：
- 角色立绘上传
- 头像上传
- 其他图片上传需求

#### 3.4 性格标签组件 (`src/components/character-creation/PersonalityTags.tsx`)

性格标签选择组件，支持预设标签和自定义添加。

**特性**：
- 预设标签快速选择
- 自定义标签添加
- 标签数量限制
- 实时验证和提示

**Props**：
- `selectedTags`: 已选择的标签
- `onTagsChange`: 标签变更回调
- `maxTags`: 最大标签数
- `presetTags`: 预设标签列表

**复用场景**：
- 角色性格设置
- 内容标签选择
- 其他标签选择场景

#### 3.5 文件上传组件 (`src/components/character-creation/FileUpload.tsx`)

通用的文件上传组件，支持多文件、多格式。

**特性**：
- 多文件上传
- 文件类型验证
- 文件大小检查
- 拖拽支持
- 文件列表管理

**Props**：
- `files`: 已上传文件列表
- `onFilesChange`: 文件变更回调
- `accept`: 允许的文件类型
- `maxFiles`: 最大文件数
- `supportedFormats`: 支持的格式列表

**复用场景**：
- 世界书文件上传
- 脚本文件上传
- 其他文档上传需求

#### 3.6 Flash生成组件 (`src/components/character-creation/FlashGenerator.tsx`)

Flash一键生成功能的完整UI组件。

**特性**：
- 提示词输入
- 随机生成按钮
- 生成结果预览
- 重新生成功能
- 复制功能

**Props**：
- `prompt`: 输入提示词
- `result`: 生成结果
- `isGenerating`: 生成状态
- `onGenerate`: 生成回调
- `onAccept`: 接受结果回调

### 4. 步骤组件拆分

每个创建步骤可以进一步拆分为独立组件：

- `EssentialsStep.tsx`: 基础信息步骤
- `BehaviorStep.tsx`: 行为设置步骤  
- `FinalizeStep.tsx`: 最终确认步骤

### 5. 主页面重构

重构后的主页面 (`CreateCharacterClientPage.tsx`) 将变得非常简洁：

```tsx
const CreateCharacterClientPage: FC<CreateCharacterClientPageProps> = ({ lang }) => {
  const {
    selectedFunction,
    setSelectedFunction,
    // ... 其他状态和方法
  } = useCharacterCreation(lang);

  return (
    <MainAppLayout lang={lang}>
      <div className="max-w-4xl mx-auto p-4 space-y-6">
        <FunctionSelector 
          selectedFunction={selectedFunction}
          onFunctionSelect={setSelectedFunction}
        />
        
        {selectedFunction === 'flash' && (
          <FlashGenerator {...flashProps} />
        )}
        
        {selectedFunction === 'customize' && (
          <CustomizeFlow {...customizeProps} />
        )}
        
        {selectedFunction === 'import' && (
          <ImportFlow {...importProps} />
        )}
      </div>
    </MainAppLayout>
  );
};
```

## 使用示例

### 在其他地方复用组件

```tsx
// 在其他页面使用图片上传组件
<ImageUpload
  id="profile-image"
  label="个人头像"
  description="(聊天界面显示)"
  preview={avatarPreview}
  aspectRatio="1:1"
  onUpload={handleAvatarUpload}
  required
/>

// 使用性格标签组件
<PersonalityTags
  selectedTags={personality}
  onTagsChange={setPersonality}
  maxTags={8}
/>

// 使用文件上传组件
<FileUpload
  id="knowledge-files"
  label="知识库文件"
  files={knowledgeFiles}
  onFilesChange={setKnowledgeFiles}
  supportedFormats={['.txt', '.md', '.json']}
  maxFiles={5}
/>
```

## 优势总结

### 1. 代码组织
- **模块化**：功能明确的独立模块
- **可读性**：代码结构清晰，易于理解
- **可维护性**：修改影响范围小，易于定位问题

### 2. 复用性
- **组件复用**：通用组件可在多处使用
- **逻辑复用**：自定义Hook可跨组件共享
- **类型复用**：统一的类型定义

### 3. 开发效率
- **并行开发**：不同开发者可同时开发不同组件
- **测试便利**：小组件易于编写单元测试
- **调试方便**：问题定位更精确

### 4. 扩展性
- **新功能添加**：新组件易于集成
- **功能修改**：改动影响范围可控
- **版本迭代**：渐进式重构和升级

## 迁移建议

### 阶段1：基础拆分
1. 创建类型定义文件
2. 抽取自定义Hook
3. 拆分基础UI组件

### 阶段2：功能模块化
1. 拆分各功能区块组件
2. 重构主页面
3. 测试功能完整性

### 阶段3：优化完善
1. 添加单元测试
2. 性能优化
3. 文档完善

## 后续扩展

基于这个拆分架构，未来可以轻松扩展：

1. **新的创建方式**：添加新的功能选项
2. **模板系统**：预设角色模板
3. **高级编辑器**：更复杂的编辑功能
4. **实时预览**：边编辑边预览
5. **协作功能**：多人协作创建角色

这个拆分方案不仅解决了当前代码的复杂性问题，还为未来的功能扩展奠定了良好的基础。 