import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Brain, Search, Plus } from 'lucide-react';
import Link from 'next/link';

interface MemoryEmptyStateProps {
  hasFilters: boolean;
  lang: string;
}

const MemoryEmptyState: React.FC<MemoryEmptyStateProps> = ({ hasFilters, lang }) => {
  const { t: _ } = useTranslation(lang, 'translation');

  if (hasFilters) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
          <Search className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {_('memory.empty.noResults', 'No matching memories found')}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
          {_('memory.empty.tryDifferent', 'Try adjusting your filters or using different search keywords')}
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center py-16">
      <div className="relative mb-6">
        <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-full flex items-center justify-center">
          <Brain className="w-12 h-12 text-purple-600 dark:text-purple-400" />
        </div>
        <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce">
          <Plus className="w-5 h-5 text-white" />
        </div>
      </div>
      
      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
        {_('memory.empty.title', 'Create Your First Memory Capsule')}
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 text-center max-w-md mb-8">
        {_('memory.empty.description', 'Permanently preserve beautiful conversations with your AI companions, treasuring every important moment. Memory capsules help AI better understand and remember you.')}
      </p>

      <div className="flex flex-col sm:flex-row gap-4">
        <Link
          href={`/${lang}/chats`}
          className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
        >
          <Plus size={20} />
          {_('memory.empty.goChat', 'Start Chatting to Create Memories')}
        </Link>
        
        <button className="px-6 py-3 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-colors">
          {_('memory.empty.learnMore', 'Learn About Memory Capsules')}
        </button>
      </div>

      {/* Feature Highlights */}
      <div className="mt-12 grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-3xl">
        <div className="text-center">
          <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-lg flex items-center justify-center mx-auto mb-3">
            <span className="text-2xl">🎯</span>
          </div>
          <h4 className="font-medium text-gray-900 dark:text-white mb-1">
            {_('memory.feature.smart', 'Smart Retrieval')}
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {_('memory.feature.smartDesc', 'AI automatically references relevant memories')}
          </p>
        </div>

        <div className="text-center">
          <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-lg flex items-center justify-center mx-auto mb-3">
            <span className="text-2xl">🎨</span>
          </div>
          <h4 className="font-medium text-gray-900 dark:text-white mb-1">
            {_('memory.feature.art', 'Memory Art')}
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {_('memory.feature.artDesc', 'Transform memories into beautiful artwork')}
          </p>
        </div>

        <div className="text-center">
          <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-lg flex items-center justify-center mx-auto mb-3">
            <span className="text-2xl">💝</span>
          </div>
          <h4 className="font-medium text-gray-900 dark:text-white mb-1">
            {_('memory.feature.bond', 'Deepen Bonds')}
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {_('memory.feature.bondDesc', 'Help AI companions understand you better')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default MemoryEmptyState; 