-- Enhanced PostgreSQL Schema Additions for Alphane AI Platform
-- This file contains all missing tables and enhancements needed for full frontend-backend integration

-- ============================================================================
-- 1. CHAT SYSTEM - Complete conversation tracking with emotional context
-- ============================================================================

CREATE TABLE IF NOT EXISTS chat_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    story_id UUID REFERENCES stories(id) ON DELETE SET NULL,
    chapter_id INTEGER,
    session_title VARCHAR(200),
    session_type VARCHAR(50) DEFAULT 'chat' CHECK (session_type IN ('chat', 'story', 'roleplay')),
    message_count INTEGER DEFAULT 0,
    last_message_content TEXT,
    last_message_at TIMESTAMP,
    emotional_context JSONB DEFAULT '{}',
    session_metadata JSONB DEFAULT '{}',
    is_archived BOOLEAN DEFAULT false,
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_chat_sessions_user_character ON chat_sessions(user_id, character_id);
CREATE INDEX idx_chat_sessions_last_activity ON chat_sessions(last_message_at DESC);
CREATE INDEX idx_chat_sessions_type ON chat_sessions(session_type);
CREATE INDEX idx_chat_sessions_archived ON chat_sessions(is_archived);

CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    sender_type VARCHAR(20) NOT NULL CHECK (sender_type IN ('user', 'character', 'system')),
    content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'audio', 'action', 'system')),
    metadata JSONB DEFAULT '{}',
    emotional_tone VARCHAR(50),
    relationship_impact DECIMAL(3,2),
    memory_triggers TEXT[],
    character_state JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_chat_messages_session_created ON chat_messages(session_id, created_at);
CREATE INDEX idx_chat_messages_sender_type ON chat_messages(sender_type);
CREATE INDEX idx_chat_messages_emotional_tone ON chat_messages(emotional_tone);
CREATE INDEX idx_chat_messages_memory_triggers_gin ON chat_messages USING GIN(memory_triggers);

-- ============================================================================
-- 2. CHARACTER MEMORY SYSTEM - Advanced memory management with emotional context
-- ============================================================================

CREATE TABLE IF NOT EXISTS character_memories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    memory_type VARCHAR(50) NOT NULL CHECK (memory_type IN ('fact', 'emotion', 'preference', 'event', 'relationship', 'conversation')),
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    importance_score DECIMAL(3,2) DEFAULT 5.0 CHECK (importance_score >= 0 AND importance_score <= 10),
    emotional_context JSONB DEFAULT '{}',
    context_tags TEXT[] DEFAULT '{}',
    source_type VARCHAR(50) DEFAULT 'conversation',
    source_id UUID,
    association_strength DECIMAL(3,2) DEFAULT 5.0,
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP,
    expires_at TIMESTAMP,
    is_core_memory BOOLEAN DEFAULT false,
    memory_hierarchy_level INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_character_memories_character_user ON character_memories(character_id, user_id);
CREATE INDEX idx_character_memories_type ON character_memories(memory_type);
CREATE INDEX idx_character_memories_importance ON character_memories(importance_score DESC);
CREATE INDEX idx_character_memories_tags_gin ON character_memories USING GIN(context_tags);
CREATE INDEX idx_character_memories_expires ON character_memories(expires_at);

CREATE TABLE IF NOT EXISTS memory_associations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    source_memory_id UUID NOT NULL REFERENCES character_memories(id) ON DELETE CASCADE,
    target_memory_id UUID NOT NULL REFERENCES character_memories(id) ON DELETE CASCADE,
    association_type VARCHAR(50) NOT NULL,
    association_strength DECIMAL(3,2) DEFAULT 5.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_memory_associations_unique ON memory_associations(source_memory_id, target_memory_id, association_type);
CREATE INDEX idx_memory_associations_target ON memory_associations(target_memory_id);

-- ============================================================================
-- 3. RELATIONSHIP TRACKING - Multi-metric relationship system
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_character_relationships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    relationship_level INTEGER DEFAULT 1 CHECK (relationship_level >= 1 AND relationship_level <= 100),
    relationship_type VARCHAR(50) DEFAULT 'neutral',
    trust_score DECIMAL(3,2) DEFAULT 50.0,
    intimacy_score DECIMAL(3,2) DEFAULT 0.0,
    respect_score DECIMAL(3,2) DEFAULT 50.0,
    affection_score DECIMAL(3,2) DEFAULT 0.0,
    compatibility_score DECIMAL(3,2) DEFAULT 50.0,
    relationship_milestones TEXT[] DEFAULT '{}',
    shared_memories_count INTEGER DEFAULT 0,
    conversation_count INTEGER DEFAULT 0,
    last_interaction_at TIMESTAMP,
    relationship_history JSONB DEFAULT '[]',
    dynamic_traits JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_user_character_relationships_unique ON user_character_relationships(user_id, character_id);
CREATE INDEX idx_relationships_level ON user_character_relationships(relationship_level DESC);
CREATE INDEX idx_relationships_type ON user_character_relationships(relationship_type);
CREATE INDEX idx_relationships_last_interaction ON user_character_relationships(last_interaction_at DESC);

CREATE TABLE IF NOT EXISTS relationship_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    relationship_id UUID NOT NULL REFERENCES user_character_relationships(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    event_description TEXT,
    impact_scores JSONB DEFAULT '{}',
    context_data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_relationship_events_relationship ON relationship_events(relationship_id);
CREATE INDEX idx_relationship_events_type ON relationship_events(event_type);
CREATE INDEX idx_relationship_events_created ON relationship_events(created_at);

-- ============================================================================
-- 4. DYNAMIC CHARACTER STATES - Real-time emotional and mental state
-- ============================================================================

CREATE TABLE IF NOT EXISTS character_dynamic_states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    current_emotion VARCHAR(50) DEFAULT 'neutral',
    emotion_intensity DECIMAL(3,2) DEFAULT 5.0,
    mental_state VARCHAR(50) DEFAULT 'balanced',
    energy_level DECIMAL(3,2) DEFAULT 70.0,
    stress_level DECIMAL(3,2) DEFAULT 30.0,
    mood_trend VARCHAR(20) DEFAULT 'stable',
    recent_triggers TEXT[] DEFAULT '{}',
    active_concerns TEXT[] DEFAULT '{}',
    state_duration INTEGER DEFAULT 0, -- minutes in current state
    last_state_change TIMESTAMP,
    context_factors JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_character_dynamic_states_unique ON character_dynamic_states(character_id, user_id);
CREATE INDEX idx_dynamic_states_emotion ON character_dynamic_states(current_emotion);
CREATE INDEX idx_dynamic_states_mental ON character_dynamic_states(mental_state);

-- ============================================================================
-- 5. ENHANCED PERSONALITY HIERARCHY - 5-Layer personality implementation
-- ============================================================================

CREATE TABLE IF NOT EXISTS character_five_layer_personality (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    
    -- Layer 1: Core Identity
    core_species VARCHAR(100) DEFAULT 'Human',
    core_vocation TEXT,
    core_social_role TEXT,
    core_archetypal_story TEXT,
    core_origin TEXT,
    core_education TEXT,
    core_relationships JSONB DEFAULT '{}',
    
    -- Layer 2: Cognitive Model
    cognitive_openness DECIMAL(3,2) DEFAULT 50.0,
    cognitive_conscientiousness DECIMAL(3,2) DEFAULT 50.0,
    cognitive_extraversion DECIMAL(3,2) DEFAULT 50.0,
    cognitive_agreeableness DECIMAL(3,2) DEFAULT 50.0,
    cognitive_neuroticism DECIMAL(3,2) DEFAULT 50.0,
    cognitive_humor_style TEXT,
    cognitive_moral_alignment JSONB DEFAULT '{}',
    cognitive_priorities JSONB DEFAULT '{}',
    cognitive_motivations JSONB DEFAULT '{}',
    
    -- Layer 3: Interaction Style
    interaction_language_style TEXT,
    interaction_questioning_approach TEXT,
    interaction_role_to_user VARCHAR(50),
    interaction_boundaries JSONB DEFAULT '{}',
    interaction_expertise_areas TEXT[] DEFAULT '{}',
    interaction_conversation_starters TEXT[] DEFAULT '{}',
    
    -- Layer 4: Dynamic System
    dynamic_learning_mechanism JSONB DEFAULT '{}',
    dynamic_belief_updates JSONB DEFAULT '{}',
    dynamic_emotional_states JSONB DEFAULT '{}',
    dynamic_state_transitions JSONB DEFAULT '{}',
    dynamic_short_term_goals JSONB DEFAULT '{}',
    dynamic_long_term_goals JSONB DEFAULT '{}',
    
    -- Layer 5: Meta Rules
    meta_interaction_boundaries JSONB DEFAULT '{}',
    meta_ethical_guardrails JSONB DEFAULT '{}',
    meta_system_directives JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_character_five_layer_unique ON character_five_layer_personality(character_id);

-- ============================================================================
-- 6. STORY INTERACTION TRACKING - Enhanced story progression
-- ============================================================================

CREATE TABLE IF NOT EXISTS story_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    scene_index INTEGER NOT NULL,
    interaction_type VARCHAR(50) NOT NULL CHECK (interaction_type IN ('choice', 'dialogue', 'action', 'emotion', 'custom')),
    interaction_data JSONB NOT NULL,
    time_spent INTEGER DEFAULT 0,
    emotional_response JSONB DEFAULT '{}',
    relationship_impact JSONB DEFAULT '{}',
    character_memory_created BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_story_interactions_user_story ON story_interactions(user_id, story_id);
CREATE INDEX idx_story_interactions_scene ON story_interactions(story_id, scene_index);
CREATE INDEX idx_story_interactions_type ON story_interactions(interaction_type);

-- ============================================================================
-- 7. USER ACHIEVEMENTS & GAMIFICATION
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    achievement_id VARCHAR(100) NOT NULL,
    achievement_name VARCHAR(200) NOT NULL,
    achievement_description TEXT,
    category VARCHAR(50),
    rarity VARCHAR(20) DEFAULT 'common',
    progress_value DECIMAL(5,2) DEFAULT 0.0,
    max_value DECIMAL(5,2) DEFAULT 100.0,
    is_unlocked BOOLEAN DEFAULT false,
    unlocked_at TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_user_achievements_unique ON user_achievements(user_id, achievement_id);
CREATE INDEX idx_user_achievements_unlocked ON user_achievements(is_unlocked, unlocked_at);

CREATE TABLE IF NOT EXISTS daily_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id VARCHAR(100) NOT NULL,
    task_name VARCHAR(200) NOT NULL,
    task_description TEXT,
    task_type VARCHAR(50) NOT NULL,
    target_value INTEGER DEFAULT 1,
    reward_currency_type VARCHAR(50),
    reward_amount INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    day_of_week INTEGER, -- 1-7 for Monday-Sunday, null for daily
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_daily_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    task_id VARCHAR(100) NOT NULL REFERENCES daily_tasks(task_id),
    progress_value INTEGER DEFAULT 0,
    is_completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP,
    progress_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_user_daily_progress_unique ON user_daily_progress(user_id, task_id, progress_date);
CREATE INDEX idx_user_daily_progress_completed ON user_daily_progress(is_completed, progress_date);

-- ============================================================================
-- 8. SOCIAL FEATURES - Following, likes, comments, sharing
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_following (
    follower_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    following_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (follower_id, following_id)
);

CREATE INDEX idx_user_following_follower ON user_following(follower_id);
CREATE INDEX idx_user_following_following ON user_following(following_id);

CREATE TABLE IF NOT EXISTS content_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content_type VARCHAR(50) NOT NULL CHECK (content_type IN ('character', 'story', 'moment', 'comment')),
    content_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE (user_id, content_type, content_id)
);

CREATE INDEX idx_content_likes_user ON content_likes(user_id);
CREATE INDEX idx_content_likes_content ON content_likes(content_type, content_id);

CREATE TABLE IF NOT EXISTS content_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content_type VARCHAR(50) NOT NULL,
    content_id UUID NOT NULL,
    parent_comment_id UUID REFERENCES content_comments(id) ON DELETE SET NULL,
    content TEXT NOT NULL,
    is_deleted BOOLEAN DEFAULT false,
    like_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_content_comments_content ON content_comments(content_type, content_id);
CREATE INDEX idx_content_comments_parent ON content_comments(parent_comment_id);
CREATE INDEX idx_content_comments_created ON content_comments(created_at DESC);

-- ============================================================================
-- 9. USER MOMENTS & SHARING
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_moments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    character_id UUID REFERENCES characters(id) ON DELETE SET NULL,
    content TEXT NOT NULL,
    moment_type VARCHAR(50) DEFAULT 'general',
    visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'followers', 'private')),
    mood VARCHAR(50),
    tags TEXT[] DEFAULT '{}',
    media_urls TEXT[] DEFAULT '{}',
    is_featured BOOLEAN DEFAULT false,
    like_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    bookmark_count INTEGER DEFAULT 0,
    location_data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_user_moments_user ON user_moments(user_id);
CREATE INDEX idx_user_moments_character ON user_moments(character_id);
CREATE INDEX idx_user_moments_type ON user_moments(moment_type);
CREATE INDEX idx_user_moments_visibility ON user_moments(visibility);
CREATE INDEX idx_user_moments_created ON user_moments(created_at DESC);
CREATE INDEX idx_user_moments_featured ON user_moments(is_featured);

-- ============================================================================
-- 10. PERFORMANCE OPTIMIZATION VIEWS
-- ============================================================================

-- Enhanced character stats with relationship data
CREATE OR REPLACE VIEW enhanced_character_stats AS
SELECT 
    c.id,
    c.name,
    c.creator_id,
    c.avatar_url,
    c.mbti_type,
    c.era,
    c.region,
    c.is_public,
    c.is_featured,
    c.tags,
    c.created_at,
    c.updated_at,
    
    -- Stats
    COALESCE(cs.chats_count, 0) as chats_count,
    COALESCE(cs.unique_chatters, 0) as unique_chatters,
    COALESCE(cs.likes_count, 0) as likes_count,
    COALESCE(cs.friends_count, 0) as friends_count,
    COALESCE(cs.moments_count, 0) as moments_count,
    COALESCE(cs.stories_count, 0) as stories_count,
    COALESCE(cs.total_engagement, 0) as total_engagement,
    COALESCE(cs.avg_rating, 0) as avg_rating,
    COALESCE(cs.rating_count, 0) as rating_count,
    
    -- Creator info
    u.username as creator_username,
    u.display_name as creator_display_name,
    up.avatar_url as creator_avatar,
    
    -- Activity tracking
    COALESCE(cs.last_chat_activity, c.created_at) as last_chat_activity,
    COALESCE(cs.last_moment_created, c.created_at) as last_moment_created,
    
    -- Relationship averages
    COALESCE(rel.avg_relationship_level, 0) as avg_relationship_level,
    COALESCE(rel.total_relationships, 0) as total_relationships
    
FROM characters c
LEFT JOIN users u ON c.creator_id = u.id
LEFT JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN character_stats cs ON c.id = cs.id
LEFT JOIN (
    SELECT 
        character_id,
        AVG(relationship_level) as avg_relationship_level,
        COUNT(*) as total_relationships
    FROM user_character_relationships 
    GROUP BY character_id
) rel ON c.id = rel.character_id;

-- ============================================================================
-- 11. TRIGGER FUNCTIONS
-- ============================================================================

-- Update chat session stats when new message is added
CREATE OR REPLACE FUNCTION update_chat_session_stats()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE chat_sessions 
    SET 
        message_count = message_count + 1,
        last_message_content = NEW.content,
        last_message_at = NEW.created_at,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.session_id;
    
    -- Update relationship stats
    IF NEW.sender_type = 'user' THEN
        INSERT INTO user_character_relationships (
            user_id, character_id, conversation_count, last_interaction_at, updated_at
        ) VALUES (
            (SELECT user_id FROM chat_sessions WHERE id = NEW.session_id),
            (SELECT character_id FROM chat_sessions WHERE id = NEW.session_id),
            1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
        ON CONFLICT (user_id, character_id) 
        DO UPDATE SET
            conversation_count = user_character_relationships.conversation_count + 1,
            last_interaction_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_chat_session_stats
    AFTER INSERT ON chat_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_chat_session_stats();

-- Update relationship scores based on interactions
CREATE OR REPLACE FUNCTION update_relationship_scores()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.sender_type = 'user' AND NEW.relationship_impact IS NOT NULL THEN
        UPDATE user_character_relationships
        SET
            relationship_level = LEAST(100, relationship_level + (NEW.relationship_impact * 2)),
            trust_score = LEAST(100, trust_score + NEW.relationship_impact),
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = (SELECT user_id FROM chat_sessions WHERE id = NEW.session_id)
          AND character_id = (SELECT character_id FROM chat_sessions WHERE id = NEW.session_id);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_relationship_scores
    AFTER INSERT ON chat_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_relationship_scores();

-- Auto-refresh materialized views
CREATE OR REPLACE FUNCTION refresh_enhanced_stats()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY enhanced_character_stats;
    -- Add other view refreshes as needed
END;
$$ LANGUAGE plpgsql;

-- Schedule to run every 15 minutes
-- This would be configured via pg_cron or external scheduler

-- ============================================================================
-- 12. SECURITY AND AUDIT
-- ============================================================================

-- Audit log for sensitive operations
CREATE TABLE IF NOT EXISTS audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    action_type VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_audit_log_user ON audit_log(user_id);
CREATE INDEX idx_audit_log_action ON audit_log(action_type);
CREATE INDEX idx_audit_log_table ON audit_log(table_name, record_id);
CREATE INDEX idx_audit_log_created ON audit_log(created_at);

-- Rate limiting for API endpoints
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    ip_address INET,
    endpoint VARCHAR(100) NOT NULL,
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    window_end TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '1 hour'),
    
    UNIQUE (user_id, ip_address, endpoint)
);

CREATE INDEX idx_rate_limits_user ON rate_limits(user_id);
CREATE INDEX idx_rate_limits_ip ON rate_limits(ip_address);
CREATE INDEX idx_rate_limits_endpoint ON rate_limits(endpoint);
CREATE INDEX idx_rate_limits_window ON rate_limits(window_end);

COMMIT;