{"openapi": "3.0.3", "info": {"title": "Alphane AI Character Platform API", "version": "1.0.0", "description": "RESTful API for the Alphane AI character interaction platform", "contact": {"name": "Alphane Team", "email": "<EMAIL>"}}, "servers": [{"url": "https://api.alphane.com/v1", "description": "Production server"}, {"url": "https://api-staging.alphane.com/v1", "description": "Staging server"}, {"url": "http://localhost:3001/v1", "description": "Development server"}], "paths": {"/auth": {"post": {"tags": ["Authentication"], "summary": "User login", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}}, "required": ["email", "password"]}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"token": {"type": "string"}, "refresh_token": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}, "expires_in": {"type": "integer"}}}}}}}}}, "/auth/register": {"post": {"tags": ["Authentication"], "summary": "User registration", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "minLength": 3, "maxLength": 50}, "email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 8}, "display_name": {"type": "string", "maxLength": 100}}, "required": ["username", "email", "password"]}}}}, "responses": {"201": {"description": "Registration successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/User"}, "verification_sent": {"type": "boolean"}}}}}}}}}, "/auth/refresh": {"post": {"tags": ["Authentication"], "summary": "Refresh access token", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"refresh_token": {"type": "string"}}, "required": ["refresh_token"]}}}}, "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully"}}}}, "/users/profile": {"get": {"tags": ["Users"], "summary": "Get current user profile", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "User profile retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}}}}, "put": {"tags": ["Users"], "summary": "Update user profile", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileUpdate"}}}}, "responses": {"200": {"description": "Profile updated successfully"}}}}, "/users/{userId}/follow": {"post": {"tags": ["Users"], "summary": "Follow a user", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User followed successfully"}}}, "delete": {"tags": ["Users"], "summary": "Unfollow a user", "security": [{"BearerAuth": []}], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "User unfollowed successfully"}}}}, "/characters": {"get": {"tags": ["Characters"], "summary": "Get characters with filtering and pagination", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "genre", "in": "query", "schema": {"type": "string"}}, {"name": "tags", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "is_featured", "in": "query", "schema": {"type": "boolean"}}, {"name": "sort", "in": "query", "schema": {"type": "string", "enum": ["popular", "newest", "trending", "random"]}}], "responses": {"200": {"description": "Characters retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"characters": {"type": "array", "items": {"$ref": "#/components/schemas/Character"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "post": {"tags": ["Characters"], "summary": "Create a new character", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterCreate"}}}}, "responses": {"201": {"description": "Character created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Character"}}}}}}}, "/characters/{characterId}": {"get": {"tags": ["Characters"], "summary": "Get character by ID", "parameters": [{"name": "characterId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Character retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterDetailed"}}}}}}, "put": {"tags": ["Characters"], "summary": "Update character", "security": [{"BearerAuth": []}], "parameters": [{"name": "characterId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterUpdate"}}}}, "responses": {"200": {"description": "Character updated successfully"}}}, "delete": {"tags": ["Characters"], "summary": "Delete character", "security": [{"BearerAuth": []}], "parameters": [{"name": "characterId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"204": {"description": "Character deleted successfully"}}}}, "/characters/{characterId}/like": {"post": {"tags": ["Characters"], "summary": "Like a character", "security": [{"BearerAuth": []}], "parameters": [{"name": "characterId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Character liked successfully"}}}, "delete": {"tags": ["Characters"], "summary": "Unlike a character", "security": [{"BearerAuth": []}], "parameters": [{"name": "characterId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Character unliked successfully"}}}}, "/stories": {"get": {"tags": ["Stories"], "summary": "Get stories with filtering and pagination", "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "character_id", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "genre", "in": "query", "schema": {"type": "string"}}, {"name": "difficulty", "in": "query", "schema": {"type": "string", "enum": ["easy", "medium", "hard"]}}], "responses": {"200": {"description": "Stories retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"stories": {"type": "array", "items": {"$ref": "#/components/schemas/Story"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "post": {"tags": ["Stories"], "summary": "Create a new story", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoryCreate"}}}}, "responses": {"201": {"description": "Story created successfully"}}}}, "/stories/{storyId}": {"get": {"tags": ["Stories"], "summary": "Get story by <PERSON>", "parameters": [{"name": "storyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Story retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoryDetailed"}}}}}}}, "/stories/{storyId}/chapters": {"get": {"tags": ["Stories"], "summary": "Get story chapters", "parameters": [{"name": "storyId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Story chapters retrieved successfully"}}}}, "/chats": {"get": {"tags": ["Chats"], "summary": "Get user's chat sessions", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}, {"name": "character_id", "in": "query", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "<PERSON><PERSON> retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"chats": {"type": "array", "items": {"$ref": "#/components/schemas/Chat"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}}}, "post": {"tags": ["Chats"], "summary": "Start a new chat session", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"character_id": {"type": "string", "format": "uuid"}, "story_id": {"type": "string", "format": "uuid"}, "chapter_id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "maxLength": 200}}, "required": ["character_id"]}}}}, "responses": {"201": {"description": "Chat session created successfully"}}}}, "/chats/{chatId}": {"get": {"tags": ["Chats"], "summary": "Get chat session details", "security": [{"BearerAuth": []}], "parameters": [{"name": "chatId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Chat session retrieved successfully"}}}, "put": {"tags": ["Chats"], "summary": "Update chat session", "security": [{"BearerAuth": []}], "parameters": [{"name": "chatId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "maxLength": 200}, "is_archived": {"type": "boolean"}, "is_favorite": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "Chat session updated successfully"}}}}, "/chats/{chatId}/messages": {"get": {"tags": ["Chats"], "summary": "Get chat messages", "security": [{"BearerAuth": []}], "parameters": [{"name": "chatId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 50}}], "responses": {"200": {"description": "Chat messages retrieved successfully"}}}, "post": {"tags": ["Chats"], "summary": "Send a message in chat", "security": [{"BearerAuth": []}], "parameters": [{"name": "chatId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"content": {"type": "string", "minLength": 1}, "message_type": {"type": "string", "enum": ["text", "image", "audio", "action"], "default": "text"}, "metadata": {"type": "object"}}, "required": ["content"]}}}}, "responses": {"201": {"description": "Message sent successfully"}}}}, "/memory": {"get": {"tags": ["Memory"], "summary": "Get user's memory capsules", "security": [{"BearerAuth": []}], "parameters": [{"name": "character_id", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "memory_type", "in": "query", "schema": {"type": "string", "enum": ["general", "relationship", "preference", "event", "emotion"]}}, {"name": "tags", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "Memory capsules retrieved successfully"}}}, "post": {"tags": ["Memory"], "summary": "Create a memory capsule", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemoryCreate"}}}}, "responses": {"201": {"description": "Memory capsule created successfully"}}}}, "/journey": {"get": {"tags": ["Journey"], "summary": "Get user journey progress", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Journey progress retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JourneyProgress"}}}}}}}, "/journey/missions": {"get": {"tags": ["Journey"], "summary": "Get available missions", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Missions retrieved successfully"}}}}, "/journey/rewards/{rewardId}/claim": {"post": {"tags": ["Journey"], "summary": "Claim a journey reward", "security": [{"BearerAuth": []}], "parameters": [{"name": "rewardId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON> claimed successfully"}}}}, "/achievements": {"get": {"tags": ["Achievements"], "summary": "Get user achievements", "security": [{"BearerAuth": []}], "parameters": [{"name": "type", "in": "query", "schema": {"type": "string"}}, {"name": "unlocked", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Achievements retrieved successfully"}}}}, "/wallet": {"get": {"tags": ["Wallet"], "summary": "Get user wallet balance", "security": [{"BearerAuth": []}], "responses": {"200": {"description": "Wallet balance retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WalletBalance"}}}}}}}, "/wallet/transactions": {"get": {"tags": ["Wallet"], "summary": "Get transaction history", "security": [{"BearerAuth": []}], "parameters": [{"name": "currency_type", "in": "query", "schema": {"type": "string", "enum": ["coins", "gems", "tokens", "hearts"]}}, {"name": "transaction_type", "in": "query", "schema": {"type": "string", "enum": ["earn", "spend", "purchase", "reward", "refund"]}}, {"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}], "responses": {"200": {"description": "Transaction history retrieved successfully"}}}}, "/notifications": {"get": {"tags": ["Notifications"], "summary": "Get user notifications", "security": [{"BearerAuth": []}], "parameters": [{"name": "is_read", "in": "query", "schema": {"type": "boolean"}}, {"name": "category", "in": "query", "schema": {"type": "string"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}], "responses": {"200": {"description": "Notifications retrieved successfully"}}}}, "/notifications/{notificationId}/read": {"put": {"tags": ["Notifications"], "summary": "Mark notification as read", "security": [{"BearerAuth": []}], "parameters": [{"name": "notificationId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Notification marked as read"}}}}, "/moments": {"get": {"tags": ["Moments"], "summary": "Get moments feed", "parameters": [{"name": "user_id", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "character_id", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "tags", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}], "responses": {"200": {"description": "Moments retrieved successfully"}}}, "post": {"tags": ["Moments"], "summary": "Create a new moment", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MomentCreate"}}}}, "responses": {"201": {"description": "Moment created successfully"}}}}, "/upload/image": {"post": {"tags": ["Upload"], "summary": "Upload image file", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "type": {"type": "string", "enum": ["avatar", "character", "story", "moment", "memory"]}}, "required": ["file", "type"]}}}}, "responses": {"200": {"description": "Image uploaded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "thumbnail_url": {"type": "string", "format": "uri"}}}}}}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "username": {"type": "string"}, "email": {"type": "string", "format": "email"}, "display_name": {"type": "string"}, "avatar_url": {"type": "string", "format": "uri"}, "bio": {"type": "string"}, "is_creator": {"type": "boolean"}, "is_verified": {"type": "boolean"}, "is_premium": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}}}, "UserProfile": {"allOf": [{"$ref": "#/components/schemas/User"}, {"type": "object", "properties": {"birth_date": {"type": "string", "format": "date"}, "gender": {"type": "string"}, "location": {"type": "string"}, "social_links": {"type": "object"}, "interests": {"type": "array", "items": {"type": "string"}}, "privacy_settings": {"type": "object"}, "notification_settings": {"type": "object"}}}]}, "UserProfileUpdate": {"type": "object", "properties": {"display_name": {"type": "string", "maxLength": 100}, "bio": {"type": "string"}, "avatar_url": {"type": "string", "format": "uri"}, "birth_date": {"type": "string", "format": "date"}, "gender": {"type": "string"}, "location": {"type": "string"}, "social_links": {"type": "object"}, "interests": {"type": "array", "items": {"type": "string"}}, "privacy_settings": {"type": "object"}, "notification_settings": {"type": "object"}}}, "Character": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "creator_id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string"}, "avatar_url": {"type": "string", "format": "uri"}, "cover_image_url": {"type": "string", "format": "uri"}, "tags": {"type": "array", "items": {"type": "string"}}, "genre": {"type": "string"}, "age_range": {"type": "string"}, "language": {"type": "string"}, "is_public": {"type": "boolean"}, "is_featured": {"type": "boolean"}, "is_nsfw": {"type": "boolean"}, "chat_count": {"type": "integer"}, "like_count": {"type": "integer"}, "view_count": {"type": "integer"}, "created_at": {"type": "string", "format": "date-time"}}}, "CharacterDetailed": {"allOf": [{"$ref": "#/components/schemas/Character"}, {"type": "object", "properties": {"personality_traits": {"type": "object"}, "background_story": {"type": "string"}, "voice_settings": {"type": "object"}, "creator": {"$ref": "#/components/schemas/User"}}}]}, "CharacterCreate": {"type": "object", "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100}, "description": {"type": "string"}, "avatar_url": {"type": "string", "format": "uri"}, "cover_image_url": {"type": "string", "format": "uri"}, "personality_traits": {"type": "object"}, "background_story": {"type": "string"}, "voice_settings": {"type": "object"}, "ai_model_config": {"type": "object"}, "tags": {"type": "array", "items": {"type": "string"}}, "genre": {"type": "string"}, "age_range": {"type": "string"}, "language": {"type": "string", "default": "en"}, "is_public": {"type": "boolean", "default": true}, "is_nsfw": {"type": "boolean", "default": false}}, "required": ["name"]}, "CharacterUpdate": {"type": "object", "properties": {"name": {"type": "string", "maxLength": 100}, "description": {"type": "string"}, "avatar_url": {"type": "string", "format": "uri"}, "cover_image_url": {"type": "string", "format": "uri"}, "personality_traits": {"type": "object"}, "background_story": {"type": "string"}, "voice_settings": {"type": "object"}, "ai_model_config": {"type": "object"}, "tags": {"type": "array", "items": {"type": "string"}}, "genre": {"type": "string"}, "age_range": {"type": "string"}, "is_public": {"type": "boolean"}, "is_nsfw": {"type": "boolean"}}}, "Story": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "creator_id": {"type": "string", "format": "uuid"}, "character_id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "description": {"type": "string"}, "cover_image_url": {"type": "string", "format": "uri"}, "genre": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "difficulty_level": {"type": "string"}, "estimated_duration": {"type": "integer"}, "language": {"type": "string"}, "is_public": {"type": "boolean"}, "is_featured": {"type": "boolean"}, "play_count": {"type": "integer"}, "like_count": {"type": "integer"}, "rating_avg": {"type": "number"}, "rating_count": {"type": "integer"}, "created_at": {"type": "string", "format": "date-time"}}}, "StoryDetailed": {"allOf": [{"$ref": "#/components/schemas/Story"}, {"type": "object", "properties": {"world_setting": {"type": "object"}, "objectives": {"type": "array"}, "subjectives": {"type": "array"}, "reward_settings": {"type": "object"}, "character": {"$ref": "#/components/schemas/Character"}, "creator": {"$ref": "#/components/schemas/User"}}}]}, "StoryCreate": {"type": "object", "properties": {"character_id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "minLength": 1, "maxLength": 200}, "description": {"type": "string"}, "cover_image_url": {"type": "string", "format": "uri"}, "world_setting": {"type": "object"}, "objectives": {"type": "array"}, "subjectives": {"type": "array"}, "reward_settings": {"type": "object"}, "genre": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "difficulty_level": {"type": "string", "enum": ["easy", "medium", "hard"], "default": "medium"}, "estimated_duration": {"type": "integer"}, "language": {"type": "string", "default": "en"}, "is_public": {"type": "boolean", "default": true}, "is_nsfw": {"type": "boolean", "default": false}}, "required": ["character_id", "title"]}, "Chat": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "user_id": {"type": "string", "format": "uuid"}, "character_id": {"type": "string", "format": "uuid"}, "story_id": {"type": "string", "format": "uuid"}, "chapter_id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "message_count": {"type": "integer"}, "last_message_at": {"type": "string", "format": "date-time"}, "is_archived": {"type": "boolean"}, "is_favorite": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}}}, "MemoryCreate": {"type": "object", "properties": {"character_id": {"type": "string", "format": "uuid"}, "title": {"type": "string", "minLength": 1, "maxLength": 200}, "content": {"type": "string", "minLength": 1}, "memory_type": {"type": "string", "enum": ["general", "relationship", "preference", "event", "emotion"], "default": "general"}, "importance_score": {"type": "integer", "minimum": 1, "maximum": 10, "default": 5}, "tags": {"type": "array", "items": {"type": "string"}}, "emotional_context": {"type": "object"}, "expires_at": {"type": "string", "format": "date-time"}}, "required": ["character_id", "title", "content"]}, "JourneyProgress": {"type": "object", "properties": {"season_id": {"type": "string"}, "level": {"type": "integer"}, "experience_points": {"type": "integer"}, "current_streak": {"type": "integer"}, "longest_streak": {"type": "integer"}, "last_activity_date": {"type": "string", "format": "date"}, "premium_tier": {"type": "string"}, "premium_benefits": {"type": "object"}, "missions_completed": {"type": "array"}, "rewards_claimed": {"type": "array"}, "season_progress": {"type": "object"}}}, "WalletBalance": {"type": "object", "properties": {"coins": {"type": "integer"}, "gems": {"type": "integer"}, "tokens": {"type": "integer"}, "hearts": {"type": "integer"}, "daily_bonus_available": {"type": "boolean"}, "next_daily_bonus": {"type": "string", "format": "date-time"}}}, "MomentCreate": {"type": "object", "properties": {"character_id": {"type": "string", "format": "uuid"}, "content": {"type": "string", "minLength": 1}, "media_urls": {"type": "array", "items": {"type": "string", "format": "uri"}}, "tags": {"type": "array", "items": {"type": "string"}}, "mood": {"type": "string"}, "visibility": {"type": "string", "enum": ["public", "followers", "private"], "default": "public"}}, "required": ["content"]}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "limit": {"type": "integer"}, "total": {"type": "integer"}, "pages": {"type": "integer"}, "has_next": {"type": "boolean"}, "has_prev": {"type": "boolean"}}}, "Error": {"type": "object", "properties": {"error": {"type": "string"}, "message": {"type": "string"}, "code": {"type": "integer"}, "details": {"type": "object"}}}}}, "tags": [{"name": "Authentication", "description": "User authentication and authorization"}, {"name": "Users", "description": "User management and profiles"}, {"name": "Characters", "description": "AI character management"}, {"name": "Stories", "description": "Interactive story management"}, {"name": "Chats", "description": "Chat session management"}, {"name": "Memory", "description": "Memory capsule management"}, {"name": "Journey", "description": "User journey and progression"}, {"name": "Achievements", "description": "Achievement and trophy system"}, {"name": "Wallet", "description": "Virtual currency and transactions"}, {"name": "Notifications", "description": "User notification system"}, {"name": "Moments", "description": "User-generated content moments"}, {"name": "Upload", "description": "File upload services"}]}