import { useState, useEffect, useCallback } from 'react';

// 共享类型定义



export interface SeasonStats {
  rank: number;
  percentile: number;
  weeklyGain: number;
  bestStreak: number;
}



export interface JourneyData {
  // 用户基础信息
  currentLevel: number;
  currentTrophies: number;
  membershipTier: 'standard' | 'pass' | 'diamond' | 'metaverse';
  
  // 时间相关
  seasonEndTime: Date;
  
  // 会员状态
  hasPassTrack: boolean;
  hasDiamondTrack: boolean;
  hasMetaverseTrack: boolean;
  
  // 进度相关
  seasonStats: SeasonStats;
  

  
  // 刷新计数器
  refreshTrigger: number;
}

export const useJourneyData = () => {
  // 基础状态
  const [data, setData] = useState<JourneyData>({
    currentLevel: 8,
    currentTrophies: 1247,
    membershipTier: 'pass',
    seasonEndTime: new Date('2024-03-15T23:59:59'),
    hasPassTrack: true,
    hasDiamondTrack: false,
    hasMetaverseTrack: false,
    

    
    seasonStats: {
      rank: Math.floor(1247 / 100) + 1,
      percentile: 25,
      weeklyGain: 156,
      bestStreak: 12
    },
    

    

    
    refreshTrigger: 0
  });

  // 更新函数
  const updateMembershipTier = useCallback((newTier: 'standard' | 'pass' | 'diamond' | 'metaverse') => {
    setData(prev => ({ ...prev, membershipTier: newTier }));
  }, []);

  const updateTrackPurchase = useCallback((trackType: 'pass' | 'diamond' | 'metaverse') => {
    setData(prev => ({
      ...prev,
      [`has${trackType.charAt(0).toUpperCase() + trackType.slice(1)}Track` as keyof JourneyData]: true
    }));
  }, []);

  const updateTrophies = useCallback((newTrophies: number) => {
    setData(prev => {
      const newLevel = Math.floor(newTrophies / 1000) + 1; // 1000奖杯 = 1级
      return {
        ...prev,
        currentTrophies: newTrophies,
        currentLevel: newLevel
      };
    });
  }, []);

  const addTrophies = useCallback((trophiesToAdd: number) => {
    setData(prev => {
      const newTrophies = prev.currentTrophies + trophiesToAdd;
      const newLevel = Math.floor(newTrophies / 1000) + 1; // 1000奖杯 = 1级
      return {
        ...prev,
        currentTrophies: newTrophies,
        currentLevel: newLevel
      };
    });
  }, []);



  // 计算属性
  const progressPercentage = (data.currentLevel % 10) / 10 * 100;
  
  const dailyProgress = 67; // 模拟每日进度

  const getLevelName = (level: number): string => {
    if (level <= 10) return 'journey.levels.beginnerJourney';
    if (level <= 25) return 'journey.levels.warmCompanion';
    if (level <= 40) return 'journey.levels.deepBond';
    if (level <= 60) return 'journey.levels.soulConnection';
    if (level <= 80) return 'journey.levels.eternalPath';
    return 'journey.levels.legendaryVoyage';
  };

  // 自动刷新
  useEffect(() => {
    const interval = setInterval(() => {
      setData(prev => ({ ...prev, refreshTrigger: prev.refreshTrigger + 1 }));
    }, 30000);
    return () => clearInterval(interval);
  }, []);

  return {
    data,
    updateMembershipTier,
    updateTrackPurchase,
    updateTrophies,
    addTrophies,
    progressPercentage: Math.round(progressPercentage),
    dailyProgress: Math.round(dailyProgress),
    getLevelName
  };
}; 