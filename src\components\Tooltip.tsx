'use client';

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface TooltipProps {
  content: string;
  children: React.ReactNode;
  side?: 'right' | 'left' | 'top' | 'bottom';
  delayDuration?: number;
  className?: string;
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  side = 'right',
  delayDuration = 200,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showTimeout, setShowTimeout] = useState<NodeJS.Timeout | null>(null);
  const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isMounted, setIsMounted] = useState(false);
  const triggerRef = useRef<HTMLDivElement>(null);

  // Auto-hide tooltip after a certain duration to prevent it getting stuck
  useEffect(() => {
    if (isVisible) {
      const autoHideTimeout = setTimeout(() => {
        setIsVisible(false);
      }, 3000); // Hide after 3 seconds

      return () => {
        clearTimeout(autoHideTimeout);
      };
    }
  }, [isVisible]);

  // 确保组件已挂载（避免SSR问题）
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const updatePosition = () => {
    if (!triggerRef.current) return;
    
    const rect = triggerRef.current.getBoundingClientRect();
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;
    
    setPosition({
      x: rect.left + scrollX,
      y: rect.top + scrollY
    });
  };

  const handleMouseEnter = () => {
    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }
    
    updatePosition();
    
    const timeout = setTimeout(() => {
      setIsVisible(true);
    }, delayDuration);
    
    setShowTimeout(timeout);
  };

  const handleMouseLeave = () => {
    if (showTimeout) {
      clearTimeout(showTimeout);
      setShowTimeout(null);
    }
    
    const timeout = setTimeout(() => {
      setIsVisible(false);
    }, 100);
    
    setHideTimeout(timeout);
  };

  useEffect(() => {
    return () => {
      if (showTimeout) clearTimeout(showTimeout);
      if (hideTimeout) clearTimeout(hideTimeout);
    };
  }, [showTimeout, hideTimeout]);

  // 监听滚动和窗口大小变化，更新tooltip位置
  useEffect(() => {
    if (!isVisible) return;

    const handlePositionUpdate = () => {
      updatePosition();
    };

    window.addEventListener('scroll', handlePositionUpdate, true);
    window.addEventListener('resize', handlePositionUpdate);

    return () => {
      window.removeEventListener('scroll', handlePositionUpdate, true);
      window.removeEventListener('resize', handlePositionUpdate);
    };
  }, [isVisible]);

  const getTooltipStyles = () => {
    if (!triggerRef.current) return {};
    
    const rect = triggerRef.current.getBoundingClientRect();
    const triggerWidth = rect.width;
    const triggerHeight = rect.height;
    
    const baseStyles: React.CSSProperties = {
      position: 'absolute',
      zIndex: 9999,
      opacity: isVisible ? 1 : 0,
      transform: isVisible ? 'scale(1)' : 'scale(0.95)',
      transition: 'all 0.2s ease-out',
      pointerEvents: 'none',
    };

    switch (side) {
      case 'right':
        return {
          ...baseStyles,
          left: position.x + triggerWidth + 8,
          top: position.y + triggerHeight / 2,
          transform: `translateY(-50%) ${isVisible ? 'scale(1)' : 'scale(0.95)'}`,
        };
      case 'left':
        return {
          ...baseStyles,
          right: window.innerWidth - position.x + 8,
          top: position.y + triggerHeight / 2,
          transform: `translateY(-50%) ${isVisible ? 'scale(1)' : 'scale(0.95)'}`,
        };
      case 'top':
        return {
          ...baseStyles,
          left: position.x + triggerWidth / 2,
          bottom: window.innerHeight - position.y + 8,
          transform: `translateX(-50%) ${isVisible ? 'scale(1)' : 'scale(0.95)'}`,
        };
      case 'bottom':
        return {
          ...baseStyles,
          left: position.x + triggerWidth / 2,
          top: position.y + triggerHeight + 8,
          transform: `translateX(-50%) ${isVisible ? 'scale(1)' : 'scale(0.95)'}`,
        };
      default:
        return baseStyles;
    }
  };

  const getArrowStyles = () => {
    const baseStyles: React.CSSProperties = {
      position: 'absolute',
      width: 0,
      height: 0,
      pointerEvents: 'none',
    };

    switch (side) {
      case 'right':
        return {
          ...baseStyles,
          left: -8,
          top: '50%',
          transform: 'translateY(-50%)',
          borderTop: '4px solid transparent',
          borderBottom: '4px solid transparent',
          borderRight: '4px solid',
          borderLeft: 'none',
        };
      case 'left':
        return {
          ...baseStyles,
          right: -8,
          top: '50%',
          transform: 'translateY(-50%)',
          borderTop: '4px solid transparent',
          borderBottom: '4px solid transparent',
          borderLeft: '4px solid',
          borderRight: 'none',
        };
      case 'top':
        return {
          ...baseStyles,
          bottom: -8,
          left: '50%',
          transform: 'translateX(-50%)',
          borderLeft: '4px solid transparent',
          borderRight: '4px solid transparent',
          borderTop: '4px solid',
          borderBottom: 'none',
        };
      case 'bottom':
        return {
          ...baseStyles,
          top: -8,
          left: '50%',
          transform: 'translateX(-50%)',
          borderLeft: '4px solid transparent',
          borderRight: '4px solid transparent',
          borderBottom: '4px solid',
          borderTop: 'none',
        };
      default:
        return baseStyles;
    }
  };

  const tooltipElement = isMounted && content ? (
    <div
      style={getTooltipStyles()}
      className={`px-3 py-2 text-sm font-medium bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 whitespace-nowrap backdrop-blur-sm ${className}`}
    >
      {content}
      <div
        style={getArrowStyles()}
        className="border-white dark:border-gray-800"
      />
    </div>
  ) : null;

  return (
    <>
      <div 
        ref={triggerRef}
        className="relative inline-block"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {children}
      </div>
      
      {isMounted && typeof document !== 'undefined' && tooltipElement && 
        createPortal(tooltipElement, document.body)
      }
    </>
  );
};

export default Tooltip; 