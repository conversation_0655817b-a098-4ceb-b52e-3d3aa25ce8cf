'use client';

import React from 'react';

interface TabItem {
  id: string;
  label: string;
  icon?: React.ComponentType<{ className?: string; size?: number }>;
  count?: number;
  disabled?: boolean;
}

interface TabNavigationProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  variant?: 'default' | 'pills' | 'underline' | 'glass';
  size?: 'sm' | 'md' | 'lg';
  orientation?: 'horizontal' | 'vertical';
  sticky?: boolean;
  stickyTop?: string;
  className?: string;
  fullWidth?: boolean;
}

const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  variant = 'default',
  size = 'md',
  orientation = 'horizontal',
  sticky = false,
  stickyTop = 'top-12 lg:top-16',
  className = '',
  fullWidth = true
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'py-2',
          tab: 'py-2 px-3 text-sm',
          icon: 'w-4 h-4',
          count: 'text-xs px-1.5 py-0.5'
        };
      case 'md':
        return {
          container: 'py-3',
          tab: 'py-3 px-4 text-sm md:text-base',
          icon: 'w-4 h-4 md:w-5 md:h-5',
          count: 'text-xs px-2 py-0.5'
        };
      case 'lg':
        return {
          container: 'py-4',
          tab: 'py-4 px-6 text-base md:text-lg',
          icon: 'w-5 h-5 md:w-6 md:h-6',
          count: 'text-sm px-2 py-1'
        };
      default:
        return {
          container: 'py-3',
          tab: 'py-3 px-4 text-sm md:text-base',
          icon: 'w-4 h-4 md:w-5 md:h-5',
          count: 'text-xs px-2 py-0.5'
        };
    }
  };

  const getVariantClasses = (isActive: boolean) => {
    const baseClasses = 'font-semibold transition-all duration-200 relative';
    
    switch (variant) {
      case 'pills':
        return `${baseClasses} rounded-lg ${
          isActive
            ? 'bg-romantic-gradient text-white shadow-lg'
            : 'text-muted-foreground hover:text-foreground hover:bg-muted'
        }`;
      case 'underline':
        return `${baseClasses} ${
          isActive
            ? 'text-system-primary border-b-2 border-romantic'
            : 'text-muted-foreground hover:text-foreground'
        }`;
      case 'glass':
        return `${baseClasses} rounded-lg ${
          isActive
            ? 'backdrop-blur-xl bg-white/20 dark:bg-black/20 border border-white/30 dark:border-white/10 text-white shadow-lg'
            : 'text-white/80 hover:text-white hover:bg-white/10 dark:hover:bg-black/10'
        }`;
      default:
        return `${baseClasses} ${
          isActive
            ? 'text-system-primary bg-romantic-gradient/10 border-b-2 border-romantic'
            : 'text-muted-foreground hover:text-foreground'
        }`;
    }
  };

  const getContainerClasses = () => {
    const sizeClasses = getSizeClasses();
    const orientationClasses = orientation === 'vertical' 
      ? 'flex-col space-y-1' 
      : `${fullWidth ? 'flex justify-around' : 'flex gap-2'} px-2`;
    
    const stickyClasses = sticky 
      ? `sticky ${stickyTop} z-20 bg-background border-b border-border`
      : '';

    const variantContainerClasses = variant === 'glass' 
      ? 'backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 rounded-lg'
      : '';

    return `${sizeClasses.container} ${orientationClasses} ${stickyClasses} ${variantContainerClasses} ${className}`;
  };

  const sizeClasses = getSizeClasses();

  return (
    <div className={getContainerClasses()}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        const Icon = tab.icon;
        
        return (
          <button
            key={tab.id}
            onClick={() => !tab.disabled && onTabChange(tab.id)}
            disabled={tab.disabled}
            className={`
              ${sizeClasses.tab} ${getVariantClasses(isActive)}
              ${fullWidth && orientation === 'horizontal' ? 'flex-1' : ''}
              ${tab.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              flex items-center justify-center gap-2 whitespace-nowrap
            `}
          >
            {/* Icon */}
            {Icon && (
              <Icon className={sizeClasses.icon} />
            )}
            
            {/* Label */}
            <span className="capitalize">{tab.label}</span>
            
            {/* Count Badge */}
            {typeof tab.count === 'number' && tab.count > 0 && (
              <span className={`
                ${sizeClasses.count} rounded-full font-medium
                ${isActive
                  ? variant === 'glass' 
                    ? 'bg-white/30 text-white'
                    : variant === 'pills'
                    ? 'bg-white/20 text-white'
                    : 'bg-romantic-gradient/20 text-system-primary'
                  : 'bg-muted text-muted-foreground'
                }
              `}>
                {tab.count > 999 ? '999+' : tab.count}
              </span>
            )}
            
            {/* Active indicator for underline variant */}
            {variant === 'underline' && isActive && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-romantic-gradient rounded-full"></div>
            )}
          </button>
        );
      })}
    </div>
  );
};

export default TabNavigation;
