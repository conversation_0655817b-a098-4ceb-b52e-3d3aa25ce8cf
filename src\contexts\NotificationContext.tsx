'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

export interface Notification {
  id: string;
  type: 'achievement' | 'purchase' | 'milestone' | 'reward' | 'celebration' | 'mission' | 'error' | 'success' | 'info';
  title: string;
  message: string;
  icon?: React.ComponentType<any>;
  duration?: number; // in milliseconds, 0 for persistent
  action?: {
    label: string;
    onClick: () => void;
  };
  metadata?: {
    source: 'store' | 'journey' | 'trophies' | 'wallet' | 'system';
    category?: string;
    value?: string | number;
  };
  timestamp: Date;
  read: boolean;
  persistent?: boolean;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  getNotificationsBySource: (source: string) => Notification[];
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const addNotification = useCallback((notificationData: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const notification: Notification = {
      ...notificationData,
      id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      read: false,
    };

    setNotifications(prev => [notification, ...prev]);

    // Auto-remove non-persistent notifications after duration
    if (!notification.persistent && notification.duration !== 0) {
      const duration = notification.duration || 5000;
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, duration);
    }
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const getNotificationsBySource = useCallback((source: string) => {
    return notifications.filter(n => n.metadata?.source === source);
  }, [notifications]);

  const unreadCount = notifications.filter(n => !n.read).length;

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    getNotificationsBySource,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Notification helper functions for different types
export const createAchievementNotification = (title: string, description: string, reward?: string): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type: 'achievement',
  title,
  message: description,
  duration: 8000,
  metadata: {
    source: 'trophies',
    category: 'achievement',
    value: reward
  }
});

export const createPurchaseNotification = (itemName: string, price: number, currency: string): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type: 'purchase',
  title: 'Purchase Successful',
  message: `You purchased ${itemName} for ${price} ${currency}`,
  duration: 5000,
  metadata: {
    source: 'store',
    category: 'purchase',
    value: price
  }
});

export const createMilestoneNotification = (milestone: string, character?: string): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type: 'milestone',
  title: 'Milestone Reached!',
  message: character ? `${milestone} with ${character}` : milestone,
  duration: 6000,
  metadata: {
    source: 'journey',
    category: 'milestone'
  }
});

export const createRewardNotification = (reward: string, amount: number, source: 'store' | 'journey' | 'trophies' | 'wallet'): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type: 'reward',
  title: 'Reward Earned!',
  message: `You received ${amount} ${reward}`,
  duration: 5000,
  metadata: {
    source,
    category: 'reward',
    value: amount
  }
});

export const createCelebrationNotification = (celebration: string, character: string): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type: 'celebration',
  title: 'Special Celebration!',
  message: `${celebration} - ${character}`,
  duration: 7000,
  persistent: true,
  metadata: {
    source: 'store',
    category: 'celebration'
  }
});

export const createMissionNotification = (missionName: string, reward: string): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type: 'mission',
  title: 'Mission Completed!',
  message: `${missionName} - Reward: ${reward}`,
  duration: 6000,
  metadata: {
    source: 'journey',
    category: 'mission'
  }
});
