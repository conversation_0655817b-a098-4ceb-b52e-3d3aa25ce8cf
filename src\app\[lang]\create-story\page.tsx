import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import MainAppLayout from '@/components/MainAppLayout';
import CreateStorySelectCharacterPage from './CreateStorySelectCharacterPage';
import { useTranslation } from '@/app/i18n';

interface CreateStoryPageProps {
  params: Promise<{ lang: string }>;
}

export default async function CreateStoryPage({ params }: CreateStoryPageProps) {
  const { lang } = await params;

  return (
    <AuthGuard requireAuth={true}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <MainAppLayout lang={lang}>
          <CreateStorySelectCharacterPage lang={lang} />
        </MainAppLayout>
      </Suspense>
    </AuthGuard>
  );
}

export async function generateMetadata({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const { t } = await useTranslation(lang, 'translation');

  return {
    title: t('storyCreation.pageTitle'),
    description: t('storyCreation.pageDescription'),
  };
}