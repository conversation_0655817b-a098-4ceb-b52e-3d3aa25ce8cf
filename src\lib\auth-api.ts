import { apiRequest, ApiResponse, LoginResponseData, User, authenticatedRequest } from './api';

// 登录请求参数
export interface LoginRequest {
  email: string;
  password: string;
  loginMethod?: 'password' | 'otp';
}

// OTP验证请求参数
export interface OtpRequest {
  email: string;
  otp: string;
}

// 重置密码请求参数
export interface ChangePasswordRequest {
  email: string;
  otp: string;
  password: string;
}

// 发送OTP验证码
export async function sendOtp(email: string): Promise<ApiResponse<null>> {
  return apiRequest('/auth/send-otp', {
    method: 'POST',
    body: JSON.stringify({ email }),
  });
}

// 登录/注册
export async function login(data: LoginRequest): Promise<ApiResponse<LoginResponseData>> {
  return apiRequest<LoginResponseData>('/auth', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 验证OTP登录
export async function verifyOtp(data: OtpRequest): Promise<ApiResponse<LoginResponseData>> {
  return apiRequest<LoginResponseData>('/auth/verify-otp', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 刷新Token
export async function refreshToken(refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> {
  return apiRequest('/auth/refresh', {
    method: 'POST',
    body: JSON.stringify({ refreshToken }),
  });
}

// 重置密码
export async function changePassword(data: ChangePasswordRequest): Promise<ApiResponse<null>> {
  return apiRequest('/auth/change-password', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 获取当前用户信息
export async function getCurrentUser(): Promise<ApiResponse<{ user: User }>> {
  return authenticatedRequest<{ user: User }>('/auth/me');
}

// 通过UID获取用户信息
export async function getUserByUid(uid: string): Promise<ApiResponse<{ user: User }>> {
  return apiRequest<{ user: User }>(`/users/${uid}`);
}

// 获取用户的关注列表
export async function getUserFollowing(uid: string, page?: number, limit?: number): Promise<ApiResponse<{ users: User[]; pagination: any }>> {
  return apiRequest<{ users: User[]; pagination: any }>(`/users/${uid}/following?page=${page || 1}&limit=${limit || 20}`);
}

// 获取用户的粉丝列表
export async function getUserFollowers(uid: string, page?: number, limit?: number): Promise<ApiResponse<{ users: User[]; pagination: any }>> {
  return apiRequest<{ users: User[]; pagination: any }>(`/users/${uid}/followers?page=${page || 1}&limit=${limit || 20}`);
}

// 关注用户
export async function followUser(userId: string): Promise<ApiResponse<null>> {
  return authenticatedRequest('/users/follow', {
    method: 'POST',
    body: JSON.stringify({ userId }),
  });
}

// 取消关注用户
export async function unfollowUser(userId: string): Promise<ApiResponse<null>> {
  return authenticatedRequest('/users/unfollow', {
    method: 'POST',
    body: JSON.stringify({ userId }),
  });
}

// 本地存储管理
export function saveAuthToken(token: string, refreshToken?: string) {
  localStorage.setItem('token', token);
  if (refreshToken) {
    localStorage.setItem('refreshToken', refreshToken);
  }
}

export function clearAuthTokens() {
  localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
}

export function getAccessToken(): string | null {
  return localStorage.getItem('token');
}

export function getRefreshToken(): string | null {
  return localStorage.getItem('refreshToken');
}