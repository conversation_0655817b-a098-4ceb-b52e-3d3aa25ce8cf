-- =====================================================
-- Data Aggregation Views and Performance Optimization
-- Supporting frontend Card components with efficient queries
-- =====================================================

-- 1. Moment Statistics Materialized View
-- Aggregated stats for MomentCard component
CREATE MATERIALIZED VIEW moment_stats AS
SELECT 
    m.id,
    m.user_id,
    m.character_id,
    m.content,
    m.moment_type,
    m.visibility,
    m.mood,
    m.tags,
    m.is_featured,
    
    -- Interaction Statistics
    COUNT(DISTINCT si_like.id) as likes_count,
    COUNT(DISTINCT si_share.id) as shares_count,
    COUNT(DISTINCT c.id) as comments_count,
    COUNT(DISTINCT si_bookmark.id) as bookmarks_count,
    
    -- Engagement Metrics
    (COUNT(DISTINCT si_like.id) + COUNT(DISTINCT si_share.id) + COUNT(DISTINCT c.id)) as total_engagement,
    
    -- User Information
    u.username,
    u.display_name,
    up.avatar_url,
    
    -- Character Information
    ch.name as character_name,
    ch.avatar_url as character_avatar_url,
    
    -- Timing
    m.created_at,
    m.updated_at
    
FROM moments m
LEFT JOIN users u ON m.user_id = u.id
LEFT JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN characters ch ON m.character_id = ch.id
LEFT JOIN social_interactions si_like ON m.id = si_like.target_id 
    AND si_like.target_type = 'moment' 
    AND si_like.interaction_type = 'like' 
    AND si_like.is_active = true
LEFT JOIN social_interactions si_share ON m.id = si_share.target_id 
    AND si_share.target_type = 'moment' 
    AND si_share.interaction_type = 'share' 
    AND si_share.is_active = true
LEFT JOIN social_interactions si_bookmark ON m.id = si_bookmark.target_id 
    AND si_bookmark.target_type = 'moment' 
    AND si_bookmark.interaction_type = 'bookmark' 
    AND si_bookmark.is_active = true
LEFT JOIN comments c ON m.id = c.target_id 
    AND c.target_type = 'moment' 
    AND c.is_deleted = false
WHERE m.visibility IN ('public', 'followers')
GROUP BY m.id, m.user_id, m.character_id, m.content, m.moment_type, m.visibility, 
         m.mood, m.tags, m.is_featured, m.created_at, m.updated_at,
         u.username, u.display_name, up.avatar_url, ch.name, ch.avatar_url;

-- 2. User Dashboard Data View
-- Comprehensive user data for dashboard and profile cards
CREATE MATERIALIZED VIEW user_dashboard_data AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.display_name,
    
    -- Profile Information
    up.avatar_url,
    up.bio,
    up.location,
    up.website,
    
    -- Membership Information
    COALESCE(us_active.tier, 'standard') as current_membership_tier,
    us_active.expires_at as membership_expires_at,
    
    -- Currency Balances
    uc.star_diamonds,
    uc.joy_crystals,
    uc.glimmering_dust,
    uc.memory_puzzles,
    uc.daily_bonus_available,
    uc.next_daily_bonus,
    
    -- Statistics
    ust.total_conversations,
    ust.characters_created,
    ust.stories_completed,
    ust.achievements_unlocked,
    ust.consecutive_login_days,
    ust.total_login_days,
    
    -- Social Statistics
    uss.followers_count,
    uss.following_count,
    uss.moments_count,
    uss.avg_moment_engagement,
    uss.influence_score,
    
    -- Journey Progress
    uj.current_level,
    uj.current_xp,
    uj.total_xp_earned,
    
    -- Recent Activity
    ust.last_login_date,
    uss.last_activity_date,
    
    -- Metadata
    u.created_at as user_created_at,
    u.updated_at as user_updated_at
    
FROM users u
LEFT JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN user_currencies uc ON u.id = uc.user_id
LEFT JOIN user_statistics ust ON u.id = ust.user_id
LEFT JOIN user_social_stats uss ON u.id = uss.user_id 
    AND uss.stats_period_start = DATE_TRUNC('month', CURRENT_DATE)
LEFT JOIN user_journey uj ON u.id = uj.user_id AND uj.journey_type = 'main'
LEFT JOIN (
    SELECT DISTINCT ON (user_id) 
        user_id, 
        sp.tier,
        expires_at
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.subscription_plan_id = sp.id
    WHERE us.status = 'active' AND (us.expires_at IS NULL OR us.expires_at > CURRENT_TIMESTAMP)
    ORDER BY user_id, sp.tier DESC
) us_active ON u.id = us_active.user_id;

-- 3. Character Performance View
-- Enhanced character statistics for CharacterCard
CREATE MATERIALIZED VIEW character_performance AS
SELECT 
    c.id,
    c.name,
    c.description,
    c.avatar_url,
    c.creator_id,
    c.is_public,
    c.mbti_type,
    c.era,
    c.region,
    
    -- Creator Information
    u.username as creator_username,
    u.display_name as creator_display_name,
    
    -- Interaction Statistics
    COUNT(DISTINCT ch.id) as total_chats,
    COUNT(DISTINCT ch.user_id) as unique_chatters,
    COUNT(DISTINCT m.id) as moments_count,
    COUNT(DISTINCT si_like.id) as likes_count,
    COUNT(DISTINCT f.follower_id) as followers_count,
    
    -- Story Statistics
    COUNT(DISTINCT s.id) as stories_count,
    COUNT(DISTINCT sp.user_id) as story_players_count,
    AVG(sp.completion_percentage) as avg_story_completion,
    
    -- Quality Metrics
    AVG(sr.rating) as avg_rating,
    COUNT(DISTINCT sr.id) as rating_count,
    
    -- Engagement Metrics
    (COUNT(DISTINCT si_like.id) + COUNT(DISTINCT m.id) + COUNT(DISTINCT ch.id)) as total_engagement,
    
    -- Recent Activity
    MAX(ch.updated_at) as last_chat_activity,
    MAX(m.created_at) as last_moment_created,
    
    -- Metadata
    c.created_at,
    c.updated_at
    
FROM characters c
LEFT JOIN users u ON c.creator_id = u.id
LEFT JOIN chats ch ON c.id = ch.character_id
LEFT JOIN moments m ON c.id = m.character_id
LEFT JOIN social_interactions si_like ON c.id = si_like.target_id 
    AND si_like.target_type = 'character' 
    AND si_like.interaction_type = 'like' 
    AND si_like.is_active = true
LEFT JOIN follows f ON c.creator_id = f.following_id
LEFT JOIN stories s ON c.id = s.character_id
LEFT JOIN story_progression sp ON s.id = sp.story_id
LEFT JOIN story_ratings sr ON s.id = sr.story_id
WHERE c.is_public = true
GROUP BY c.id, c.name, c.description, c.avatar_url, c.creator_id, c.is_public,
         c.mbti_type, c.era, c.region, c.created_at, c.updated_at,
         u.username, u.display_name;

-- 4. Achievement Progress View
-- Detailed achievement data for TrophyCard
CREATE MATERIALIZED VIEW achievement_progress AS
SELECT 
    ua.id,
    ua.user_id,
    ua.achievement_id,
    ua.unlocked_at,
    ua.rarity,
    ua.category,
    ua.progress_current,
    ua.progress_total,
    ua.progress_percentage,
    ua.rewards,
    
    -- Achievement Details
    a.name as achievement_name,
    a.description as achievement_description,
    a.icon_url as achievement_icon,
    a.points as achievement_points,
    
    -- User Information
    u.username,
    u.display_name,
    up.avatar_url as user_avatar,
    
    -- Progress Status
    CASE 
        WHEN ua.unlocked_at IS NOT NULL THEN 'unlocked'
        WHEN ua.progress_percentage >= 100 THEN 'ready_to_unlock'
        WHEN ua.progress_percentage >= 50 THEN 'in_progress'
        ELSE 'not_started'
    END as status,
    
    -- Rarity Ranking
    ROW_NUMBER() OVER (PARTITION BY ua.rarity ORDER BY ua.unlocked_at ASC) as rarity_rank,
    
    -- Metadata
    ua.created_at,
    ua.updated_at
    
FROM user_achievements ua
JOIN achievements a ON ua.achievement_id = a.id
JOIN users u ON ua.user_id = u.id
LEFT JOIN user_profiles up ON u.id = up.user_id
WHERE ua.is_hidden = false OR ua.unlocked_at IS NOT NULL;

-- 5. Memorial Events View
-- Anniversary and special events for MemorialCard
CREATE MATERIALIZED VIEW memorial_events_view AS
SELECT 
    me.id,
    me.user_id,
    me.character_id,
    me.event_type,
    me.event_name,
    me.description,
    me.anniversary_date,
    me.next_anniversary,
    me.rewards,
    me.is_claimed,
    me.claimed_at,
    
    -- Character Information
    c.name as character_name,
    c.avatar_url as character_avatar,
    
    -- User Information
    u.username,
    u.display_name,
    
    -- Time Calculations
    EXTRACT(DAYS FROM (me.next_anniversary - CURRENT_DATE)) as days_until_anniversary,
    EXTRACT(YEARS FROM (CURRENT_DATE - me.original_date)) as years_since_event,
    
    -- Event Status
    CASE 
        WHEN me.next_anniversary = CURRENT_DATE THEN 'today'
        WHEN me.next_anniversary BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '7 days' THEN 'this_week'
        WHEN me.next_anniversary BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days' THEN 'this_month'
        ELSE 'future'
    END as anniversary_status,
    
    -- Metadata
    me.created_at,
    me.updated_at
    
FROM memorial_events me
JOIN users u ON me.user_id = u.id
LEFT JOIN characters c ON me.character_id = c.id
WHERE me.is_active = true;

-- 6. Daily Mission Progress View
-- Current daily missions for users
CREATE MATERIALIZED VIEW daily_mission_progress_view AS
SELECT 
    udmp.id,
    udmp.user_id,
    udmp.daily_mission_id,
    udmp.current_progress,
    udmp.target_progress,
    udmp.is_completed,
    udmp.is_claimed,
    udmp.assigned_date,
    udmp.expires_at,
    
    -- Mission Information
    dm.name as mission_name,
    dm.description as mission_description,
    dm.category as mission_category,
    dm.difficulty,
    dm.rarity,
    dm.rewards,
    
    -- Progress Calculations
    ROUND((udmp.current_progress::DECIMAL / udmp.target_progress * 100), 2) as completion_percentage,
    (udmp.target_progress - udmp.current_progress) as remaining_progress,
    
    -- Time Calculations
    EXTRACT(HOURS FROM (udmp.expires_at - CURRENT_TIMESTAMP)) as hours_remaining,
    
    -- Status
    CASE 
        WHEN udmp.is_claimed THEN 'claimed'
        WHEN udmp.is_completed THEN 'ready_to_claim'
        WHEN udmp.expires_at < CURRENT_TIMESTAMP THEN 'expired'
        ELSE 'active'
    END as status,
    
    -- User Information
    u.username,
    u.display_name,
    
    -- Metadata
    udmp.created_at,
    udmp.updated_at
    
FROM user_daily_mission_progress udmp
JOIN daily_missions dm ON udmp.daily_mission_id = dm.id
JOIN users u ON udmp.user_id = u.id
WHERE udmp.assigned_date >= CURRENT_DATE - INTERVAL '7 days';

-- 7. Leaderboard Rankings View
-- Current leaderboard positions
CREATE MATERIALIZED VIEW leaderboard_rankings AS
SELECT 
    ule.id,
    ule.user_id,
    ule.leaderboard_id,
    ule.current_rank,
    ule.previous_rank,
    ule.rank_change,
    ule.score,
    ule.score_change,
    
    -- Leaderboard Information
    l.name as leaderboard_name,
    l.category as leaderboard_category,
    l.metric_type,
    l.period_type,
    
    -- User Information
    u.username,
    u.display_name,
    up.avatar_url as user_avatar,
    
    -- Ranking Context
    LAG(ule.current_rank) OVER (PARTITION BY ule.leaderboard_id ORDER BY ule.current_rank) as rank_above,
    LEAD(ule.current_rank) OVER (PARTITION BY ule.leaderboard_id ORDER BY ule.current_rank) as rank_below,
    
    -- Badge Eligibility
    CASE 
        WHEN ule.current_rank = 1 THEN 'gold'
        WHEN ule.current_rank = 2 THEN 'silver'
        WHEN ule.current_rank = 3 THEN 'bronze'
        WHEN ule.current_rank <= 10 THEN 'top_10'
        WHEN ule.current_rank <= 100 THEN 'top_100'
        ELSE 'participant'
    END as badge_tier,
    
    -- Metadata
    ule.period_start,
    ule.period_end,
    ule.last_score_update
    
FROM user_leaderboard_entries ule
JOIN leaderboards l ON ule.leaderboard_id = l.id
JOIN users u ON ule.user_id = u.id
LEFT JOIN user_profiles up ON u.id = up.user_id
WHERE l.is_active = true 
    AND ule.period_end >= CURRENT_TIMESTAMP;

-- 8. Indexes for Materialized Views
CREATE UNIQUE INDEX idx_moment_stats_id ON moment_stats(id);
CREATE INDEX idx_moment_stats_user ON moment_stats(user_id);
CREATE INDEX idx_moment_stats_character ON moment_stats(character_id);
CREATE INDEX idx_moment_stats_engagement ON moment_stats(total_engagement DESC);
CREATE INDEX idx_moment_stats_created ON moment_stats(created_at DESC);

CREATE UNIQUE INDEX idx_user_dashboard_data_id ON user_dashboard_data(id);
CREATE INDEX idx_user_dashboard_data_membership ON user_dashboard_data(current_membership_tier);
CREATE INDEX idx_user_dashboard_data_influence ON user_dashboard_data(influence_score DESC);

CREATE UNIQUE INDEX idx_character_performance_id ON character_performance(id);
CREATE INDEX idx_character_performance_creator ON character_performance(creator_id);
CREATE INDEX idx_character_performance_engagement ON character_performance(total_engagement DESC);
CREATE INDEX idx_character_performance_rating ON character_performance(avg_rating DESC);

CREATE UNIQUE INDEX idx_achievement_progress_id ON achievement_progress(id);
CREATE INDEX idx_achievement_progress_user ON achievement_progress(user_id);
CREATE INDEX idx_achievement_progress_status ON achievement_progress(status);
CREATE INDEX idx_achievement_progress_rarity ON achievement_progress(rarity, rarity_rank);

CREATE UNIQUE INDEX idx_memorial_events_view_id ON memorial_events_view(id);
CREATE INDEX idx_memorial_events_view_user ON memorial_events_view(user_id);
CREATE INDEX idx_memorial_events_view_status ON memorial_events_view(anniversary_status);
CREATE INDEX idx_memorial_events_view_date ON memorial_events_view(anniversary_date);

CREATE UNIQUE INDEX idx_daily_mission_progress_view_id ON daily_mission_progress_view(id);
CREATE INDEX idx_daily_mission_progress_view_user ON daily_mission_progress_view(user_id);
CREATE INDEX idx_daily_mission_progress_view_status ON daily_mission_progress_view(status);
CREATE INDEX idx_daily_mission_progress_view_date ON daily_mission_progress_view(assigned_date);

CREATE UNIQUE INDEX idx_leaderboard_rankings_id ON leaderboard_rankings(id);
CREATE INDEX idx_leaderboard_rankings_leaderboard_rank ON leaderboard_rankings(leaderboard_id, current_rank);
CREATE INDEX idx_leaderboard_rankings_user ON leaderboard_rankings(user_id);
CREATE INDEX idx_leaderboard_rankings_badge ON leaderboard_rankings(badge_tier);

-- 9. Refresh Functions for All Materialized Views
CREATE OR REPLACE FUNCTION refresh_all_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY moment_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_dashboard_data;
    REFRESH MATERIALIZED VIEW CONCURRENTLY character_performance;
    REFRESH MATERIALIZED VIEW CONCURRENTLY achievement_progress;
    REFRESH MATERIALIZED VIEW CONCURRENTLY memorial_events_view;
    REFRESH MATERIALIZED VIEW CONCURRENTLY daily_mission_progress_view;
    REFRESH MATERIALIZED VIEW CONCURRENTLY leaderboard_rankings;
    REFRESH MATERIALIZED VIEW CONCURRENTLY character_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY story_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY store_stats;
END;
$$ LANGUAGE plpgsql;

-- 10. Scheduled Refresh (requires pg_cron extension)
-- Refresh every 15 minutes for real-time data
-- SELECT cron.schedule('refresh-all-views', '*/15 * * * *', 'SELECT refresh_all_materialized_views();');

COMMENT ON MATERIALIZED VIEW moment_stats IS 'Aggregated moment statistics for MomentCard component';
COMMENT ON MATERIALIZED VIEW user_dashboard_data IS 'Comprehensive user data for dashboard and profile cards';
COMMENT ON MATERIALIZED VIEW character_performance IS 'Enhanced character statistics for CharacterCard component';
COMMENT ON MATERIALIZED VIEW achievement_progress IS 'Detailed achievement data for TrophyCard component';
COMMENT ON MATERIALIZED VIEW memorial_events_view IS 'Anniversary and special events for MemorialCard component';
COMMENT ON MATERIALIZED VIEW daily_mission_progress_view IS 'Current daily missions for users';
COMMENT ON MATERIALIZED VIEW leaderboard_rankings IS 'Current leaderboard positions and rankings';
