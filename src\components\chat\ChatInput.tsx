'use client';

import { FC, useState } from 'react';
import { Send, Mic, Plus, Keyboard, Camera, Image as ImageIcon, Phone, Share, User, Clock, Heart } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface ChatInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onSend: () => void;
  onSuggestionToggle: () => void;
  showSuggestions: boolean;
  isVoiceMode: boolean;
  onVoiceModeToggle: () => void;
  onMenuToggle?: (isOpen: boolean) => void; // 新增：通知父组件菜单状态变化
  lang?: string;
}

const ChatInput: FC<ChatInputProps> = ({
  value,
  onChange,
  onKeyPress,
  onSend,
  onSuggestionToggle,
  showSuggestions,
  isVoiceMode,
  onVoiceModeToggle,
  onMenuToggle,
  lang = 'en'
}) => {
  const [showActionMenu, setShowActionMenu] = useState(false);
  const { t } = useTranslation(lang, 'translation');

  const handlePlusClick = () => {
    const newState = !showActionMenu;
    setShowActionMenu(newState);
    // 通知父组件菜单状态变化
    onMenuToggle?.(newState);
  };

  const actionMenuItems = [
    { icon: ImageIcon, label: t('chat.actions.gallery'), onClick: () => console.log('Gallery') },
    { icon: Camera, label: t('chat.actions.camera'), onClick: () => console.log('Camera') },
    { icon: Phone, label: t('chat.actions.voiceCall'), onClick: () => console.log('Voice Call') },
    { icon: Heart, label: t('chat.actions.shareMoment'), onClick: () => console.log('Share Moment') },
    { icon: User, label: t('chat.actions.shareCharacter'), onClick: () => console.log('Share Character') },
    { icon: Clock, label: t('chat.actions.shareMemory'), onClick: () => console.log('Share Memory') }
  ];

  if (isVoiceMode) {
    // Voice mode: entire bar is a voice recording button
    return (
      <div className="flex-shrink-0 px-4 py-3 bg-gradient-to-t from-black via-black/90 to-transparent h-18 flex items-center">
        <div className="flex w-full items-center gap-2">
          {/* Voice mode toggle button - keyboard icon to switch back to text */}
          <button
            title={t('chat.input.switchToTextMode')}
            onClick={onVoiceModeToggle}
            className="py-3 px-3 text-blue-400 hover:text-blue-300 transition-colors rounded-lg bg-gray-900/80 flex-shrink-0"
          >
            <Keyboard size={28}/>
          </button>

          {/* Voice Recording Button */}
          <div className="flex-1 bg-red-600 hover:bg-red-700 transition-colors rounded-lg p-4 text-center cursor-pointer">
            <span className="text-white font-medium">{t('chat.input.recording')}</span>
          </div>
        </div>
      </div>
    );
  }

  // Text mode: normal input with buttons
  return (
    <div className="flex-shrink-0 relative">
      {/* Chat Input Bar */}
      <div className="px-4 py-3 bg-gradient-to-t from-black via-black via-black/95 to-transparent h-18 flex items-center">
        <div className="flex w-full items-center gap-2 bg-gray-900/80 rounded-lg p-2">
        {/* Voice mode toggle on the left */}
        <button
          title={t('chat.input.switchToVoiceMode')}
          onClick={onVoiceModeToggle}
          className="py-3 px-1 text-gray-400 hover:text-white transition-colors rounded-lg flex-shrink-0"
        >
          <Mic size={28}/>
        </button>

        {/* Plus button for actions */}
        <button
          title={t('chat.input.moreActions')}
          onClick={handlePlusClick}
          className="py-3 px-1 text-gray-400 hover:text-white transition-colors rounded-lg flex-shrink-0"
        >
          <Plus size={28}/>
        </button>

        {/* The input field will shrink to fit. min-w-0 is crucial for flex-shrink to work. */}
        <input
          type="text"
          placeholder={t('chat.input.placeholder')}
          className="flex-1 w-full min-w-0 bg-transparent focus:outline-none px-2 text-white text-lg"
          value={value}
          onChange={onChange}
          onKeyPress={onKeyPress}
        />

        {/* Send button */}
        <button
          title={t('chat.input.sendMessage')}
          onClick={onSend}
          className="py-3 px-3 text-white hover:text-gray-300 transition-colors rounded-lg bg-blue-600 hover:bg-blue-700 flex-shrink-0"
        >
          <Send size={28}/>
        </button>
        </div>
      </div>

      {/* 输入框和6按钮区之间的纯黑背景区域 */}
      <div className={`bg-black transition-all duration-300 ease-in-out ${
        showActionMenu ? 'h-0' : 'h-0'
      }`} />

      {/* Action Menu - 高度立即变化确保第一帧全黑背景，内容有动画效果 */}
      <div
        className={`bg-black overflow-hidden ${
          showActionMenu ? 'h-40' : 'h-0'
        }`}
      >
        <div className={`grid grid-cols-3 gap-3 bg-black p-4 pb-8 h-full transition-all duration-300 ease-in-out ${
          showActionMenu ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-4'
        }`}>
          {actionMenuItems.map((item, index) => (
            <button
              key={index}
              onClick={() => {
                item.onClick();
                const newState = false;
                setShowActionMenu(newState);
                onMenuToggle?.(newState);
              }}
              className="flex flex-col items-center justify-center gap-1 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors h-full"
            >
              <item.icon size={24} />
              <span className="text-xs">{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
