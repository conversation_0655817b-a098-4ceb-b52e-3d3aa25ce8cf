'use client';

import React, { useState } from 'react';
import SubscriptionSection from '@/components/store/SubscriptionSection';
import PlanCard from '@/components/PlanCard';

interface Plan {
  name: string;
  price: number;
  period: string;
  status: string;
  features: string[];
}

interface SubscriptionData {
  currentPlans: {
    standard: { isActive: boolean; expiresAt: string | null };
    pass: { isActive: boolean; expiresAt: string | null };
    diamond: { isActive: boolean; expiresAt: string | null };
    metaverse: { isActive: boolean; expiresAt: string | null };
  };
  plans: {
    standard: Plan;
    pass: Plan;
    diamond: Plan;
    metaverse: Plan;
  };
}

export default function SubscriptionDemoPage() {
  const [demoState, setDemoState] = useState<'default' | 'pass-active' | 'diamond-active' | 'metaverse-active'>('default');

  // Demo data with different states
  const getSubscriptionData = (): SubscriptionData => {
    const baseData = {
      plans: {
        standard: {
          name: 'Standard',
          price: 0,
          period: 'forever',
          status: 'permanent',
          features: [
            '5 AI conversations daily',
            'Basic character library access',
            'Standard quality avatars',
            'Community features'
          ]
        },
        pass: {
          name: 'Pass',
          price: 9.99,
          period: 'month',
          status: 'inactive',
          features: [
            'Unlimited AI conversations',
            'Full character library access',
            'High-definition avatars',
            'Priority customer support',
            'Exclusive badges',
            'Advanced customization options'
          ]
        },
        diamond: {
          name: 'Diamond',
          price: 29.99,
          period: 'month',
          status: 'inactive',
          features: [
            'All Pass features included',
            'Exclusive diamond characters',
            '4K ultra-HD avatars',
            'Private chat rooms',
            'Early access to new features',
            'Dedicated account manager',
            'Unlimited storage space'
          ]
        },
        metaverse: {
          name: 'MetaVerse Pass',
          price: 99.99,
          period: 'month',
          status: 'inactive',
          features: [
            'All Diamond features included',
            'MetaVerse world access',
            'Virtual reality integration',
            'Custom avatar creation',
            'Exclusive MetaVerse events',
            'Cross-platform synchronization',
            'Advanced AI interactions',
            'Virtual property ownership',
            'Premium MetaVerse content'
          ]
        }
      }
    };

    switch (demoState) {
      case 'pass-active':
        return {
          ...baseData,
          currentPlans: {
            standard: { isActive: true, expiresAt: null },
            pass: { isActive: true, expiresAt: '2024-12-31' },
            diamond: { isActive: false, expiresAt: null },
            metaverse: { isActive: false, expiresAt: null }
          }
        };
      case 'diamond-active':
        return {
          ...baseData,
          currentPlans: {
            standard: { isActive: true, expiresAt: null },
            pass: { isActive: false, expiresAt: null },
            diamond: { isActive: true, expiresAt: '2024-12-31' },
            metaverse: { isActive: false, expiresAt: null }
          }
        };
      case 'metaverse-active':
        return {
          ...baseData,
          currentPlans: {
            standard: { isActive: true, expiresAt: null },
            pass: { isActive: false, expiresAt: null },
            diamond: { isActive: false, expiresAt: null },
            metaverse: { isActive: true, expiresAt: '2024-12-31' }
          }
        };
      default:
        return {
          ...baseData,
          currentPlans: {
            standard: { isActive: true, expiresAt: null },
            pass: { isActive: false, expiresAt: null },
            diamond: { isActive: false, expiresAt: null },
            metaverse: { isActive: false, expiresAt: null }
          }
        };
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-slate-900 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Demo Controls */}
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Subscription Cards Demo
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Demonstrating improved button alignment and states
          </p>
          
          <div className="flex justify-center gap-4 mb-8">
            <button
              onClick={() => setDemoState('default')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                demoState === 'default'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              Default State
            </button>
            <button
              onClick={() => setDemoState('pass-active')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                demoState === 'pass-active'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              Pass Active
            </button>
            <button
              onClick={() => setDemoState('diamond-active')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                demoState === 'diamond-active'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              Diamond Active
            </button>
            <button
              onClick={() => setDemoState('metaverse-active')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                demoState === 'metaverse-active'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              MetaVerse Active
            </button>
          </div>
        </div>

        {/* Improved SubscriptionSection */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
            Improved SubscriptionSection Component
          </h2>
          <SubscriptionSection 
            lang="en" 
            subscriptionData={getSubscriptionData()}
          />
        </div>

        {/* PlanCard Examples */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
            PlanCard Component Examples
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <PlanCard
              name="Standard"
              price="Free"
              period="forever"
              seats="Personal Use"
              features={[
                '5 AI conversations daily',
                'Basic character library',
                'Standard avatars',
                'Community features'
              ]}
              buttonType="current"
              isPopular={false}
              isCurrent={true}
            />
            <PlanCard
              name="Pass"
              price="$9.99"
              period="month"
              seats="Personal Use"
              features={[
                'Unlimited conversations',
                'Full character library',
                'HD avatars',
                'Priority support',
                'Exclusive badges'
              ]}
              buttonType={demoState === 'pass-active' ? 'cancel' : 'upgrade'}
              isPopular={true}
            />
            <PlanCard
              name="Diamond"
              price="$29.99"
              period="month"
              originalPrice="$39.99"
              discount="25% OFF"
              seats="Premium Use"
              features={[
                'All Pass features',
                'Exclusive characters',
                '4K ultra-HD avatars',
                'Private chat rooms',
                'Early access',
                'Dedicated manager'
              ]}
              buttonType={demoState === 'diamond-active' ? 'cancel' : 'upgrade'}
              isPopular={false}
            />
          </div>
        </div>

        {/* Key Improvements */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            ✨ Key Improvements
          </h3>
          <ul className="space-y-2 text-gray-700 dark:text-gray-300">
            <li className="flex items-start gap-2">
              <span className="text-green-500 font-bold">✓</span>
              <span><strong>Bottom Alignment:</strong> All buttons are now aligned at the bottom using flexbox layout</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-green-500 font-bold">✓</span>
              <span><strong>Consistent Height:</strong> Cards maintain equal height regardless of content length</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-green-500 font-bold">✓</span>
              <span><strong>Improved Button States:</strong> "Cancel At Next Billing Cycle" instead of "Active (Click to Cancel)"</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-green-500 font-bold">✓</span>
              <span><strong>Better Visual Feedback:</strong> Enhanced hover effects and transitions</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-green-500 font-bold">✓</span>
              <span><strong>Consistent Styling:</strong> Unified button colors and states across components</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
