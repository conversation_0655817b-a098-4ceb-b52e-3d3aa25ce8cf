'use client';

import React, { useState } from 'react';
import MainAppLayout from '@/components/MainAppLayout';
import AnalyticsOverview from '@/components/analytics/AnalyticsOverview';
import UserEngagementAnalytics from '@/components/analytics/UserEngagementAnalytics';
import PurchaseAnalytics from '@/components/analytics/PurchaseAnalytics';
import ProgressionAnalytics from '@/components/analytics/ProgressionAnalytics';
import RealtimeMetrics from '@/components/analytics/RealtimeMetrics';

interface AnalyticsClientPageProps {
  lang: string;
}

const AnalyticsClientPage: React.FC<AnalyticsClientPageProps> = ({ lang }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'engagement' | 'purchases' | 'progression' | 'realtime'>('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', description: 'Key metrics and insights' },
    { id: 'engagement', label: 'User Engagement', description: 'Interaction patterns and behavior' },
    { id: 'purchases', label: 'Purchase Analytics', description: 'Revenue and conversion metrics' },
    { id: 'progression', label: 'User Progression', description: 'Journey and achievement tracking' },
    { id: 'realtime', label: 'Real-time', description: 'Live metrics and activity' }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <AnalyticsOverview lang={lang} />;
      case 'engagement':
        return <UserEngagementAnalytics lang={lang} />;
      case 'purchases':
        return <PurchaseAnalytics lang={lang} />;
      case 'progression':
        return <ProgressionAnalytics lang={lang} />;
      case 'realtime':
        return <RealtimeMetrics lang={lang} />;
      default:
        return <AnalyticsOverview lang={lang} />;
    }
  };

  return (
    <MainAppLayout lang={lang} title="Analytics Dashboard">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Analytics Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Comprehensive insights into user behavior, engagement, and business metrics
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-8 overflow-x-auto">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <div className="text-left">
                      <div className="font-semibold">{tab.label}</div>
                      <div className="text-xs opacity-75">{tab.description}</div>
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="space-y-8">
            {renderContent()}
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
};

export default AnalyticsClientPage;
