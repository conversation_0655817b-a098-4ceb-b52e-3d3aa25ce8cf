'use client';

import React from 'react';
import AvatarCropper from '@/components/AvatarCropper';
import type {
  AvatarAspectRatio,
  CharacterImageAspectRatio
} from '@/types/character-creation';

interface ImageCropModalsProps {
  // Ratio selection (kept for compatibility but not used)
  showRatioSelection: boolean;
  characterImageAspectRatio: CharacterImageAspectRatio;
  setCharacterImageAspectRatio: (ratio: CharacterImageAspectRatio) => void;
  onRatioSelectionConfirm: () => void;
  setShowRatioSelection: (show: boolean) => void;

  // Avatar cropping
  showAvatarCrop: boolean;
  originalImagePreview: string;
  onAvatarCropComplete: (croppedImage: File, cropData: any) => void;
  onCancelCrop: () => void;

  // Character image cropping
  showCharacterImageCrop: boolean;
  onCharacterImageCropComplete: (croppedImage: File, cropData: any) => void;
  
  // Language
  lang?: string;
}

const ImageCropModals: React.FC<ImageCropModalsProps> = ({
  showRatioSelection,
  characterImageAspectRatio,
  setCharacterImageAspectRatio,
  onRatioSelectionConfirm,
  setShowRatioSelection,
  showAvatarCrop,
  originalImagePreview,
  onAvatarCropComplete,
  onCancelCrop,
  showCharacterImageCrop,
  onCharacterImageCropComplete,
  lang,
}) => {
  // Handle aspect ratio change for character image cropper
  const handleCharacterImageAspectRatioChange = (ratio: '1:1' | '5:6' | '5:8') => {
    // Only call the callback for valid CharacterImageAspectRatio values
    if (ratio === '5:6' || ratio === '5:8') {
      setCharacterImageAspectRatio(ratio);
    }
  };
  return (
    <>


      {/* Avatar Cropper */}
      {showAvatarCrop && originalImagePreview && (
        <AvatarCropper
          characterImage={originalImagePreview}
          aspectRatio="1:1"
          onCropComplete={onAvatarCropComplete}
          onCancel={onCancelCrop}
          cropType="avatar"
          exportSize={128}
          lang={lang}
        />
      )}

      {/* Character Image Cropper */}
      {showCharacterImageCrop && originalImagePreview && (
        <AvatarCropper
          characterImage={originalImagePreview}
          aspectRatio={characterImageAspectRatio}
          onCropComplete={onCharacterImageCropComplete}
          onCancel={onCancelCrop}
          cropType="characterImage"
          onAspectRatioChange={handleCharacterImageAspectRatioChange}
          lang={lang}
        />
      )}
    </>
  );
};

export default ImageCropModals;
