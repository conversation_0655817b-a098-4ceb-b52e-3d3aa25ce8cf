'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { TrendingUp, TrendingDown, Users, Heart, Play, DollarSign, Crown, Calendar, ExternalLink } from 'lucide-react';
import CharacterCard from '@/components/CharacterCard';
import ProfileModeToggle from '@/components/ProfileModeToggle';
import HeroSection from '@/components/HeroSection';
import TabNavigation from '@/components/TabNavigation';
import { ManagedCharacter } from '@/lib/mock-data';

// Icon wrapper to fix type compatibility
const iconWrapper = (IconComponent: any) => ({ className, size }: { className?: string; size?: number }) => (
  <IconComponent className={className} size={size} />
);

interface CreatorStory {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  character: any;
  stats: {
    plays: number;
    likes: number;
    shares: number;
  };
  createdAt: string;
  status: 'published' | 'draft';
  earnings: number;
}

interface DashboardData {
  totalEarnings: number;
  monthlyEarnings: number;
  totalFollowers: number;
  totalLikes: number;
  totalCharacters: number;
  totalStories: number;
  topCharacters: any[];
  topStories: CreatorStory[];
  monthlyStats: any[];
}

interface CreatorProfileClientPageProps {
  lang: string;
  createdCharacters: ManagedCharacter[];
  createdStories: CreatorStory[];
  dashboardData: DashboardData;
}

const TABS = ['characters', 'stories', 'dashboard'] as const;

const CreatorProfileClientPage: React.FC<CreatorProfileClientPageProps> = ({ 
  lang, 
  createdCharacters, 
  createdStories, 
  dashboardData 
}) => {
  const [activeTab, setActiveTab] = useState<typeof TABS[number]>('characters');

  return (
    <>
      {/* Top profile cover - Now using optimized HeroSection */}
      <HeroSection
        backgroundImage="https://picsum.photos/1200/800?blur=3"
        title="Moonlight Wanderer"
        subtitle="👑 Creator"
        description="Passionate creator crafting immersive AI characters and captivating stories. Building worlds where imagination meets technology."
        avatar="https://i.pravatar.cc/160?u=profile"
        avatarSize="lg"
        variant="profile"
        height="lg"
        stats={[
          { label: 'Followers', value: dashboardData.totalFollowers.toLocaleString(), icon: iconWrapper(Users) },
          { label: 'Following', value: '256', icon: iconWrapper(Heart) },
          { label: 'Characters', value: dashboardData.totalCharacters.toString(), icon: iconWrapper(Play) },
          { label: 'Stories', value: dashboardData.totalStories.toString(), icon: iconWrapper(Calendar) },
          { label: 'Earnings', value: `$${dashboardData.totalEarnings.toLocaleString()}`, icon: iconWrapper(DollarSign) },
          { label: 'Links', value: '7', icon: iconWrapper(ExternalLink) }
        ]}
      >
        <div className="flex items-center gap-2">
          <ProfileModeToggle lang={lang} currentPage="creator-profile" size="md" className="flex-shrink-0" />
        </div>
      </HeroSection>

      {/* Sticky tab bar */}
      <TabNavigation
        tabs={[
          { id: 'characters', label: 'Characters', icon: iconWrapper(Users) },
          { id: 'stories', label: 'Stories', icon: iconWrapper(Play) },
          { id: 'dashboard', label: 'Dashboard', icon: iconWrapper(TrendingUp) }
        ]}
        activeTab={activeTab}
        onTabChange={(tabId) => setActiveTab(tabId as typeof TABS[number])}
        variant="underline"
        sticky={true}
        stickyTop="top-12 lg:top-16"
        className="bg-background border-b border-border"
      />

      {/* Content */}
      <div className="bg-background p-1.5 md:p-2">
        {activeTab === 'characters' && (
          <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
            {createdCharacters.map((character, idx) => (
              <div key={`char-${idx}`} className="break-inside-avoid">
                <CharacterCard
                  lang={lang}
                  character={character}
                  stats={{
                    likes: Math.floor(Math.random() * 1000) + 100,
                    friends: character.followers,
                    shares: Math.floor(Math.random() * 100) + 10,
                  }}
                  aspectRatio={0.75}
                />
              </div>
            ))}
          </div>
        )}

        {activeTab === 'stories' && (
          <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
            {createdStories.map((story, idx) => (
              <div key={`story-${idx}`} className="break-inside-avoid">
                <div className="relative overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 group">
                  {/* Story cover */}
                  <div style={{ paddingBottom: '150%' }} />
                  <Image
                    src={story.coverImage}
                    alt={story.title}
                    fill
                    className="absolute top-0 left-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    unoptimized
                  />
                  
                  {/* Gradient overlay covering bottom 60% */}
                  <div className="absolute bottom-0 left-0 right-0 h-[60%] bg-gradient-to-t from-black/80 via-black/40 to-transparent" />

                  {/* Content overlay */}
                  <div className="absolute inset-0 p-4 flex flex-col justify-end">
                    {/* Story description */}
                    <h3 className="font-medium text-sm text-white line-clamp-3 mb-3">
                      {story.description}
                    </h3>

                    {/* Story info */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Image
                          src={story.character.character_avatar}
                          alt={story.character.name}
                          width={32}
                          height={32}
                          className="w-8 h-8 rounded-full border-2 border-white/60 flex-shrink-0 object-cover"
                          unoptimized
                        />
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-sm text-white truncate">{story.title}</p>
                          <div className="flex items-center gap-1 text-xs text-white/80">
                            <Calendar size={12} />
                            <span>{story.createdAt}</span>
                          </div>
                        </div>
                      </div>

                      {/* Stats */}
                      <div className="flex items-center gap-3 text-white/90 text-xs">
                        <span className="flex items-center gap-0.5">
                          <Play size={12} />{story.stats.plays}
                        </span>
                        <span className="flex items-center gap-0.5">
                          <DollarSign size={12} />{story.earnings}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'dashboard' && (
          <div className="max-w-6xl mx-auto space-y-6">
            {/* Stats Overview */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Earnings</p>
                    <p className="text-2xl font-bold text-green-600">${dashboardData.totalEarnings.toLocaleString()}</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Monthly Earnings</p>
                    <p className="text-2xl font-bold text-blue-600">${dashboardData.monthlyEarnings.toLocaleString()}</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-blue-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Followers</p>
                    <p className="text-2xl font-bold text-purple-600">{dashboardData.totalFollowers.toLocaleString()}</p>
                  </div>
                  <Users className="w-8 h-8 text-purple-500" />
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Likes</p>
                    <p className="text-2xl font-bold text-red-600">{dashboardData.totalLikes.toLocaleString()}</p>
                  </div>
                  <Heart className="w-8 h-8 text-red-500" />
                </div>
              </div>
            </div>

            {/* Top Performing Content */}
            <div className="grid md:grid-cols-2 gap-6">
              {/* Top Characters */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold mb-4">Top Characters</h3>
                <div className="space-y-4">
                  {dashboardData.topCharacters.map((character, idx) => (
                    <div key={character.id} className="flex items-center gap-3">
                      <span className="text-sm font-bold text-gray-400 w-6">#{idx + 1}</span>
                      <Image
                        src={character.character_avatar}
                        alt={character.name}
                        width={40}
                        height={40}
                        className="w-10 h-10 rounded-full object-cover"
                        unoptimized
                      />
                      <div className="flex-1">
                        <p className="font-medium text-sm">{character.name}</p>
                        <p className="text-xs text-gray-500">{character.interactions} interactions</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold text-green-600">${character.earnings}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Top Stories */}
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold mb-4">Top Stories</h3>
                <div className="space-y-4">
                  {dashboardData.topStories.map((story, idx) => (
                    <div key={story.id} className="flex items-center gap-3">
                      <span className="text-sm font-bold text-gray-400 w-6">#{idx + 1}</span>
                      <Image
                        src={story.coverImage}
                        alt={story.title}
                        width={40}
                        height={40}
                        className="w-10 h-10 rounded object-cover"
                        unoptimized
                      />
                      <div className="flex-1">
                        <p className="font-medium text-sm">{story.title}</p>
                        <p className="text-xs text-gray-500">{story.stats.plays} plays</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold text-green-600">${story.earnings}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default CreatorProfileClientPage;
