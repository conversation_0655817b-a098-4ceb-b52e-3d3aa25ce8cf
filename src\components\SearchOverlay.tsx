'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from '@/app/i18n/client';
import { 
  Search, 
  X, 
  Clock, 
  TrendingUp, 
  Filter,
  SlidersHorizontal,
  Users,
  BookOpen,
  User,
  Brain,
  Heart,
  Star,
  Eye,
  MessageCircle,
  Calendar,
  Verified,
  Crown
} from 'lucide-react';
import SearchTabs, { SearchTab } from './search/SearchTabs';
import SearchFiltersComponent, { SearchFilters } from './search/SearchFilters';

interface SearchOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  lang?: string;
}

const SearchOverlay: React.FC<SearchOverlayProps> = ({ 
  isOpen, 
  onClose, 
  lang = 'en' 
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [isMobile, setIsMobile] = useState(false);
  
  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<SearchTab>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [filters, setFilters] = useState<SearchFilters>({
    sortBy: 'relevance',
    category: 'all',
    tags: [],
    dateRange: 'all',
    verified: false,
    premium: false
  });

  // Search results state
  const [searchResults, setSearchResults] = useState<any>({
    characters: [],
    stories: [],
    users: [],
    memories: []
  });

  // Popular searches mock data
  const popularSearches = [
    t('search.categories.anime'),
    t('search.categories.romance'),
    t('search.categories.fantasy'),
    t('search.categories.slice_of_life'),
    t('search.categories.mystery')
  ];

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Load search history from localStorage
  useEffect(() => {
    if (isOpen) {
      const savedHistory = localStorage.getItem('searchHistory');
      if (savedHistory) {
        setSearchHistory(JSON.parse(savedHistory));
      }
    }
  }, [isOpen]);

  // Handle search
  const handleSearch = async (query: string) => {
    if (!query.trim()) return;
    
    setIsLoading(true);
    
    // Add to search history
    const newHistory = [query, ...searchHistory.filter(item => item !== query)].slice(0, 5);
    setSearchHistory(newHistory);
    localStorage.setItem('searchHistory', JSON.stringify(newHistory));
    
    try {
      // Mock search results - 在实际应用中这里会调用API
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const mockResults = {
        characters: [
          {
            id: '1',
            name: 'Luna',
            description: 'A mysterious character with silver hair and magical abilities',
            creator: 'Admin',
            followers: 1250,
            chats: 3400,
            rating: 4.8,
            verified: true,
            premium: false,
            tags: ['fantasy', 'mysterious'],
            avatar: '/api/placeholder/64/64'
          },
          {
            id: '2', 
            name: 'Kai',
            description: 'Adventurous spirit with a warm heart and brave soul',
            creator: 'Creator2',
            followers: 890,
            chats: 2100,
            rating: 4.6,
            verified: false,
            premium: true,
            tags: ['adventure', 'friendly'],
            avatar: '/api/placeholder/64/64'
          }
        ],
        stories: [
          {
            id: '1',
            title: 'The Magic Garden',
            description: 'A heartwarming story about friendship and magic in a secret garden',
            creator: 'StoryWriter',
            chapters: 12,
            likes: 450,
            reads: 1200,
            rating: 4.7,
            character: 'Luna',
            status: 'completed',
            tags: ['fantasy', 'friendship']
          }
        ],
        users: [
          {
            id: '1',
            name: 'AliceCreator',
            bio: 'Creating magical experiences and wonderful stories',
            followers: 2300,
            characters: 15,
            stories: 8,
            verified: true,
            premium: true,
            avatar: '/api/placeholder/64/64'
          }
        ],
        memories: [
          {
            id: '1',
            title: 'A Beautiful Promise',
            character: 'Luna',
            emotion: 'happy',
            importance: 9,
            tags: ['promise', 'friendship'],
            created: '2024-01-15',
            private: false
          }
        ]
      };
      
      setSearchResults(mockResults);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter search results
  const filteredResults = useMemo(() => {
    const results = { ...searchResults };
    
    if (activeTab !== 'all') {
      return {
        [activeTab]: results[activeTab] || []
      };
    }
    
    return results;
  }, [searchResults, activeTab, filters]);

  // Get result counts
  const resultCounts = useMemo(() => {
    const counts = {
      characters: searchResults.characters?.length || 0,
      stories: searchResults.stories?.length || 0,
      users: searchResults.users?.length || 0,
      memories: searchResults.memories?.length || 0
    };
    
    return {
      ...counts,
      all: Object.values(counts).reduce((sum, count) => sum + count, 0)
    };
  }, [searchResults]);

  // Handle clear search
  const handleClearSearch = () => {
    setSearchQuery('');
    setSearchResults({
      characters: [],
      stories: [],
      users: [],
      memories: []
    });
  };

  // Handle clear history
  const handleClearHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem('searchHistory');
  };

  // Handle filter reset
  const handleResetFilters = () => {
    setFilters({
      sortBy: 'relevance',
      category: 'all',
      tags: [],
      dateRange: 'all',
      verified: false,
      premium: false
    });
  };

  // 根据屏幕尺寸设置header高度
  const headerHeight = isMobile ? 'h-14' : 'h-16';
  const hasResults = resultCounts.all > 0;
  
  return (
    <div
      className={`fixed inset-0 z-[70] transition-opacity duration-300 ease-in-out ${
        isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}
      onClick={onClose}
    >
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
      <div
        className={`absolute top-0 right-0 h-full w-full bg-white dark:bg-gray-900 shadow-2xl transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Enhanced Search Header */}
        <div className={`sticky top-0 z-20 ${headerHeight} flex-shrink-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700`}>
          <div className="flex items-center h-full px-4">
            {/* Search Input */}
            <div className="flex-1 relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl blur-sm group-focus-within:blur-none transition-all duration-300"></div>
              <div className="relative backdrop-blur-xl bg-gray-50/90 dark:bg-gray-800/90 rounded-xl border border-gray-200 dark:border-gray-700">
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <Search className="w-5 h-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch(searchQuery)}
                  placeholder={t('search.searchPlaceholder')}
                  className="w-full pl-12 pr-12 py-3 bg-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 outline-none text-sm"
                  autoFocus
                />
                {searchQuery && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
            
            {/* Filter and Close buttons */}
            <div className="flex items-center gap-2 ml-4">
              {hasResults && (
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`p-2 rounded-lg border transition-all duration-200 ${
                    showFilters 
                      ? 'bg-blue-100 dark:bg-blue-900/50 border-blue-300 dark:border-blue-700 text-blue-600 dark:text-blue-400' 
                      : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <SlidersHorizontal className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={onClose}
                className="p-2 text-gray-500 hover:text-gray-800 dark:hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="h-full overflow-y-auto pb-16">
          {!searchQuery && !hasResults ? (
            /* Search Home */
            <div className="p-6 space-y-6">
              {/* Search History */}
              {searchHistory.length > 0 && (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      {t('search.recentSearches')}
                    </h3>
                    <button
                      onClick={handleClearHistory}
                      className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                      {t('search.clearHistory')}
                    </button>
                  </div>
                  <div className="space-y-2">
                    {searchHistory.map((query, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          setSearchQuery(query);
                          handleSearch(query);
                        }}
                        className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center gap-2"
                      >
                        <Clock className="w-3 h-3 text-gray-400" />
                        {query}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Popular Searches */}
              <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  {t('search.popularSearches')}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {popularSearches.map((query, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setSearchQuery(query);
                        handleSearch(query);
                      }}
                      className="px-3 py-1.5 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium hover:from-blue-200 hover:to-purple-200 dark:hover:from-blue-900/50 dark:hover:to-purple-900/50 transition-all duration-200"
                    >
                      {query}
                    </button>
                  ))}
                </div>
              </div>

              {/* Search Tips */}
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Search className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {t('search.title')}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {t('search.subtitle')}
                </p>
              </div>
            </div>
          ) : (
            /* Search Results */
            <div className="space-y-4 p-4">
              {/* Tabs */}
              <SearchTabs
                lang={lang}
                activeTab={activeTab}
                onTabChange={setActiveTab}
                resultCounts={resultCounts}
              />

              {/* Filters */}
              {showFilters && (
                <SearchFiltersComponent
                  lang={lang}
                  filters={filters}
                  onFiltersChange={setFilters}
                  onResetFilters={handleResetFilters}
                />
              )}

              {/* Loading State */}
              {isLoading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">{t('search.searching')}</p>
                </div>
              ) : searchQuery && resultCounts.all === 0 ? (
                /* No Results */
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Search className="w-6 h-6 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    {t('search.noResults')}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {t('search.noResultsDescription')}
                  </p>
                </div>
              ) : (
                /* Results Display */
                <div className="space-y-6">
                  {/* Characters */}
                  {(activeTab === 'all' || activeTab === 'characters') && filteredResults.characters?.length > 0 && (
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                        <Users className="w-5 h-5" />
                        {t('search.characters.title')}
                        <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                          ({filteredResults.characters.length})
                        </span>
                      </h2>
                      <div className="space-y-3">
                        {filteredResults.characters.map((character: any) => (
                          <div
                            key={character.id}
                            className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200 cursor-pointer"
                          >
                            <div className="flex items-start gap-3">
                              <img
                                src={character.avatar}
                                alt={character.name}
                                className="w-10 h-10 rounded-full object-cover"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h3 className="font-medium text-gray-900 dark:text-white text-sm truncate">
                                    {character.name}
                                  </h3>
                                  {character.verified && (
                                    <Verified className="w-4 h-4 text-blue-500" />
                                  )}
                                  {character.premium && (
                                    <Crown className="w-4 h-4 text-yellow-500" />
                                  )}
                                </div>
                                <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                                  {character.description}
                                </p>
                                <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                                  <span className="flex items-center gap-1">
                                    <Heart className="w-3 h-3" />
                                    {character.followers}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <MessageCircle className="w-3 h-3" />
                                    {character.chats}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <Star className="w-3 h-3" />
                                    {character.rating}
                                  </span>
                                </div>
                              </div>
                              <button className="px-3 py-1.5 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-lg text-xs font-medium hover:bg-blue-200 dark:hover:bg-blue-900/70 transition-colors">
                                {t('search.actions.chat')}
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Stories */}
                  {(activeTab === 'all' || activeTab === 'stories') && filteredResults.stories?.length > 0 && (
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                        <BookOpen className="w-5 h-5" />
                        {t('search.stories.title')}
                        <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                          ({filteredResults.stories.length})
                        </span>
                      </h2>
                      <div className="space-y-3">
                        {filteredResults.stories.map((story: any) => (
                          <div
                            key={story.id}
                            className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200 cursor-pointer"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <h3 className="font-medium text-gray-900 dark:text-white text-sm mb-1">
                                  {story.title}
                                </h3>
                                <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                                  {story.description}
                                </p>
                                <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                                  <span>{story.chapters} chapters</span>
                                  <span className="flex items-center gap-1">
                                    <Heart className="w-3 h-3" />
                                    {story.likes}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <Eye className="w-3 h-3" />
                                    {story.reads}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <Star className="w-3 h-3" />
                                    {story.rating}
                                  </span>
                                </div>
                              </div>
                              <button className="px-3 py-1.5 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 rounded-lg text-xs font-medium hover:bg-green-200 dark:hover:bg-green-900/70 transition-colors ml-3">
                                {t('search.actions.read')}
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Users */}
                  {(activeTab === 'all' || activeTab === 'users') && filteredResults.users?.length > 0 && (
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                        <User className="w-5 h-5" />
                        {t('search.users.title')}
                        <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                          ({filteredResults.users.length})
                        </span>
                      </h2>
                      <div className="space-y-3">
                        {filteredResults.users.map((user: any) => (
                          <div
                            key={user.id}
                            className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200 cursor-pointer"
                          >
                            <div className="flex items-start gap-3">
                              <img
                                src={user.avatar}
                                alt={user.name}
                                className="w-10 h-10 rounded-full object-cover"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h3 className="font-medium text-gray-900 dark:text-white text-sm">
                                    {user.name}
                                  </h3>
                                  {user.verified && (
                                    <Verified className="w-4 h-4 text-blue-500" />
                                  )}
                                  {user.premium && (
                                    <Crown className="w-4 h-4 text-yellow-500" />
                                  )}
                                </div>
                                <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                                  {user.bio}
                                </p>
                                <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                                  <span className="flex items-center gap-1">
                                    <Heart className="w-3 h-3" />
                                    {user.followers} followers
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <Users className="w-3 h-3" />
                                    {user.characters} characters
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <BookOpen className="w-3 h-3" />
                                    {user.stories} stories
                                  </span>
                                </div>
                              </div>
                              <button className="px-3 py-1.5 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-lg text-xs font-medium hover:bg-purple-200 dark:hover:bg-purple-900/70 transition-colors">
                                {t('search.actions.follow')}
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Memories */}
                  {(activeTab === 'all' || activeTab === 'memories') && filteredResults.memories?.length > 0 && (
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                        <Brain className="w-5 h-5" />
                        {t('search.memories.title')}
                        <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                          ({filteredResults.memories.length})
                        </span>
                      </h2>
                      <div className="space-y-3">
                        {filteredResults.memories.map((memory: any) => (
                          <div
                            key={memory.id}
                            className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200 cursor-pointer"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <h3 className="font-medium text-gray-900 dark:text-white text-sm mb-1">
                                  {memory.title}
                                </h3>
                                <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400 mb-2">
                                  <span>Character: {memory.character}</span>
                                  <span>Emotion: {memory.emotion}</span>
                                  <span>Importance: {memory.importance}/10</span>
                                  <span className="flex items-center gap-1">
                                    <Calendar className="w-3 h-3" />
                                    {memory.created}
                                  </span>
                                </div>
                                <div className="flex flex-wrap gap-1">
                                  {memory.tags.map((tag: string, index: number) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs"
                                    >
                                      {tag}
                                    </span>
                                  ))}
                                </div>
                              </div>
                              <button className="px-3 py-1.5 bg-amber-100 dark:bg-amber-900/50 text-amber-700 dark:text-amber-300 rounded-lg text-xs font-medium hover:bg-amber-200 dark:hover:bg-amber-900/70 transition-colors ml-3">
                                {t('search.actions.view')}
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SearchOverlay;