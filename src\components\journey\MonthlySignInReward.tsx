'use client';

import React, { useState } from 'react';
import { Calendar, Flame, Gift, Crown, Zap, Lock, Sparkles, TrendingUp, Timer } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { useResponsive } from '@/hooks/useResponsive';
import UnifiedTrackGrid from './UnifiedTrackGrid';

interface MonthlySignInRewardProps {
  lang: string;
}

interface RewardCard {
  day: number;
  reward: {
    type: 'currency' | 'cosmetic' | 'item' | 'exclusive';
    name: string;
    amount?: number;
    icon: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
  };
  claimed: boolean;
  available: boolean;
}

// 游戏化签到概览组件
const GameifiedSignInOverview: React.FC<{ lang: string }> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const { breakpoint } = useResponsive();

  // 模拟数据
  const currentDay = 8;
  const signedInDays = 6;
  const consecutiveDays = 5;
  const hasPass = false; // 用户是否有Pass
  
  // 计算免费vs付费奖励对比
  const freeRewards = { alphane: 150, endora: 50 };
  const passRewards = { alphane: 450, endora: 280, exclusive: 3 };
  const potentialLoss = passRewards.alphane + passRewards.endora - freeRewards.alphane - freeRewards.endora;

  return (
    <div className="space-y-4 mb-6">
      {/* 主签到状态 - 游戏化设计 */}
      <div className="relative overflow-hidden bg-gradient-to-br from-indigo-900/20 via-purple-900/20 to-pink-900/20 backdrop-blur-sm border border-purple-500/20 rounded-3xl p-6">
        {/* 装饰性背景元素 */}
        <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-yellow-400/10 to-transparent rounded-full -translate-y-20 translate-x-20"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-cyan-400/10 to-transparent rounded-full translate-y-16 -translate-x-16"></div>
        <div className="absolute top-1/2 left-1/2 w-2 h-2 bg-white/30 rounded-full animate-ping"></div>
        <div className="absolute top-1/4 right-1/4 w-1 h-1 bg-purple-400/50 rounded-full animate-pulse"></div>

        <div className="relative flex items-center justify-between">
          {/* 左侧：签到状态 */}
          <div className="flex items-center gap-4">
            <div className="relative">
              {/* 签到图标 - 3D效果 */}
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-xl shadow-purple-500/30 transform rotate-3 hover:rotate-0 transition-all duration-300">
                <Calendar className="w-8 h-8 text-white drop-shadow-lg" />
              </div>
              {/* 连击火焰徽章 */}
              {consecutiveDays >= 3 && (
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center animate-pulse">
                  <Flame className="w-4 h-4 text-white" />
                </div>
              )}
            </div>
            
            <div>
              <h2 className="text-2xl font-bold text-white mb-1 flex items-center gap-2">
                {t('journey.signIn.monthlySignIn')}
                {consecutiveDays >= 5 && <span className="text-lg">🔥</span>}
              </h2>
              <div className="flex items-center gap-4 text-sm">
                <span className="text-green-400 font-semibold">{signedInDays}/30 {t('common.days')}</span>
                <span className="text-orange-400">{t('journey.signIn.streak')} {consecutiveDays} {t('common.days')}</span>
              </div>
            </div>
          </div>

          {/* 右侧：下次签到倒计时 - 游戏化设计 */}
          <div className="text-right">
            <div className="bg-black/20 backdrop-blur rounded-lg px-4 py-2 border border-white/10">
              <div className="flex items-center gap-2 text-purple-300 text-sm mb-1">
                <Timer className="w-4 h-4" />
                {t('journey.signIn.nextSignIn')}
              </div>
              <div className="text-xl font-bold text-white font-mono">14:32:45</div>
            </div>
          </div>
        </div>

        {/* 进度条 - 游戏化设计 */}
        <div className="mt-6 relative">
          <div className="flex justify-between text-xs text-white/60 mb-2">
            <span>{t('journey.signIn.monthProgress')}</span>
            <span>{Math.round((signedInDays / 30) * 100)}% {t('overview.completed')}</span>
          </div>
          <div className="relative h-3 bg-black/30 rounded-full overflow-hidden border border-white/10">
            <div
              className="h-full bg-gradient-to-r from-green-400 via-blue-500 to-purple-500 transition-all duration-1000 ease-out relative"
              style={{ width: `${(signedInDays / 30) * 100}%` }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shine"></div>
            </div>
            {/* 里程碑标记 */}
            {[7, 14, 21, 30].map((milestone) => (
              <div
                key={milestone}
                className={`absolute top-0 w-1 h-full ${
                  signedInDays >= milestone ? 'bg-yellow-400' : 'bg-white/30'
                }`}
                style={{ left: `${(milestone / 30) * 100}%` }}
              />
            ))}
          </div>
        </div>
      </div>

      {/* 响应式网格：PC端并排，移动端垂直堆叠 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* 会员升级推销区域 - 重点设计 */}
        {!hasPass && (
          <div className="lg:col-span-2 relative bg-gradient-to-r from-yellow-500/20 via-orange-500/20 to-red-500/20 border-2 border-yellow-400/30 rounded-2xl p-4 animate-pulse">
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/10 to-orange-400/10 rounded-2xl animate-ping"></div>
            
            <div className="relative">
              {/* 顶部：标题和升级按钮 */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                    <Crown className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-yellow-100 flex items-center gap-2">
                      {t('journey.signIn.upgradeToUnlock')}
                      <Sparkles className="w-4 h-4 text-yellow-400" />
                    </h3>
                    <p className="text-sm text-yellow-200/80">
                      {t('journey.signIn.unlockPass')}<span className="font-bold text-yellow-300">3x</span>
                    </p>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-2xl font-bold text-yellow-300 mb-1">
                    +{potentialLoss}
                  </div>
                  <div className="text-xs text-yellow-200/80 mb-2">{t('journey.signIn.extraEarnings')}</div>
                  <button className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-2 rounded-lg font-bold text-sm hover:scale-105 transition-all shadow-lg">
                    {t('journey.signIn.upgradeNow')}
                  </button>
                </div>
              </div>

              {/* 底部：对比预览 */}
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-black/20 rounded-lg p-3 border border-gray-500/30">
                  <div className="text-center">
                    <div className="text-gray-400 text-xs mb-1">{t('journey.signIn.freeTrackRewards')}</div>
                    <div className="text-gray-300 font-bold">{freeRewards.alphane + freeRewards.endora} {t('tokens.currency')}/月</div>
                  </div>
                </div>
                <div className="bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-lg p-3 border border-yellow-400/30">
                  <div className="text-center">
                    <div className="text-yellow-300 text-xs mb-1">{t('journey.signIn.passTrackRewards')}</div>
                    <div className="text-yellow-100 font-bold">{passRewards.alphane + passRewards.endora} {t('tokens.currency')}/月</div>
                    <div className="text-green-400 text-xs mt-1">+{passRewards.exclusive} {t('journey.rewards.exclusive')}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 今日奖励预览 - 精简高效 */}
        <div className={`bg-white/5 dark:bg-black/5 backdrop-blur-sm border border-white/10 rounded-xl p-4 ${!hasPass ? '' : 'lg:col-span-3'}`}>
          <div className="flex flex-col lg:items-center lg:justify-center h-full">
            <div className="flex items-center gap-3 mb-4 lg:mb-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Gift className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">{t('journey.signIn.todayReward')}</h3>
                <p className="text-sm text-foreground/60">{t('common.days')} {currentDay} {t('journey.rewards.reward')}</p>
              </div>
            </div>
            
            <div className="flex items-center justify-center gap-4">
              <div className="text-center bg-green-500/10 border border-green-500/30 rounded-lg p-3 min-w-[80px]">
                <div className="text-lg font-bold text-green-400">25 🪙</div>
                <div className="text-xs text-foreground/60">{t('journey.tracks.free')}</div>
              </div>
              {!hasPass && (
                <>
                  <div className="text-foreground/30 text-2xl">vs</div>
                  <div className="text-center bg-purple-500/10 border border-purple-500/30 rounded-lg p-3 min-w-[80px] opacity-50">
                    <div className="text-lg font-bold text-purple-400 flex items-center justify-center gap-1">
                      <Lock className="w-3 h-3" />
                      75 💎
                    </div>
                    <div className="text-xs text-foreground/60">Pass</div>
                  </div>
                </>
              )}
            </div>

            {/* 如果有Pass，显示今日可领取的所有奖励 */}
            {hasPass && (
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-2">
                <div className="text-center bg-green-500/10 border border-green-500/30 rounded-lg p-2">
                  <div className="font-bold text-green-400">25 🪙</div>
                  <div className="text-xs text-foreground/60">{t('journey.tracks.free')}</div>
                </div>
                <div className="text-center bg-purple-500/10 border border-purple-500/30 rounded-lg p-2">
                  <div className="font-bold text-purple-400">75 💎</div>
                  <div className="text-xs text-foreground/60">Pass</div>
                </div>
                <div className="text-center bg-blue-500/10 border border-blue-500/30 rounded-lg p-2">
                  <div className="font-bold text-blue-400">100 🔮</div>
                  <div className="text-xs text-foreground/60">Diamond</div>
                </div>
                <div className="text-center bg-indigo-500/10 border border-indigo-500/30 rounded-lg p-2">
                  <div className="font-bold text-indigo-400">150 🌌</div>
                  <div className="text-xs text-foreground/60">MetaVerse</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const MonthlySignInReward: React.FC<MonthlySignInRewardProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const { breakpoint } = useResponsive();
  
  // 模拟数据
  const [currentLevel] = useState(8);
  const [hasHeartTrack] = useState(false);
  const [hasDiamondTrack] = useState(false);
  const [hasMetaverseTrack] = useState(false);

  const handleTrackPurchase = (trackType: 'free' | 'pass' | 'diamond' | 'metaverse') => {
    console.log(`Purchasing ${trackType} track`);
  };

  // Generate 26 reward cards for each track
  const generateRewardCards = (trackType: 'free' | 'pass' | 'diamond' | 'metaverse'): RewardCard[] => {
    const cards: RewardCard[] = [];

    for (let day = 1; day <= 26; day++) {
      const isAvailable = day <= currentLevel;
      const isClaimed = day < currentLevel;

      let reward;

      if (trackType === 'free') {
        reward = {
          type: 'currency' as const,
          name: day % 7 === 0 ? 'Endora' : 'Alphane',
          amount: day % 7 === 0 ? 50 : 25,
          icon: day % 7 === 0 ? '💎' : '🪙',
          rarity: day % 7 === 0 ? 'rare' as const : 'common' as const
        };
      } else if (trackType === 'pass') {
        reward = {
          type: day % 5 === 0 ? 'cosmetic' as const : 'currency' as const,
          name: day % 5 === 0 ? 'Heart Frame' : 'Endora',
          amount: day % 5 === 0 ? undefined : 75,
          icon: day % 5 === 0 ? '💖' : '💎',
          rarity: day % 5 === 0 ? 'epic' as const : 'rare' as const
        };
      } else if (trackType === 'diamond') {
        reward = {
          type: day % 3 === 0 ? 'exclusive' as const : 'currency' as const,
          name: day % 3 === 0 ? 'Diamond Avatar' : 'Oxytol',
          amount: day % 3 === 0 ? undefined : 100,
          icon: day % 3 === 0 ? '💠' : '🔮',
          rarity: day % 3 === 0 ? 'legendary' as const : 'epic' as const
        };
      } else {
        // MetaVerse track - most premium rewards
        reward = {
          type: day % 2 === 0 ? 'exclusive' as const : 'currency' as const,
          name: day % 2 === 0 ? 'MetaVerse Avatar' : 'Quantum Crystals',
          amount: day % 2 === 0 ? undefined : 150,
          icon: day % 2 === 0 ? '🌌' : '🔮',
          rarity: day % 2 === 0 ? 'legendary' as const : 'legendary' as const
        };
      }
      
      cards.push({
        day,
        reward,
        claimed: isClaimed,
        available: isAvailable
      });
    }
    
    return cards;
  };

  // Generate cards for all tracks
  const freeTrackCards = generateRewardCards('free');
  const passTrackCards = generateRewardCards('pass');
  const diamondTrackCards = generateRewardCards('diamond');
  const metaverseTrackCards = generateRewardCards('metaverse');

  // Track data configuration
  const tracks = [
    {
      type: 'free' as const,
      name: 'Standard',
      isUnlocked: true,
      cards: freeTrackCards
    },
    {
      type: 'pass' as const,
      name: 'Pass',
      isUnlocked: hasHeartTrack,
      cards: passTrackCards
    },
    {
      type: 'diamond' as const,
      name: 'Diamond',
      isUnlocked: hasDiamondTrack,
      cards: diamondTrackCards
    },
    {
      type: 'metaverse' as const,
      name: 'MetaVerse',
      isUnlocked: hasMetaverseTrack,
      cards: metaverseTrackCards
    }
  ];

  return (
    <div className={`space-y-4 md:space-y-6 ${breakpoint === 'mobile' ? 'px-2' : ''} animate-in fade-in duration-700 -mx-4`}>
      <div className="mx-2 space-y-4 md:space-y-6">
        {/* 游戏化签到概览区域 */}
        <GameifiedSignInOverview lang={lang} />

      {/* Unified Track Grid */}
      <div className="animate-in slide-in-from-bottom duration-700">
        <UnifiedTrackGrid
          tracks={tracks}
          currentLevel={currentLevel}
            onTrackPurchase={handleTrackPurchase}
        />
      </div>
      </div>
    </div>
  );
};

export default MonthlySignInReward;
