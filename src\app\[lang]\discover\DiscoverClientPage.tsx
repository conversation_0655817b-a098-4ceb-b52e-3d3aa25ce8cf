'use client';

import { useState } from 'react';
import MainAppLayout from '@/components/MainAppLayout';
import CharacterCard from '@/components/CharacterCard';
import CharacterGenreFilter, { GenreFilters } from '@/components/CharacterGenreFilter';
import { SlidersHorizontal, X } from 'lucide-react';
import Link from 'next/link';
import { Character } from '@/lib/mock-data';

interface CardData {
  character: Character;
  stats: {
    likes: number;
    friends: number;
    shares: number;
  };
  aspectRatio: number;
}

interface DiscoverClientPageProps {
  lang: string;
  cardsData: CardData[];
}

const DiscoverClientPage: React.FC<DiscoverClientPageProps> = ({ lang, cardsData }) => {
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<GenreFilters>({
    gender: '',
    pov: ''
  });

  return (
    <MainAppLayout lang={lang}>
      <div className="space-y-4 p-1.5">
        {/* Top bar */}
        <div className="flex items-center justify-between">
          <Link href="#" className="text-indigo-600 font-semibold">灵魂测试</Link>
          <h1 className="font-bold text-lg text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 to-pink-500">Alphane.ai</h1>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-full transition-colors ${
              showFilters
                ? 'bg-purple-100 dark:bg-purple-800 text-purple-600 dark:text-purple-300'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300'
            }`}
          >
            {showFilters ? <X size={18} /> : <SlidersHorizontal size={18} />}
          </button>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 shadow-lg">
            <CharacterGenreFilter
              filters={filters}
              onFiltersChange={setFilters}
              lang={lang}
            />
          </div>
        )}

        {/* Cards masonry */}
        <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
          {cardsData.map(({ character, stats, aspectRatio }) => (
            <div key={character.id} className="break-inside-avoid">
              <CharacterCard lang={lang} character={character} stats={stats} aspectRatio={aspectRatio} />
            </div>
          ))}
        </div>
      </div>
    </MainAppLayout>
  );
};

export default DiscoverClientPage; 