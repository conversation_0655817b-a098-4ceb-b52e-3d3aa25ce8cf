import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import EditCharacterClientPage from '../EditCharacterClientPage';

interface EditCharacterPageProps {
  params: Promise<{
    lang: string;
    characterId: string;
  }>;
}

export default async function EditCharacterPage({ params }: EditCharacterPageProps) {
  const { lang, characterId } = await params;

  return (
    <AuthGuard requireAuth={true}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <EditCharacterClientPage lang={lang} characterId={characterId} />
      </Suspense>
    </AuthGuard>
  );
} 