'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { MoreHorizontal } from 'lucide-react';
import { characters } from '@/lib/mock-data';
import { useTranslation } from '@/app/i18n/client';

interface SidebarRecentChatsProps {
  lang: string;
}

const SidebarRecentChats: React.FC<SidebarRecentChatsProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  // Get first 4 characters from mock data
  const recentChats = characters.slice(0, 4);

  return (
    <div className="px-4 py-2">
      <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
        {t('sidebar.recentChats')}
      </h3>

      {/* Evenly distributed character list - 5 equal width columns */}
      <div className="grid grid-cols-5 gap-2">
        {/* 4 Recent Characters */}
        {recentChats.map((character) => (
          <Link
            key={character.id}
            href={`/${lang}/chats/${character.id}`}
            className="flex flex-col items-center hover:opacity-80 transition-opacity"
          >
            <Image
              src={character.character_avatar}
              alt={character.name}
              width={44}
              height={44}
              className="w-11 h-11 rounded-full border-2 border-pink-200 dark:border-pink-600 mb-1"
            />
            <span className="text-xs font-medium text-gray-700 dark:text-gray-200 text-center w-full overflow-hidden text-ellipsis whitespace-nowrap">
              {character.name}
            </span>
          </Link>
        ))}

        {/* View All Button */}
        <Link
          href={`/${lang}/chats`}
          className="flex flex-col items-center hover:opacity-80 transition-opacity"
        >
          <div className="w-11 h-11 rounded-full border-2 border-dashed border-gray-300 dark:border-gray-600 mb-1 flex items-center justify-center">
            <MoreHorizontal size={18} className="text-gray-400 dark:text-gray-500" />
          </div>
          <span className="text-xs font-medium text-gray-700 dark:text-gray-200 text-center w-full overflow-hidden text-ellipsis whitespace-nowrap">
            All
          </span>
        </Link>
      </div>
    </div>
  );
};

export default SidebarRecentChats;
