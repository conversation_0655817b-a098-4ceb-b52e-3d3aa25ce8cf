# Alphane AI - Your Warm AI Companion

Alphane AI is an emotional companion AI platform that creates deep connections through immersive character interactions and personalized conversations. Built with [Next.js](https://nextjs.org) and modern web technologies.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

## Features

- 🤖 **AI Character Creation**: Create and customize AI companions with unique personalities
- 💬 **Immersive Chat Experience**: Deep, emotional conversations with AI characters
- 🎨 **Character Customization**: Detailed character creation with appearance, personality, and behavior settings
- 🌍 **Multi-language Support**: Available in English, Chinese, and Japanese
- 🎯 **Gamification Elements**: Daily tasks, journey system, and interaction streaks
- 🎭 **Role-playing Features**: Rich character backgrounds and interactive storytelling

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS with custom romantic purple-pink theme
- **UI Components**: Shadcn/ui components
- **Internationalization**: i18next for multi-language support
- **Icons**: Lucide React
- **Fonts**: Custom Japanese (JK Maru Gothic) and English (Quicksand) fonts

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
├── components/             # Reusable UI components
├── hooks/                  # Custom React hooks
├── lib/                    # Utility functions
└── i18n/                   # Internationalization files

Docs/                       # Project documentation
├── Plan.md                 # Project planning document
├── Pricing.md              # Pricing and currency system
└── APIdocs.md              # API documentation
```

## Development

This project uses modern development tools:

- **Biome**: For linting and formatting
- **TypeScript**: For type safety
- **Tailwind CSS**: For styling

Run linting and formatting:
```bash
npm run lint
npm run format
```

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
