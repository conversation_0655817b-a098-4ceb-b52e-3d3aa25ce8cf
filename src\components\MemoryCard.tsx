'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Heart, Share2, Clock } from 'lucide-react';
import { ChatRecord } from './MemoryCapsule';

interface MemoryCardProps {
  chatRecord: ChatRecord;
  onClick: () => void;
  aspectRatio?: number;
}

const MemoryCard: React.FC<MemoryCardProps> = ({ chatRecord, onClick, aspectRatio = 0.75 }) => {
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(chatRecord.stats.likes);
  const [imageSrc, setImageSrc] = useState(chatRecord.backgroundImage || 'https://picsum.photos/500/800');

  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
  };

  const handleCardClick = () => {
    onClick();
  };

  return (
    <div
      className="group relative overflow-hidden rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer"
      onClick={handleCardClick}
    >
      {/* Placeholder to maintain aspect ratio */}
      <div style={{ paddingBottom: `${(1 / aspectRatio) * 100}%` }} />

      {/* Background Image */}
      <Image
        src={imageSrc}
        alt={chatRecord.title}
        fill
        className="absolute top-0 left-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        unoptimized
        onError={() => setImageSrc('https://picsum.photos/500/800')}
      />

      {/* Gradient overlay covering bottom 60% */}
      <div className="absolute bottom-0 left-0 right-0 h-[60%] bg-gradient-to-t from-black/80 via-black/40 to-transparent" />

      {/* Content overlay */}
      <div className="absolute inset-0 p-4 flex flex-col justify-end">
        {/* Memory description (complete text) */}
        <h3 className="font-medium text-xs text-white mb-3">
          {chatRecord.title}
        </h3>

        {/* Participants info and actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Multiple participant avatars */}
            <div className="flex -space-x-1">
              {chatRecord.participants.map((participant, index) => (
                <Image
                  key={participant.id}
                  src={participant.avatar}
                  alt={participant.name}
                  width={32}
                  height={32}
                  className="w-8 h-8 rounded-full border-2 border-white/60 flex-shrink-0 object-cover"
                  style={{ zIndex: chatRecord.participants.length - index }}
                  unoptimized
                  onError={() => {}}
                />
              ))}
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-semibold text-sm text-white truncate">
                {chatRecord.participants.map(p => p.name).join(' & ')}
              </p>
              <div className="flex items-center gap-1 text-xs text-white/80">
                <Clock size={12} />
                <span>{chatRecord.createdAt}</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-3 text-white/90">
            <button
              onClick={handleLike}
              className="flex items-center gap-1 hover:text-red-400 transition-colors"
            >
              <Heart size={16} className={isLiked ? 'text-red-400 fill-current' : ''} />
              <span className="text-xs">{likeCount}</span>
            </button>
            <div className="flex items-center gap-1">
              <Share2 size={16} />
              <span className="text-xs">{chatRecord.stats.shares}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemoryCard;
