'use client';

import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { Plus, X, Send, ArrowRight, ArrowLeft, Book, GitBranch, Settings, Heart, MessageCircle, Upload, Check } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryFormData, StoryChapter, StoryChoice, ChapterCompletionEffects } from '@/types/story-creation';
import StoryFlow from './StoryFlow';
import { reorderChapters, getNextBranchLetter } from '@/utils/story-chapter-reorder';
import { useStoryImageUpload } from '@/hooks/story-creation/useStoryImageUpload';

interface StoryFlowStepProps {
  formData: StoryFormData;
  setFormData: (data: StoryFormData | ((prev: StoryFormData) => StoryFormData)) => void;
  lang: string;
  onStepChange?: (step: 'worldSetting' | 'storyFlow' | 'objectivesSubjectives') => void;
  onSubmit?: () => void;
  selectedChapter: string | null;
  setSelectedChapter: (chapterId: string | null) => void;
}

const StoryFlowStep: React.FC<StoryFlowStepProps> = ({
  formData,
  setFormData,
  lang,
  onStepChange,
  onSubmit,
  selectedChapter,
  setSelectedChapter
}) => {
  const { t } = useTranslation(lang, 'translation');

  // Use the story image upload hook
  const {
    worldSettingImagePreview,
    chapterImageInputRefs,
    handleChapterImageUpload,
    toggleUseWorldSettingImage,
    triggerChapterImageInput,
    getChapterImagePreview
  } = useStoryImageUpload(formData, setFormData);

  // 生成章节显示名称
  const getChapterDisplayName = useCallback((chapter: StoryChapter) => {
    if (chapter.chapterType === 'main') {
      return `${chapter.order}`;
    } else {
      return `${chapter.order}${chapter.branchLetter || ''}`;
    }
  }, []);

  // 获取下一个主章节编号
  const getNextMainChapterNumber = useCallback(() => {
    const mainChapters = formData.chapters.filter(c => c.chapterType === 'main');
    return mainChapters.length + 1;
  }, [formData.chapters]);

  // 获取可用的分支字母 - 使用工具函数
  const getNextBranchLetterForChapter = useCallback((chapterNumber: number) => {
    return getNextBranchLetter(formData.chapters, chapterNumber);
  }, [formData.chapters]);

  // 添加主章节
  const handleAddMainChapter = useCallback(() => {
    const chapterNumber = getNextMainChapterNumber();
    const newChapter: StoryChapter = {
      id: `chapter-${Date.now()}`,
      title: `Chapter ${chapterNumber}`,
      description: '',
      content: '',
      order: chapterNumber,
      chapterType: 'main',
      backgroundSetting: '',
      completionEffects: {
        bondPointsChange: 0,
        greetingChange: '',
        characterMoodChange: '',
        customEffects: ''
      },
      choices: [],
      nextChapters: []
    };
    setFormData(prev => ({
      ...prev,
      chapters: [...prev.chapters, newChapter]
    }));
    setSelectedChapter(newChapter.id);
  }, [getNextMainChapterNumber, setFormData, setSelectedChapter]);

  // 添加分支章节
  const handleAddBranchChapter = useCallback((parentChapterNumber: number) => {
    const branchLetter = getNextBranchLetterForChapter(parentChapterNumber);
    const parentChapter = formData.chapters.find(
      c => c.chapterType === 'main' && c.order === parentChapterNumber
    );
    
    const newChapter: StoryChapter = {
      id: `chapter-${Date.now()}`,
      title: `Chapter ${parentChapterNumber}${branchLetter}`,
      description: '',
      content: '',
      order: parentChapterNumber,
      chapterType: 'branch',
      parentChapter: parentChapter?.id,
      branchLetter,
      backgroundSetting: '',
      completionEffects: {
        bondPointsChange: 0,
        greetingChange: '',
        characterMoodChange: '',
        customEffects: ''
      },
      choices: [],
      nextChapters: []
    };
    setFormData(prev => ({
      ...prev,
      chapters: [...prev.chapters, newChapter]
    }));
    setSelectedChapter(newChapter.id);
  }, [getNextBranchLetterForChapter, formData.chapters, setFormData, setSelectedChapter]);

  // 删除章节
  const handleRemoveChapter = useCallback((chapterId: string) => {
    setFormData(prev => ({
      ...prev,
      chapters: prev.chapters.filter(c => c.id !== chapterId)
    }));
    if (selectedChapter === chapterId) {
      setSelectedChapter(null);
    }
  }, [setFormData, selectedChapter, setSelectedChapter]);

  // 章节重新排序 - 使用工具函数
  const handleChapterReorder = useCallback((chapterId: string, newOrder: number, newParent?: string) => {
    setFormData(prev => ({
      ...prev,
      chapters: reorderChapters(prev.chapters, chapterId, newOrder, newParent)
    }));
  }, [setFormData]);

  // 更新章节信息
  const handleChapterUpdate = useCallback((chapterId: string, updates: Partial<StoryChapter>) => {
    const chapters = formData.chapters.map(chapter =>
      chapter.id === chapterId ? { ...chapter, ...updates } : chapter
    );
    setFormData(prev => ({ ...prev, chapters }));
  }, [formData.chapters, setFormData]);

  // 获取当前选中的章节数据
  const selectedChapterData = selectedChapter 
    ? formData.chapters.find(c => c.id === selectedChapter)
    : null;

  // 返回上一步
  const handleBackToWorldSetting = () => {
    if (onStepChange) {
      onStepChange('worldSetting');
    }
  };

  // 继续到下一步
  const handleContinueToObjectivesSubjectives = () => {
    if (onStepChange) {
      onStepChange('objectivesSubjectives');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2 flex items-center justify-center gap-2">
          <Book className="w-6 h-6 text-purple-500" />
          {t('storyCreation.steps.storyFlow.title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {t('storyCreation.steps.storyFlow.description')}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-10 gap-6">
        {/* Left Side: Story Flow (40% width on desktop, full width on mobile) */}
        <div className="lg:col-span-4 space-y-4">
          <StoryFlow
            chapters={formData.chapters}
            selectedChapter={selectedChapter}
            onChapterSelect={setSelectedChapter}
            onAddMainChapter={handleAddMainChapter}
            onAddBranchChapter={handleAddBranchChapter}
            onRemoveChapter={handleRemoveChapter}
            onChapterReorder={handleChapterReorder}
            lang={lang}
            title={t('storyCreation.steps.storyFlow.storyFlow')}
            icon={<Book className="w-5 h-5" />}
            accentColor="purple"
            showAddButton={true}
            showBranchButtons={true}
            showRemoveButtons={true}
          />
        </div>

        {/* Right Side: Chapter Editing (60% width on desktop, full width on mobile) */}
        <div className="lg:col-span-6 space-y-4">
          {selectedChapterData ? (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 lg:p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4 lg:mb-6">
                <h3 className="text-base lg:text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
                  <Settings className="w-4 h-4 lg:w-5 lg:h-5 text-purple-500" />
                  <span className="truncate">Edit Chapter {getChapterDisplayName(selectedChapterData)}</span>
                </h3>
              </div>

              <div className="space-y-4">
                {/* 章节标题 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.storyFlow.chapterTitle')}
                  </label>
                  <input
                    type="text"
                    value={selectedChapterData.title}
                    onChange={(e) => handleChapterUpdate(selectedChapterData.id, { title: e.target.value })}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                    placeholder={t('storyCreation.steps.storyFlow.chapterTitlePlaceholder')}
                  />
                </div>

                {/* 章节描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.storyFlow.chapterDescription')}
                  </label>
                  <textarea
                    value={selectedChapterData.description}
                    onChange={(e) => handleChapterUpdate(selectedChapterData.id, { description: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                    placeholder={t('storyCreation.steps.storyFlow.chapterDescriptionPlaceholder')}
                  />
                </div>

                {/* 章节内容 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.storyFlow.chapterContent')}
                  </label>
                  <textarea
                    value={selectedChapterData.content}
                    onChange={(e) => handleChapterUpdate(selectedChapterData.id, { content: e.target.value })}
                    rows={4}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                    placeholder={t('storyCreation.steps.storyFlow.chapterContentPlaceholder')}
                  />
                </div>

                {/* 背景设定 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.storyFlow.backgroundSetting')}
                  </label>
                  <textarea
                    value={selectedChapterData.backgroundSetting}
                    onChange={(e) => handleChapterUpdate(selectedChapterData.id, { backgroundSetting: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                    placeholder={t('storyCreation.steps.storyFlow.backgroundSettingPlaceholder')}
                  />
                </div>

                {/* 背景图片 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.storyFlow.backgroundImage')}
                  </label>

                  {/* Use World Setting Image Checkbox */}
                  <div className="mb-3">
                    <label className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedChapterData.useWorldSettingImage || false}
                        onChange={(e) => toggleUseWorldSettingImage(selectedChapterData.id, e.target.checked)}
                        className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 dark:focus:ring-purple-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <Check className="w-3 h-3 text-purple-500" />
                      {t('storyCreation.steps.storyFlow.useWorldSettingImage')}
                    </label>
                  </div>

                  {/* Image Upload Section */}
                  {!selectedChapterData.useWorldSettingImage && (
                    <button
                      type="button"
                      onClick={() => triggerChapterImageInput(selectedChapterData.id)}
                      className="w-full p-4 border-2 border-dashed border-purple-300 dark:border-purple-700 rounded-lg hover:border-purple-400 dark:hover:border-purple-600 transition-all duration-300 bg-white/50 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800 hover:scale-105"
                    >
                      {getChapterImagePreview(selectedChapterData.id) ? (
                        <div className="text-purple-700 dark:text-purple-300">
                          <Image
                            src={getChapterImagePreview(selectedChapterData.id)!}
                            alt="Chapter Background"
                            width={120}
                            height={80}
                            className="mx-auto mb-2 rounded-lg object-cover"
                          />
                          <p className="font-semibold text-sm">{t('storyCreation.steps.storyFlow.backgroundImageUploaded')}</p>
                          <p className="text-xs">{t('storyCreation.steps.storyFlow.clickToChangeImage')}</p>
                        </div>
                      ) : (
                        <div className="text-purple-600 dark:text-purple-400">
                          <Upload className="mx-auto mb-2" size={24} />
                          <p className="font-semibold text-sm">{t('storyCreation.steps.storyFlow.uploadBackgroundImage')}</p>
                          <p className="text-xs">{t('storyCreation.steps.storyFlow.clickOrDragToUpload')}</p>
                        </div>
                      )}
                    </button>
                  )}

                  {/* World Setting Image Preview */}
                  {selectedChapterData.useWorldSettingImage && worldSettingImagePreview && (
                    <div className="p-4 border-2 border-green-300 dark:border-green-700 rounded-lg bg-green-50/50 dark:bg-green-900/20">
                      <div className="text-green-700 dark:text-green-300 text-center">
                        <Image
                          src={worldSettingImagePreview}
                          alt="World Setting Image"
                          width={120}
                          height={80}
                          className="mx-auto mb-2 rounded-lg object-cover"
                        />
                        <p className="font-semibold text-sm">{t('storyCreation.steps.storyFlow.usingWorldSettingImage')}</p>
                        <p className="text-xs">{t('storyCreation.steps.storyFlow.inheritedFromWorldSetting')}</p>
                      </div>
                    </div>
                  )}

                  {/* Hidden file input */}
                  <input
                    ref={(el) => {
                      if (chapterImageInputRefs.current) {
                        chapterImageInputRefs.current[selectedChapterData.id] = el;
                      }
                    }}
                    type="file"
                    accept="image/*"
                    onChange={(e) => e.target.files?.[0] && handleChapterImageUpload(selectedChapterData.id, e.target.files[0])}
                    className="hidden"
                  />
                </div>

                {/* 完成效果 */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                    <Heart className="w-4 h-4 text-rose-500" />
                    {t('storyCreation.steps.storyFlow.completionEffects')}
                  </h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* 好感度变化 */}
                    <div>
                      <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                        {t('storyCreation.steps.storyFlow.bondPointsChange')}
                      </label>
                      <input
                        type="number"
                        value={selectedChapterData.completionEffects?.bondPointsChange || 0}
                        onChange={(e) => handleChapterUpdate(selectedChapterData.id, {
                          completionEffects: {
                            ...selectedChapterData.completionEffects,
                            bondPointsChange: parseInt(e.target.value) || 0
                          }
                        })}
                        className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                        placeholder="0"
                      />
                    </div>

                    {/* 问候语变化 */}
                    <div>
                      <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                        {t('storyCreation.steps.storyFlow.greetingChange')}
                      </label>
                      <input
                        type="text"
                        value={selectedChapterData.completionEffects?.greetingChange || ''}
                        onChange={(e) => handleChapterUpdate(selectedChapterData.id, {
                          completionEffects: {
                            ...selectedChapterData.completionEffects,
                            greetingChange: e.target.value
                          }
                        })}
                        className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                        placeholder={t('storyCreation.steps.storyFlow.greetingChangePlaceholder')}
                      />
                    </div>
                  </div>

                  {/* 角色心情变化 */}
                  <div className="mt-3">
                    <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      {t('storyCreation.steps.storyFlow.characterMoodChange')}
                    </label>
                    <input
                      type="text"
                      value={selectedChapterData.completionEffects?.characterMoodChange || ''}
                      onChange={(e) => handleChapterUpdate(selectedChapterData.id, {
                        completionEffects: {
                          ...selectedChapterData.completionEffects,
                          characterMoodChange: e.target.value
                        }
                      })}
                      className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                      placeholder={t('storyCreation.steps.storyFlow.characterMoodChangePlaceholder')}
                    />
                  </div>

                  {/* 自定义效果 */}
                  <div className="mt-3">
                    <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                      {t('storyCreation.steps.storyFlow.customEffects')}
                    </label>
                    <textarea
                      value={selectedChapterData.completionEffects?.customEffects || ''}
                      onChange={(e) => handleChapterUpdate(selectedChapterData.id, {
                        completionEffects: {
                          ...selectedChapterData.completionEffects,
                          customEffects: e.target.value
                        }
                      })}
                      rows={2}
                      className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                      placeholder={t('storyCreation.steps.storyFlow.customEffectsPlaceholder')}
                    />
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 text-center">
              <Book className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
                {t('storyCreation.steps.storyFlow.selectChapter')}
              </h3>
              <p className="text-gray-500 dark:text-gray-500">
                {t('storyCreation.steps.storyFlow.selectChapterDescription')}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleBackToWorldSetting}
          className="flex items-center gap-2 px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200"
        >
          <ArrowLeft className="w-4 h-4" />
          {t('storyCreation.storyFlow.navigation.backToWorldSetting')}
        </button>
        
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {t('storyCreation.navigation.stepOf', { current: 2, total: 3 })}
        </div>
        
        <button
          onClick={handleContinueToObjectivesSubjectives}
          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          {t('storyCreation.storyFlow.navigation.continueToObjectives')}
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default StoryFlowStep;
