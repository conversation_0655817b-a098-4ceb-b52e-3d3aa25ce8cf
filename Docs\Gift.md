# Gift System Documentation

## Overview
The Gift System allows users to send and receive coupon codes as gifts to share premium features, tokens, and special content. This system supports both user-to-user gifting and system-generated rewards.

## Core Features

### 1. Receive Section
- **Redeem Coupon**: Users can enter coupon codes to redeem gifts
- **Received Coupons**: Display of all coupons received from other users or system rewards

### 2. Send Gifts Section
- **Available Coupons to Send**: Display of coupons that users can send to others
- **Send Gift Interface**: Form to send gifts to specific users

### 3. History Section
- **Transaction History**: Complete record of all gift-related transactions (sent and received)
- **Gift Send History**: Detailed tracking of sent gifts with redemption status

## User Benefits

### New User Registration Benefits
- **2-Day Mini Monthly Pass**: All newly registered users receive a 2-day mini monthly pass benefit upon account creation
- This provides new users with immediate access to premium features to encourage engagement

### Paid User Benefits  
- **1-Week Monthly Pass Equivalent**: Paid users (those who have made any purchase) receive a 1-week monthly pass equivalent benefit
- This rewards paying customers with extended premium access and encourages continued engagement

## Technical Implementation

### Gift Types
- **Premium**: Time-based premium access (days, weeks, months)
- **Tokens**: Virtual currency for platform interactions
- **Character**: Special character unlocks or customizations
- **Special**: Limited-time or event-specific rewards

### Coupon Code Structure
- Unique alphanumeric codes
- Expiration dates
- Usage tracking (single-use vs multi-use)
- Source tracking (system, user, event)

### Database Tables
- `Gift_Definitions`: Master table for gift types and properties
- `User_Gift_Coupons`: Individual coupon instances
- `Gift_Transactions`: Transaction history for auditing
- `User_Gift_History`: User-specific gift sending/receiving history

## User Experience Flow

### Receiving Gifts
1. User receives coupon code (via system reward, friend, or purchase)
2. User enters code in "Redeem Coupon" section
3. System validates code and applies benefits
4. Gift appears in "Received Coupons" list

### Sending Gifts
1. User views "Available Coupons to Send"
2. User selects recipient and coupon to send
3. System transfers coupon ownership
4. Transaction recorded in history

### History Tracking
1. All gift transactions are logged
2. Users can view complete send/receive history
3. Status tracking (pending, redeemed, expired)

## Business Logic

### Gift Distribution Rules
- New users: Automatic 2-day mini monthly pass on registration
- Paid users: Automatic 1-week monthly pass equivalent on first purchase
- Event rewards: Distributed based on participation and achievements
- User-to-user: Voluntary transfers of owned coupons

### Validation Rules
- Coupon codes must be unique and valid
- Expiration dates are enforced
- Single-use coupons cannot be redeemed multiple times
- Users cannot send gifts they don't own

## Future Enhancements
- Gift wrapping and personalized messages
- Scheduled gift delivery
- Gift exchange marketplace
- Bulk gift distribution for events
- Gift recommendation system based on user preferences
