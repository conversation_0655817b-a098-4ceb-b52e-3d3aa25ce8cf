'use client';

import React from 'react';
import { Gift, Crown, Gem, Star, Sparkles, Zap } from 'lucide-react';

interface JourneyProgressRingProps {
  onRingClick: (type: 'standard' | 'pass_cycle' | 'diamond_cycle') => void;
}

interface SubscriptionReward {
  id: string;
  name: string;
  type: 'currency' | 'item' | 'special';
  icon: string;
  amount?: string;
  completed: boolean;
}

const JourneyProgressRing: React.FC<JourneyProgressRingProps> = ({ onRingClick }) => {
  // Mock data for subscription progress rings
  const subscriptionRings = [
    {
      type: 'standard' as const,
      label: 'Free Journey',
      progress: 85,
      icon: Gift,
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      borderColor: 'border-blue-200 dark:border-blue-800',
      rewards: [
        { id: '1', name: 'Daily Bonus', type: 'currency', icon: '✨', amount: '100', completed: true },
        { id: '2', name: 'Basic Badge', type: 'item', icon: '🏅', completed: true },
        { id: '3', name: 'Journey XP', type: 'special', icon: '⭐', amount: '500', completed: false }
      ] as SubscriptionReward[]
    },
    {
      type: 'pass_cycle' as const,
      label: 'Heart Journey',
      progress: 60,
      icon: Crown,
      color: 'from-purple-500 to-pink-500',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
      borderColor: 'border-purple-200 dark:border-purple-800',
      rewards: [
        { id: '4', name: 'Premium Currency', type: 'currency', icon: '💎', amount: '250', completed: true },
        { id: '5', name: 'Exclusive Avatar', type: 'item', icon: '👑', completed: true },
        { id: '6', name: 'Special Title', type: 'special', icon: '🎭', completed: false }
      ] as SubscriptionReward[]
    },
    {
      type: 'diamond_cycle' as const,
      label: 'Diamond Journey',
      progress: 35,
      icon: Gem,
      color: 'from-amber-500 to-orange-500',
      bgColor: 'bg-amber-50 dark:bg-amber-900/20',
      borderColor: 'border-amber-200 dark:border-amber-800',
      rewards: [
        { id: '7', name: 'Legendary Gems', type: 'currency', icon: '💠', amount: '500', completed: true },
        { id: '8', name: 'Epic Cosmetic', type: 'item', icon: '✨', completed: false },
        { id: '9', name: 'VIP Status', type: 'special', icon: '👑', completed: false }
      ] as SubscriptionReward[]
    }
  ];

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'text-green-600 dark:text-green-400';
    if (progress >= 50) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getProgressRing = (progress: number, color: string) => {
    const circumference = 2 * Math.PI * 45; // radius = 45
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (progress / 100) * circumference;

    return (
      <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
        {/* Background circle */}
        <circle
          cx="50"
          cy="50"
          r="45"
          stroke="currentColor"
          strokeWidth="8"
          fill="none"
          className="text-muted/20"
        />
        {/* Progress circle */}
        <circle
          cx="50"
          cy="50"
          r="45"
          stroke="url(#gradient)"
          strokeWidth="8"
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-500 ease-out"
        />
        {/* Gradient definition */}
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" className={color.split(' ')[0]?.replace('from-', 'text-') || 'text-blue-500'} stopOpacity="1" />
            <stop offset="100%" className={color.split(' ')[1]?.replace('to-', 'text-') || 'text-cyan-500'} stopOpacity="1" />
          </linearGradient>
        </defs>
      </svg>
    );
  };

  return (
    <div className="bg-card border rounded-xl p-6 theme-transition">
      <h2 className="text-xl font-semibold mb-6 flex items-center gap-1.5">
        <Gift className="w-5 h-5" />
        Journey Progress
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {subscriptionRings.map((ring) => {
          const IconComponent = ring.icon;
          return (
            <div
              key={ring.type}
              onClick={() => onRingClick(ring.type)}
              className={`${ring.bgColor} ${ring.borderColor} border-2 rounded-xl p-6 cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105`}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <IconComponent className="w-5 h-5 text-foreground" />
                  <h3 className="font-semibold text-foreground">{ring.label}</h3>
                </div>
                <span className={`text-sm font-bold ${getProgressColor(ring.progress)}`}>
                  {ring.progress}%
                </span>
              </div>

              {/* Progress Ring */}
              <div className="flex justify-center mb-4">
                <div className="relative">
                  {getProgressRing(ring.progress, ring.color)}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">{ring.progress}%</div>
                      <div className="text-xs text-muted-foreground">Complete</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Rewards */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-foreground/80 mb-2">Rewards:</h4>
                {ring.rewards.map((reward) => (
                  <div
                    key={reward.id}
                    className={`flex items-center justify-between p-2 rounded-lg ${
                      reward.completed 
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200' 
                        : 'bg-muted/50 text-muted-foreground'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{reward.icon}</span>
                      <span className="text-sm font-medium">
                        {reward.amount && `${reward.amount} `}{reward.name}
                      </span>
                    </div>
                    {reward.completed && (
                      <Star className="w-4 h-4 text-green-600 dark:text-green-400" fill="currentColor" />
                    )}
                  </div>
                ))}
              </div>

              {/* Action Button */}
              <button className="w-full mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors flex items-center justify-center gap-2">
                <Sparkles className="w-4 h-4" />
                View {ring.label}
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default JourneyProgressRing;
