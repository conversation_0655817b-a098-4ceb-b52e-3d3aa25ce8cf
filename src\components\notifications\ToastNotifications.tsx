'use client';

import React, { useEffect, useState } from 'react';
import { X, Trophy, ShoppingBag, Target, Gift, Heart, Zap, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { useNotifications, Notification } from '@/contexts/NotificationContext';

const ToastNotifications: React.FC = () => {
  const { notifications, removeNotification } = useNotifications();
  const [visibleToasts, setVisibleToasts] = useState<Notification[]>([]);

  useEffect(() => {
    // Show only non-persistent notifications as toasts
    const toastNotifications = notifications.filter(
      n => !n.persistent && !n.read && n.duration !== 0
    ).slice(0, 3); // Limit to 3 visible toasts

    setVisibleToasts(toastNotifications);
  }, [notifications]);

  const getToastIcon = (type: string) => {
    switch (type) {
      case 'achievement':
        return <Trophy className="w-5 h-5" />;
      case 'purchase':
        return <ShoppingBag className="w-5 h-5" />;
      case 'milestone':
        return <Target className="w-5 h-5" />;
      case 'reward':
        return <Gift className="w-5 h-5" />;
      case 'celebration':
        return <Heart className="w-5 h-5" />;
      case 'mission':
        return <Zap className="w-5 h-5" />;
      case 'error':
        return <AlertCircle className="w-5 h-5" />;
      case 'success':
        return <CheckCircle className="w-5 h-5" />;
      case 'info':
        return <Info className="w-5 h-5" />;
      default:
        return <Info className="w-5 h-5" />;
    }
  };

  const getToastStyles = (type: string) => {
    switch (type) {
      case 'achievement':
        return {
          bg: 'bg-gradient-to-r from-yellow-500 to-orange-500',
          text: 'text-white',
          icon: 'text-white'
        };
      case 'purchase':
        return {
          bg: 'bg-gradient-to-r from-green-500 to-emerald-500',
          text: 'text-white',
          icon: 'text-white'
        };
      case 'milestone':
        return {
          bg: 'bg-gradient-to-r from-blue-500 to-indigo-500',
          text: 'text-white',
          icon: 'text-white'
        };
      case 'reward':
        return {
          bg: 'bg-gradient-to-r from-purple-500 to-pink-500',
          text: 'text-white',
          icon: 'text-white'
        };
      case 'celebration':
        return {
          bg: 'bg-gradient-to-r from-pink-500 to-rose-500',
          text: 'text-white',
          icon: 'text-white'
        };
      case 'mission':
        return {
          bg: 'bg-gradient-to-r from-indigo-500 to-purple-500',
          text: 'text-white',
          icon: 'text-white'
        };
      case 'error':
        return {
          bg: 'bg-gradient-to-r from-red-500 to-red-600',
          text: 'text-white',
          icon: 'text-white'
        };
      case 'success':
        return {
          bg: 'bg-gradient-to-r from-green-500 to-green-600',
          text: 'text-white',
          icon: 'text-white'
        };
      default:
        return {
          bg: 'bg-gradient-to-r from-gray-500 to-gray-600',
          text: 'text-white',
          icon: 'text-white'
        };
    }
  };

  if (visibleToasts.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3 pointer-events-none">
      {visibleToasts.map((notification, index) => {
        const styles = getToastStyles(notification.type);
        
        return (
          <div
            key={notification.id}
            className={`${styles.bg} ${styles.text} rounded-xl shadow-lg p-4 max-w-sm pointer-events-auto transform transition-all duration-300 ease-in-out animate-in slide-in-from-right-full`}
            style={{
              animationDelay: `${index * 100}ms`,
              animationFillMode: 'both'
            }}
          >
            <div className="flex items-start gap-3">
              <div className={`flex-shrink-0 ${styles.icon}`}>
                {getToastIcon(notification.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-sm mb-1">
                  {notification.title}
                </h4>
                <p className="text-sm opacity-90 line-clamp-2">
                  {notification.message}
                </p>
                
                {notification.metadata?.value && (
                  <div className="mt-2 text-xs opacity-75">
                    {notification.metadata.category === 'reward' && `+${notification.metadata.value}`}
                    {notification.metadata.category === 'purchase' && `$${notification.metadata.value}`}
                  </div>
                )}
                
                {notification.action && (
                  <button
                    onClick={notification.action.onClick}
                    className="mt-2 text-xs underline hover:no-underline opacity-90 hover:opacity-100 transition-opacity"
                  >
                    {notification.action.label}
                  </button>
                )}
              </div>
              
              <button
                onClick={() => removeNotification(notification.id)}
                className="flex-shrink-0 text-white/70 hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            
            {/* Progress bar for timed notifications */}
            {notification.duration && notification.duration > 0 && (
              <div className="mt-3 w-full bg-white/20 rounded-full h-1 overflow-hidden">
                <div 
                  className="h-full bg-white/60 rounded-full transition-all ease-linear"
                  style={{
                    animation: `shrink ${notification.duration}ms linear forwards`
                  }}
                />
              </div>
            )}
          </div>
        );
      })}
      
      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
        
        @keyframes slide-in-from-right-full {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        .animate-in {
          animation-duration: 300ms;
          animation-timing-function: ease-out;
        }
        
        .slide-in-from-right-full {
          animation-name: slide-in-from-right-full;
        }
        
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default ToastNotifications;
