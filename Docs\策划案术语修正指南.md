# 📝 **策划案术语修正指南**

## 🔄 **术语替换规则**

为了与现有的journey页面实现保持一致，需要将以下术语进行统一替换：

### **主要术语替换**
| 原术语 | 新术语 |
|--------|--------|
| battle-pass | journey |
| Battle Pass | Journey |
| 荣耀战令 | 心灵之旅 |
| 战令 | 旅程 |
| 战令系统 | 心灵之旅系统 |
| 战令经验 | 旅程经验 |
| 战令等级 | 旅程等级 |
| 战令奖励 | 旅程奖励 |
| 战令币 | 旅程币 |
| 战令商店 | 旅程商店 |
| 战令任务 | 旅程任务 |
| 战令轨道 | 旅程轨道 |
| 战令主宰 | 旅程达人 |
| 战令达人 | 旅程达人 |

### **轨道名称替换**
| 原术语 | 新术语 |
|--------|--------|
| Alphane Pass轨道 | Heart Track轨道 |
| Diamond Pass轨道 | Diamond Track轨道 |
| 免费通行证 | 免费轨道 |
| 付费进阶通行证 | 付费进阶轨道 |

### **API端点替换**
| 原API | 新API |
|-------|-------|
| `/battlepass/current` | `/journey/current` |
| `/battlepass/user-progress` | `/journey/user-progress` |
| `/battlepass/claim-reward` | `/journey/claim-reward` |
| `/battlepass/buy-level` | `/journey/buy-level` |
| `/battlepass/shop` | `/journey/shop` |
| `/user/battle-pass` | `/user/journey` |
| `/user/battle-pass/claim-reward` | `/user/journey/claim-reward` |

### **数据库表名替换**
| 原表名 | 新表名 |
|--------|--------|
| `Battle_Pass_Seasons` | `Journey_Seasons` |
| `Battle_Pass_Tracks` | `Journey_Tracks` |
| `User_Battle_Pass_Progress` | `User_Journey_Progress` |
| `battle_pass_coin_balance` | `journey_coin_balance` |

### **URL路径替换**
| 原路径 | 新路径 |
|--------|--------|
| `/battle-pass` | `/journey` |
| `/app/battle-pass/page.tsx` | `/app/journey/page.tsx` |

## 📂 **需要修改的文件清单**

### **核心策划文档**
- [ ] `README.md` - 功能介绍
- [ ] `Docs/Plan.md` - 项目策划案
- [ ] `Docs/Final_Development_Plan.md` - 开发计划
- [ ] `Docs/UI设计/design.md` - UI设计文档

### **技术文档**
- [ ] `Docs/APIdocs.md` - API文档
- [ ] `Docs/ArchitectureDesign.md` - 架构设计
- [ ] `Docs/TodoList.md` - 任务列表
- [ ] `Docs/Pricing.md` - 定价方案

### **项目文件**
- [ ] `todo.md` - 待办事项
- [ ] 各种Mermaid图表中的术语

## 🎯 **修正原因**

1. **保持一致性**：当前项目中journey页面已经实现，应该保持术语统一
2. **避免混淆**：在后续开发中避免battle-pass和journey两套术语混用
3. **提升用户体验**：journey（心灵之旅）比battle-pass（荣耀战令）更符合产品的情感化定位
4. **团队协作**：统一术语便于团队沟通和文档维护

## 🔧 **修正建议**

### **优先级高的修正**
1. **Docs/Plan.md** - 核心策划文档
2. **Docs/Final_Development_Plan.md** - 开发计划
3. **Docs/APIdocs.md** - API文档
4. **README.md** - 项目介绍

### **修正方法**
1. **全局搜索替换**：在IDE中使用全局搜索替换功能
2. **逐个文件检查**：确保上下文语义正确
3. **测试验证**：修改后检查现有journey页面功能是否正常

## 📋 **修正检查清单**

完成修正后，请检查以下内容：

- [ ] 所有文档中的术语已统一为journey相关
- [ ] API端点命名已更新
- [ ] 数据库表设计已更新
- [ ] URL路径已统一
- [ ] 现有journey页面功能正常
- [ ] 团队成员已知悉术语变更

## 🎉 **修正后的效果**

- ✅ 术语统一，避免混淆
- ✅ 更符合产品情感化定位
- ✅ 与现有实现保持一致
- ✅ 便于后续开发和维护 