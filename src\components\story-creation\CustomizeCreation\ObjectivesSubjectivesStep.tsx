'use client';

import React, { useState } from 'react';
import { Target, Brain, Heart, MessageCircle, ArrowLeft, Send, Clock, MapPin, Cloud, Sun, Volume2, Thermometer, ChevronDown, ChevronUp, History, User, Zap, Lightbulb, Users, Eye } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryFormData, StoryChapter } from '@/types/story-creation';
import StoryFlow from './StoryFlow';

interface ObjectivesSubjectivesStepProps {
  formData: StoryFormData;
  setFormData: React.Dispatch<React.SetStateAction<StoryFormData>>;
  lang: string;
  onStepChange?: (step: 'worldSetting' | 'storyFlow' | 'objectivesSubjectives') => void;
  onSubmit?: () => void;
  selectedChapter: string | null;
  setSelectedChapter: (chapterId: string | null) => void;
}

const ObjectivesSubjectivesStep: React.FC<ObjectivesSubjectivesStepProps> = ({
  formData,
  setFormData,
  lang,
  onStepChange,
  onSubmit,
  selectedChapter,
  setSelectedChapter
}) => {
  const { t } = useTranslation(lang, 'translation');
  
  // Tab state
  const [activeTab, setActiveTab] = useState<'objectives' | 'subjectives'>('objectives');
  
  // Expanded sections state
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    scene: true,
    antecedent: false,
    character: true,
    interaction: false
  });

  const selectedChapterData = formData.chapters.find(c => c.id === selectedChapter);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  const updateChapter = (chapterId: string, updates: Partial<StoryChapter>) => {
    setFormData(prev => ({
      ...prev,
      chapters: prev.chapters.map(chapter =>
        chapter.id === chapterId ? { ...chapter, ...updates } : chapter
      )
    }));
  };

  // Time Elements handlers
  const updateTimeElements = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const timeElements = { 
      season: '',
      timeOfDay: '',
      duration: '',
      specialDate: '',
      ...chapter.timeElements, 
      [field]: value 
    };
    updateChapter(chapterId, { timeElements });
  };

  // Spatial Elements handlers
  const updateSpatialElements = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const spatialElements = { 
      location: '',
      atmosphere: '',
      keyObjects: '',
      ...chapter.spatialElements, 
      [field]: value 
    };
    updateChapter(chapterId, { spatialElements });
  };

  // Environmental Elements handlers
  const updateEnvironmentalElements = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const environmentalElements = { 
      weather: '',
      lighting: '',
      sounds: '',
      scents: '',
      temperature: '',
      ...chapter.environmentalElements, 
      [field]: value 
    };
    updateChapter(chapterId, { environmentalElements });
  };

  // Mental Model handlers
  const updateMentalModel = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const mentalModel = { 
      coreValues: '',
      thinkingMode: '',
      decisionLogic: '',
      ...chapter.mentalModel, 
      [field]: value 
    };
    updateChapter(chapterId, { mentalModel });
  };

  // Emotional Baseline handlers
  const updateEmotionalBaseline = (chapterId: string, field: string, value: string | number) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const emotionalBaseline = { 
      displayedEmotion: '',
      hiddenEmotion: '',
      emotionalIntensity: 50,
      emotionalStability: 50,
      ...chapter.emotionalBaseline, 
      [field]: value 
    };
    updateChapter(chapterId, { emotionalBaseline });
  };

  // Memory System handlers
  const updateMemorySystem = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const memorySystem = { 
      triggeredMemories: '',
      emotionalMemories: '',
      knowledgePriority: '',
      ...chapter.memorySystem, 
      [field]: value 
    };
    updateChapter(chapterId, { memorySystem });
  };

  // Dialogue Strategy handlers
  const updateDialogueStrategy = (chapterId: string, field: string, value: string | number) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const dialogueStrategy = { 
      initiative: 50,
      listeningRatio: 50,
      questioningStyle: '',
      responseSpeed: '',
      ...chapter.dialogueStrategy, 
      [field]: value 
    };
    updateChapter(chapterId, { dialogueStrategy });
  };

  // Relationship Dynamics handlers
  const updateRelationshipDynamics = (chapterId: string, field: string, value: string | number) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const relationshipDynamics = { 
      initialGoodwill: 50,
      trustLevel: 50,
      intimacyLevel: '',
      powerRelation: '',
      ...chapter.relationshipDynamics, 
      [field]: value 
    };
    updateChapter(chapterId, { relationshipDynamics });
  };

  // Goal Orientation handlers
  const updateGoalOrientation = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const goalOrientation = { 
      sceneGoal: '',
      displayedIntent: '',
      hiddenIntent: '',
      successCriteria: '',
      ...chapter.goalOrientation, 
      [field]: value 
    };
    updateChapter(chapterId, { goalOrientation });
  };

  // Navigation handlers
  const handleBackToStoryFlow = () => {
    if (onStepChange) {
      onStepChange('storyFlow');
    }
  };

  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2 flex items-center justify-center gap-2">
          <Target className="w-6 h-6 text-purple-500" />
          {t('storyCreation.steps.objectivesSubjectives.title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {t('storyCreation.steps.objectivesSubjectives.description')}
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex bg-gray-100 dark:bg-gray-800 rounded-xl p-1 mb-6">
        <button
          onClick={() => setActiveTab('objectives')}
          className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
            activeTab === 'objectives'
              ? 'bg-white dark:bg-gray-700 text-purple-600 dark:text-purple-400 shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
          }`}
        >
          <Target className="w-4 h-4" />
          {t('storyCreation.steps.objectives.title')}
        </button>
        <button
          onClick={() => setActiveTab('subjectives')}
          className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
            activeTab === 'subjectives'
              ? 'bg-white dark:bg-gray-700 text-purple-600 dark:text-purple-400 shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
          }`}
        >
          <Brain className="w-4 h-4" />
          {t('storyCreation.steps.subjectives.title')}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-10 gap-6">
        {/* Left Side: Story Flow (30% width on desktop, full width on mobile) */}
        <div className="lg:col-span-3 space-y-4">
          <StoryFlow
            chapters={formData.chapters}
            selectedChapter={selectedChapter}
            onChapterSelect={setSelectedChapter}
            lang={lang}
            title={t('storyCreation.storyFlow.title')}
            icon={activeTab === 'objectives' ? <Target className="w-5 h-5" /> : <Brain className="w-5 h-5" />}
            accentColor={activeTab === 'objectives' ? 'blue' : 'rose'}
            showAddButton={false}
            showBranchButtons={false}
            showRemoveButtons={false}
          />
        </div>

        {/* Right Side: Content Editing (70% width on desktop, full width on mobile) */}
        <div className="lg:col-span-7 space-y-4">
          {selectedChapterData ? (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 lg:p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4 lg:mb-6">
                <h3 className="text-base lg:text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
                  {activeTab === 'objectives' ? (
                    <Target className="w-4 h-4 lg:w-5 lg:h-5 text-blue-500" />
                  ) : (
                    <Brain className="w-4 h-4 lg:w-5 lg:h-5 text-rose-500" />
                  )}
                  <span className="truncate">
                    {activeTab === 'objectives' ? t('storyCreation.steps.objectives.title') : t('storyCreation.steps.subjectives.title')} - {t('storyCreation.steps.chapters.chapterTitle')} {selectedChapterData.order}{selectedChapterData.branchLetter || ''}
                  </span>
                </h3>
              </div>

              {/* Content based on active tab */}
              {activeTab === 'objectives' ? (
                <div className="space-y-6">
                  {/* Scene Section */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
                    <button
                      onClick={() => toggleSection('scene')}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <Clock className="w-5 h-5 text-blue-500" />
                        <span className="font-medium text-gray-800 dark:text-gray-200">
                          {t('storyCreation.steps.objectives.scene.title')}
                        </span>
                      </div>
                      {expandedSections.scene ? (
                        <ChevronUp className="w-5 h-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-500" />
                      )}
                    </button>
                    
                    {expandedSections.scene && (
                      <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
                        {/* Time Elements */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                            <Clock className="w-4 h-4 text-blue-400" />
                            {t('storyCreation.steps.objectives.scene.timeElements')}
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.objectives.scene.season')}
                              </label>
                              <select
                                value={selectedChapterData.timeElements?.season || ''}
                                onChange={(e) => updateTimeElements(selectedChapterData.id, 'season', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                              >
                                <option value="">{t('storyCreation.steps.objectives.scene.selectSeason')}</option>
                                <option value="spring">{t('storyCreation.steps.objectives.scene.seasons.spring')}</option>
                                <option value="summer">{t('storyCreation.steps.objectives.scene.seasons.summer')}</option>
                                <option value="autumn">{t('storyCreation.steps.objectives.scene.seasons.autumn')}</option>
                                <option value="winter">{t('storyCreation.steps.objectives.scene.seasons.winter')}</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.objectives.scene.timeOfDay')}
                              </label>
                              <select
                                value={selectedChapterData.timeElements?.timeOfDay || ''}
                                onChange={(e) => updateTimeElements(selectedChapterData.id, 'timeOfDay', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                              >
                                <option value="">{t('storyCreation.steps.objectives.scene.selectTimeOfDay')}</option>
                                <option value="dawn">{t('storyCreation.steps.objectives.scene.timesOfDay.dawn')}</option>
                                <option value="morning">{t('storyCreation.steps.objectives.scene.timesOfDay.morning')}</option>
                                <option value="noon">{t('storyCreation.steps.objectives.scene.timesOfDay.noon')}</option>
                                <option value="afternoon">{t('storyCreation.steps.objectives.scene.timesOfDay.afternoon')}</option>
                                <option value="evening">{t('storyCreation.steps.objectives.scene.timesOfDay.evening')}</option>
                                <option value="night">{t('storyCreation.steps.objectives.scene.timesOfDay.night')}</option>
                                <option value="midnight">{t('storyCreation.steps.objectives.scene.timesOfDay.midnight')}</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.objectives.scene.duration')}
                              </label>
                              <input
                                type="text"
                                value={selectedChapterData.timeElements?.duration || ''}
                                onChange={(e) => updateTimeElements(selectedChapterData.id, 'duration', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                                placeholder={t('storyCreation.steps.objectives.scene.durationPlaceholder')}
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.objectives.scene.specialDate')}
                              </label>
                              <input
                                type="text"
                                value={selectedChapterData.timeElements?.specialDate || ''}
                                onChange={(e) => updateTimeElements(selectedChapterData.id, 'specialDate', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                                placeholder={t('storyCreation.steps.objectives.scene.specialDatePlaceholder')}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Spatial Elements */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                            <MapPin className="w-4 h-4 text-blue-400" />
                            {t('storyCreation.steps.objectives.scene.spatialElements')}
                          </h4>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.objectives.scene.location')}
                              </label>
                              <input
                                type="text"
                                value={selectedChapterData.spatialElements?.location || ''}
                                onChange={(e) => updateSpatialElements(selectedChapterData.id, 'location', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                                placeholder={t('storyCreation.steps.objectives.scene.locationPlaceholder')}
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.objectives.scene.atmosphere')}
                              </label>
                              <textarea
                                value={selectedChapterData.spatialElements?.atmosphere || ''}
                                onChange={(e) => updateSpatialElements(selectedChapterData.id, 'atmosphere', e.target.value)}
                                rows={2}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 transition-all resize-none"
                                placeholder={t('storyCreation.steps.objectives.scene.atmospherePlaceholder')}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Environmental Elements */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                            <Cloud className="w-4 h-4 text-blue-400" />
                            {t('storyCreation.steps.objectives.scene.environmentalElements')}
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                                                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    {t('storyCreation.steps.objectives.scene.weather.label')}
                  </label>
                              <select
                                value={selectedChapterData.environmentalElements?.weather || ''}
                                onChange={(e) => updateEnvironmentalElements(selectedChapterData.id, 'weather', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                              >
                                <option value="">{t('storyCreation.steps.objectives.scene.weather.selectWeather')}</option>
                                <option value="sunny">{t('storyCreation.steps.objectives.scene.weather.sunny')}</option>
                                <option value="cloudy">{t('storyCreation.steps.objectives.scene.weather.cloudy')}</option>
                                <option value="rainy">{t('storyCreation.steps.objectives.scene.weather.rainy')}</option>
                                <option value="stormy">{t('storyCreation.steps.objectives.scene.weather.stormy')}</option>
                                <option value="snowy">{t('storyCreation.steps.objectives.scene.weather.snowy')}</option>
                                <option value="foggy">{t('storyCreation.steps.objectives.scene.weather.foggy')}</option>
                              </select>
                            </div>
                            <div>
                                                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    {t('storyCreation.steps.objectives.scene.lighting.label')}
                  </label>
                              <select
                                value={selectedChapterData.environmentalElements?.lighting || ''}
                                onChange={(e) => updateEnvironmentalElements(selectedChapterData.id, 'lighting', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                              >
                                <option value="">{t('storyCreation.steps.objectives.scene.lighting.selectLighting')}</option>
                                <option value="bright">{t('storyCreation.steps.objectives.scene.lighting.bright')}</option>
                                <option value="dim">{t('storyCreation.steps.objectives.scene.lighting.dim')}</option>
                                <option value="dark">{t('storyCreation.steps.objectives.scene.lighting.dark')}</option>
                                <option value="candlelit">{t('storyCreation.steps.objectives.scene.lighting.candlelit')}</option>
                                <option value="neon">{t('storyCreation.steps.objectives.scene.lighting.neon')}</option>
                              </select>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Antecedent Section */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
                    <button
                      onClick={() => toggleSection('antecedent')}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <History className="w-5 h-5 text-blue-500" />
                        <span className="font-medium text-gray-800 dark:text-gray-200">
                          {t('storyCreation.steps.objectives.antecedent.title')}
                        </span>
                      </div>
                      {expandedSections.antecedent ? (
                        <ChevronUp className="w-5 h-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-500" />
                      )}
                    </button>

                    {expandedSections.antecedent && (
                      <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {t('storyCreation.steps.objectives.antecedent.macroHistory')}
                          </label>
                          <textarea
                            value={selectedChapterData.macroHistory || ''}
                            onChange={(e) => updateChapter(selectedChapterData.id, { macroHistory: e.target.value })}
                            rows={3}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all resize-none"
                            placeholder={t('storyCreation.steps.objectives.antecedent.macroHistoryPlaceholder')}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {t('storyCreation.steps.objectives.antecedent.characterPast')}
                          </label>
                          <textarea
                            value={selectedChapterData.characterPast || ''}
                            onChange={(e) => updateChapter(selectedChapterData.id, { characterPast: e.target.value })}
                            rows={3}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all resize-none"
                            placeholder={t('storyCreation.steps.objectives.antecedent.characterPastPlaceholder')}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {t('storyCreation.steps.objectives.antecedent.immediateTrigger')}
                          </label>
                          <textarea
                            value={selectedChapterData.immediateTrigger || ''}
                            onChange={(e) => updateChapter(selectedChapterData.id, { immediateTrigger: e.target.value })}
                            rows={2}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all resize-none"
                            placeholder={t('storyCreation.steps.objectives.antecedent.immediateTriggerPlaceholder')}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Character Section */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
                    <button
                      onClick={() => toggleSection('character')}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <Brain className="w-5 h-5 text-rose-500" />
                        <span className="font-medium text-gray-800 dark:text-gray-200">
                          {t('storyCreation.steps.subjectives.character.title')}
                        </span>
                      </div>
                      {expandedSections.character ? (
                        <ChevronUp className="w-5 h-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-500" />
                      )}
                    </button>
                    
                    {expandedSections.character && (
                      <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
                        {/* Mental Model */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                            <Lightbulb className="w-4 h-4 text-rose-400" />
                            {t('storyCreation.steps.subjectives.character.mentalModel')}
                          </h4>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.subjectives.character.coreValues')}
                              </label>
                              <input
                                type="text"
                                value={selectedChapterData.mentalModel?.coreValues || ''}
                                onChange={(e) => updateMentalModel(selectedChapterData.id, 'coreValues', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                                placeholder={t('storyCreation.steps.subjectives.character.coreValuesPlaceholder')}
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.subjectives.character.thinkingMode')}
                              </label>
                              <input
                                type="text"
                                value={selectedChapterData.mentalModel?.thinkingMode || ''}
                                onChange={(e) => updateMentalModel(selectedChapterData.id, 'thinkingMode', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                                placeholder={t('storyCreation.steps.subjectives.character.thinkingModePlaceholder')}
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.subjectives.character.decisionLogic')}
                              </label>
                              <textarea
                                value={selectedChapterData.mentalModel?.decisionLogic || ''}
                                onChange={(e) => updateMentalModel(selectedChapterData.id, 'decisionLogic', e.target.value)}
                                rows={2}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-rose-500/20 focus:border-rose-500 transition-all resize-none"
                                placeholder={t('storyCreation.steps.subjectives.character.decisionLogicPlaceholder')}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Emotional Baseline */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                            <Heart className="w-4 h-4 text-rose-400" />
                            {t('storyCreation.steps.subjectives.character.emotionalBaseline')}
                          </h4>
                          <div className="space-y-3">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div>
                                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                  {t('storyCreation.steps.subjectives.character.displayedEmotion')}
                                </label>
                                <input
                                  type="text"
                                  value={selectedChapterData.emotionalBaseline?.displayedEmotion || ''}
                                  onChange={(e) => updateEmotionalBaseline(selectedChapterData.id, 'displayedEmotion', e.target.value)}
                                  className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                                  placeholder={t('storyCreation.steps.subjectives.character.displayedEmotionPlaceholder')}
                                />
                              </div>
                              <div>
                                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                  {t('storyCreation.steps.subjectives.character.hiddenEmotion')}
                                </label>
                                <input
                                  type="text"
                                  value={selectedChapterData.emotionalBaseline?.hiddenEmotion || ''}
                                  onChange={(e) => updateEmotionalBaseline(selectedChapterData.id, 'hiddenEmotion', e.target.value)}
                                  className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                                  placeholder={t('storyCreation.steps.subjectives.character.hiddenEmotionPlaceholder')}
                                />
                              </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div>
                                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                  {t('storyCreation.steps.subjectives.character.emotionalIntensity')} ({selectedChapterData.emotionalBaseline?.emotionalIntensity || 50})
                                </label>
                                <input
                                  type="range"
                                  min="0"
                                  max="100"
                                  value={selectedChapterData.emotionalBaseline?.emotionalIntensity || 50}
                                  onChange={(e) => updateEmotionalBaseline(selectedChapterData.id, 'emotionalIntensity', parseInt(e.target.value))}
                                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider-rose"
                                />
                              </div>
                              <div>
                                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                  {t('storyCreation.steps.subjectives.character.emotionalStability')} ({selectedChapterData.emotionalBaseline?.emotionalStability || 50})
                                </label>
                                <input
                                  type="range"
                                  min="0"
                                  max="100"
                                  value={selectedChapterData.emotionalBaseline?.emotionalStability || 50}
                                  onChange={(e) => updateEmotionalBaseline(selectedChapterData.id, 'emotionalStability', parseInt(e.target.value))}
                                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer slider-rose"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Interaction Section */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
                    <button
                      onClick={() => toggleSection('interaction')}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <MessageCircle className="w-5 h-5 text-rose-500" />
                        <span className="font-medium text-gray-800 dark:text-gray-200">
                          {t('storyCreation.steps.subjectives.interaction.title')}
                        </span>
                      </div>
                      {expandedSections.interaction ? (
                        <ChevronUp className="w-5 h-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-gray-500" />
                      )}
                    </button>

                    {expandedSections.interaction && (
                      <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
                        {/* Dialogue Strategy */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                            <MessageCircle className="w-4 h-4 text-rose-400" />
                            {t('storyCreation.steps.subjectives.interaction.dialogueStrategy')}
                          </h4>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.subjectives.interaction.communicationStyle')}
                              </label>
                              <input
                                type="text"
                                value={selectedChapterData.dialogueStrategy?.questioningStyle || ''}
                                onChange={(e) => updateDialogueStrategy(selectedChapterData.id, 'questioningStyle', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                                placeholder={t('storyCreation.steps.subjectives.interaction.communicationStylePlaceholder')}
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.subjectives.interaction.responsePattern')}
                              </label>
                              <textarea
                                value={selectedChapterData.dialogueStrategy?.responseSpeed || ''}
                                onChange={(e) => updateDialogueStrategy(selectedChapterData.id, 'responseSpeed', e.target.value)}
                                rows={2}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-rose-500/20 focus:border-rose-500 transition-all resize-none"
                                placeholder={t('storyCreation.steps.subjectives.interaction.responsePatternPlaceholder')}
                              />
                            </div>
                          </div>
                        </div>

                        {/* Goal Orientation */}
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                            <Target className="w-4 h-4 text-rose-400" />
                            {t('storyCreation.steps.subjectives.interaction.goalOrientation')}
                          </h4>
                          <div className="space-y-3">
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.subjectives.interaction.primaryGoal')}
                              </label>
                              <input
                                type="text"
                                value={selectedChapterData.goalOrientation?.sceneGoal || ''}
                                onChange={(e) => updateGoalOrientation(selectedChapterData.id, 'sceneGoal', e.target.value)}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-rose-500/20 focus:border-rose-500 transition-all"
                                placeholder={t('storyCreation.steps.subjectives.interaction.primaryGoalPlaceholder')}
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                {t('storyCreation.steps.subjectives.interaction.conflictResolution')}
                              </label>
                              <textarea
                                value={selectedChapterData.goalOrientation?.successCriteria || ''}
                                onChange={(e) => updateGoalOrientation(selectedChapterData.id, 'successCriteria', e.target.value)}
                                rows={2}
                                className="w-full px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-200 focus:ring-1 focus:ring-rose-500/20 focus:border-rose-500 transition-all resize-none"
                                placeholder={t('storyCreation.steps.subjectives.interaction.conflictResolutionPlaceholder')}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 text-center">
              {activeTab === 'objectives' ? (
                <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              ) : (
                <Brain className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              )}
              <h3 className="text-lg font-medium text-gray-600 dark:text-gray-400 mb-2">
                {t('storyCreation.steps.objectivesSubjectives.selectChapter')}
              </h3>
              <p className="text-gray-500 dark:text-gray-500">
                {t('storyCreation.steps.objectivesSubjectives.selectChapterDescription')}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={handleBackToStoryFlow}
          className="flex items-center gap-2 px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200"
        >
          <ArrowLeft className="w-4 h-4" />
          {t('storyCreation.steps.objectivesSubjectives.navigation.backToStoryFlow')}
        </button>
        
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {t('storyCreation.navigation.stepOf', { current: 3, total: 3 })}
        </div>
        
        <button
          onClick={handleSubmit}
          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          {t('storyCreation.buttons.createStory')}
          <Send className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default ObjectivesSubjectivesStep;
