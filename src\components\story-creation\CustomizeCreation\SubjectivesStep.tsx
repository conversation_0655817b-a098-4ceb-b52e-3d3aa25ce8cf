'use client';

import React, { useState } from 'react';
import { Brain, Heart, MessageCircle, Target, ArrowLeft, Send, ChevronDown, ChevronUp, Lightbulb, Zap, Users, Eye } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryFormData, StoryStep, StoryChapter } from '@/types/story-creation';
import StoryFlow from './StoryFlow';

interface SubjectivesStepProps {
  formData: StoryFormData;
  setFormData: React.Dispatch<React.SetStateAction<StoryFormData>>;
  lang: string;
  onSubmit?: () => void;
  onStepChange?: (step: StoryStep) => void;
  selectedChapter: string | null;
  setSelectedChapter: (chapterId: string | null) => void;
}

const SubjectivesStep: React.FC<SubjectivesStepProps> = ({
  formData,
  setFormData,
  lang,
  onSubmit,
  onStepChange,
  selectedChapter,
  setSelectedChapter
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    character: true,
    interaction: false
  });

  const selectedChapterData = formData.chapters.find(c => c.id === selectedChapter);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  const updateChapter = (chapterId: string, updates: Partial<StoryChapter>) => {
    setFormData(prev => ({
      ...prev,
      chapters: prev.chapters.map(chapter =>
        chapter.id === chapterId ? { ...chapter, ...updates } : chapter
      )
    }));
  };

  const updateMentalModel = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      updateChapter(chapterId, {
        mentalModel: {
          coreValues: '',
          thinkingMode: '',
          decisionLogic: '',
          ...chapter.mentalModel,
          [field]: value
        }
      });
    }
  };

  const updateEmotionalBaseline = (chapterId: string, field: string, value: string | number) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      updateChapter(chapterId, {
        emotionalBaseline: {
          displayedEmotion: '',
          hiddenEmotion: '',
          emotionalIntensity: 50,
          emotionalStability: 50,
          ...chapter.emotionalBaseline,
          [field]: value
        }
      });
    }
  };

  const updateMemorySystem = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      updateChapter(chapterId, {
        memorySystem: {
          triggeredMemories: '',
          emotionalMemories: '',
          knowledgePriority: '',
          ...chapter.memorySystem,
          [field]: value
        }
      });
    }
  };

  const updateDialogueStrategy = (chapterId: string, field: string, value: string | number) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      updateChapter(chapterId, {
        dialogueStrategy: {
          initiative: 50,
          listeningRatio: 50,
          questioningStyle: '',
          responseSpeed: '',
          ...chapter.dialogueStrategy,
          [field]: value
        }
      });
    }
  };

  const updateRelationshipDynamics = (chapterId: string, field: string, value: string | number) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      updateChapter(chapterId, {
        relationshipDynamics: {
          initialGoodwill: 50,
          trustLevel: 50,
          intimacyLevel: '',
          powerRelation: '',
          ...chapter.relationshipDynamics,
          [field]: value
        }
      });
    }
  };

  const updateGoalOrientation = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      updateChapter(chapterId, {
        goalOrientation: {
          sceneGoal: '',
          displayedIntent: '',
          hiddenIntent: '',
          successCriteria: '',
          ...chapter.goalOrientation,
          [field]: value
        }
      });
    }
  };

  const getChapterDisplayName = (chapter: StoryChapter) => {
    if (chapter.chapterType === 'main') {
      return `${chapter.order}`;
    } else {
      return `${chapter.order}${chapter.branchLetter || ''}`;
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-purple-800 dark:text-purple-200 mb-2 flex items-center justify-center gap-2">
          <Brain className="w-8 h-8" />
          {t('storyCreation.steps.subjectives.title')} - {t('storyCreation.steps.subjectives.description')}
        </h2>
        <p className="text-purple-600 dark:text-purple-300">
          {t('storyCreation.steps.subjectives.selectChapterDescription')}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-10 gap-6">
        {/* Left Side: Story Flow (40% width on desktop, full width on mobile) */}
        <div className="lg:col-span-4 space-y-4">
          <StoryFlow
            chapters={formData.chapters}
            selectedChapter={selectedChapter}
            onChapterSelect={setSelectedChapter}
            lang={lang}
            title={t('storyCreation.storyFlow.title')}
            icon={<Brain className="w-5 h-5" />}
            accentColor="rose"
            showAddButton={false}
            showBranchButtons={false}
            showRemoveButtons={false}
          />
        </div>

        {/* Right Side: Subjectives Editor (60% width on desktop, full width on mobile) */}
        <div className="lg:col-span-6 space-y-4">
          {selectedChapterData ? (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
                  <Brain className="w-5 h-5 text-purple-500" />
                  {t('storyCreation.steps.chapters.chapterTitle')} {getChapterDisplayName(selectedChapterData)} - {t('storyCreation.steps.subjectives.title')}
                </h3>
              </div>

              <div className="space-y-6">
                {/* Character Psychology Layer */}
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700">
                  <button
                    onClick={() => toggleSection('character')}
                    className="w-full flex items-center justify-between text-left mb-4"
                  >
                    <h4 className="text-xl font-bold text-purple-800 dark:text-purple-200 flex items-center gap-2">
                      <Brain className="w-6 h-6" />
                      {t('storyCreation.steps.subjectives.character.title')}
                    </h4>
                    {expandedSections.character ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                  </button>
                  
                  {expandedSections.character && (
                    <div className="space-y-4">
                      {/* Mental Model */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-purple-700 dark:text-purple-300 flex items-center gap-2">
                          <Lightbulb className="w-4 h-4" />
                          {t('storyCreation.steps.subjectives.character.mentalModel')}
                        </h5>
                        
                        <div>
                          <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                            {t('storyCreation.steps.subjectives.character.coreValues')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.mentalModel?.coreValues || ''}
                            onChange={(e) => updateMentalModel(selectedChapterData.id, 'coreValues', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                            placeholder={t('storyCreation.steps.subjectives.character.coreValuesPlaceholder')}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                            {t('storyCreation.steps.subjectives.character.thinkingMode')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.mentalModel?.thinkingMode || ''}
                            onChange={(e) => updateMentalModel(selectedChapterData.id, 'thinkingMode', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                            placeholder={t('storyCreation.steps.subjectives.character.thinkingModePlaceholder')}
                          />
                        </div>
                      </div>

                      {/* Emotional Baseline */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-purple-700 dark:text-purple-300 flex items-center gap-2">
                          <Heart className="w-4 h-4" />
                          {t('storyCreation.steps.subjectives.character.emotionalBaseline')}
                        </h5>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                              {t('storyCreation.steps.subjectives.character.displayedEmotion')}
                            </label>
                            <input
                              type="text"
                              value={selectedChapterData.emotionalBaseline?.displayedEmotion || ''}
                              onChange={(e) => updateEmotionalBaseline(selectedChapterData.id, 'displayedEmotion', e.target.value)}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                              placeholder={t('storyCreation.steps.subjectives.character.displayedEmotionPlaceholder')}
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                              {t('storyCreation.steps.subjectives.character.hiddenEmotion')}
                            </label>
                            <input
                              type="text"
                              value={selectedChapterData.emotionalBaseline?.hiddenEmotion || ''}
                              onChange={(e) => updateEmotionalBaseline(selectedChapterData.id, 'hiddenEmotion', e.target.value)}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                              placeholder={t('storyCreation.steps.subjectives.character.hiddenEmotionPlaceholder')}
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                            {t('storyCreation.steps.subjectives.character.emotionalIntensity')}: {selectedChapterData.emotionalBaseline?.emotionalIntensity || 50}%
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={selectedChapterData.emotionalBaseline?.emotionalIntensity || 50}
                            onChange={(e) => updateEmotionalBaseline(selectedChapterData.id, 'emotionalIntensity', parseInt(e.target.value))}
                            className="w-full h-2 bg-purple-200 rounded-lg appearance-none cursor-pointer dark:bg-purple-700"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Interaction Dynamics Layer */}
                <div className="bg-gradient-to-br from-rose-50 to-orange-50 dark:from-rose-900/20 dark:to-orange-900/20 rounded-xl p-6 border border-rose-200 dark:border-rose-700">
                  <button
                    onClick={() => toggleSection('interaction')}
                    className="w-full flex items-center justify-between text-left mb-4"
                  >
                    <h4 className="text-xl font-bold text-rose-800 dark:text-rose-200 flex items-center gap-2">
                      <Users className="w-6 h-6" />
                      {t('storyCreation.steps.subjectives.interaction.title')}
                    </h4>
                    {expandedSections.interaction ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                  </button>

                  {expandedSections.interaction && (
                    <div className="space-y-4">
                      {/* Dialogue Strategy */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-rose-700 dark:text-rose-300 flex items-center gap-2">
                          <MessageCircle className="w-4 h-4" />
                          {t('storyCreation.steps.subjectives.interaction.dialogueStrategy')}
                        </h5>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              {t('storyCreation.steps.subjectives.interaction.communicationStyle')}: {selectedChapterData.dialogueStrategy?.initiative || 50}%
                            </label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={selectedChapterData.dialogueStrategy?.initiative || 50}
                              onChange={(e) => updateDialogueStrategy(selectedChapterData.id, 'initiative', parseInt(e.target.value))}
                              className="w-full h-2 bg-rose-200 rounded-lg appearance-none cursor-pointer dark:bg-rose-700"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              {t('storyCreation.steps.subjectives.interaction.responsePattern')}: {selectedChapterData.dialogueStrategy?.listeningRatio || 50}%
                            </label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={selectedChapterData.dialogueStrategy?.listeningRatio || 50}
                              onChange={(e) => updateDialogueStrategy(selectedChapterData.id, 'listeningRatio', parseInt(e.target.value))}
                              className="w-full h-2 bg-rose-200 rounded-lg appearance-none cursor-pointer dark:bg-rose-700"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                            {t('storyCreation.steps.subjectives.interaction.communicationStyle')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.dialogueStrategy?.questioningStyle || ''}
                            onChange={(e) => updateDialogueStrategy(selectedChapterData.id, 'questioningStyle', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                            placeholder={t('storyCreation.steps.subjectives.interaction.communicationStylePlaceholder')}
                          />
                        </div>
                      </div>

                      {/* Relationship Dynamics */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-rose-700 dark:text-rose-300 flex items-center gap-2">
                          <Heart className="w-4 h-4" />
                          {t('storyCreation.steps.subjectives.interaction.title')}
                        </h5>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              {t('storyCreation.steps.subjectives.interaction.initialGoodwill')}: {selectedChapterData.relationshipDynamics?.initialGoodwill || 50}%
                            </label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={selectedChapterData.relationshipDynamics?.initialGoodwill || 50}
                              onChange={(e) => updateRelationshipDynamics(selectedChapterData.id, 'initialGoodwill', parseInt(e.target.value))}
                              className="w-full h-2 bg-rose-200 rounded-lg appearance-none cursor-pointer dark:bg-rose-700"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              {t('storyCreation.steps.subjectives.interaction.trustLevel')}: {selectedChapterData.relationshipDynamics?.trustLevel || 50}%
                            </label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={selectedChapterData.relationshipDynamics?.trustLevel || 50}
                              onChange={(e) => updateRelationshipDynamics(selectedChapterData.id, 'trustLevel', parseInt(e.target.value))}
                              className="w-full h-2 bg-rose-200 rounded-lg appearance-none cursor-pointer dark:bg-rose-700"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                            {t('storyCreation.steps.subjectives.interaction.intimacyLevel')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.relationshipDynamics?.intimacyLevel || ''}
                            onChange={(e) => updateRelationshipDynamics(selectedChapterData.id, 'intimacyLevel', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                            placeholder={t('storyCreation.steps.subjectives.interaction.intimacyLevelPlaceholder')}
                          />
                        </div>
                      </div>

                      {/* Goal Orientation */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-rose-700 dark:text-rose-300 flex items-center gap-2">
                          <Target className="w-4 h-4" />
                          {t('storyCreation.steps.subjectives.interaction.goalOrientation')}
                        </h5>

                        <div>
                          <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                            {t('storyCreation.steps.subjectives.interaction.primaryGoal')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.goalOrientation?.sceneGoal || ''}
                            onChange={(e) => updateGoalOrientation(selectedChapterData.id, 'sceneGoal', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                            placeholder={t('storyCreation.steps.subjectives.interaction.primaryGoalPlaceholder')}
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              {t('storyCreation.steps.subjectives.interaction.displayedIntent')}
                            </label>
                            <input
                              type="text"
                              value={selectedChapterData.goalOrientation?.displayedIntent || ''}
                              onChange={(e) => updateGoalOrientation(selectedChapterData.id, 'displayedIntent', e.target.value)}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                              placeholder={t('storyCreation.steps.subjectives.interaction.displayedIntentPlaceholder')}
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              {t('storyCreation.steps.subjectives.interaction.hiddenIntent')}
                            </label>
                            <input
                              type="text"
                              value={selectedChapterData.goalOrientation?.hiddenIntent || ''}
                              onChange={(e) => updateGoalOrientation(selectedChapterData.id, 'hiddenIntent', e.target.value)}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                              placeholder={t('storyCreation.steps.subjectives.interaction.hiddenIntentPlaceholder')}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Chapter Navigation */}
              <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={() => onStepChange?.('storyFlow')}
                  className="flex items-center gap-2 px-6 py-3 border-2 border-purple-300 dark:border-purple-700 text-purple-700 dark:text-purple-300 rounded-xl hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-300 font-medium"
                >
                  <ArrowLeft size={18} />
                  {t('storyCreation.storyFlow.navigation.previous')}
                </button>

                <button
                  type="button"
                  onClick={() => onStepChange?.('objectivesSubjectives')}
                  className="flex items-center gap-2 px-6 py-3 border-2 border-purple-300 dark:border-purple-700 text-purple-700 dark:text-purple-300 rounded-xl hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-300 font-medium"
                >
                  <ArrowLeft size={18} />
                  {t('storyCreation.storyFlow.navigation.previous')}
                </button>
              </div>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 lg:p-8 border border-gray-200 dark:border-gray-700 text-center">
              <Brain className="w-12 h-12 lg:w-16 lg:h-16 mx-auto mb-3 lg:mb-4 text-gray-400" />
              <p className="text-sm lg:text-base text-gray-500 dark:text-gray-400">
                {t('storyCreation.steps.subjectives.selectChapterDescription')}
              </p>
            </div>
          )}

          {/* Unified Navigation */}
          <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => window.history.back()}
              className="flex items-center gap-2 px-6 py-3 border-2 border-purple-300 dark:border-purple-700 text-purple-700 dark:text-purple-300 rounded-xl hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-300 font-medium"
            >
              <ArrowLeft size={18} />
              {t('storyCreation.storyFlow.navigation.backToWorldSetting')}
            </button>

            <button
              type="button"
              onClick={onSubmit}
              disabled={formData.chapters.length === 0}
              className="flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
            >
              <Send size={20} />
              {t('storyCreation.buttons.createStory')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubjectivesStep;
