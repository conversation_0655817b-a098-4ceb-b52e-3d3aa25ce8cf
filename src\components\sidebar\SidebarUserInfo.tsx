'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { User, LogOut } from 'lucide-react';
import { useAuthContext } from '../AuthProvider';
import { useAuth } from '@/hooks/useAuth';
import { useCreatorMode } from '@/contexts/CreatorModeContext';
import ModeToggleButton from '../ModeToggleButton';
import { useTranslation } from '@/app/i18n/client';

interface SidebarUserInfoProps {
  lang: string;
}

const SidebarUserInfo: React.FC<SidebarUserInfoProps> = ({ lang }) => {
  const { user, isAuthenticated, isLoading } = useAuthContext();
  const { logout } = useAuth();
  const { isCreatorMode } = useCreatorMode();
  const { t } = useTranslation(lang, 'translation');

  if (isLoading) {
    return (
      <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-2 h-20">
        <div className="flex items-center gap-3 h-full">
          <div className="w-10 h-10 rounded-full bg-gray-300 dark:bg-gray-700 animate-pulse" />
          <div className="flex-1">
            <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-700 rounded animate-pulse w-24"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return (
      <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-4">
        <div className="space-y-2">
          <Link
            href={`/${lang}/auth`}
            className="block w-full px-4 py-2 text-center text-sm font-medium text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 transition-colors"
          >
            {t('sidebar.login')}
          </Link>
          <Link
            href={`/${lang}/auth`}
            className="block w-full px-4 py-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          >
            {t('sidebar.register')}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-2 h-20">
      <div className="flex items-center gap-3 h-full">
        {/* 用户头像 */}
        <Link href={`/${lang}/${isCreatorMode ? 'creator-profile' : 'profile'}/${user.uid}`} className="flex-shrink-0">
          <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-indigo-200 dark:border-indigo-600">
            {user.avatar ? (
              <Image
                src={user.avatar}
                alt={user.name}
                width={40}
                height={40}
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gray-300 dark:bg-gray-700 flex items-center justify-center">
                <User size={20} className="text-gray-500 dark:text-gray-400" />
              </div>
            )}
          </div>
        </Link>

        {/* 用户信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <Link
              href={`/${lang}/${isCreatorMode ? 'creator-profile' : 'profile'}/${user.uid}`}
              className="font-medium text-gray-800 dark:text-gray-100 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors truncate"
            >
              {user.name || '未设置用户名'}
            </Link>
            <ModeToggleButton size="sm" />
          </div>
          <div className="flex items-center space-x-2">
            {user.alphane_pass_details?.is_active || user.alphane_diamond_details?.is_active ? (
              <span className="text-xs font-semibold text-purple-600 dark:text-purple-400">
                {user.alphane_diamond_details?.is_active ? 'Diamond Pass' : 'Alphane Pass'}
              </span>
            ) : (
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {t('sidebar.freeUser')}
              </span>
            )}
          </div>
        </div>

        {/* 登出按钮 */}
        <button
          onClick={logout}
          title={t('sidebar.logout')}
          className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
        >
          <LogOut size={16} />
        </button>
      </div>
    </div>
  );
};

export default SidebarUserInfo;
