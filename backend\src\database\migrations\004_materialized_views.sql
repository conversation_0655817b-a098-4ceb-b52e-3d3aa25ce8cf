-- =====================================================
-- Materialized Views and Aggregation Functions
-- Version: 2.0.0 (Part 4)
-- Description: Create all materialized views for Card components
-- =====================================================

-- =====================================================
-- MATERIALIZED VIEWS FOR CARD COMPONENTS
-- =====================================================

-- 1. Character Statistics View (for CharacterCard)
CREATE MATERIALIZED VIEW IF NOT EXISTS character_stats AS
SELECT 
    c.id,
    c.name,
    c.creator_id,
    c.avatar_url,
    c.mbti_type,
    c.era,
    c.region,
    
    -- Interaction Statistics
    COUNT(DISTINCT ch.id) as chats_count,
    COUNT(DISTINCT ch.user_id) as unique_chatters,
    COUNT(DISTINCT si_like.id) as likes_count,
    COUNT(DISTINCT f.follower_id) as friends_count,
    COUNT(DISTINCT m.id) as moments_count,
    COUNT(DISTINCT s.id) as stories_count,
    
    -- Engagement Metrics
    (COUNT(DISTINCT si_like.id) + COUNT(DISTINCT m.id) + COUNT(DISTINCT ch.id)) as total_engagement,
    
    -- Quality Metrics
    AVG(sr.rating) as avg_rating,
    COUNT(DISTINCT sr.id) as rating_count,
    
    -- Creator Information
    u.username as creator_username,
    u.display_name as creator_display_name,
    up.avatar_url as creator_avatar,
    
    -- Recent Activity
    MAX(ch.updated_at) as last_chat_activity,
    MAX(m.created_at) as last_moment_created,
    
    -- Metadata
    c.created_at,
    c.updated_at
    
FROM characters c
LEFT JOIN users u ON c.creator_id = u.id
LEFT JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN chats ch ON c.id = ch.character_id
LEFT JOIN social_interactions si_like ON c.id = si_like.target_id 
    AND si_like.target_type = 'character' 
    AND si_like.interaction_type = 'like' 
    AND si_like.is_active = true
LEFT JOIN follows f ON c.creator_id = f.following_id
LEFT JOIN moments m ON c.id = m.character_id
LEFT JOIN stories s ON c.id = s.character_id
LEFT JOIN story_ratings sr ON s.id = sr.story_id
WHERE c.is_public = true
GROUP BY c.id, c.name, c.creator_id, c.avatar_url, c.mbti_type, c.era, c.region,
         c.created_at, c.updated_at, u.username, u.display_name, up.avatar_url;

-- 2. Moment Statistics View (for MomentCard)
CREATE MATERIALIZED VIEW IF NOT EXISTS moment_stats AS
SELECT 
    m.id,
    m.user_id,
    m.character_id,
    m.content,
    m.moment_type,
    m.visibility,
    m.mood,
    m.tags,
    m.is_featured,
    m.media_urls,
    
    -- Interaction Statistics
    COUNT(DISTINCT si_like.id) as likes_count,
    COUNT(DISTINCT si_share.id) as shares_count,
    COUNT(DISTINCT c.id) as comments_count,
    COUNT(DISTINCT si_bookmark.id) as bookmarks_count,
    
    -- Engagement Metrics
    (COUNT(DISTINCT si_like.id) + COUNT(DISTINCT si_share.id) + COUNT(DISTINCT c.id)) as total_engagement,
    
    -- User Information
    u.username,
    u.display_name,
    up.avatar_url as user_avatar,
    
    -- Character Information
    ch.name as character_name,
    ch.avatar_url as character_avatar,
    
    -- Timing
    m.created_at as published_at,
    m.updated_at
    
FROM moments m
LEFT JOIN users u ON m.user_id = u.id
LEFT JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN characters ch ON m.character_id = ch.id
LEFT JOIN social_interactions si_like ON m.id = si_like.target_id 
    AND si_like.target_type = 'moment' 
    AND si_like.interaction_type = 'like' 
    AND si_like.is_active = true
LEFT JOIN social_interactions si_share ON m.id = si_share.target_id 
    AND si_share.target_type = 'moment' 
    AND si_share.interaction_type = 'share' 
    AND si_share.is_active = true
LEFT JOIN social_interactions si_bookmark ON m.id = si_bookmark.target_id 
    AND si_bookmark.target_type = 'moment' 
    AND si_bookmark.interaction_type = 'bookmark' 
    AND si_bookmark.is_active = true
LEFT JOIN comments c ON m.id = c.target_id 
    AND c.target_type = 'moment' 
    AND c.is_deleted = false
WHERE m.visibility IN ('public', 'followers')
GROUP BY m.id, m.user_id, m.character_id, m.content, m.moment_type, m.visibility, 
         m.mood, m.tags, m.is_featured, m.media_urls, m.created_at, m.updated_at,
         u.username, u.display_name, up.avatar_url, ch.name, ch.avatar_url;

-- 3. User Dashboard Data View (for various user cards)
CREATE MATERIALIZED VIEW IF NOT EXISTS user_dashboard_data AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.display_name,
    
    -- Profile Information
    up.avatar_url,
    up.bio,
    up.location,
    up.website,
    
    -- Membership Information
    COALESCE(us_active.tier, 'standard') as current_membership_tier,
    us_active.expires_at as membership_expires_at,
    
    -- Currency Balances (for CurrencyCard)
    COALESCE(uc.star_diamonds, 0) as star_diamonds,
    COALESCE(uc.joy_crystals, 0) as joy_crystals,
    COALESCE(uc.glimmering_dust, 0) as glimmering_dust,
    COALESCE(uc.memory_puzzles, 0) as memory_puzzles,
    COALESCE(uc.daily_bonus_available, true) as daily_bonus_available,
    uc.next_daily_bonus,
    
    -- Statistics (for StatCard)
    COALESCE(ust.total_conversations, 0) as total_conversations,
    COALESCE(ust.characters_created, 0) as characters_created,
    COALESCE(ust.stories_completed, 0) as stories_completed,
    COALESCE(ust.achievements_unlocked, 0) as achievements_unlocked,
    COALESCE(ust.consecutive_login_days, 0) as consecutive_login_days,
    COALESCE(ust.total_login_days, 0) as total_login_days,
    
    -- Social Statistics
    COALESCE(uss.followers_count, 0) as followers_count,
    COALESCE(uss.following_count, 0) as following_count,
    COALESCE(uss.moments_count, 0) as moments_count,
    COALESCE(uss.avg_moment_engagement, 0) as avg_moment_engagement,
    COALESCE(uss.influence_score, 0) as influence_score,
    
    -- Journey Progress (for JourneyCard)
    COALESCE(uj.current_level, 1) as current_level,
    COALESCE(uj.current_xp, 0) as current_xp,
    COALESCE(uj.total_xp_earned, 0) as total_xp_earned,
    COALESCE(uj.level_xp_required, 100) as level_xp_required,
    
    -- Recent Activity
    ust.last_login_date,
    uss.last_activity_date,
    
    -- Metadata
    u.created_at as user_created_at,
    u.updated_at as user_updated_at
    
FROM users u
LEFT JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN user_currencies uc ON u.id = uc.user_id
LEFT JOIN user_statistics ust ON u.id = ust.user_id
LEFT JOIN user_social_stats uss ON u.id = uss.user_id 
    AND uss.stats_period_start = DATE_TRUNC('month', CURRENT_DATE)
LEFT JOIN user_journey uj ON u.id = uj.user_id AND uj.journey_type = 'main'
LEFT JOIN (
    SELECT DISTINCT ON (user_id) 
        user_id, 
        sp.tier,
        expires_at
    FROM user_subscriptions us
    JOIN subscription_plans sp ON us.subscription_plan_id = sp.id
    WHERE us.status = 'active' AND (us.expires_at IS NULL OR us.expires_at > CURRENT_TIMESTAMP)
    ORDER BY user_id, sp.tier DESC
) us_active ON u.id = us_active.user_id;

-- 4. Achievement Progress View (for TrophyCard)
CREATE MATERIALIZED VIEW IF NOT EXISTS achievement_progress AS
SELECT 
    ua.id,
    ua.user_id,
    ua.achievement_id,
    ua.unlocked_at,
    ua.rarity,
    ua.category,
    ua.progress_current,
    ua.progress_total,
    ua.progress_percentage,
    ua.rewards,
    
    -- Achievement Details
    a.name as achievement_name,
    a.description as achievement_description,
    a.icon_url as achievement_icon,
    a.points as achievement_points,
    
    -- User Information
    u.username,
    u.display_name,
    up.avatar_url as user_avatar,
    
    -- Progress Status
    CASE 
        WHEN ua.unlocked_at IS NOT NULL THEN 'unlocked'
        WHEN ua.progress_percentage >= 100 THEN 'ready_to_unlock'
        WHEN ua.progress_percentage >= 50 THEN 'in_progress'
        ELSE 'not_started'
    END as status,
    
    -- Rarity Ranking
    ROW_NUMBER() OVER (PARTITION BY ua.rarity ORDER BY ua.unlocked_at ASC NULLS LAST) as rarity_rank,
    
    -- Metadata
    ua.created_at,
    ua.updated_at
    
FROM user_achievements ua
JOIN achievements a ON ua.achievement_id = a.id
JOIN users u ON ua.user_id = u.id
LEFT JOIN user_profiles up ON u.id = up.user_id
WHERE ua.is_hidden = false OR ua.unlocked_at IS NOT NULL;

-- 5. Memorial Events View (for MemorialCard)
CREATE MATERIALIZED VIEW IF NOT EXISTS memorial_events_view AS
SELECT 
    me.id,
    me.user_id,
    me.character_id,
    me.event_type,
    me.event_name,
    me.description,
    me.anniversary_date,
    me.next_anniversary,
    me.rewards,
    me.is_claimed,
    me.claimed_at,
    
    -- Character Information
    c.name as character_name,
    c.avatar_url as character_avatar,
    
    -- User Information
    u.username,
    u.display_name,
    
    -- Time Calculations
    EXTRACT(DAYS FROM (me.next_anniversary - CURRENT_DATE))::INTEGER as days_until_anniversary,
    EXTRACT(YEARS FROM (CURRENT_DATE - me.original_date))::INTEGER as years_since_event,
    
    -- Event Status
    CASE 
        WHEN me.next_anniversary = CURRENT_DATE THEN 'today'
        WHEN me.next_anniversary BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '7 days' THEN 'this_week'
        WHEN me.next_anniversary BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days' THEN 'this_month'
        ELSE 'future'
    END as anniversary_status,
    
    -- Metadata
    me.created_at,
    me.updated_at
    
FROM memorial_events me
JOIN users u ON me.user_id = u.id
LEFT JOIN characters c ON me.character_id = c.id
WHERE me.is_active = true;

-- 6. Store Statistics View (for FeaturedCard and store analytics)
CREATE MATERIALIZED VIEW IF NOT EXISTS store_stats AS
SELECT 
    sp.id,
    sp.sku,
    sp.name,
    sp.category,
    sp.product_type,
    sp.price_data,
    sp.is_featured,
    sp.is_first_time_only,
    sp.special_tag,
    sp.valid_until,
    
    -- Purchase Statistics
    COUNT(DISTINCT ph.user_id) as unique_purchasers,
    COUNT(DISTINCT ph.id) as total_purchases,
    SUM(ph.amount) as total_revenue,
    AVG(ph.amount) as avg_purchase_value,
    
    -- Recent Activity
    MAX(ph.created_at) as last_purchase_date,
    
    -- Availability
    sp.is_available,
    sp.stock_quantity,
    sp.purchase_limit,
    
    -- Metadata
    sp.created_at,
    sp.updated_at
    
FROM store_products sp
LEFT JOIN purchase_history ph ON sp.id = ph.product_id 
    AND ph.status = 'completed'
WHERE sp.is_available = true
GROUP BY sp.id, sp.sku, sp.name, sp.category, sp.product_type, sp.price_data,
         sp.is_featured, sp.is_first_time_only, sp.special_tag, sp.valid_until,
         sp.is_available, sp.stock_quantity, sp.purchase_limit,
         sp.created_at, sp.updated_at;

-- =====================================================
-- INDEXES FOR MATERIALIZED VIEWS
-- =====================================================

-- Character Stats Indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_character_stats_id ON character_stats(id);
CREATE INDEX IF NOT EXISTS idx_character_stats_creator ON character_stats(creator_id);
CREATE INDEX IF NOT EXISTS idx_character_stats_engagement ON character_stats(total_engagement DESC);
CREATE INDEX IF NOT EXISTS idx_character_stats_rating ON character_stats(avg_rating DESC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_character_stats_mbti ON character_stats(mbti_type) WHERE mbti_type IS NOT NULL;

-- Moment Stats Indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_moment_stats_id ON moment_stats(id);
CREATE INDEX IF NOT EXISTS idx_moment_stats_user ON moment_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_moment_stats_character ON moment_stats(character_id);
CREATE INDEX IF NOT EXISTS idx_moment_stats_engagement ON moment_stats(total_engagement DESC);
CREATE INDEX IF NOT EXISTS idx_moment_stats_published ON moment_stats(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_moment_stats_featured ON moment_stats(is_featured) WHERE is_featured = true;

-- User Dashboard Data Indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_dashboard_data_id ON user_dashboard_data(id);
CREATE INDEX IF NOT EXISTS idx_user_dashboard_data_membership ON user_dashboard_data(current_membership_tier);
CREATE INDEX IF NOT EXISTS idx_user_dashboard_data_influence ON user_dashboard_data(influence_score DESC);
CREATE INDEX IF NOT EXISTS idx_user_dashboard_data_level ON user_dashboard_data(current_level DESC);

-- Achievement Progress Indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_achievement_progress_id ON achievement_progress(id);
CREATE INDEX IF NOT EXISTS idx_achievement_progress_user ON achievement_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_achievement_progress_status ON achievement_progress(status);
CREATE INDEX IF NOT EXISTS idx_achievement_progress_rarity ON achievement_progress(rarity, rarity_rank);

-- Memorial Events View Indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_memorial_events_view_id ON memorial_events_view(id);
CREATE INDEX IF NOT EXISTS idx_memorial_events_view_user ON memorial_events_view(user_id);
CREATE INDEX IF NOT EXISTS idx_memorial_events_view_status ON memorial_events_view(anniversary_status);
CREATE INDEX IF NOT EXISTS idx_memorial_events_view_date ON memorial_events_view(anniversary_date);

-- Store Stats Indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_store_stats_id ON store_stats(id);
CREATE INDEX IF NOT EXISTS idx_store_stats_category ON store_stats(category);
CREATE INDEX IF NOT EXISTS idx_store_stats_featured ON store_stats(is_featured) WHERE is_featured = true;
CREATE INDEX IF NOT EXISTS idx_store_stats_revenue ON store_stats(total_revenue DESC NULLS LAST);

-- =====================================================
-- REFRESH FUNCTIONS
-- =====================================================

-- Function to refresh all materialized views
CREATE OR REPLACE FUNCTION refresh_all_card_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY character_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY moment_stats;
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_dashboard_data;
    REFRESH MATERIALIZED VIEW CONCURRENTLY achievement_progress;
    REFRESH MATERIALIZED VIEW CONCURRENTLY memorial_events_view;
    REFRESH MATERIALIZED VIEW CONCURRENTLY store_stats;
    
    -- Log refresh completion
    INSERT INTO system_logs (log_type, message, created_at)
    VALUES ('materialized_view_refresh', 'All card component views refreshed successfully', CURRENT_TIMESTAMP);
    
EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO system_logs (log_type, message, error_details, created_at)
    VALUES ('materialized_view_refresh_error', 'Error refreshing materialized views', SQLERRM, CURRENT_TIMESTAMP);
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- Function to refresh specific view
CREATE OR REPLACE FUNCTION refresh_card_view(view_name TEXT)
RETURNS void AS $$
BEGIN
    CASE view_name
        WHEN 'character_stats' THEN
            REFRESH MATERIALIZED VIEW CONCURRENTLY character_stats;
        WHEN 'moment_stats' THEN
            REFRESH MATERIALIZED VIEW CONCURRENTLY moment_stats;
        WHEN 'user_dashboard_data' THEN
            REFRESH MATERIALIZED VIEW CONCURRENTLY user_dashboard_data;
        WHEN 'achievement_progress' THEN
            REFRESH MATERIALIZED VIEW CONCURRENTLY achievement_progress;
        WHEN 'memorial_events_view' THEN
            REFRESH MATERIALIZED VIEW CONCURRENTLY memorial_events_view;
        WHEN 'store_stats' THEN
            REFRESH MATERIALIZED VIEW CONCURRENTLY store_stats;
        ELSE
            RAISE EXCEPTION 'Unknown view name: %', view_name;
    END CASE;
    
    -- Log refresh completion
    INSERT INTO system_logs (log_type, message, created_at)
    VALUES ('materialized_view_refresh', format('View %s refreshed successfully', view_name), CURRENT_TIMESTAMP);
    
EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO system_logs (log_type, message, error_details, created_at)
    VALUES ('materialized_view_refresh_error', format('Error refreshing view %s', view_name), SQLERRM, CURRENT_TIMESTAMP);
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON MATERIALIZED VIEW character_stats IS 'Aggregated character statistics for CharacterCard component with engagement metrics';
COMMENT ON MATERIALIZED VIEW moment_stats IS 'Aggregated moment statistics for MomentCard component with social interactions';
COMMENT ON MATERIALIZED VIEW user_dashboard_data IS 'Comprehensive user data for dashboard cards including currencies, stats, and progress';
COMMENT ON MATERIALIZED VIEW achievement_progress IS 'Detailed achievement progress for TrophyCard component with rarity rankings';
COMMENT ON MATERIALIZED VIEW memorial_events_view IS 'Anniversary and special events for MemorialCard component with time calculations';
COMMENT ON MATERIALIZED VIEW store_stats IS 'Store product statistics for FeaturedCard and store analytics';

COMMENT ON FUNCTION refresh_all_card_views() IS 'Refreshes all materialized views used by frontend Card components';
COMMENT ON FUNCTION refresh_card_view(TEXT) IS 'Refreshes a specific materialized view by name';
