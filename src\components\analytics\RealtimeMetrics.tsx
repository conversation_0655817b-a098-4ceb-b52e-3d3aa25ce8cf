'use client';

import React, { useState, useEffect } from 'react';
import { Activity, Users, MessageCircle, ShoppingBag, Zap, TrendingUp } from 'lucide-react';

interface RealtimeMetricsProps {
  lang: string;
}

const RealtimeMetrics: React.FC<RealtimeMetricsProps> = ({ lang }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const realtimeData = [
    { title: 'Active Users', value: '1,247', icon: Users, color: 'from-blue-500 to-indigo-600', trend: '+12' },
    { title: 'Messages/min', value: '89', icon: MessageCircle, color: 'from-green-500 to-emerald-600', trend: '+5' },
    { title: 'Purchases/hr', value: '23', icon: ShoppingBag, color: 'from-purple-500 to-pink-600', trend: '+8' },
    { title: 'Server Load', value: '67%', icon: Activity, color: 'from-orange-500 to-red-600', trend: '-3' }
  ];

  const liveActivity = [
    { user: 'User_8472', action: 'Purchased MetaVerse Pass', time: '2 seconds ago', type: 'purchase' },
    { user: 'User_3691', action: 'Completed mission: Daily Chat', time: '15 seconds ago', type: 'mission' },
    { user: 'User_5284', action: 'Started conversation with Luna', time: '23 seconds ago', type: 'interaction' },
    { user: 'User_7159', action: 'Unlocked achievement: Social Butterfly', time: '45 seconds ago', type: 'achievement' },
    { user: 'User_9346', action: 'Created new memory scene', time: '1 minute ago', type: 'creation' }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'purchase': return <ShoppingBag className="w-4 h-4 text-green-500" />;
      case 'mission': return <Zap className="w-4 h-4 text-blue-500" />;
      case 'interaction': return <MessageCircle className="w-4 h-4 text-purple-500" />;
      case 'achievement': return <TrendingUp className="w-4 h-4 text-yellow-500" />;
      case 'creation': return <Activity className="w-4 h-4 text-pink-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-8">
      {/* Current Time */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            {currentTime.toLocaleTimeString()}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {currentTime.toLocaleDateString()} • Real-time Analytics
          </p>
        </div>
      </div>

      {/* Real-time Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {realtimeData.map((metric, index) => {
          const IconComponent = metric.icon;
          const isPositive = metric.trend.startsWith('+');
          return (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${metric.color} rounded-lg flex items-center justify-center`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <span className={`text-sm font-medium ${isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                  {metric.trend}
                </span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">{metric.value}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">{metric.title}</p>
              <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                <div className={`bg-gradient-to-r ${metric.color} h-1 rounded-full animate-pulse`} style={{ width: '70%' }} />
              </div>
            </div>
          );
        })}
      </div>

      {/* Live Activity Feed */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
          <Activity className="w-5 h-5 text-green-500" />
          Live Activity Feed
        </h3>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {liveActivity.map((activity, index) => (
            <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg animate-in fade-in duration-300">
              <div className="flex-shrink-0">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  <span className="text-blue-600 dark:text-blue-400">{activity.user}</span> {activity.action}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">{activity.time}</p>
              </div>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            </div>
          ))}
        </div>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">System Health</h3>
          <div className="space-y-3">
            {[
              { service: 'API Server', status: 'Healthy', uptime: '99.9%', color: 'bg-green-500' },
              { service: 'Database', status: 'Healthy', uptime: '99.8%', color: 'bg-green-500' },
              { service: 'CDN', status: 'Healthy', uptime: '99.9%', color: 'bg-green-500' },
              { service: 'Analytics', status: 'Warning', uptime: '98.2%', color: 'bg-yellow-500' }
            ].map((service, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 ${service.color} rounded-full`} />
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{service.service}</span>
                </div>
                <div className="text-right">
                  <span className="text-sm text-gray-600 dark:text-gray-400">{service.uptime}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Quick Stats</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Response Time</span>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">127ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Error Rate</span>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">0.02%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Requests/sec</span>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">1,247</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Data Transfer</span>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">2.4 GB/hr</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealtimeMetrics;
