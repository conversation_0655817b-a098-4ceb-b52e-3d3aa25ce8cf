'use client';

import { FC, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { BookOpen, Settings, Layers } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';
import { characters, getManagedStories, type ManagedStory } from '@/lib/mock-data';
import toast from 'react-hot-toast';

// Import new components
import CustomizeCreation from '@/components/story-creation/CustomizeCreation';
import CharacterInfoDisplay from '@/components/story-creation/CharacterInfoDisplay';

// Import hooks
import { useStoryForm } from '@/hooks/story-creation/useStoryForm';

// Import types
import type { 
  StoryStep, 
  StoryStepConfig 
} from '@/types/story-creation';

// Edit Story Client Page Props
export interface EditStoryClientPageProps {
  lang: string;
  storyId: string;
}

const EditStoryClientPage: FC<EditStoryClientPageProps> = ({ lang, storyId }) => {
  const router = useRouter();
  const { t } = useTranslation(lang, 'translation');

  // Loading states
  const [isLoadingStory, setIsLoadingStory] = useState(true);
  const [story, setStory] = useState<ManagedStory | null>(null);
  const [character, setCharacter] = useState<any>(null);

  // State for step management
  const [currentStep, setCurrentStep] = useState<StoryStep>('worldSetting');

  // Use custom hooks - pass existing story data for edit mode
  const storyForm = useStoryForm(lang, storyId, story);

  // Load story data
  useEffect(() => {
    const loadStory = async () => {
      try {
        setIsLoadingStory(true);
        // In a real app, this would be an API call
        const stories = getManagedStories();
        const existingStory = stories.find(s => s.id === storyId);
        
        if (existingStory) {
          setStory(existingStory);
          // Find the character associated with this story
          const storyCharacter = characters.find(c => c.id === existingStory.characterId);
          setCharacter(storyCharacter);
        } else {
          toast.error(t('storyEdit.error.storyNotFound'));
          router.push(`/${lang}/manage-stories`);
        }
      } catch (error) {
        console.error('Failed to load story:', error);
        toast.error(t('storyEdit.error.failedToLoad'));
        router.push(`/${lang}/manage-stories`);
      } finally {
        setIsLoadingStory(false);
      }
    };

    loadStory();
  }, [storyId, lang, router, t]);

  // Step configuration
  const steps: readonly StoryStepConfig[] = [
    { 
      id: 'worldSetting', 
      label: t('storyCreation.functions.customize.steps.worldSetting'), 
      icon: Layers, 
      description: t('storyCreation.steps.worldSetting.description') 
    },
    { 
      id: 'storyFlow', 
      label: t('storyCreation.functions.customize.steps.storyFlow'), 
      icon: BookOpen, 
      description: t('storyCreation.steps.storyFlow.description') 
    },
    { 
      id: 'objectivesSubjectives', 
      label: t('storyCreation.functions.customize.steps.objectivesSubjectives'), 
      icon: Settings, 
      description: t('storyCreation.steps.objectivesSubjectives.description') 
    },
  ] as const;

  // Render customize creation content
  const renderCustomizeContent = () => {
    return (
      <CustomizeCreation
        lang={lang}
        currentStep={currentStep}
        formData={storyForm.formData}
        setFormData={storyForm.setFormData}
        onStepChange={setCurrentStep}
        onSubmit={handleCustomizeSubmit}
        isStepComplete={storyForm.isStepComplete}
        steps={steps}
        characterId={character?.id || ''}
      />
    );
  };

  // Handle story submission
  const handleCustomizeSubmit = async () => {
    try {
      const result = await storyForm.handleSubmit();
      
      if (result.success) {
        toast.success(t('storyEdit.success.storyUpdated'));
        router.push(`/${lang}/manage-stories`);
      }
    } catch (error) {
      console.error('Story update failed:', error);
      toast.error(t('storyEdit.error.failedToUpdate'));
    }
  };

  // Main content renderer
  const renderMainContent = () => (
    <div className="space-y-8">
      {/* Story Edit Content */}
      <div className="animate-in slide-in-from-bottom-4 duration-500">
        {renderCustomizeContent()}
      </div>
    </div>
  );

  // Loading state
  if (isLoadingStory) {
    return (
      <MainAppLayout lang={lang}>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10">
          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center py-12">
              <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">{t('common.loading')}...</p>
            </div>
          </div>
        </div>
      </MainAppLayout>
    );
  }

  // Story not found state
  if (!story || !character) {
    return (
      <MainAppLayout lang={lang}>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10">
          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center py-12">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">{t('storyEdit.error.storyNotFound')}</h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                {t('storyEdit.error.storyNotFoundDescription')}
              </p>
              <button
                onClick={() => router.push(`/${lang}/manage-stories`)}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                {t('storyEdit.error.backToStoryManagement')}
              </button>
            </div>
          </div>
        </div>
      </MainAppLayout>
    );
  }

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200/20 dark:bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-200/20 dark:bg-pink-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Character Info Section */}
          <div className="mb-12">
            <CharacterInfoDisplay character={character} lang={lang} />
          </div>

          {/* Page Header for Edit Mode */}
          <div className="text-center mb-12">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-500 to-rose-500 bg-clip-text text-transparent mb-4">
              {t('storyEdit.title')}
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              {t('storyEdit.subtitle')} "{story.title}"
            </p>
          </div>

          {/* Main Content */}
          {renderMainContent()}
        </div>
      </div>
    </MainAppLayout>
  );
};

export default EditStoryClientPage; 