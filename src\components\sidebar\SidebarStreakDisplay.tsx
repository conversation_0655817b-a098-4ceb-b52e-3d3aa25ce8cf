'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { calculateStreakProgress, getStreakStatusText } from '@/lib/utils';

interface User {
  streak_current_days?: number;
}

interface SidebarStreakDisplayProps {
  user: User | null;
  lang: string;
}

const SidebarStreakDisplay: React.FC<SidebarStreakDisplayProps> = ({ user, lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const currentStreak = user?.streak_current_days || 15; // Default to 15 for demo
  const progress = calculateStreakProgress(currentStreak);

  return (
    <div>
      {/* Header with title and streak count - matching Today's Gifts style */}
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
          {t('sidebar.interactionStreak').toUpperCase()}
        </h3>
        <span className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-500 to-pink-500">
          {currentStreak}/{Math.floor(currentStreak / 25) * 25 + 25} {t('common.days')}
        </span>
      </div>

      {/* Progress bar */}
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          className="bg-gradient-to-r from-orange-400 to-red-500 h-2 rounded-full transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
      </div>

      {/* Status text */}
      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
        {getStreakStatusText(currentStreak, t)}
      </p>
    </div>
  );
};

export default SidebarStreakDisplay;
