'use client';

import { FC } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';

interface ChatHeaderProps {
  characterData: {
    id?: string;
    name: string;
    character_avatar?: string;
  };
  lang?: string;
  onSettingsClick?: () => void;
}

const ChatHeader: FC<ChatHeaderProps> = ({
  characterData,
  lang = 'en',
  onSettingsClick
}) => {
  const router = useRouter();
  const { t } = useTranslation(lang, 'translation');

  const headerClasses = "h-12 flex items-center justify-between px-4 bg-gradient-to-b from-white/60 to-transparent dark:from-black/60 dark:to-transparent backdrop-blur-sm shrink-0";
  const buttonClasses = "text-white hover:text-gray-300 transition-colors bg-gray-800/60 rounded-lg";

  return (
    <header className={headerClasses}>
      {/* Left: Back Button */}
      <button
        onClick={() => router.back()}
        className={buttonClasses}
        style={{ padding: '6px' }}
      >
        <ArrowLeft size={24} />
      </button>

      {/* Center: Character Avatar and Info */}
      <div className="flex items-center gap-3">
        {characterData.character_avatar ? (
          <Link href={`/${lang}/character/${characterData.id}`} prefetch={false}>
            <div className="w-8 h-8 rounded-full border-2 border-white/60 overflow-hidden" style={{ margin: '8px 0' }}>
              <Image
                src={characterData.character_avatar.replace(/'/g, "")}
                alt={characterData.name}
                width={32}
                height={32}
                className="w-full h-full object-cover"
                unoptimized
              />
            </div>
          </Link>
        ) : (
          <div className="w-8 h-8 rounded-full bg-gray-700 border-2 border-white/60" style={{ margin: '8px 0' }} />
        )}
        <div>
          <h1 className="font-bold text-white">{characterData.name}</h1>
          <p className="text-xs text-gray-400">{t('chat.status.online')}</p>
        </div>
      </div>

      {/* Right: Settings Button */}
      <button
        className={buttonClasses}
        onClick={onSettingsClick}
        style={{ padding: '6px' }}
      >
        <Settings size={24} />
      </button>
    </header>
  );
};

export default ChatHeader;
