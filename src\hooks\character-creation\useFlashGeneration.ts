'use client';

import { useState, useCallback } from 'react';
import toast from 'react-hot-toast';
import type { FlashGenerationResult } from '@/types/character-creation';
import type { GenreFilters } from '@/components/CharacterGenreFilter';

export const useFlashGeneration = () => {
  const [characterName, setCharacterName] = useState('');
  const [oneClickPrompt, setOneClickPrompt] = useState('');
  const [flashResult, setFlashResult] = useState<FlashGenerationResult | null>(null);
  const [regenerateCount, setRegenerateCount] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);

  // One-click character generation
  const handleOneClickGenerate = useCallback(async () => {
    // Allow empty input for random generation
    
    setIsGenerating(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate AI generation

      // Simulate AI-generated complete character data
      const isRandom = !oneClickPrompt.trim();
      const prompt = isRandom ? 'random character' : oneClickPrompt;

      // Generate random or prompt-based character data
      const mockResult: FlashGenerationResult = {
        name: characterName.trim() || (isRandom ?
          ['Luna', 'Aria', 'Zara', 'Nova', 'Echo'][Math.floor(Math.random() * 5)] :
          (prompt.toLowerCase().includes('wizard') ? 'Merlin' :
           prompt.toLowerCase().includes('warrior') ? 'Valeria' : 'Aurora')),

        personality: isRandom ?
          'Mysterious and introspective on the surface, but deeply caring and protective of those close to them. Has a playful side that emerges around trusted friends.' :
          `Based on your description: ${prompt.slice(0, 100)}... Generated personality traits that complement the concept.`,

        appearance: isRandom ?
          'Tall and elegant with silver hair that seems to shimmer in moonlight. Deep violet eyes that hold ancient wisdom. Wears flowing robes with intricate star patterns.' :
          `Physical appearance generated from your concept: ${prompt.slice(0, 50)}... Detailed visual description.`,

        behavior: isRandom ?
          'Speaks in measured tones, often pausing to consider words carefully. Gestures gracefully when explaining complex concepts. Has a habit of stargazing when deep in thought.' :
          'Behavioral patterns and mannerisms that align with the character concept you provided.',

        knowledge: isRandom ?
          'Expert in celestial magic, ancient languages, and interdimensional theory. Knowledgeable about herbs, potions, and mystical artifacts. Well-versed in historical events across multiple realms.' :
          'Knowledge base and skills generated to support the character concept and background.',

        greetings: isRandom ?
          'Hello there! I sense something special about you... Are you perhaps interested in learning about the mysteries of the cosmos?' :
          `Greeting generated based on your concept: ${prompt.slice(0, 30)}...`,

        storyboard: isRandom ?
          'In a world where magic and mystery intertwine, your character encounters someone special during a chance meeting. What begins as curiosity quickly develops into a meaningful connection as they discover shared interests and complementary personalities. Through conversations and shared experiences, they learn to trust each other and find comfort in their growing bond. The story explores themes of friendship, understanding, and the magic that happens when two souls connect on a deeper level.' :
          `A ~100 token story based on your character concept: ${prompt.slice(0, 30)}... The narrative explores the development of a meaningful relationship between your character and someone they meet, showcasing their personality traits and creating an engaging backdrop for future interactions.`
      };

      setFlashResult(mockResult);
      toast.success('Character generated successfully! Review the preview below.');
    } catch (error) {
      console.error('Generation failed:', error);
      toast.error('Generation failed, please try again');
    } finally {
      setIsGenerating(false);
    }
  }, [oneClickPrompt]);

  // Handle regeneration
  const handleRegenerate = useCallback(() => {
    if (regenerateCount < 2) {
      setRegenerateCount(prev => prev + 1);
      handleOneClickGenerate();
    }
  }, [regenerateCount, handleOneClickGenerate]);

  // Handle accept character
  const handleAccept = useCallback(() => {
    // TODO: Implement accept and create character logic
    console.log('Accept character');
    toast.success('Character accepted! You can now customize details.');
  }, []);

  // Reset generation state
  const resetGeneration = useCallback(() => {
    setCharacterName('');
    setOneClickPrompt('');
    setFlashResult(null);
    setRegenerateCount(0);
    setIsGenerating(false);
  }, []);

  return {
    // State
    characterName,
    oneClickPrompt,
    flashResult,
    regenerateCount,
    isGenerating,

    // Actions
    setCharacterName,
    setOneClickPrompt,
    handleOneClickGenerate,
    handleRegenerate,
    handleAccept,
    resetGeneration,
  };
};
