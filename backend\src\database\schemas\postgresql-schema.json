{"database": "alphane_production", "version": "1.0.0", "description": "Complete PostgreSQL schema for Alphane AI character interaction platform", "tables": {"users": {"description": "User accounts and authentication", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "username": {"type": "VARCHAR(50)", "unique": true, "not_null": true}, "email": {"type": "VARCHAR(255)", "unique": true, "not_null": true}, "password_hash": {"type": "VARCHAR(255)", "not_null": true}, "display_name": {"type": "VARCHAR(100)"}, "avatar_url": {"type": "TEXT"}, "bio": {"type": "TEXT"}, "language_preference": {"type": "VARCHAR(10)", "default": "'en'"}, "timezone": {"type": "VARCHAR(50)", "default": "'UTC'"}, "is_creator": {"type": "BOOLEAN", "default": false}, "is_verified": {"type": "BOOLEAN", "default": false}, "is_premium": {"type": "BOOLEAN", "default": false}, "premium_expires_at": {"type": "TIMESTAMP"}, "email_verified_at": {"type": "TIMESTAMP"}, "last_login_at": {"type": "TIMESTAMP"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_users_email", "columns": ["email"]}, {"name": "idx_users_username", "columns": ["username"]}, {"name": "idx_users_is_creator", "columns": ["is_creator"]}]}, "user_profiles": {"description": "Extended user profile information", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "birth_date": {"type": "DATE"}, "gender": {"type": "VARCHAR(20)"}, "location": {"type": "VARCHAR(100)"}, "social_links": {"type": "JSONB", "default": "'{}'"}, "interests": {"type": "TEXT[]", "default": "ARRAY[]::TEXT[]"}, "privacy_settings": {"type": "JSONB", "default": "'{\"profile_visibility\": \"public\", \"show_online_status\": true}'"}, "notification_settings": {"type": "JSONB", "default": "'{\"email_notifications\": true, \"push_notifications\": true}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_user_profiles_user_id", "columns": ["user_id"], "unique": true}]}, "characters": {"description": "AI characters created by users", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "creator_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "name": {"type": "VARCHAR(100)", "not_null": true}, "description": {"type": "TEXT"}, "avatar_url": {"type": "TEXT"}, "cover_image_url": {"type": "TEXT"}, "personality_traits": {"type": "JSONB", "default": "'{}'"}, "background_story": {"type": "TEXT"}, "voice_settings": {"type": "JSONB", "default": "'{}'"}, "ai_model_config": {"type": "JSONB", "default": "'{}'"}, "tags": {"type": "TEXT[]", "default": "ARRAY[]::TEXT[]"}, "genre": {"type": "VARCHAR(50)"}, "age_range": {"type": "VARCHAR(20)"}, "language": {"type": "VARCHAR(10)", "default": "'en'"}, "is_public": {"type": "BOOLEAN", "default": true}, "is_featured": {"type": "BOOLEAN", "default": false}, "is_nsfw": {"type": "BOOLEAN", "default": false}, "chat_count": {"type": "INTEGER", "default": 0}, "like_count": {"type": "INTEGER", "default": 0}, "view_count": {"type": "INTEGER", "default": 0}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_characters_creator_id", "columns": ["creator_id"]}, {"name": "idx_characters_is_public", "columns": ["is_public"]}, {"name": "idx_characters_genre", "columns": ["genre"]}, {"name": "idx_characters_tags", "columns": ["tags"], "type": "GIN"}, {"name": "idx_characters_created_at", "columns": ["created_at"]}]}, "stories": {"description": "Interactive stories created by users", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "creator_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "character_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "characters", "column": "id", "on_delete": "CASCADE"}}, "title": {"type": "VARCHAR(200)", "not_null": true}, "description": {"type": "TEXT"}, "cover_image_url": {"type": "TEXT"}, "world_setting": {"type": "JSONB", "default": "'{}'"}, "objectives": {"type": "JSONB", "default": "'[]'"}, "subjectives": {"type": "JSONB", "default": "'[]'"}, "reward_settings": {"type": "JSONB", "default": "'{}'"}, "genre": {"type": "VARCHAR(50)"}, "tags": {"type": "TEXT[]", "default": "ARRAY[]::TEXT[]"}, "difficulty_level": {"type": "VARCHAR(20)", "default": "'medium'"}, "estimated_duration": {"type": "INTEGER"}, "language": {"type": "VARCHAR(10)", "default": "'en'"}, "is_public": {"type": "BOOLEAN", "default": true}, "is_featured": {"type": "BOOLEAN", "default": false}, "is_nsfw": {"type": "BOOLEAN", "default": false}, "play_count": {"type": "INTEGER", "default": 0}, "like_count": {"type": "INTEGER", "default": 0}, "rating_avg": {"type": "DECIMAL(3,2)", "default": 0}, "rating_count": {"type": "INTEGER", "default": 0}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_stories_creator_id", "columns": ["creator_id"]}, {"name": "idx_stories_character_id", "columns": ["character_id"]}, {"name": "idx_stories_is_public", "columns": ["is_public"]}, {"name": "idx_stories_genre", "columns": ["genre"]}, {"name": "idx_stories_tags", "columns": ["tags"], "type": "GIN"}]}, "story_chapters": {"description": "Individual chapters within stories", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "story_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "stories", "column": "id", "on_delete": "CASCADE"}}, "chapter_number": {"type": "INTEGER", "not_null": true}, "title": {"type": "VARCHAR(200)", "not_null": true}, "content": {"type": "TEXT"}, "scene_setting": {"type": "JSONB", "default": "'{}'"}, "character_instructions": {"type": "TEXT"}, "choices": {"type": "JSONB", "default": "'[]'"}, "completion_requirements": {"type": "JSONB", "default": "'{}'"}, "rewards": {"type": "JSONB", "default": "'{}'"}, "is_locked": {"type": "BOOLEAN", "default": false}, "unlock_requirements": {"type": "JSONB", "default": "'{}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_story_chapters_story_id", "columns": ["story_id"]}, {"name": "idx_story_chapters_story_chapter", "columns": ["story_id", "chapter_number"], "unique": true}]}, "chats": {"description": "Chat sessions between users and characters", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "character_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "characters", "column": "id", "on_delete": "CASCADE"}}, "story_id": {"type": "UUID", "foreign_key": {"table": "stories", "column": "id", "on_delete": "SET NULL"}}, "chapter_id": {"type": "UUID", "foreign_key": {"table": "story_chapters", "column": "id", "on_delete": "SET NULL"}}, "title": {"type": "VARCHAR(200)"}, "context": {"type": "JSONB", "default": "'{}'"}, "memory_context": {"type": "JSONB", "default": "'{}'"}, "ai_settings": {"type": "JSONB", "default": "'{}'"}, "message_count": {"type": "INTEGER", "default": 0}, "last_message_at": {"type": "TIMESTAMP"}, "is_archived": {"type": "BOOLEAN", "default": false}, "is_favorite": {"type": "BOOLEAN", "default": false}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_chats_user_id", "columns": ["user_id"]}, {"name": "idx_chats_character_id", "columns": ["character_id"]}, {"name": "idx_chats_story_id", "columns": ["story_id"]}, {"name": "idx_chats_last_message_at", "columns": ["last_message_at"]}]}, "chat_messages": {"description": "Individual messages within chat sessions", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "chat_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "chats", "column": "id", "on_delete": "CASCADE"}}, "sender_type": {"type": "VARCHAR(20)", "not_null": true, "check": "sender_type IN ('user', 'character', 'system')"}, "sender_id": {"type": "UUID"}, "content": {"type": "TEXT", "not_null": true}, "message_type": {"type": "VARCHAR(20)", "default": "'text'", "check": "message_type IN ('text', 'image', 'audio', 'action', 'system')"}, "metadata": {"type": "JSONB", "default": "'{}'"}, "ai_response_time": {"type": "INTEGER"}, "tokens_used": {"type": "INTEGER"}, "is_edited": {"type": "BOOLEAN", "default": false}, "edited_at": {"type": "TIMESTAMP"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_chat_messages_chat_id", "columns": ["chat_id"]}, {"name": "idx_chat_messages_created_at", "columns": ["created_at"]}, {"name": "idx_chat_messages_sender", "columns": ["sender_type", "sender_id"]}]}, "memory_capsules": {"description": "Memory storage for character interactions", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "character_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "characters", "column": "id", "on_delete": "CASCADE"}}, "title": {"type": "VARCHAR(200)", "not_null": true}, "content": {"type": "TEXT", "not_null": true}, "memory_type": {"type": "VARCHAR(20)", "default": "'general'", "check": "memory_type IN ('general', 'relationship', 'preference', 'event', 'emotion')"}, "importance_score": {"type": "INTEGER", "default": 5, "check": "importance_score >= 1 AND importance_score <= 10"}, "tags": {"type": "TEXT[]", "default": "ARRAY[]::TEXT[]"}, "emotional_context": {"type": "JSONB", "default": "'{}'"}, "associated_chat_id": {"type": "UUID", "foreign_key": {"table": "chats", "column": "id", "on_delete": "SET NULL"}}, "expires_at": {"type": "TIMESTAMP"}, "access_count": {"type": "INTEGER", "default": 0}, "last_accessed_at": {"type": "TIMESTAMP"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_memory_capsules_user_character", "columns": ["user_id", "character_id"]}, {"name": "idx_memory_capsules_type", "columns": ["memory_type"]}, {"name": "idx_memory_capsules_importance", "columns": ["importance_score"]}, {"name": "idx_memory_capsules_tags", "columns": ["tags"], "type": "GIN"}]}, "user_achievements": {"description": "User achievements and trophies", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "achievement_type": {"type": "VARCHAR(50)", "not_null": true}, "achievement_id": {"type": "VARCHAR(100)", "not_null": true}, "title": {"type": "VARCHAR(200)", "not_null": true}, "description": {"type": "TEXT"}, "icon_url": {"type": "TEXT"}, "rarity": {"type": "VARCHAR(20)", "default": "'common'", "check": "rarity IN ('common', 'rare', 'epic', 'legendary')"}, "points": {"type": "INTEGER", "default": 0}, "progress": {"type": "JSONB", "default": "'{}'"}, "metadata": {"type": "JSONB", "default": "'{}'"}, "unlocked_at": {"type": "TIMESTAMP"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_user_achievements_user_id", "columns": ["user_id"]}, {"name": "idx_user_achievements_type", "columns": ["achievement_type"]}, {"name": "idx_user_achievements_unique", "columns": ["user_id", "achievement_id"], "unique": true}]}, "user_journey": {"description": "User journey progress and season data", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "season_id": {"type": "VARCHAR(50)", "not_null": true}, "level": {"type": "INTEGER", "default": 1}, "experience_points": {"type": "INTEGER", "default": 0}, "current_streak": {"type": "INTEGER", "default": 0}, "longest_streak": {"type": "INTEGER", "default": 0}, "last_activity_date": {"type": "DATE"}, "premium_tier": {"type": "VARCHAR(20)", "default": "'free'", "check": "premium_tier IN ('free', 'basic', 'premium', 'ultimate')"}, "premium_benefits": {"type": "JSONB", "default": "'{}'"}, "missions_completed": {"type": "JSONB", "default": "'[]'"}, "rewards_claimed": {"type": "JSONB", "default": "'[]'"}, "season_progress": {"type": "JSONB", "default": "'{}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_user_journey_user_season", "columns": ["user_id", "season_id"], "unique": true}, {"name": "idx_user_journey_level", "columns": ["level"]}]}, "user_currencies": {"description": "User virtual currency balances", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "currency_type": {"type": "VARCHAR(20)", "not_null": true, "check": "currency_type IN ('coins', 'gems', 'tokens', 'hearts')"}, "balance": {"type": "INTEGER", "default": 0, "check": "balance >= 0"}, "total_earned": {"type": "INTEGER", "default": 0}, "total_spent": {"type": "INTEGER", "default": 0}, "last_daily_bonus": {"type": "DATE"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_user_currencies_user_currency", "columns": ["user_id", "currency_type"], "unique": true}]}, "transactions": {"description": "Currency transaction history", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "currency_type": {"type": "VARCHAR(20)", "not_null": true, "check": "currency_type IN ('coins', 'gems', 'tokens', 'hearts')"}, "transaction_type": {"type": "VARCHAR(20)", "not_null": true, "check": "transaction_type IN ('earn', 'spend', 'purchase', 'reward', 'refund')"}, "amount": {"type": "INTEGER", "not_null": true}, "balance_before": {"type": "INTEGER", "not_null": true}, "balance_after": {"type": "INTEGER", "not_null": true}, "description": {"type": "TEXT"}, "reference_type": {"type": "VARCHAR(50)"}, "reference_id": {"type": "UUID"}, "metadata": {"type": "JSONB", "default": "'{}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_transactions_user_id", "columns": ["user_id"]}, {"name": "idx_transactions_created_at", "columns": ["created_at"]}, {"name": "idx_transactions_reference", "columns": ["reference_type", "reference_id"]}]}, "notifications": {"description": "User notifications system", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "type": {"type": "VARCHAR(50)", "not_null": true}, "title": {"type": "VARCHAR(200)", "not_null": true}, "message": {"type": "TEXT"}, "icon": {"type": "VARCHAR(100)"}, "action_url": {"type": "TEXT"}, "priority": {"type": "VARCHAR(20)", "default": "'normal'", "check": "priority IN ('low', 'normal', 'high', 'urgent')"}, "category": {"type": "VARCHAR(50)", "default": "'general'"}, "metadata": {"type": "JSONB", "default": "'{}'"}, "is_read": {"type": "BOOLEAN", "default": false}, "read_at": {"type": "TIMESTAMP"}, "expires_at": {"type": "TIMESTAMP"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_notifications_user_id", "columns": ["user_id"]}, {"name": "idx_notifications_is_read", "columns": ["is_read"]}, {"name": "idx_notifications_created_at", "columns": ["created_at"]}]}, "follows": {"description": "User following relationships", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "follower_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "following_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_follows_unique", "columns": ["follower_id", "following_id"], "unique": true}, {"name": "idx_follows_follower", "columns": ["follower_id"]}, {"name": "idx_follows_following", "columns": ["following_id"]}]}, "character_likes": {"description": "User likes for characters", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "character_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "characters", "column": "id", "on_delete": "CASCADE"}}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_character_likes_unique", "columns": ["user_id", "character_id"], "unique": true}, {"name": "idx_character_likes_character", "columns": ["character_id"]}]}, "story_likes": {"description": "User likes for stories", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "story_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "stories", "column": "id", "on_delete": "CASCADE"}}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_story_likes_unique", "columns": ["user_id", "story_id"], "unique": true}, {"name": "idx_story_likes_story", "columns": ["story_id"]}]}, "story_ratings": {"description": "User ratings for stories", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "story_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "stories", "column": "id", "on_delete": "CASCADE"}}, "rating": {"type": "INTEGER", "not_null": true, "check": "rating >= 1 AND rating <= 5"}, "review": {"type": "TEXT"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_story_ratings_unique", "columns": ["user_id", "story_id"], "unique": true}, {"name": "idx_story_ratings_story", "columns": ["story_id"]}]}, "user_story_progress": {"description": "User progress through stories", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "story_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "stories", "column": "id", "on_delete": "CASCADE"}}, "current_chapter_id": {"type": "UUID", "foreign_key": {"table": "story_chapters", "column": "id", "on_delete": "SET NULL"}}, "completed_chapters": {"type": "UUID[]", "default": "ARRAY[]::UUID[]"}, "choices_made": {"type": "JSONB", "default": "'{}'"}, "completion_percentage": {"type": "DECIMAL(5,2)", "default": 0}, "total_play_time": {"type": "INTEGER", "default": 0}, "rewards_earned": {"type": "JSONB", "default": "'{}'"}, "is_completed": {"type": "BOOLEAN", "default": false}, "completed_at": {"type": "TIMESTAMP"}, "last_played_at": {"type": "TIMESTAMP"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_user_story_progress_unique", "columns": ["user_id", "story_id"], "unique": true}, {"name": "idx_user_story_progress_last_played", "columns": ["last_played_at"]}]}, "moments": {"description": "User-generated content moments", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "foreign_key": {"table": "users", "column": "id", "on_delete": "CASCADE"}}, "character_id": {"type": "UUID", "foreign_key": {"table": "characters", "column": "id", "on_delete": "SET NULL"}}, "content": {"type": "TEXT", "not_null": true}, "media_urls": {"type": "TEXT[]", "default": "ARRAY[]::TEXT[]"}, "tags": {"type": "TEXT[]", "default": "ARRAY[]::TEXT[]"}, "mood": {"type": "VARCHAR(50)"}, "visibility": {"type": "VARCHAR(20)", "default": "'public'", "check": "visibility IN ('public', 'followers', 'private')"}, "like_count": {"type": "INTEGER", "default": 0}, "comment_count": {"type": "INTEGER", "default": 0}, "share_count": {"type": "INTEGER", "default": 0}, "view_count": {"type": "INTEGER", "default": 0}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_moments_user_id", "columns": ["user_id"]}, {"name": "idx_moments_character_id", "columns": ["character_id"]}, {"name": "idx_moments_created_at", "columns": ["created_at"]}, {"name": "idx_moments_tags", "columns": ["tags"], "type": "GIN"}]}}, "functions": {"update_updated_at_column": {"description": "Trigger function to automatically update updated_at timestamp", "body": "CREATE OR REPLACE FUNCTION update_updated_at_column() RETURNS TRIGGER AS $$ BEGIN NEW.updated_at = CURRENT_TIMESTAMP; RETURN NEW; END; $$ language 'plpgsql';"}}, "triggers": {"users_updated_at": {"table": "users", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "user_profiles_updated_at": {"table": "user_profiles", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "characters_updated_at": {"table": "characters", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "stories_updated_at": {"table": "stories", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "story_chapters_updated_at": {"table": "story_chapters", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "chats_updated_at": {"table": "chats", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "memory_capsules_updated_at": {"table": "memory_capsules", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "user_journey_updated_at": {"table": "user_journey", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "user_currencies_updated_at": {"table": "user_currencies", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "story_ratings_updated_at": {"table": "story_ratings", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "user_story_progress_updated_at": {"table": "user_story_progress", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}, "moments_updated_at": {"table": "moments", "event": "BEFORE UPDATE", "function": "update_updated_at_column()"}}, "extensions": ["uuid-ossp", "pgcrypto"]}