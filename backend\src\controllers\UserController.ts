import { Request, Response } from 'express';
import { UserService } from '../services/UserService';
import { validationResult } from 'express-validator';
import { AuthenticatedRequest } from '../middleware/auth';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  /**
   * Get current user's profile
   */
  public async getCurrentUserProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const user = await this.userService.getUserById(userId);

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // Get user currencies
      const currencies = await this.userService.getUserCurrencies(userId);

      res.json({
        success: true,
        data: {
          ...user,
          currencies
        }
      });
    } catch (error) {
      console.error('Get current user profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Update current user's profile
   */
  public async updateUserProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const userId = req.user!.id;
      const { display_name, bio, location, website, birth_date, gender, interests, language_preference, timezone, privacy_settings } = req.body;

      // Update user basic info
      const userUpdates: Partial<{
        display_name: string;
        language_preference: string;
        timezone: string;
      }> = {};
      if (display_name !== undefined) userUpdates.display_name = display_name;
      if (language_preference !== undefined) userUpdates.language_preference = language_preference;
      if (timezone !== undefined) userUpdates.timezone = timezone;

      if (Object.keys(userUpdates).length > 0) {
        await this.userService.updateUser(userId, userUpdates);
      }

      // Update user profile
      const profileUpdates: Partial<{
        bio: string;
        location: string;
        website: string;
        birth_date: Date;
        gender: string;
        interests: string[];
        privacy_settings: Record<string, unknown>;
      }> = {};
      if (bio !== undefined) profileUpdates.bio = bio;
      if (location !== undefined) profileUpdates.location = location;
      if (website !== undefined) profileUpdates.website = website;
      if (birth_date !== undefined) profileUpdates.birth_date = birth_date;
      if (gender !== undefined) profileUpdates.gender = gender;
      if (interests !== undefined) profileUpdates.interests = interests;
      if (privacy_settings !== undefined) profileUpdates.privacy_settings = privacy_settings;

      if (Object.keys(profileUpdates).length > 0) {
        await this.userService.updateUserProfile(userId, profileUpdates);
      }

      // Get updated user
      const updatedUser = await this.userService.getUserById(userId);

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: updatedUser
      });
    } catch (error) {
      console.error('Update user profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Get user by UID
   */
  public async getUserByUid(req: Request, res: Response): Promise<void> {
    try {
      const { uid } = req.params;
      const user = await this.userService.getUserById(uid);

      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // Check if current user is following this user
      let isFollowing = false;
      if ((req as AuthenticatedRequest).user) {
        const currentUserId = (req as AuthenticatedRequest).user!.id;
        if (currentUserId !== uid) {
          isFollowing = await this.userService.isFollowing(currentUserId, uid);
        }
      }

      // Get user stats (followers count, etc.)
      const followersCount = await this.userService.getUserFollowers(uid, 1, 1).then(r => r.total);
      const followingCount = await this.userService.getUserFollowing(uid, 1, 1).then(r => r.total);

      res.json({
        success: true,
        data: {
          ...user,
          stats: {
            followers_count: followersCount,
            following_count: followingCount
          },
          is_following: isFollowing
        }
      });
    } catch (error) {
      console.error('Get user by UID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Get user's following list
   */
  public async getUserFollowing(req: Request, res: Response): Promise<void> {
    try {
      const { uid } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      // Check if user exists
      const user = await this.userService.getUserById(uid);
      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      const result = await this.userService.getUserFollowing(uid, page, limit);

      res.json({
        success: true,
        data: {
          users: result.users,
          pagination: {
            page,
            limit,
            total: result.total,
            pages: Math.ceil(result.total / limit)
          }
        }
      });
    } catch (error) {
      console.error('Get user following error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Get user's followers list
   */
  public async getUserFollowers(req: Request, res: Response): Promise<void> {
    try {
      const { uid } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      // Check if user exists
      const user = await this.userService.getUserById(uid);
      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      const result = await this.userService.getUserFollowers(uid, page, limit);

      res.json({
        success: true,
        data: {
          users: result.users,
          pagination: {
            page,
            limit,
            total: result.total,
            pages: Math.ceil(result.total / limit)
          }
        }
      });
    } catch (error) {
      console.error('Get user followers error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Follow a user
   */
  public async followUser(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const currentUserId = req.user!.id;
      const { userId: targetUserId } = req.body;

      // Can't follow yourself
      if (currentUserId === targetUserId) {
        res.status(400).json({
          success: false,
          message: 'Cannot follow yourself'
        });
        return;
      }

      // Check if target user exists
      const targetUser = await this.userService.getUserById(targetUserId);
      if (!targetUser) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // Follow user
      const success = await this.userService.followUser(currentUserId, targetUserId);

      if (success) {
        res.json({
          success: true,
          message: 'User followed successfully'
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Already following this user'
        });
      }
    } catch (error) {
      console.error('Follow user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Unfollow a user
   */
  public async unfollowUser(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const currentUserId = req.user!.id;
      const { userId: targetUserId } = req.body;

      // Unfollow user
      const success = await this.userService.unfollowUser(currentUserId, targetUserId);

      if (success) {
        res.json({
          success: true,
          message: 'User unfollowed successfully'
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Not following this user'
        });
      }
    } catch (error) {
      console.error('Unfollow user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Get user's currencies
   */
  public async getUserCurrencies(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const currencies = await this.userService.getUserCurrencies(userId);

      if (!currencies) {
        res.status(404).json({
          success: false,
          message: 'User currencies not found'
        });
        return;
      }

      res.json({
        success: true,
        data: currencies
      });
    } catch (error) {
      console.error('Get user currencies error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Claim daily bonus
   */
  public async claimDailyBonus(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const currencies = await this.userService.getUserCurrencies(userId);

      if (!currencies) {
        res.status(404).json({
          success: false,
          message: 'User currencies not found'
        });
        return;
      }

      // Check if daily bonus is available
      if (!currencies.daily_bonus_available) {
        const nextBonusTime = new Date(currencies.next_daily_bonus);
        const now = new Date();
        const hoursUntilBonus = Math.ceil((nextBonusTime.getTime() - now.getTime()) / (1000 * 60 * 60));

        res.status(400).json({
          success: false,
          message: 'Daily bonus not available',
          data: {
            next_bonus_in_hours: hoursUntilBonus
          }
        });
        return;
      }

      // Calculate bonus amount (can be based on user level, streak, etc.)
      const bonusAmounts = {
        coins: 100,
        gems: 10,
        tokens: 5
      };

      // Update currencies
      const updatedCurrencies = await this.userService.updateUserCurrencies(userId, {
        coins: currencies.coins + bonusAmounts.coins,
        gems: currencies.gems + bonusAmounts.gems,
        tokens: currencies.tokens + bonusAmounts.tokens,
        daily_bonus_available: false,
        next_daily_bonus: new Date(Date.now() + 24 * 60 * 60 * 1000) // Next bonus in 24 hours
      });

      res.json({
        success: true,
        message: 'Daily bonus claimed successfully',
        data: {
          bonus: bonusAmounts,
          currencies: updatedCurrencies
        }
      });
    } catch (error) {
      console.error('Claim daily bonus error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}