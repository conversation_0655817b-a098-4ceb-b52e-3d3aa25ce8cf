'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { CheckCircle } from 'lucide-react';

interface SuccessMessageProps {
  onSendAnother: () => void;
  lang: string;
}

const SuccessMessage: React.FC<SuccessMessageProps> = ({ onSendAnother, lang }) => {
  const { t } = useTranslation(lang, 'translation');

  return (
    <div className="text-center space-y-6">
      <div className="flex justify-center mb-6">
        <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
          <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
      </div>
      
      <div className="space-y-4">
        <h1 className="text-3xl font-bold text-foreground">
          {t('contact.success.title')}
        </h1>
        <p className="text-foreground/70 text-lg max-w-md mx-auto">
          {t('contact.success.message')}
        </p>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button 
          onClick={onSendAnother}
          className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
        >
          {t('contact.success.sendAnother')}
        </button>
        <button 
          onClick={() => window.location.href = `/${lang}`}
          className="px-6 py-3 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/90 transition-colors font-medium"
        >
          {t('contact.success.backToHome')}
        </button>
      </div>
    </div>
  );
};

export default SuccessMessage; 