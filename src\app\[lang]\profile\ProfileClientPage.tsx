'use client';

import { useState } from 'react';
import Link from 'next/link';
import MomentCard from '@/components/MomentCard';
import CharacterCard from '@/components/CharacterCard';
import ProfileModeToggle from '@/components/ProfileModeToggle';
import { Character } from '@/lib/mock-data';
import { ExternalLink, Users, Heart, Eye, Link as LinkIcon, Sparkles, FileText } from 'lucide-react';
import Image from 'next/image';
import HeroSection from '@/components/HeroSection';
import TabNavigation from '@/components/TabNavigation';

// Icon wrapper to fix type compatibility
const iconWrapper = (IconComponent: any) => ({ className, size }: { className?: string; size?: number }) => (
  <IconComponent className={className} size={size} />
);

interface ProfileClientPageProps {
  lang: string;
  moments: any[];
  liked: any[];
  friendsCards: {
    character: Character;
    stats: { likes: number; friends: number; shares: number };
    aspectRatio: number;
  }[];
}

const TABS = ['moments', 'likes', 'friends'] as const;

const ProfileClientPage: React.FC<ProfileClientPageProps> = ({ lang, moments, liked, friendsCards }) => {
  const [activeTab, setActiveTab] = useState<typeof TABS[number]>('moments');

  return (
    <>
      {/* Top profile cover */}
      <HeroSection
        backgroundImage="https://picsum.photos/1200/800?blur=3"
        title="Moonlight Wanderer"
        description="An adventurer who loves exploring AI realms and connecting with amazing characters and stories."
        avatar="https://i.pravatar.cc/160?u=profile"
        avatarSize="lg"
        variant="profile"
        height="lg"
        stats={[
          { label: 'Followers', value: '1.2k', icon: iconWrapper(Users) },
          { label: 'Following', value: '256', icon: iconWrapper(Heart) },
          { label: 'Moments', value: '87', icon: iconWrapper(FileText) },
          { label: 'Memories', value: '45', icon: iconWrapper(Sparkles) },
          { label: 'Views', value: '2.3k', icon: iconWrapper(Eye) },
          { label: 'Links', value: '7', icon: iconWrapper(LinkIcon) }
        ]}
      >
        <div className="flex items-center gap-2">
          <ProfileModeToggle lang={lang} currentPage="profile" size="md" className="flex-shrink-0" />
        </div>
      </HeroSection>

      {/* Sticky tab bar */}
      <TabNavigation
        tabs={[
          { id: 'moments', label: 'Moments', icon: iconWrapper(FileText) },
          { id: 'likes', label: 'Likes', icon: iconWrapper(Heart) },
          { id: 'friends', label: 'Friends', icon: iconWrapper(Users) }
        ]}
        activeTab={activeTab}
        onTabChange={(tabId) => setActiveTab(tabId as typeof TABS[number])}
        variant="underline"
        sticky={true}
        stickyTop="top-12 lg:top-16"
        className="bg-background border-b border-border"
      />

      {/* Feed */}
      <div className="bg-background p-1.5 md:p-2">
        <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
          {activeTab === 'moments' && moments.map((m, idx) => (
            <div key={`mom-${idx}`} className="break-inside-avoid">
              <MomentCard
                lang={lang}
                character={m.character}
                moment={m.moment}
                stats={m.stats}
                aspectRatio={m.aspectRatio}
                publishedAt={m.publishedAt}
                storyTemplateId={m.storyTemplateId}
              />
            </div>
          ))}
          {activeTab === 'likes' && liked.map((m, idx) => (
            <div key={`like-${idx}`} className="break-inside-avoid">
              <MomentCard
                lang={lang}
                character={m.character}
                moment={m.moment}
                stats={m.stats}
                aspectRatio={m.aspectRatio}
                publishedAt={m.publishedAt}
                storyTemplateId={m.storyTemplateId}
              />
            </div>
          ))}
          {activeTab === 'friends' && friendsCards.map(({character, stats, aspectRatio}) => (
            <div key={`friend-${character.id}`} className="break-inside-avoid">
              <CharacterCard lang={lang} character={character} stats={stats} aspectRatio={aspectRatio} />
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default ProfileClientPage; 