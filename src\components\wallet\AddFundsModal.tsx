import React, { useState } from 'react';
import { X, CreditCard, Wallet, Shield, Check } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'crypto' | 'bank';
  name: string;
  icon: string;
  last4?: string;
  isDefault?: boolean;
}

interface AddFundsModalProps {
  lang: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (amount: number, method: PaymentMethod) => void;
  paymentMethods?: PaymentMethod[];
  className?: string;
}

const predefinedAmounts = [10, 25, 50, 100, 200, 500];

const AddFundsModal: React.FC<AddFundsModalProps> = ({ 
  lang, 
  isOpen, 
  onClose, 
  onSuccess,
  paymentMethods = [],
  className = ''
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [selectedAmount, setSelectedAmount] = useState<number>(50);
  const [customAmount, setCustomAmount] = useState<string>('');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(
    paymentMethods.find(m => m.isDefault) || paymentMethods[0] || null
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [step, setStep] = useState<'amount' | 'method' | 'confirm'>('amount');

  if (!isOpen) return null;

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue > 0) {
      setSelectedAmount(numValue);
    }
  };

  const getCurrentAmount = () => {
    return customAmount ? parseFloat(customAmount) || 0 : selectedAmount;
  };

  const handlePayment = async () => {
    if (!selectedMethod) return;
    
    setIsProcessing(true);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsProcessing(false);
    onSuccess(getCurrentAmount(), selectedMethod);
    onClose();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            {t('wallet.addFunds')}
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto">
          {step === 'amount' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  {t('wallet.selectAmount')}
                </label>
                <div className="grid grid-cols-3 gap-3 mb-4">
                  {predefinedAmounts.map((amount) => (
                    <button
                      key={amount}
                      onClick={() => handleAmountSelect(amount)}
                      className={`p-3 rounded-xl border-2 font-semibold transition-all ${
                        selectedAmount === amount && !customAmount
                          ? 'border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300'
                          : 'border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:border-emerald-300 dark:hover:border-emerald-600'
                      }`}
                    >
                      {formatCurrency(amount)}
                    </button>
                  ))}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('wallet.customAmount')}
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="10000"
                    step="0.01"
                    value={customAmount}
                    onChange={(e) => handleCustomAmountChange(e.target.value)}
                    placeholder="0.00"
                    className="w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{t('wallet.total')}</span>
                  <span className="text-xl font-bold text-gray-900 dark:text-gray-100">
                    {formatCurrency(getCurrentAmount())}
                  </span>
                </div>
              </div>
            </div>
          )}

          {step === 'method' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {t('wallet.selectPaymentMethod')}
              </h3>
              
              <div className="space-y-3">
                {paymentMethods.map((method) => (
                  <button
                    key={method.id}
                    onClick={() => setSelectedMethod(method)}
                    className={`w-full p-4 border-2 rounded-xl flex items-center gap-3 transition-all ${
                      selectedMethod?.id === method.id
                        ? 'border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20'
                        : 'border-gray-200 dark:border-gray-600 hover:border-emerald-300 dark:hover:border-emerald-600'
                    }`}
                  >
                    <CreditCard className="w-6 h-6 text-gray-400" />
                    <div className="flex-1 text-left">
                      <div className="font-medium text-gray-900 dark:text-gray-100">
                        {method.name}
                      </div>
                      {method.last4 && (
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          **** {method.last4}
                        </div>
                      )}
                    </div>
                    {method.isDefault && (
                      <div className="text-xs bg-emerald-100 dark:bg-emerald-900 text-emerald-700 dark:text-emerald-300 px-2 py-1 rounded">
                        {t('wallet.default')}
                      </div>
                    )}
                    {selectedMethod?.id === method.id && (
                      <Check className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                    )}
                  </button>
                ))}
                
                <button className="w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl text-gray-500 dark:text-gray-400 hover:border-emerald-400 hover:text-emerald-600 dark:hover:text-emerald-400 transition-colors">
                  + {t('wallet.addNewMethod')}
                </button>
              </div>
            </div>
          )}

          {step === 'confirm' && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-emerald-100 dark:bg-emerald-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Wallet className="w-8 h-8 text-emerald-600 dark:text-emerald-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {t('wallet.confirmPayment')}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {t('wallet.confirmDescription')}
                </p>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{t('wallet.amount')}</span>
                  <span className="font-semibold text-gray-900 dark:text-gray-100">
                    {formatCurrency(getCurrentAmount())}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{t('wallet.method')}</span>
                  <span className="font-semibold text-gray-900 dark:text-gray-100">
                    {selectedMethod?.name}
                  </span>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
                  <div className="flex justify-between">
                    <span className="font-semibold text-gray-900 dark:text-gray-100">{t('wallet.total')}</span>
                    <span className="text-xl font-bold text-emerald-600 dark:text-emerald-400">
                      {formatCurrency(getCurrentAmount())}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                <Shield className="w-4 h-4" />
                <span>{t('wallet.securePayment')}</span>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={step === 'amount' ? onClose : () => setStep(step === 'method' ? 'amount' : 'method')}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            {step === 'amount' ? t('common.cancel') : t('common.back')}
          </button>
          <button
            onClick={() => {
              if (step === 'amount') setStep('method');
              else if (step === 'method') setStep('confirm');
              else handlePayment();
            }}
            disabled={isProcessing || getCurrentAmount() <= 0 || (step !== 'amount' && !selectedMethod)}
            className="flex-1 px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-xl font-semibold hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('wallet.processing')}
              </div>
            ) : (
              step === 'confirm' ? t('wallet.confirmPayment') : t('common.continue')
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddFundsModal; 