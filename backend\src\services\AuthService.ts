import { UserService } from './UserService';
import { JwtService } from './JwtService';
import { OtpService } from './OtpService';
import { PasswordService } from './PasswordService';
import { User } from '../models/User';
import { v4 as uuidv4 } from 'uuid';

export interface AuthResult {
  success: boolean;
  message?: string;
  user?: User;
  token?: string;
  refreshToken?: string;
  otpExpiry?: Date;
}

export class AuthService {
  private userService: UserService;
  private jwtService: JwtService;
  private otpService: OtpService;
  private passwordService: PasswordService;

  constructor() {
    this.userService = new UserService();
    this.jwtService = new JwtService();
    this.otpService = new OtpService();
    this.passwordService = new PasswordService();
  }

  /**
   * Login user with email and password
   */
  public async loginWithEmailAndPassword(email: string, password: string): Promise<AuthResult> {
    try {
      // Find user by email
      const user = await this.userService.getUserByEmail(email);
      
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Check if user is active
      if (!user.is_active) {
        return {
          success: false,
          message: 'Account is deactivated'
        };
      }

      // Verify password
      const isPasswordValid = await this.passwordService.comparePassword(password, user.password_hash);
      
      if (!isPasswordValid) {
        return {
          success: false,
          message: 'Invalid password'
        };
      }

      // Generate tokens
      const token = this.jwtService.generateAccessToken(user.id);
      const refreshToken = this.jwtService.generateRefreshToken(user.id);

      // Update last login
      await this.userService.updateLastLogin(user.id);

      // Remove sensitive data
      const { password_hash, ...userWithoutPassword } = user;

      return {
        success: true,
        user: userWithoutPassword,
        token,
        refreshToken
      };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Login failed'
      };
    }
  }

  /**
   * Register new user with email and password
   */
  public async registerWithEmailAndPassword(email: string, password: string): Promise<AuthResult> {
    try {
      // Check if user already exists
      const existingUser = await this.userService.getUserByEmail(email);
      
      if (existingUser) {
        return {
          success: false,
          message: 'User already exists'
        };
      }

      // Hash password
      const hashedPassword = await this.passwordService.hashPassword(password);

      // Create new user
      const newUser: Partial<User> = {
        id: uuidv4(),
        email,
        password_hash: hashedPassword,
        username: email.split('@')[0] + '_' + Math.random().toString(36).substr(2, 5),
        display_name: email.split('@')[0],
        language_preference: 'en',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      };

      const user = await this.userService.createUser(newUser);

      // Create user profile
      await this.userService.createUserProfile({
        user_id: user.id,
        privacy_settings: {
          profile_visibility: 'public',
          show_email: false,
          allow_messages: 'followers'
        }
      });

      // Initialize user currencies
      await this.userService.initializeUserCurrencies(user.id);

      // Generate tokens
      const token = this.jwtService.generateAccessToken(user.id);
      const refreshToken = this.jwtService.generateRefreshToken(user.id);

      // Remove sensitive data
      const { password_hash, ...userWithoutPassword } = user;

      return {
        success: true,
        user: userWithoutPassword,
        token,
        refreshToken
      };
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        message: 'Registration failed'
      };
    }
  }

  /**
   * Send OTP for email verification
   */
  public async sendOTP(email: string): Promise<AuthResult> {
    try {
      // Check if user exists
      const user = await this.userService.getUserByEmail(email);
      
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Generate and store OTP
      const otp = this.otpService.generateOTP();
      const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      await this.otpService.storeOTP(email, otp, otpExpiry);

      // In production, send email with OTP
      console.log(`OTP for ${email}: ${otp}`); // Remove in production

      return {
        success: true,
        message: 'OTP sent successfully',
        otpExpiry
      };
    } catch (error) {
      console.error('Send OTP error:', error);
      return {
        success: false,
        message: 'Failed to send OTP'
      };
    }
  }

  /**
   * Verify OTP and authenticate user
   */
  public async verifyOTP(email: string, otp: string): Promise<AuthResult> {
    try {
      // Verify OTP
      const isValid = await this.otpService.verifyOTP(email, otp);
      
      if (!isValid) {
        return {
          success: false,
          message: 'Invalid or expired OTP'
        };
      }

      // Find or create user
      let user = await this.userService.getUserByEmail(email);
      
      if (!user) {
        // Create new user if doesn't exist
        const newUser: Partial<User> = {
          id: uuidv4(),
          email,
          username: email.split('@')[0] + '_' + Math.random().toString(36).substr(2, 5),
          display_name: email.split('@')[0],
          language_preference: 'en',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        };

        user = await this.userService.createUser(newUser);
        
        // Create user profile
        await this.userService.createUserProfile({
          user_id: user.id,
          privacy_settings: {
            profile_visibility: 'public',
            show_email: false,
            allow_messages: 'followers'
          }
        });

        // Initialize user currencies
        await this.userService.initializeUserCurrencies(user.id);
      }

      // Generate tokens
      const token = this.jwtService.generateAccessToken(user.id);
      const refreshToken = this.jwtService.generateRefreshToken(user.id);

      // Update last login
      await this.userService.updateLastLogin(user.id);

      // Remove sensitive data
      const { password_hash, ...userWithoutPassword } = user;

      return {
        success: true,
        user: userWithoutPassword,
        token,
        refreshToken
      };
    } catch (error) {
      console.error('Verify OTP error:', error);
      return {
        success: false,
        message: 'OTP verification failed'
      };
    }
  }

  /**
   * Refresh access token
   */
  public async refreshToken(refreshToken: string): Promise<AuthResult> {
    try {
      // Verify refresh token
      const decoded = this.jwtService.verifyRefreshToken(refreshToken);
      
      if (!decoded) {
        return {
          success: false,
          message: 'Invalid refresh token'
        };
      }

      // Check if user exists and is active
      const user = await this.userService.getUserById(decoded.userId);
      
      if (!user || !user.is_active) {
        return {
          success: false,
          message: 'User not found or inactive'
        };
      }

      // Generate new tokens
      const newToken = this.jwtService.generateAccessToken(user.id);
      const newRefreshToken = this.jwtService.generateRefreshToken(user.id);

      return {
        success: true,
        token: newToken,
        refreshToken: newRefreshToken
      };
    } catch (error) {
      console.error('Refresh token error:', error);
      return {
        success: false,
        message: 'Token refresh failed'
      };
    }
  }

  /**
   * Logout user by invalidating refresh token
   */
  public async logout(refreshToken: string): Promise<void> {
    try {
      // Add refresh token to blacklist
      await this.jwtService.blacklistToken(refreshToken);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }
}