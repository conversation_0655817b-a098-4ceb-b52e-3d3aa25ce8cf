'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Package, Settings, UserPlus, User, CreditCard } from 'lucide-react';

export type NotificationTab = 'all' | 'system' | 'social' | 'profile' | 'subscription';

interface NotificationTabsProps {
  lang: string;
  activeTab: NotificationTab;
  onTabChange: (tab: NotificationTab) => void;
  unreadCounts: Record<NotificationTab, number>;
}

interface TabButtonProps {
  tab: NotificationTab;
  label: string;
  isActive: boolean;
  onClick: () => void;
  count?: number;
  icon: React.ComponentType<any>;
}

const TabButton: React.FC<TabButtonProps> = ({ 
  tab, 
  label, 
  isActive, 
  onClick, 
  count, 
  icon: IconComponent 
}) => (
  <button
    onClick={onClick}
    className={`px-3 sm:px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center gap-2 whitespace-nowrap relative ${
      isActive
        ? 'bg-primary text-primary-foreground shadow-sm'
        : 'text-foreground hover:text-foreground'
    }`}
  >
    <IconComponent 
      size={16} 
      className={isActive ? 'text-primary-foreground' : 'text-foreground'} 
    />
    <span className={`hidden sm:inline ${isActive ? 'text-primary-foreground' : 'text-foreground'}`}>
      {label}
    </span>
    {count !== undefined && count > 0 && (
      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center text-[10px]">
        {count}
      </span>
    )}
  </button>
);

const NotificationTabs: React.FC<NotificationTabsProps> = ({
  lang,
  activeTab,
  onTabChange,
  unreadCounts
}) => {
  const { t } = useTranslation(lang, 'translation');

  const tabs: Array<{
    id: NotificationTab;
    label: string;
    icon: React.ComponentType<any>;
  }> = [
    {
      id: 'all',
      label: t('notifications.all'),
      icon: Package
    },
    {
      id: 'system',
      label: t('notifications.system'),
      icon: Settings
    },
    {
      id: 'social',
      label: t('notifications.social'),
      icon: UserPlus
    },
    {
      id: 'profile',
      label: t('notifications.profile'),
      icon: User
    },
    {
      id: 'subscription',
      label: t('notifications.subscription'),
      icon: CreditCard
    }
  ];

  return (
    <div className="flex bg-card border border-border rounded-lg p-1 theme-transition w-fit mx-auto">
      {tabs.map((tab) => (
        <TabButton
          key={tab.id}
          tab={tab.id}
          label={tab.label}
          isActive={activeTab === tab.id}
          onClick={() => onTabChange(tab.id)}
          count={unreadCounts[tab.id] || undefined}
          icon={tab.icon}
        />
      ))}
    </div>
  );
};

export default NotificationTabs; 