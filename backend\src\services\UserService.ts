import { User } from '../models/User';
import { UserProfile } from '../models/UserProfile';
import { UserCurrency } from '../models/UserCurrency';
import { db } from '../database/connection';

export class UserService {
  /**
   * Get user by ID
   */
  public async getUserById(id: string): Promise<User | null> {
    try {
      const query = `
        SELECT 
          u.*,
          up.avatar_url as profile_avatar,
          up.bio as profile_bio,
          up.location,
          up.website,
          up.birth_date,
          up.gender,
          up.interests,
          up.social_links,
          up.privacy_settings
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE u.id = $1 AND u.is_active = true
      `;
      
      const result = await db.query(query, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];
      
      // Transform to User model
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        password_hash: user.password_hash,
        display_name: user.display_name,
        avatar_url: user.profile_avatar || user.avatar_url,
        bio: user.profile_bio || user.bio,
        language_preference: user.language_preference,
        timezone: user.timezone,
        is_creator: user.is_creator,
        is_verified: user.is_verified,
        is_active: user.is_active,
        created_at: user.created_at,
        updated_at: user.updated_at,
        profile: {
          location: user.location,
          website: user.website,
          birth_date: user.birth_date,
          gender: user.gender,
          interests: user.interests || [],
          social_links: user.social_links || {},
          privacy_settings: user.privacy_settings || {}
        }
      };
    } catch (error) {
      console.error('Get user by ID error:', error);
      throw error;
    }
  }

  /**
   * Get user by email
   */
  public async getUserByEmail(email: string): Promise<User | null> {
    try {
      const query = `
        SELECT * FROM users 
        WHERE email = $1 AND is_active = true
      `;
      
      const result = await db.query(query, [email]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      console.error('Get user by email error:', error);
      throw error;
    }
  }

  /**
   * Get user by username
   */
  public async getUserByUsername(username: string): Promise<User | null> {
    try {
      const query = `
        SELECT 
          u.*,
          up.avatar_url as profile_avatar,
          up.bio as profile_bio,
          up.location,
          up.website,
          up.birth_date,
          up.gender,
          up.interests,
          up.social_links,
          up.privacy_settings
        FROM users u
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE u.username = $1 AND u.is_active = true
      `;
      
      const result = await db.query(query, [username]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];
      
      return {
        id: user.id,
        username: user.username,
        email: user.email,
        password_hash: user.password_hash,
        display_name: user.display_name,
        avatar_url: user.profile_avatar || user.avatar_url,
        bio: user.profile_bio || user.bio,
        language_preference: user.language_preference,
        timezone: user.timezone,
        is_creator: user.is_creator,
        is_verified: user.is_verified,
        is_active: user.is_active,
        created_at: user.created_at,
        updated_at: user.updated_at,
        profile: {
          location: user.location,
          website: user.website,
          birth_date: user.birth_date,
          gender: user.gender,
          interests: user.interests || [],
          social_links: user.social_links || {},
          privacy_settings: user.privacy_settings || {}
        }
      };
    } catch (error) {
      console.error('Get user by username error:', error);
      throw error;
    }
  }

  /**
   * Create new user
   */
  public async createUser(userData: Partial<User>): Promise<User> {
    try {
      const query = `
        INSERT INTO users (
          id, username, email, password_hash, display_name, 
          avatar_url, bio, language_preference, timezone, 
          is_creator, is_verified, is_active, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
        ) RETURNING *
      `;
      
      const values = [
        userData.id,
        userData.username,
        userData.email,
        userData.password_hash,
        userData.display_name,
        userData.avatar_url,
        userData.bio,
        userData.language_preference,
        userData.timezone,
        userData.is_creator,
        userData.is_verified,
        userData.is_active,
        userData.created_at,
        userData.updated_at
      ];
      
      const result = await db.query(query, values);
      return result.rows[0];
    } catch (error) {
      console.error('Create user error:', error);
      throw error;
    }
  }

  /**
   * Update user
   */
  public async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    try {
      const setClause: string[] = [];
      const values: unknown[] = [];
      let paramIndex = 1;

      // Build dynamic SET clause
      Object.entries(updates).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'created_at' && value !== undefined) {
          setClause.push(`${key} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      });

      // Add updated_at timestamp
      setClause.push(`updated_at = $${paramIndex}`);
      values.push(new Date());
      paramIndex++;

      // Add user ID
      values.push(id);

      const query = `
        UPDATE users 
        SET ${setClause.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `;
      
      const result = await db.query(query, values);
      
      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  }

  /**
   * Create user profile
   */
  public async createUserProfile(profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const query = `
        INSERT INTO user_profiles (
          id, user_id, avatar_url, bio, location, website,
          birth_date, gender, interests, social_links, 
          privacy_settings, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
        ) RETURNING *
      `;
      
      const values = [
        uuidv4(),
        profileData.user_id,
        profileData.avatar_url,
        profileData.bio,
        profileData.location,
        profileData.website,
        profileData.birth_date,
        profileData.gender,
        profileData.interests,
        profileData.social_links,
        profileData.privacy_settings,
        new Date(),
        new Date()
      ];
      
      const result = await db.query(query, values);
      return result.rows[0];
    } catch (error) {
      console.error('Create user profile error:', error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  public async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> {
    try {
      const setClause: string[] = [];
      const values: unknown[] = [];
      let paramIndex = 1;

      // Build dynamic SET clause
      Object.entries(updates).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'user_id' && key !== 'created_at' && value !== undefined) {
          setClause.push(`${key} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      });

      // Add updated_at timestamp
      setClause.push(`updated_at = $${paramIndex}`);
      values.push(new Date());
      paramIndex++;

      // Add user ID
      values.push(userId);

      const query = `
        UPDATE user_profiles 
        SET ${setClause.join(', ')}
        WHERE user_id = $${paramIndex}
        RETURNING *
      `;
      
      const result = await db.query(query, values);
      
      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      console.error('Update user profile error:', error);
      throw error;
    }
  }

  /**
   * Initialize user currencies
   */
  public async initializeUserCurrencies(userId: string): Promise<UserCurrency> {
    try {
      const query = `
        INSERT INTO user_currencies (
          id, user_id, coins, gems, tokens, hearts,
          star_diamonds, joy_crystals, glimmering_dust,
          memory_puzzles, daily_bonus_available, next_daily_bonus,
          currency_stats, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
        ) RETURNING *
      `;
      
      const values = [
        uuidv4(),
        userId,
        100, // Initial coins
        10,  // Initial gems
        5,   // Initial tokens
        0,   // Hearts
        0,   // Star diamonds
        0,   // Joy crystals
        0,   // Glimmering dust
        0,   // Memory puzzles
        true, // Daily bonus available
        new Date(Date.now() + 24 * 60 * 60 * 1000), // Next bonus in 24 hours
        {}, // Currency stats
        new Date(),
        new Date()
      ];
      
      const result = await db.query(query, values);
      return result.rows[0];
    } catch (error) {
      console.error('Initialize user currencies error:', error);
      throw error;
    }
  }

  /**
   * Get user currencies
   */
  public async getUserCurrencies(userId: string): Promise<UserCurrency | null> {
    try {
      const query = `
        SELECT * FROM user_currencies 
        WHERE user_id = $1
      `;
      
      const result = await db.query(query, [userId]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      console.error('Get user currencies error:', error);
      throw error;
    }
  }

  /**
   * Update user currencies
   */
  public async updateUserCurrencies(userId: string, updates: Partial<UserCurrency>): Promise<UserCurrency | null> {
    try {
      const setClause: string[] = [];
      const values: unknown[] = [];
      let paramIndex = 1;

      // Build dynamic SET clause
      Object.entries(updates).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'user_id' && key !== 'created_at' && value !== undefined) {
          setClause.push(`${key} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      });

      // Add updated_at timestamp
      setClause.push(`updated_at = $${paramIndex}`);
      values.push(new Date());
      paramIndex++;

      // Add user ID
      values.push(userId);

      const query = `
        UPDATE user_currencies 
        SET ${setClause.join(', ')}
        WHERE user_id = $${paramIndex}
        RETURNING *
      `;
      
      const result = await db.query(query, values);
      
      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error) {
      console.error('Update user currencies error:', error);
      throw error;
    }
  }

  /**
   * Update last login timestamp
   */
  public async updateLastLogin(userId: string): Promise<void> {
    try {
      const query = `
        UPDATE users 
        SET updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `;
      
      await db.query(query, [userId]);
    } catch (error) {
      console.error('Update last login error:', error);
      throw error;
    }
  }

  /**
   * Get user followers
   */
  public async getUserFollowers(userId: string, page: number = 1, limit: number = 20): Promise<{ users: User[]; total: number }> {
    try {
      const offset = (page - 1) * limit;
      
      const query = `
        SELECT 
          u.*,
          up.avatar_url as profile_avatar,
          up.bio as profile_bio,
          up.location,
          up.website,
          up.birth_date,
          up.gender,
          up.interests,
          up.social_links,
          up.privacy_settings,
          f.created_at as followed_at
        FROM users u
        INNER JOIN user_following f ON u.id = f.follower_id
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE f.following_id = $1 AND u.is_active = true
        ORDER BY f.created_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      const countQuery = `
        SELECT COUNT(*) as total
        FROM user_following
        WHERE following_id = $1
      `;
      
      const [usersResult, countResult] = await Promise.all([
        db.query(query, [userId, limit, offset]),
        db.query(countQuery, [userId])
      ]);
      
      return {
        users: usersResult.rows,
        total: parseInt(countResult.rows[0].total)
      };
    } catch (error) {
      console.error('Get user followers error:', error);
      throw error;
    }
  }

  /**
   * Get user following
   */
  public async getUserFollowing(userId: string, page: number = 1, limit: number = 20): Promise<{ users: User[]; total: number }> {
    try {
      const offset = (page - 1) * limit;
      
      const query = `
        SELECT 
          u.*,
          up.avatar_url as profile_avatar,
          up.bio as profile_bio,
          up.location,
          up.website,
          up.birth_date,
          up.gender,
          up.interests,
          up.social_links,
          up.privacy_settings,
          f.created_at as followed_at
        FROM users u
        INNER JOIN user_following f ON u.id = f.following_id
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE f.follower_id = $1 AND u.is_active = true
        ORDER BY f.created_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      const countQuery = `
        SELECT COUNT(*) as total
        FROM user_following
        WHERE follower_id = $1
      `;
      
      const [usersResult, countResult] = await Promise.all([
        db.query(query, [userId, limit, offset]),
        db.query(countQuery, [userId])
      ]);
      
      return {
        users: usersResult.rows,
        total: parseInt(countResult.rows[0].total)
      };
    } catch (error) {
      console.error('Get user following error:', error);
      throw error;
    }
  }

  /**
   * Follow a user
   */
  public async followUser(followerId: string, followingId: string): Promise<boolean> {
    try {
      const query = `
        INSERT INTO user_following (follower_id, following_id, created_at)
        VALUES ($1, $2, CURRENT_TIMESTAMP)
        ON CONFLICT (follower_id, following_id) DO NOTHING
      `;
      
      const result = await db.query(query, [followerId, followingId]);
      return result.rowCount > 0;
    } catch (error) {
      console.error('Follow user error:', error);
      throw error;
    }
  }

  /**
   * Unfollow a user
   */
  public async unfollowUser(followerId: string, followingId: string): Promise<boolean> {
    try {
      const query = `
        DELETE FROM user_following 
        WHERE follower_id = $1 AND following_id = $2
      `;
      
      const result = await db.query(query, [followerId, followingId]);
      return result.rowCount > 0;
    } catch (error) {
      console.error('Unfollow user error:', error);
      throw error;
    }
  }

  /**
   * Check if user is following another user
   */
  public async isFollowing(followerId: string, followingId: string): Promise<boolean> {
    try {
      const query = `
        SELECT EXISTS (
          SELECT 1 FROM user_following 
          WHERE follower_id = $1 AND following_id = $2
        )
      `;
      
      const result = await db.query(query, [followerId, followingId]);
      return result.rows[0].exists;
    } catch (error) {
      console.error('Check following error:', error);
      throw error;
    }
  }
}