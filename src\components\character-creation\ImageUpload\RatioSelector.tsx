'use client';

import React from 'react';
import type { CharacterImageAspectRatio } from '@/types/character-creation';

interface RatioSelectorProps {
  isOpen: boolean;
  selectedRatio: CharacterImageAspectRatio;
  onRatioChange: (ratio: CharacterImageAspectRatio) => void;
  onConfirm: () => void;
  onCancel: () => void;
}

const RatioSelector: React.FC<RatioSelectorProps> = ({
  isOpen,
  selectedRatio,
  onRatioChange,
  onConfirm,
  onCancel,
}) => {
  if (!isOpen) return null;

  const ratioOptions = [
    { 
      value: '5:6' as const, 
      label: '5:6 (Recommended)', 
      desc: 'Classic portrait ratio, suitable for half to full body' 
    },
    { 
      value: '5:8' as const, 
      label: '5:8 (Tall)', 
      desc: 'Full body portrait ratio, showcases complete figure' 
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full">
        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
          Select Image Aspect Ratio
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Please choose the appropriate aspect ratio for your character image
        </p>
        
        <div className="space-y-3 mb-6">
          {ratioOptions.map((ratio) => (
            <button
              key={ratio.value}
              type="button"
              onClick={() => onRatioChange(ratio.value)}
              className={`w-full text-left p-4 rounded-lg border-2 transition-all ${
                selectedRatio === ratio.value
                  ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-600'
              }`}
            >
              <div className="font-medium text-gray-900 dark:text-white">
                {ratio.label}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {ratio.desc}
              </div>
            </button>
          ))}
        </div>

        <div className="flex gap-3">
          <button
            type="button"
            onClick={onCancel}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={onConfirm}
            className="flex-1 px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
          >
            Start Cropping
          </button>
        </div>
      </div>
    </div>
  );
};

export default RatioSelector;
