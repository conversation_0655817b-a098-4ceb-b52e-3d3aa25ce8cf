'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { WelcomeGift, FirstTimePurchaseOffer, ClaimGiftResponse, MockStoreService } from '@/lib/store-api';

interface StoreContextType {
  // Welcome gifts state
  welcomeGifts: WelcomeGift[];
  loadingGifts: boolean;
  claimGift: (giftId: string) => Promise<ClaimGiftResponse>;
  refreshGifts: () => Promise<void>;
  
  // First-time purchase state
  firstTimePurchaseOffers: FirstTimePurchaseOffer[];
  loadingOffers: boolean;
  hasUserMadeFirstPurchase: boolean;
  markFirstPurchaseComplete: () => void;
  refreshOffers: () => Promise<void>;
  
  // User wallet state
  walletBalance: {
    coins: number;
    gems: number;
    tokens: number;
    hearts: number;
  };
  updateWalletBalance: (newBalance: Partial<StoreContextType['walletBalance']>) => void;
  
  // Claim states
  claimStates: Record<string, {
    isLoading: boolean;
    error: string | null;
    success: boolean;
  }>;
}

const StoreContext = createContext<StoreContextType | undefined>(undefined);

interface StoreProviderProps {
  children: ReactNode;
}

export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  const [welcomeGifts, setWelcomeGifts] = useState<WelcomeGift[]>([]);
  const [loadingGifts, setLoadingGifts] = useState(true);
  const [firstTimePurchaseOffers, setFirstTimePurchaseOffers] = useState<FirstTimePurchaseOffer[]>([]);
  const [loadingOffers, setLoadingOffers] = useState(true);
  const [hasUserMadeFirstPurchase, setHasUserMadeFirstPurchase] = useState(false);
  const [walletBalance, setWalletBalance] = useState({
    coins: 1000,
    gems: 150,
    tokens: 50,
    hearts: 25
  });
  const [claimStates, setClaimStates] = useState<Record<string, {
    isLoading: boolean;
    error: string | null;
    success: boolean;
  }>>({});

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    await Promise.all([
      refreshGifts(),
      refreshOffers(),
      loadUserPurchaseStatus()
    ]);
  };

  const refreshGifts = async () => {
    try {
      setLoadingGifts(true);
      const gifts = await MockStoreService.getWelcomeGifts();
      setWelcomeGifts(gifts);
    } catch (error) {
      console.error('Failed to load welcome gifts:', error);
    } finally {
      setLoadingGifts(false);
    }
  };

  const refreshOffers = async () => {
    try {
      setLoadingOffers(true);
      const offers = await MockStoreService.getFirstTimePurchaseOffers();
      setFirstTimePurchaseOffers(offers);
    } catch (error) {
      console.error('Failed to load first-time purchase offers:', error);
    } finally {
      setLoadingOffers(false);
    }
  };

  const loadUserPurchaseStatus = async () => {
    try {
      // In a real app, this would come from an API
      // For now, we'll check localStorage for persistence
      const storedStatus = localStorage.getItem('hasUserMadeFirstPurchase');
      setHasUserMadeFirstPurchase(storedStatus === 'true');
    } catch (error) {
      console.error('Failed to load user purchase status:', error);
    }
  };

  const claimGift = async (giftId: string): Promise<ClaimGiftResponse> => {
    try {
      // Set loading state
      setClaimStates(prev => ({
        ...prev,
        [giftId]: { isLoading: true, error: null, success: false }
      }));

      // Call API to claim gift
      const response = await MockStoreService.claimGift(giftId);
      
      if (response.success) {
        // Update claim state to success
        setClaimStates(prev => ({
          ...prev,
          [giftId]: { isLoading: false, error: null, success: true }
        }));

        // Update the gift in the list to mark as claimed
        setWelcomeGifts(prev => 
          prev.map(gift => 
            gift.id === giftId 
              ? { ...gift, claimed: true }
              : gift
          )
        );

        // Update wallet balance with new rewards
        if (response.updated_balances) {
          setWalletBalance(prev => ({
            ...prev,
            ...response.updated_balances
          }));
        }

        return response;
      } else {
        throw new Error('Failed to claim gift');
      }
    } catch (error) {
      // Set error state
      const errorMessage = error instanceof Error ? error.message : 'Failed to claim gift';
      setClaimStates(prev => ({
        ...prev,
        [giftId]: { 
          isLoading: false, 
          error: errorMessage, 
          success: false 
        }
      }));
      throw error;
    }
  };

  const markFirstPurchaseComplete = () => {
    setHasUserMadeFirstPurchase(true);
    localStorage.setItem('hasUserMadeFirstPurchase', 'true');
    MockStoreService.markFirstPurchaseMade();
    
    // Refresh offers to hide first-time purchase deals
    refreshOffers();
  };

  const updateWalletBalance = (newBalance: Partial<StoreContextType['walletBalance']>) => {
    setWalletBalance(prev => ({
      ...prev,
      ...newBalance
    }));
  };

  const value: StoreContextType = {
    welcomeGifts,
    loadingGifts,
    claimGift,
    refreshGifts,
    firstTimePurchaseOffers,
    loadingOffers,
    hasUserMadeFirstPurchase,
    markFirstPurchaseComplete,
    refreshOffers,
    walletBalance,
    updateWalletBalance,
    claimStates
  };

  return (
    <StoreContext.Provider value={value}>
      {children}
    </StoreContext.Provider>
  );
};

export const useStore = (): StoreContextType => {
  const context = useContext(StoreContext);
  if (context === undefined) {
    throw new Error('useStore must be used within a StoreProvider');
  }
  return context;
};

// Hook for easy access to wallet balance
export const useWallet = () => {
  const { walletBalance, updateWalletBalance } = useStore();
  return { walletBalance, updateWalletBalance };
};

// Hook for gift claiming functionality
export const useGiftClaiming = () => {
  const { welcomeGifts, claimGift, claimStates, refreshGifts } = useStore();
  return { welcomeGifts, claimGift, claimStates, refreshGifts };
};

// Hook for first-time purchase functionality
export const useFirstTimePurchase = () => {
  const { 
    firstTimePurchaseOffers, 
    hasUserMadeFirstPurchase, 
    markFirstPurchaseComplete,
    refreshOffers 
  } = useStore();
  
  return { 
    firstTimePurchaseOffers, 
    hasUserMadeFirstPurchase, 
    markFirstPurchaseComplete,
    refreshOffers 
  };
};
