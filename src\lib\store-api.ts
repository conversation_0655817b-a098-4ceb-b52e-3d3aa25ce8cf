import { authenticatedRequest, ApiResponse } from './api';

// Types for store and gift functionality
export interface GiftReward {
  type: 'currency' | 'character' | 'subscription' | 'bonus' | 'feature' | 'exclusive';
  name: string;
  amount?: number;
  currency_type?: string;
  item_id?: string;
}

export interface WelcomeGift {
  id: string;
  type: 'welcome' | 'first_recharge' | 'welcome_back' | 'first_popup' | 'version_limited' | 'ip_collab';
  title: string;
  description: string;
  rewards: GiftReward[];
  requirements?: string;
  time_limit?: string;
  claimed: boolean;
  available: boolean;
  expires_at?: string;
}

export interface ClaimGiftResponse {
  success: boolean;
  rewards_granted: GiftReward[];
  updated_balances: {
    coins?: number;
    gems?: number;
    tokens?: number;
    hearts?: number;
  };
  message: string;
}

export interface FirstTimePurchaseOffer {
  id: string;
  name: string;
  description: string;
  original_price: number;
  discounted_price: number;
  discount_percentage: number;
  currency: string;
  rewards: GiftReward[];
  valid_until?: string;
  is_first_time_only: boolean;
  category: 'starter_pack' | 'character_bundle' | 'premium_subscription' | 'currency_pack';
}

export interface UserPurchaseStatus {
  has_made_first_purchase: boolean;
  first_purchase_date?: string;
  total_purchases: number;
  available_first_time_offers: FirstTimePurchaseOffer[];
}

// API Functions

/**
 * Get available welcome gifts for the current user
 */
export async function getWelcomeGifts(): Promise<ApiResponse<WelcomeGift[]>> {
  return authenticatedRequest<WelcomeGift[]>('/store/welcome-gifts');
}

/**
 * Claim a specific welcome gift
 */
export async function claimWelcomeGift(giftId: string): Promise<ApiResponse<ClaimGiftResponse>> {
  return authenticatedRequest<ClaimGiftResponse>(`/store/welcome-gifts/${giftId}/claim`, {
    method: 'POST',
  });
}

/**
 * Get user's purchase status and available first-time offers
 */
export async function getUserPurchaseStatus(): Promise<ApiResponse<UserPurchaseStatus>> {
  return authenticatedRequest<UserPurchaseStatus>('/store/purchase-status');
}

/**
 * Get first-time purchase discount offers
 */
export async function getFirstTimePurchaseOffers(): Promise<ApiResponse<FirstTimePurchaseOffer[]>> {
  return authenticatedRequest<FirstTimePurchaseOffer[]>('/store/first-time-offers');
}

/**
 * Purchase a first-time discount offer
 */
export async function purchaseFirstTimeOffer(
  offerId: string,
  paymentMethod?: string
): Promise<ApiResponse<{ transaction_id: string; success: boolean }>> {
  return authenticatedRequest(`/store/first-time-offers/${offerId}/purchase`, {
    method: 'POST',
    body: JSON.stringify({ payment_method: paymentMethod }),
  });
}

/**
 * Check if user is eligible for first-time purchase bonuses
 */
export async function checkFirstTimePurchaseEligibility(): Promise<ApiResponse<{ eligible: boolean; reason?: string }>> {
  return authenticatedRequest('/store/first-time-eligibility');
}

/**
 * Get user's wallet balance
 */
export async function getWalletBalance(): Promise<ApiResponse<{
  coins: number;
  gems: number;
  tokens: number;
  hearts: number;
  daily_bonus_available: boolean;
  next_daily_bonus: string;
}>> {
  return authenticatedRequest('/wallet');
}

/**
 * Mock service for development/testing
 */
export class MockStoreService {
  private static claimedGifts = new Set<string>();
  private static userHasMadeFirstPurchase = false;

  static async getWelcomeGifts(): Promise<WelcomeGift[]> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return [
      {
        id: 'welcome_new',
        type: 'welcome',
        title: 'Welcome to Alphane!',
        description: 'Start your journey with these exclusive welcome gifts',
        rewards: [
          { type: 'currency', name: '500 Joy Crystals', amount: 500, currency_type: 'joy_crystals' },
          { type: 'currency', name: '100 Glimmering Dust', amount: 100, currency_type: 'glimmering_dust' },
          { type: 'character', name: 'Starter Character', item_id: 'char_starter_001' },
          { type: 'subscription', name: '3 Days Premium', item_id: 'sub_premium_3d' }
        ],
        claimed: this.claimedGifts.has('welcome_new'),
        available: true
      },
      {
        id: 'first_recharge',
        type: 'first_recharge',
        title: 'First Purchase Bonus',
        description: 'Double your first purchase and get exclusive rewards',
        rewards: [
          { type: 'bonus', name: '100% Bonus Diamonds', currency_type: 'diamonds' },
          { type: 'character', name: 'Premium Character', item_id: 'char_premium_001' },
          { type: 'currency', name: '1000 Joy Crystals', amount: 1000, currency_type: 'joy_crystals' }
        ],
        requirements: 'Make your first purchase of any amount',
        claimed: this.claimedGifts.has('first_recharge'),
        available: !this.userHasMadeFirstPurchase
      }
    ];
  }

  static async claimGift(giftId: string): Promise<ClaimGiftResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (this.claimedGifts.has(giftId)) {
      throw new Error('Gift already claimed');
    }

    this.claimedGifts.add(giftId);
    
    // Mock rewards based on gift type
    const mockRewards: Record<string, GiftReward[]> = {
      'welcome_new': [
        { type: 'currency', name: '500 Joy Crystals', amount: 500, currency_type: 'joy_crystals' },
        { type: 'currency', name: '100 Glimmering Dust', amount: 100, currency_type: 'glimmering_dust' },
        { type: 'character', name: 'Starter Character', item_id: 'char_starter_001' },
        { type: 'subscription', name: '3 Days Premium', item_id: 'sub_premium_3d' }
      ],
      'first_recharge': [
        { type: 'bonus', name: '100% Bonus Diamonds', currency_type: 'diamonds' },
        { type: 'character', name: 'Premium Character', item_id: 'char_premium_001' },
        { type: 'currency', name: '1000 Joy Crystals', amount: 1000, currency_type: 'joy_crystals' }
      ]
    };

    return {
      success: true,
      rewards_granted: mockRewards[giftId] || [],
      updated_balances: {
        coins: 1500,
        gems: 250,
        tokens: 100,
        hearts: 50
      },
      message: 'Gift claimed successfully!'
    };
  }

  static async getFirstTimePurchaseOffers(): Promise<FirstTimePurchaseOffer[]> {
    if (this.userHasMadeFirstPurchase) {
      return [];
    }

    return [
      {
        id: 'starter_pack_discount',
        name: 'New Player Starter Pack',
        description: 'Perfect for beginners! Get premium characters and currency at 70% off',
        original_price: 29.99,
        discounted_price: 8.99,
        discount_percentage: 70,
        currency: 'USD',
        rewards: [
          { type: 'character', name: '3 Premium Characters', item_id: 'bundle_premium_3' },
          { type: 'currency', name: '2000 Joy Crystals', amount: 2000, currency_type: 'joy_crystals' },
          { type: 'currency', name: '500 Glimmering Dust', amount: 500, currency_type: 'glimmering_dust' }
        ],
        is_first_time_only: true,
        category: 'starter_pack'
      },
      {
        id: 'premium_trial_discount',
        name: 'Premium Subscription Trial',
        description: 'Try premium features for 30 days at 50% off',
        original_price: 19.99,
        discounted_price: 9.99,
        discount_percentage: 50,
        currency: 'USD',
        rewards: [
          { type: 'subscription', name: '30 Days Premium Access', item_id: 'sub_premium_30d' },
          { type: 'currency', name: '1000 Joy Crystals', amount: 1000, currency_type: 'joy_crystals' },
          { type: 'feature', name: 'Exclusive Avatar Frames', item_id: 'avatar_frames_premium' }
        ],
        is_first_time_only: true,
        category: 'premium_subscription'
      },
      {
        id: 'character_bundle_discount',
        name: 'Character Collection Bundle',
        description: 'Unlock 5 unique characters with special first-time pricing',
        original_price: 49.99,
        discounted_price: 19.99,
        discount_percentage: 60,
        currency: 'USD',
        rewards: [
          { type: 'character', name: '5 Unique Characters', item_id: 'bundle_unique_5' },
          { type: 'exclusive', name: 'Collector Badge', item_id: 'badge_collector' },
          { type: 'currency', name: '1500 Joy Crystals', amount: 1500, currency_type: 'joy_crystals' }
        ],
        is_first_time_only: true,
        category: 'character_bundle'
      }
    ];
  }

  static markFirstPurchaseMade(): void {
    this.userHasMadeFirstPurchase = true;
  }

  static reset(): void {
    this.claimedGifts.clear();
    this.userHasMadeFirstPurchase = false;
  }
}
