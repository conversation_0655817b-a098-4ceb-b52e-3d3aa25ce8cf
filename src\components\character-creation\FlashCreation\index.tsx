'use client';

import React, { useState } from 'react';
import FlashGenerator from '../FlashGenerator';
import { useFlashGeneration } from '@/hooks/character-creation/useFlashGeneration';
import { GenreFilters } from '@/components/CharacterGenreFilter';

interface FlashCreationProps {
  onAccept?: (result: any) => void;
  filters?: GenreFilters;
  onFiltersChange?: (filters: GenreFilters) => void;
  lang?: string;
}

const FlashCreation: React.FC<FlashCreationProps> = ({
  onAccept,
  filters: externalFilters,
  onFiltersChange: externalOnFiltersChange,
  lang = 'en'
}) => {
  const [internalFilters, setInternalFilters] = useState<GenreFilters>({
    gender: '',
    pov: ''
  });

  // Use external filters if provided, otherwise use internal
  const filters = externalFilters || internalFilters;
  const onFiltersChange = externalOnFiltersChange || setInternalFilters;

  const {
    characterName,
    oneClickPrompt,
    flashResult,
    regenerateCount,
    isGenerating,
    setCharacterName,
    setOneClickPrompt,
    handleOneClickGenerate,
    handleRegenerate,
    handleAccept,
  } = useFlashGeneration();

  const handleAcceptResult = () => {
    if (flashResult && onAccept) {
      onAccept(flashResult);
    }
    handleAccept();
  };

  return (
    <FlashGenerator
      name={characterName}
      onNameChange={setCharacterName}
      prompt={oneClickPrompt}
      onPromptChange={setOneClickPrompt}
      result={flashResult}
      regenerateCount={regenerateCount}
      isGenerating={isGenerating}
      onGenerate={handleOneClickGenerate}
      onRegenerate={handleRegenerate}
      onAccept={handleAcceptResult}
      filters={filters}
      onFiltersChange={onFiltersChange}
      lang={lang}
    />
  );
};

export default FlashCreation;
