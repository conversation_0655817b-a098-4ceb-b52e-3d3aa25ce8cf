'use client';

import React, { useState } from 'react';
import { useTranslation } from '@/app/i18n/client';
import {
  Shield,
  Mail,
  Key,
  AlertTriangle,
  CheckCircle,
  Clock,
  Lock,
  Eye,
  Share2,
  <PERSON><PERSON>,
  Search,
  Users,
  MapPin,
  Database
} from 'lucide-react';
import SettingItem from './SettingItem';
import ToggleSwitch from './ToggleSwitch';
import SelectField from './SelectField';
import type { SettingsComponentProps } from '@/types/settings';

const SecurityPrivacySettings: React.FC<SettingsComponentProps> = ({
  settings,
  updateSetting,
  lang,
  user,
  hasUnsavedChanges,
  isPremiumUser
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [showCookieModal, setShowCookieModal] = useState(false);

  // Account Security Section
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [showTwoFactor, setShowTwoFactor] = useState(false);

  // Privacy options
  const profileVisibilityOptions = [
    { value: 'public', label: 'Public' },
    { value: 'followers_only', label: 'Followers Only' },
    { value: 'private', label: 'Private' }
  ];

  const digitalTwinOptions = [
    { value: 'always', label: 'Always Allow' },
    { value: 'friends_only', label: 'Friends Only' },
    { value: 'never', label: 'Never Allow' }
  ];

  const memorySharingOptions = [
    { value: 'public', label: 'Public' },
    { value: 'friends_only', label: 'Friends Only' },
    { value: 'private', label: 'Private' }
  ];

  const dataRetentionOptions = [
    { value: '30_days', label: '30 Days' },
    { value: '90_days', label: '90 Days' },
    { value: '1_year', label: '1 Year' },
    { value: 'indefinite', label: 'Indefinite' }
  ];

  // Helper functions for account verification
  const getVerificationIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
    }
  };

  const getVerificationText = (status: string) => {
    switch (status) {
      case 'verified':
        return 'Verified';
      case 'pending':
        return 'Pending';
      default:
        return 'Not Verified';
    }
  };

  // Cookie preferences handler
  const handleCookiePreferenceChange = (category: keyof typeof settings.privacy.cookiePreferences, value: boolean) => {
    updateSetting(`privacy.cookiePreferences.${category}`, value);
  };

  return (
    <div className="space-y-8">
      {/* Account Security Section */}
      <div>
        <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Shield className="w-5 h-5" />
          Account Security
        </h4>
        
        <div className="space-y-4">
          {/* Email Settings */}
          <SettingItem
            icon={<Mail className="w-5 h-5" />}
            title="Email Address"
            description="Your account email address"
          >
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {user?.email || '<EMAIL>'}
              </span>
              <CheckCircle className="w-4 h-4 text-green-500" />
            </div>
          </SettingItem>

          {/* Password Settings */}
          <SettingItem
            icon={<Key className="w-5 h-5" />}
            title="Password"
            description="Change your account password"
          >
            <button
              onClick={() => setShowChangePassword(!showChangePassword)}
              className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80 transition-colors"
            >
              Change Password
            </button>
          </SettingItem>

          {/* Two-Factor Authentication */}
          <SettingItem
            icon={<Shield className="w-5 h-5" />}
            title="Two-Factor Authentication"
            description="Add an extra layer of security to your account"
          >
            <ToggleSwitch
              checked={settings?.account?.twoFactorEnabled || false}
              onChange={(checked) => updateSetting('account.twoFactorEnabled', checked)}
            />
          </SettingItem>

          {/* Login History */}
          <SettingItem
            icon={<Clock className="w-5 h-5" />}
            title="Login History"
            description="View recent login activity"
          >
            <button className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80 transition-colors">
              View History
            </button>
          </SettingItem>

          {/* Age Verification */}
          <SettingItem
            icon={<Shield className="w-5 h-5" />}
            title="Age Verification"
            description="Verify your age for content access"
          >
            <div className="flex items-center gap-2">
              {getVerificationIcon(settings?.account?.ageVerificationStatus || 'not_verified')}
              <span className="text-sm font-medium">
                {getVerificationText(settings?.account?.ageVerificationStatus || 'not_verified')}
              </span>
              {settings?.account?.ageVerificationStatus === 'not_verified' && (
                <button
                  onClick={() => {
                    // TODO: Implement age verification flow
                    alert('Age verification process coming soon!');
                  }}
                  className="ml-2 px-2 py-1 text-xs bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                >
                  Verify
                </button>
              )}
            </div>
          </SettingItem>

          {/* Linked Accounts */}
          <SettingItem
            icon={<Users className="w-5 h-5" />}
            title="Linked Accounts"
            description="Manage connected social accounts"
          >
            <button
              onClick={() => {
                // TODO: Implement linked accounts management
                alert('Linked accounts management coming soon!');
              }}
              className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80 transition-colors"
            >
              Manage
            </button>
          </SettingItem>
        </div>
      </div>

      {/* Privacy Settings Section */}
      <div>
        <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Lock className="w-5 h-5" />
          Privacy Settings
        </h4>

        <div className="space-y-4">
          {/* Profile Visibility */}
          <SettingItem
            icon={<Eye className="w-5 h-5" />}
            title="Profile Visibility"
            description="Control who can see your profile"
          >
            <SelectField
              value={settings?.privacy?.profileVisibility || 'public'}
              onChange={(value) => updateSetting('privacy.profileVisibility', value)}
              options={profileVisibilityOptions}
            />
          </SettingItem>

          {/* Digital Twin Interaction */}
          <SettingItem
            icon={<Users className="w-5 h-5" />}
            title="Digital Twin Interaction"
            description="Control how others can interact with your digital twin"
          >
            <SelectField
              value={settings?.privacy?.digitalTwinInteraction || 'friends_only'}
              onChange={(value) => updateSetting('privacy.digitalTwinInteraction', value)}
              options={digitalTwinOptions}
            />
          </SettingItem>

          {/* Character Attribution */}
          <SettingItem
            icon={<Users className="w-5 h-5" />}
            title="Character Attribution"
            description="Show your name when you create characters"
          >
            <ToggleSwitch
              checked={settings?.privacy?.characterCreationAttribution || false}
              onChange={(checked) => updateSetting('privacy.characterCreationAttribution', checked)}
            />
          </SettingItem>
        </div>
      </div>

      {/* Data Sharing Section */}
      <div>
        <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Share2 className="w-5 h-5" />
          Data Sharing
        </h4>

        <div className="space-y-4">
          {/* Memory Sharing */}
          <SettingItem
            icon={<Database className="w-5 h-5" />}
            title="Memory Sharing"
            description="Control who can see your memories"
          >
            <SelectField
              value={settings?.privacy?.memorySharing || 'private'}
              onChange={(value) => updateSetting('privacy.memorySharing', value)}
              options={memorySharingOptions}
            />
          </SettingItem>

          {/* Social Media Sharing */}
          <SettingItem
            icon={<Share2 className="w-5 h-5" />}
            title="Social Media Sharing"
            description="Allow sharing content to social media"
          >
            <ToggleSwitch
              checked={settings?.privacy?.socialMediaSharing || false}
              onChange={(checked) => updateSetting('privacy.socialMediaSharing', checked)}
            />
          </SettingItem>

          {/* Share Usage Data */}
          <SettingItem
            icon={<Database className="w-5 h-5" />}
            title="Share Usage Data"
            description="Help improve our service by sharing usage data"
          >
            <ToggleSwitch
              checked={settings?.privacy?.shareUsageData || false}
              onChange={(checked) => updateSetting('privacy.shareUsageData', checked)}
            />
          </SettingItem>
        </div>
      </div>

      {/* Data Collection Section */}
      <div>
        <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Database className="w-5 h-5" />
          Data Collection
        </h4>

        <div className="space-y-4">

          {/* Data Collection */}
          <SettingItem
            icon={<Database className="w-5 h-5" />}
            title="Data Collection"
            description="Allow data collection for service improvement"
          >
            <ToggleSwitch
              checked={settings?.privacy?.dataCollection || false}
              onChange={(checked) => updateSetting('privacy.dataCollection', checked)}
            />
          </SettingItem>

          {/* Analytics */}
          <SettingItem
            icon={<Search className="w-5 h-5" />}
            title="Analytics"
            description="Help improve our service with usage analytics"
          >
            <ToggleSwitch
              checked={settings?.privacy?.analyticsOptIn || false}
              onChange={(checked) => updateSetting('privacy.analyticsOptIn', checked)}
            />
          </SettingItem>
        </div>
      </div>

      {/* External Services Section */}
      <div>
        <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Search className="w-5 h-5" />
          External Services
        </h4>

        <div className="space-y-4">
          {/* Search Indexing */}
          <SettingItem
            icon={<Search className="w-5 h-5" />}
            title="Search Indexing"
            description="Allow search engines to index your public content"
          >
            <ToggleSwitch
              checked={settings?.privacy?.searchIndexing || false}
              onChange={(checked) => updateSetting('privacy.searchIndexing', checked)}
            />
          </SettingItem>

          {/* Location Services */}
          <SettingItem
            icon={<MapPin className="w-5 h-5" />}
            title="Location Services"
            description="Allow location-based features"
          >
            <ToggleSwitch
              checked={settings?.privacy?.locationServices || false}
              onChange={(checked) => updateSetting('privacy.locationServices', checked)}
            />
          </SettingItem>

          {/* Location Tracking */}
          <SettingItem
            icon={<MapPin className="w-5 h-5" />}
            title="Location Tracking"
            description="Allow tracking your location for personalized content"
          >
            <ToggleSwitch
              checked={settings?.privacy?.locationTracking || false}
              onChange={(checked) => updateSetting('privacy.locationTracking', checked)}
            />
          </SettingItem>
        </div>
      </div>

      {/* Data Management Section */}
      <div>
        <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Data Management
        </h4>

        <div className="space-y-4">

          {/* Data Retention */}
          <SettingItem
            icon={<Clock className="w-5 h-5" />}
            title="Data Retention"
            description="How long to keep your data"
          >
            <SelectField
              value={settings?.privacy?.dataRetention || '1_year'}
              onChange={(value) => updateSetting('privacy.dataRetention', value)}
              options={dataRetentionOptions}
            />
          </SettingItem>
        </div>
      </div>

      {/* Cookie Preferences Section */}
      <div>
        <h4 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
          <Cookie className="w-5 h-5" />
          Cookie Preferences
        </h4>

        <div className="space-y-4">
          <SettingItem
            icon={<Cookie className="w-5 h-5" />}
            title="Cookie Settings"
            description="Manage your cookie preferences"
          >
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {Object.values(settings?.privacy?.cookiePreferences || {}).filter(Boolean).length} / 4 enabled
              </span>
              <button
                onClick={() => setShowCookieModal(true)}
                className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80 transition-colors"
              >
                Manage Cookies
              </button>
            </div>
          </SettingItem>

          {/* Cookie Modal */}
          {showCookieModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
              <div className="bg-card rounded-lg p-6 max-w-md w-full space-y-4">
                <h3 className="text-lg font-semibold text-foreground">Cookie Preferences</h3>

                <div className="space-y-4">
                  <SettingItem
                    icon={<Shield className="w-4 h-4" />}
                    title="Essential Cookies"
                    description="Required for basic website functionality"
                  >
                    <ToggleSwitch
                      checked={settings?.privacy?.cookiePreferences?.essential || true}
                      onChange={(checked) => handleCookiePreferenceChange('essential', checked)}
                      disabled={true}
                    />
                  </SettingItem>

                  <SettingItem
                    icon={<Search className="w-4 h-4" />}
                    title="Analytics Cookies"
                    description="Help us understand how you use our service"
                  >
                    <ToggleSwitch
                      checked={settings?.privacy?.cookiePreferences?.analytics || false}
                      onChange={(checked) => handleCookiePreferenceChange('analytics', checked)}
                    />
                  </SettingItem>

                  <SettingItem
                    icon={<Share2 className="w-4 h-4" />}
                    title="Marketing Cookies"
                    description="Used to show you relevant advertisements"
                  >
                    <ToggleSwitch
                      checked={settings?.privacy?.cookiePreferences?.marketing || false}
                      onChange={(checked) => handleCookiePreferenceChange('marketing', checked)}
                    />
                  </SettingItem>

                  <SettingItem
                    icon={<Users className="w-4 h-4" />}
                    title="Personalization Cookies"
                    description="Remember your preferences and settings"
                  >
                    <ToggleSwitch
                      checked={settings?.privacy?.cookiePreferences?.personalization || false}
                      onChange={(checked) => handleCookiePreferenceChange('personalization', checked)}
                    />
                  </SettingItem>
                </div>

                <div className="flex gap-2 justify-end">
                  <button
                    onClick={() => setShowCookieModal(false)}
                    className="px-4 py-2 text-foreground/70 hover:text-foreground transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      setShowCookieModal(false);
                      // TODO: Apply cookie preferences
                    }}
                    className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Save Preferences
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Privacy Summary */}
      <div className="p-4 bg-card/50 border border-border rounded-lg">
        <h5 className="font-medium text-foreground flex items-center gap-2 mb-3">
          <Shield className="w-4 h-4" />
          Privacy Summary
        </h5>

        <div className="space-y-2 text-sm text-foreground/70">
          <p>• Profile visibility: <span className="font-medium">{profileVisibilityOptions.find(opt => opt.value === (settings?.privacy?.profileVisibility || 'public'))?.label}</span></p>
          <p>• Memory sharing: <span className="font-medium">{memorySharingOptions.find(opt => opt.value === (settings?.privacy?.memorySharing || 'private'))?.label}</span></p>
          <p>• Data collection: <span className="font-medium">{settings?.privacy?.dataCollection ? 'Enabled' : 'Disabled'}</span></p>
          <p>• Analytics: <span className="font-medium">{settings?.privacy?.analyticsOptIn ? 'Enabled' : 'Disabled'}</span></p>
        </div>
      </div>

      {/* Account Deletion Warning */}
      <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-destructive mt-0.5" />
          <div>
            <h5 className="font-medium text-destructive mb-1">Account Deletion</h5>
            <p className="text-sm text-muted-foreground mb-3">
              Permanently delete your account and all associated data. This action cannot be undone.
            </p>
            <button
              onClick={() => {
                // TODO: Implement account deletion
                alert('Account deletion will be implemented when backend is ready');
              }}
              className="px-3 py-1 text-sm bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 transition-colors"
            >
              Delete Account
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityPrivacySettings;
