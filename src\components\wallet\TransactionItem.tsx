import React from 'react';
import { ArrowUpRight, ArrowDownLeft, CreditCard, Coins, Gift, ShoppingBag } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface Transaction {
  id: string;
  type: 'income' | 'expense' | 'pending';
  category: 'purchase' | 'deposit' | 'withdrawal' | 'reward' | 'refund';
  amount: number;
  currency: string;
  description: string;
  date: Date;
  status: 'completed' | 'pending' | 'failed' | 'cancelled';
}

interface TransactionItemProps {
  lang: string;
  transaction: Transaction;
  onClick?: (transaction: Transaction) => void;
}

const TransactionItem: React.FC<TransactionItemProps> = ({ 
  lang, 
  transaction, 
  onClick 
}) => {
  const { t } = useTranslation(lang, 'translation');

  const getIcon = () => {
    switch (transaction.category) {
      case 'purchase':
        return <ShoppingBag className="w-5 h-5" />;
      case 'deposit':
        return <ArrowUpRight className="w-5 h-5" />;
      case 'withdrawal':
        return <ArrowDownLeft className="w-5 h-5" />;
      case 'reward':
        return <Gift className="w-5 h-5" />;
      case 'refund':
        return <ArrowUpRight className="w-5 h-5" />;
      default:
        return <CreditCard className="w-5 h-5" />;
    }
  };

  const getIconBgColor = () => {
    switch (transaction.category) {
      case 'purchase':
        return 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400';
      case 'deposit':
        return 'bg-emerald-100 dark:bg-emerald-900 text-emerald-600 dark:text-emerald-400';
      case 'withdrawal':
        return 'bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400';
      case 'reward':
        return 'bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400';
      case 'refund':
        return 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400';
      default:
        return 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400';
    }
  };

  const getAmountColor = () => {
    if (transaction.type === 'income') {
      return 'text-emerald-600 dark:text-emerald-400';
    } else if (transaction.type === 'expense') {
      return 'text-red-600 dark:text-red-400';
    }
    return 'text-gray-600 dark:text-gray-400';
  };

  const getStatusColor = () => {
    switch (transaction.status) {
      case 'completed':
        return 'text-emerald-600 dark:text-emerald-400';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'failed':
        return 'text-red-600 dark:text-red-400';
      case 'cancelled':
        return 'text-gray-500 dark:text-gray-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    const prefix = transaction.type === 'expense' ? '-' : '+';
    return prefix + new Intl.NumberFormat(lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US', {
      style: 'currency',
      currency: currency === 'USD' ? 'USD' : 'USD',
      minimumFractionDigits: 2,
    }).format(Math.abs(amount));
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };

  return (
    <div 
      className="flex items-center justify-between py-3 border-b border-gray-100 dark:border-gray-700 last:border-0 hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors rounded-lg px-2 -mx-2"
      onClick={() => onClick?.(transaction)}
    >
      <div className="flex items-center gap-3">
        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getIconBgColor()}`}>
          {getIcon()}
        </div>
        <div>
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {transaction.description}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {formatDate(transaction.date)}
          </div>
        </div>
      </div>
      
      <div className="text-right">
        <div className={`font-semibold ${getAmountColor()}`}>
          {formatCurrency(transaction.amount, transaction.currency)}
        </div>
        <div className={`text-xs ${getStatusColor()}`}>
          {t(`wallet.status.${transaction.status}`)}
        </div>
      </div>
    </div>
  );
};

export default TransactionItem; 