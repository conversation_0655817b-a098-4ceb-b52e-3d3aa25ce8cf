-- =====================================================
-- Multi-language Story and Scene Data Storage Schema
-- Supporting data_examples complex story structure
-- =====================================================

-- 1. Story Localizations Table
-- Stores multi-language story data (Chinese, Japanese, English)
CREATE TABLE story_localizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    language_code VARCHAR(10) NOT NULL CHECK (language_code IN ('zh-CN', 'ja-JP', 'en-US')),
    
    -- Basic Story Information (localized)
    story_name VARCHAR(200) NOT NULL,
    story_rating VARCHAR(10) DEFAULT 'G',
    
    -- Core World Structure (from data_examples)
    story_core_world JSONB NOT NULL DEFAULT '{}',
    -- Structure: {
    --   core_concept: string,
    --   synopsis: string,
    --   core_constraints: string,
    --   core_goal: string,
    --   character_chronicle: string
    -- }
    
    -- Narrative Style Configuration
    story_narrative_style JSONB NOT NULL DEFAULT '{}',
    -- Structure: {
    --   narrative_structure: string,
    --   information_disclosure: string,
    --   narrative_style: string,
    --   character_arc: string,
    --   plot_pacing: string
    -- }
    
    -- Story Beats (detailed scene progression)
    story_beats JSONB NOT NULL DEFAULT '[]',
    -- Array of: {
    --   scene_index: number,
    --   scene_name: string,
    --   scene_description: string,
    --   scene_start: string,
    --   scene_conflict: string,
    --   scene_turning_point: string,
    --   scene_character_arc: string,
    --   scene_relationship_development: string,
    --   scene_target: string,
    --   scene_ending: string
    -- }
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(story_id, language_code)
);

-- 2. Enhanced Stories Table Extensions
-- Add new columns to existing stories table
ALTER TABLE stories 
ADD COLUMN IF NOT EXISTS story_rating VARCHAR(10) DEFAULT 'G',
ADD COLUMN IF NOT EXISTS scene_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS narrative_structure VARCHAR(50) DEFAULT 'linear',
ADD COLUMN IF NOT EXISTS world_setting JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS character_relationships JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS story_themes TEXT[],
ADD COLUMN IF NOT EXISTS completion_status VARCHAR(20) DEFAULT 'draft',
ADD COLUMN IF NOT EXISTS estimated_duration INTEGER; -- in minutes

-- 3. Scene Hierarchical Information Table
-- Detailed storage for complex scene structures
CREATE TABLE scene_hierarchical_info (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    scene_index INTEGER NOT NULL,
    language_code VARCHAR(10) NOT NULL CHECK (language_code IN ('zh-CN', 'ja-JP', 'en-US')),
    
    -- Scene Basic Info
    scene_name VARCHAR(200) NOT NULL,
    scene_description TEXT,
    
    -- Four-layer Scene Information (from data_examples)
    first_layer_worldview JSONB DEFAULT '{}',
    -- Structure: {
    --   time_period: string,
    --   geographical_location: string,
    --   cultural_context: string,
    --   social_environment: string
    -- }
    
    second_layer_scene JSONB DEFAULT '{}',
    -- Structure: {
    --   physical_setting: string,
    --   atmosphere: string,
    --   sensory_details: string,
    --   environmental_factors: string
    -- }
    
    third_layer_antecedent JSONB DEFAULT '{}',
    -- Structure: {
    --   prior_events: string,
    --   character_state: string,
    --   emotional_context: string,
    --   relationship_dynamics: string
    -- }
    
    fourth_layer_character JSONB DEFAULT '{}',
    -- Structure: {
    --   character_goals: string,
    --   internal_conflicts: string,
    --   emotional_arc: string,
    --   behavioral_patterns: string
    -- }
    
    -- Scene Progression
    scene_start TEXT,
    scene_conflict TEXT,
    scene_turning_point TEXT,
    scene_character_arc TEXT,
    scene_relationship_development TEXT,
    scene_target TEXT,
    scene_ending TEXT,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(story_id, scene_index, language_code)
);

-- 4. Story Progression Tracking Table
-- Track user progress through stories
CREATE TABLE story_progression (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    
    -- Progress Information
    current_scene_index INTEGER DEFAULT 1,
    completed_scenes INTEGER[] DEFAULT '{}',
    total_scenes INTEGER NOT NULL,
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    
    -- User Choices and Paths
    story_choices JSONB DEFAULT '{}',
    relationship_points JSONB DEFAULT '{}',
    character_affinity JSONB DEFAULT '{}',
    
    -- Timing Information
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    total_time_spent INTEGER DEFAULT 0, -- in seconds
    
    -- Status
    status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused')),
    
    UNIQUE(user_id, story_id)
);

-- 5. Story Statistics Materialized View
-- Aggregated stats for story-related Card components
CREATE MATERIALIZED VIEW story_stats AS
SELECT 
    s.id,
    s.title,
    s.character_id,
    s.creator_id,
    COUNT(DISTINCT sp.user_id) as players_count,
    COUNT(DISTINCT sp.user_id) FILTER (WHERE sp.status = 'completed') as completions_count,
    AVG(sp.completion_percentage) as avg_completion_rate,
    AVG(sp.total_time_spent) as avg_play_time,
    COUNT(DISTINCT sr.id) as ratings_count,
    AVG(sr.rating) as avg_rating,
    COUNT(DISTINCT sl.user_id) as likes_count,
    s.created_at,
    s.updated_at
FROM stories s
LEFT JOIN story_progression sp ON s.id = sp.story_id
LEFT JOIN story_ratings sr ON s.id = sr.story_id
LEFT JOIN story_likes sl ON s.id = sl.story_id
WHERE s.is_public = true
GROUP BY s.id, s.title, s.character_id, s.creator_id, s.created_at, s.updated_at;

-- 6. Story Moments Integration Table
-- Link moments to specific story scenes
CREATE TABLE story_moments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    moment_id UUID NOT NULL REFERENCES moments(id) ON DELETE CASCADE,
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    scene_index INTEGER,
    
    -- Moment Context
    story_context TEXT,
    emotional_significance TEXT,
    relationship_milestone VARCHAR(50),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(moment_id, story_id)
);

-- 7. Indexes for Performance Optimization
CREATE INDEX idx_story_localizations_story_lang 
ON story_localizations(story_id, language_code);

CREATE INDEX idx_story_localizations_language 
ON story_localizations(language_code);

CREATE INDEX idx_scene_hierarchical_story_scene 
ON scene_hierarchical_info(story_id, scene_index);

CREATE INDEX idx_scene_hierarchical_language 
ON scene_hierarchical_info(language_code);

CREATE INDEX idx_story_progression_user_story 
ON story_progression(user_id, story_id);

CREATE INDEX idx_story_progression_status 
ON story_progression(status);

CREATE INDEX idx_story_progression_completion 
ON story_progression(completion_percentage);

CREATE INDEX idx_story_moments_story 
ON story_moments(story_id);

CREATE INDEX idx_story_moments_scene 
ON story_moments(story_id, scene_index);

-- GIN indexes for JSONB columns
CREATE INDEX idx_story_localizations_core_world_gin 
ON story_localizations USING GIN (story_core_world);

CREATE INDEX idx_story_localizations_narrative_gin 
ON story_localizations USING GIN (story_narrative_style);

CREATE INDEX idx_story_localizations_beats_gin 
ON story_localizations USING GIN (story_beats);

CREATE INDEX idx_scene_hierarchical_worldview_gin 
ON scene_hierarchical_info USING GIN (first_layer_worldview);

CREATE INDEX idx_scene_hierarchical_scene_gin 
ON scene_hierarchical_info USING GIN (second_layer_scene);

CREATE INDEX idx_story_progression_choices_gin 
ON story_progression USING GIN (story_choices);

-- 8. Triggers for Updated Timestamps
CREATE TRIGGER story_localizations_updated_at 
BEFORE UPDATE ON story_localizations 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER scene_hierarchical_updated_at 
BEFORE UPDATE ON scene_hierarchical_info 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER story_progression_last_accessed 
BEFORE UPDATE ON story_progression 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. Functions for Story Management
CREATE OR REPLACE FUNCTION update_story_progression(
    p_user_id UUID,
    p_story_id UUID,
    p_scene_index INTEGER,
    p_time_spent INTEGER DEFAULT 0
)
RETURNS void AS $$
BEGIN
    INSERT INTO story_progression (user_id, story_id, current_scene_index, total_time_spent, total_scenes)
    VALUES (
        p_user_id, 
        p_story_id, 
        p_scene_index, 
        p_time_spent,
        (SELECT scene_count FROM stories WHERE id = p_story_id)
    )
    ON CONFLICT (user_id, story_id) 
    DO UPDATE SET
        current_scene_index = GREATEST(story_progression.current_scene_index, p_scene_index),
        completed_scenes = array_append(
            story_progression.completed_scenes, 
            p_scene_index
        ),
        total_time_spent = story_progression.total_time_spent + p_time_spent,
        completion_percentage = (
            array_length(array_append(story_progression.completed_scenes, p_scene_index), 1) * 100.0 
            / story_progression.total_scenes
        ),
        last_accessed_at = CURRENT_TIMESTAMP,
        completed_at = CASE 
            WHEN (array_length(array_append(story_progression.completed_scenes, p_scene_index), 1) >= story_progression.total_scenes)
            THEN CURRENT_TIMESTAMP 
            ELSE story_progression.completed_at 
        END,
        status = CASE 
            WHEN (array_length(array_append(story_progression.completed_scenes, p_scene_index), 1) >= story_progression.total_scenes)
            THEN 'completed'
            ELSE 'in_progress'
        END;
END;
$$ LANGUAGE plpgsql;

-- 10. Refresh Function for Materialized View
CREATE OR REPLACE FUNCTION refresh_story_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY story_stats;
END;
$$ LANGUAGE plpgsql;

COMMENT ON TABLE story_localizations IS 'Multi-language story data supporting complex narrative structures';
COMMENT ON TABLE scene_hierarchical_info IS 'Four-layer scene information hierarchy from data_examples';
COMMENT ON TABLE story_progression IS 'User progress tracking through story scenes';
COMMENT ON MATERIALIZED VIEW story_stats IS 'Aggregated story statistics for frontend components';
