'use client';

import React, { useState, useMemo } from 'react';
import { useTranslation } from '@/app/i18n/client';
import MainAppLayout from '@/components/MainAppLayout';
import { BookOpen, Eye, Plus, Search, ChevronDown, X, Tag, Star, Play } from 'lucide-react';
import Link from 'next/link';
import StoryManageRow from '@/components/StoryManageRow';
import { getManagedStories, type ManagedStory } from '@/lib/mock-data';

interface ManageStoriesClientPageProps {
  lang: string;
}

const ManageStoriesClientPage: React.FC<ManageStoriesClientPageProps> = ({ lang }) => {
  const { t: _ } = useTranslation(lang, 'translation');
  const [managedStories] = useState<ManagedStory[]>(getManagedStories());
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'title' | 'plays' | 'likes' | 'rating' | 'updatedAt'>('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filterStatus, setFilterStatus] = useState<'all' | 'published' | 'draft' | 'archived'>('all');
  const [filterDifficulty, setFilterDifficulty] = useState<'all' | 'easy' | 'normal' | 'hard'>('all');
  const [filterFeature, setFilterFeature] = useState<'all' | 'featured' | 'regular'>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  // Filter and sort story data
  const filteredAndSortedStories = useMemo(() => {
    let filtered = managedStories.filter(story => {
      const matchesSearch = story.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           story.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || story.status === filterStatus;
      const matchesDifficulty = filterDifficulty === 'all' || story.difficulty === filterDifficulty;
      const matchesFeature = filterFeature === 'all' ||
                           (filterFeature === 'featured' && story.isFeature) ||
                           (filterFeature === 'regular' && !story.isFeature);
      const matchesTags = selectedTags.length === 0 ||
                         selectedTags.every(tag => story.tags.includes(tag));

      return matchesSearch && matchesStatus && matchesDifficulty && matchesFeature && matchesTags;
    });

    filtered.sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      if (sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [managedStories, searchTerm, sortBy, sortOrder, filterStatus, filterDifficulty, filterFeature, selectedTags]);

  // Calculate overall statistics
  const totalStories = managedStories.length;
  const totalPlays = managedStories.reduce((sum, story) => sum + story.plays, 0);
  const totalLikes = managedStories.reduce((sum, story) => sum + story.likes, 0);

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const addTag = () => {
    if (newTag.trim() && !selectedTags.includes(newTag.trim())) {
      setSelectedTags([...selectedTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setSelectedTags(selectedTags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addTag();
    }
  };

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4">

          {/* Sticky Header - Statistics, Filters, and Table Header */}
          <div className="sticky top-12 lg:top-16 z-10 bg-gray-50 dark:bg-gray-900">
            {/* Statistics Row */}
            <div className="grid grid-cols-3 divide-x divide-gray-200 dark:divide-gray-700 py-2">
              {/* Total Stories */}
              <div className="flex items-center gap-3 px-4 lg:px-6 py-1.5">
                <div className="p-1.5 lg:p-3 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                  <BookOpen className="w-4 h-4 lg:w-6 lg:h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400">
                    <span className="hidden sm:inline">Total </span>Stories
                  </p>
                  <p className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white">{totalStories}</p>
                </div>
              </div>

              {/* Total Plays */}
              <div className="flex items-center gap-3 px-4 lg:px-6 py-1.5">
                <div className="p-1.5 lg:p-3 bg-green-100 dark:bg-green-900/50 rounded-lg">
                  <Play className="w-4 h-4 lg:w-6 lg:h-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400">
                    <span className="hidden sm:inline">Total </span>Plays
                  </p>
                  <p className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white">
                    <span className="sm:hidden">{Math.round(totalPlays / 1000)}k</span>
                    <span className="hidden sm:inline">{totalPlays.toLocaleString()}</span>
                  </p>
                </div>
              </div>

              {/* Total Likes */}
              <div className="flex items-center gap-3 px-4 lg:px-6 py-1.5">
                <div className="p-1.5 lg:p-3 bg-orange-100 dark:bg-orange-900/50 rounded-lg">
                  <Eye className="w-4 h-4 lg:w-6 lg:h-6 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <p className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400">
                    <span className="hidden sm:inline">Total </span>Likes
                  </p>
                  <p className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white">
                    <span className="sm:hidden">{Math.round(totalLikes / 1000)}k</span>
                    <span className="hidden sm:inline">{totalLikes.toLocaleString()}</span>
                  </p>
                </div>
              </div>
            </div>

            {/* Filters Row */}
            <div className="mt-2 space-y-3">
              {/* Search Box with Create New Button */}
              <div className="flex gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                  <input
                    type="text"
                    placeholder="Search stories..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                </div>
                <Link
                  href={`/${lang}/create-story`}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors whitespace-nowrap"
                >
                  <Plus size={16} />
                  Create New
                </Link>
              </div>

              {/* Three Filters in Same Row */}
              <div className="grid grid-cols-3 gap-3">
                {/* Status Filter */}
                <div className="relative">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value as any)}
                    className="appearance-none w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-8 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
                  >
                    <option value="all">By Status</option>
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                    <option value="archived">Archived</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={14} />
                </div>

                {/* Difficulty Filter */}
                <div className="relative">
                  <select
                    value={filterDifficulty}
                    onChange={(e) => setFilterDifficulty(e.target.value as any)}
                    className="appearance-none w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-8 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
                  >
                    <option value="all">By Difficulty</option>
                    <option value="easy">Easy</option>
                    <option value="normal">Normal</option>
                    <option value="hard">Hard</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={14} />
                </div>

                {/* Feature Filter */}
                <div className="relative">
                  <select
                    value={filterFeature}
                    onChange={(e) => setFilterFeature(e.target.value as any)}
                    className="appearance-none w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-8 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
                  >
                    <option value="all">By Feature</option>
                    <option value="featured">Featured</option>
                    <option value="regular">Regular</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={14} />
                </div>
              </div>

              {/* Add Tag Input */}
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                  <input
                    type="text"
                    placeholder="Add tag to filter..."
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyDown={handleKeyPress}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                </div>
                <button
                  onClick={addTag}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                >
                  Add
                </button>
              </div>

              {/* Selected Tags */}
              {selectedTags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {selectedTags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-200 text-sm rounded-full"
                    >
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        className="hover:text-indigo-600 dark:hover:text-indigo-300"
                      >
                        <X size={14} />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>

            {/* Table Header */}
            <div className="bg-white dark:bg-gray-800 rounded-t-lg mt-2 overflow-x-auto">
              <table className="w-full table-fixed">
                <colgroup>
                  {/* Story column - always visible, compact layout on mobile */}
                  <col className="w-[140px] sm:w-[180px] lg:w-[200px]" />
                  {/* Actions column - always visible, compact on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Plays column - visible on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Likes column - visible on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Rating column - visible on mobile */}
                  <col className="w-[50px] sm:w-[70px] lg:w-[100px]" />
                </colgroup>
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    {/* Story column - always visible */}
                    <th
                      className="px-2 sm:px-3 lg:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => handleSort('title')}
                    >
                      <div className="flex items-center gap-1">
                        <BookOpen size={14} />
                        <span className="hidden md:inline">Story</span>
                        {sortBy === 'title' && (
                          <span className="text-indigo-500 text-xs">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>

                    {/* Actions column - always visible */}
                    <th className="px-1 sm:px-2 lg:px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div className="flex items-center justify-center gap-1">
                        <span className="hidden md:inline">Actions</span>
                      </div>
                    </th>

                    {/* Plays column - visible on mobile */}
                    <th
                      className="px-1 sm:px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => handleSort('plays')}
                    >
                      <div className="flex items-center justify-center gap-1">
                        <Play size={14} />
                        <span className="hidden md:inline">Plays</span>
                        {sortBy === 'plays' && (
                          <span className="text-indigo-500 text-xs">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>

                    {/* Likes column - visible on mobile */}
                    <th
                      className="px-1 sm:px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => handleSort('likes')}
                    >
                      <div className="flex items-center justify-center gap-1">
                        <Eye size={14} />
                        <span className="hidden md:inline">Likes</span>
                        {sortBy === 'likes' && (
                          <span className="text-indigo-500 text-xs">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>

                    {/* Rating column - visible on mobile */}
                    <th
                      className="px-1 sm:px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => handleSort('rating')}
                    >
                      <div className="flex items-center justify-center gap-1">
                        <Star size={14} />
                        <span className="hidden md:inline">Rating</span>
                        {sortBy === 'rating' && (
                          <span className="text-indigo-500 text-xs">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>
                  </tr>
                </thead>
              </table>
            </div>
          </div>

          {/* Scrollable Table Body */}
          <div className="bg-white dark:bg-gray-800 rounded-b-lg shadow-sm max-h-[calc(100vh-300px)] overflow-y-auto">
            <div className="overflow-x-auto">
              <table className="w-full table-fixed">
                <colgroup>
                  {/* Story column - always visible, compact layout on mobile */}
                  <col className="w-[140px] sm:w-[180px] lg:w-[200px]" />
                  {/* Actions column - always visible, compact on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Plays column - visible on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Likes column - visible on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Rating column - visible on mobile */}
                  <col className="w-[50px] sm:w-[70px] lg:w-[100px]" />
                </colgroup>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredAndSortedStories.map((story) => (
                    <StoryManageRow
                      key={story.id}
                      lang={lang}
                      story={story}
                    />
                  ))}
                </tbody>
              </table>
            </div>

            {filteredAndSortedStories.length === 0 && managedStories.length > 0 && (
              <div className="text-center py-12">
                <Search className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No matching stories</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            )}

            {managedStories.length === 0 && (
              <div className="text-center py-12">
                <BookOpen className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No stories</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Get started by creating your first story.
                </p>
                <div className="mt-6">
                  <Link
                    href={`/${lang}/create-story`}
                    className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                  >
                    <Plus size={16} />
                    Create Story
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
};

export default ManageStoriesClientPage;
