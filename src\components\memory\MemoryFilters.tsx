import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { ChevronDown, Star, Calendar, Tag, X, Filter, Sparkles } from 'lucide-react';
import type { Memory } from '@/app/[lang]/manage-memories/ManageMemoryClientPage';

interface MemoryFiltersProps {
  characters: Array<{ id: string; name: string; avatar: string }>;
  selectedCharacter: string;
  onCharacterChange: (value: string) => void;
  selectedEmotion: string;
  onEmotionChange: (value: string) => void;
  allTags: string[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  importanceFilter: number;
  onImportanceChange: (value: number) => void;
  dateRange: 'all' | 'today' | 'week' | 'month';
  onDateRangeChange: (value: 'all' | 'today' | 'week' | 'month') => void;
  lang: string;
}

const MemoryFilters: React.FC<MemoryFiltersProps> = ({
  characters,
  selected<PERSON>hara<PERSON>,
  onCharacterChang<PERSON>,
  selectedEmotion,
  onEmotionChange,
  allTags,
  selectedTags,
  onTagsChange,
  importanceFilter,
  onImportanceChange,
  dateRange,
  onDateRangeChange,
  lang,
}) => {
  const { t: _ } = useTranslation(lang, 'translation');

  const emotions = [
    { value: 'all', label: _('memory.filter.allEmotions', 'All Emotions'), emoji: '🌈', gradient: 'from-gray-400 to-gray-500' },
    { value: 'happy', label: _('memory.filter.happy', 'Happy'), emoji: '😊', gradient: 'from-yellow-400 to-orange-400' },
    { value: 'sad', label: _('memory.filter.sad', 'Touched'), emoji: '🥺', gradient: 'from-blue-400 to-indigo-400' },
    { value: 'excited', label: _('memory.filter.excited', 'Excited'), emoji: '🎉', gradient: 'from-pink-400 to-red-400' },
    { value: 'thoughtful', label: _('memory.filter.thoughtful', 'Thoughtful'), emoji: '🤔', gradient: 'from-purple-400 to-indigo-400' },
    { value: 'important', label: _('memory.filter.important', 'Important'), emoji: '⭐', gradient: 'from-amber-400 to-yellow-400' },
  ];

  const dateRanges = [
    { value: 'all', label: _('memory.filter.allTime', 'All Time'), icon: '🕐' },
    { value: 'today', label: _('memory.filter.today', 'Today'), icon: '📅' },
    { value: 'week', label: _('memory.filter.thisWeek', 'This Week'), icon: '📊' },
    { value: 'month', label: _('memory.filter.thisMonth', 'This Month'), icon: '📋' },
  ];

  const handleTagClick = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onTagsChange(selectedTags.filter(t => t !== tag));
    } else {
      onTagsChange([...selectedTags, tag]);
    }
  };

  return (
    <div className="space-y-6">
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl backdrop-blur-sm">
            <Filter className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Smart Filters
          </h3>
          <Sparkles className="w-4 h-4 text-yellow-500 animate-pulse" />
        </div>
        
        {/* Active Filter Count */}
        {(selectedCharacter !== 'all' || selectedEmotion !== 'all' || selectedTags.length > 0 || importanceFilter > 0 || dateRange !== 'all') && (
          <div className="px-3 py-1 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 rounded-full text-sm font-medium text-purple-700 dark:text-purple-300">
            {[selectedCharacter !== 'all', selectedEmotion !== 'all', selectedTags.length > 0, importanceFilter > 0, dateRange !== 'all'].filter(Boolean).length} active filters
          </div>
        )}
      </div>

      {/* First Row: Character, Emotion, Date */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Character Filter */}
        <div className="group relative">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            👥 Select Character
          </label>
          <div className="relative">
            <select
              value={selectedCharacter}
              onChange={(e) => onCharacterChange(e.target.value)}
              className="appearance-none w-full backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 border border-white/50 dark:border-gray-600/50 rounded-xl px-4 py-3 pr-10 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent shadow-lg transition-all duration-300 hover:shadow-xl"
            >
              <option value="all">{_('memory.filter.allCharacters', 'All Characters')}</option>
              {characters.map((character) => (
                <option key={character.id} value={character.id}>
                  {character.name}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none transition-transform group-hover:rotate-180 duration-300" size={18} />
          </div>
        </div>

        {/* Emotion Filter */}
        <div className="group relative">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            😊 Emotion Type
          </label>
          <div className="relative">
            <select
              value={selectedEmotion}
              onChange={(e) => onEmotionChange(e.target.value)}
              className="appearance-none w-full backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 border border-white/50 dark:border-gray-600/50 rounded-xl px-4 py-3 pr-10 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent shadow-lg transition-all duration-300 hover:shadow-xl"
            >
              {emotions.map((emotion) => (
                <option key={emotion.value} value={emotion.value}>
                  {emotion.emoji} {emotion.label}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none transition-transform group-hover:rotate-180 duration-300" size={18} />
          </div>
        </div>

        {/* Date Range Filter */}
        <div className="group relative">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            📅 Time Range
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-purple-500 transition-colors" size={18} />
            <select
              value={dateRange}
              onChange={(e) => onDateRangeChange(e.target.value as any)}
              className="appearance-none w-full backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 border border-white/50 dark:border-gray-600/50 rounded-xl pl-11 pr-10 py-3 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent shadow-lg transition-all duration-300 hover:shadow-xl"
            >
              {dateRanges.map((range) => (
                <option key={range.value} value={range.value}>
                  {range.icon} {range.label}
                </option>
              ))}
            </select>
            <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none transition-transform group-hover:rotate-180 duration-300" size={18} />
          </div>
        </div>
      </div>

      {/* Importance Filter */}
      <div className="backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-500" />
            <label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
              {_('memory.filter.importance', 'Importance Filter')}
            </label>
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400 px-3 py-1 bg-gray-100/50 dark:bg-gray-700/50 rounded-full">
            {importanceFilter === 0 ? _('memory.filter.allImportance', 'All') : `≥ ${importanceFilter} ⭐`}
          </span>
        </div>
        <div className="flex items-center gap-3">
          {[0, 1, 2, 3, 4, 5].map((value) => (
            <button
              key={value}
              onClick={() => onImportanceChange(value)}
              className={`relative flex items-center justify-center w-12 h-12 rounded-2xl transition-all duration-300 ${
                importanceFilter >= value
                  ? 'bg-gradient-to-r from-yellow-400 to-orange-400 text-white shadow-lg transform scale-110'
                  : 'bg-gray-100/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 hover:bg-gray-200/50 dark:hover:bg-gray-600/50 hover:scale-105'
              }`}
            >
              {value === 0 ? (
                <span className="text-lg font-bold">All</span>
              ) : (
                <Star size={20} fill={importanceFilter >= value ? 'currentColor' : 'none'} />
              )}
              {importanceFilter >= value && (
                <div className="absolute inset-0 bg-white/20 rounded-2xl animate-pulse"></div>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Tags Filter */}
      <div className="backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-2xl p-6 border border-white/50 dark:border-gray-700/50 shadow-lg">
        <div className="flex items-center gap-2 mb-4">
          <Tag size={18} className="text-purple-500" />
          <label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
            {_('memory.filter.tags', 'Tag Filter')}
          </label>
          {selectedTags.length > 0 && (
            <span className="px-2 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium">
              {selectedTags.length} selected
            </span>
          )}
        </div>
        <div className="flex flex-wrap gap-2 mb-4">
          {allTags.map((tag) => (
            <button
              key={tag}
              onClick={() => handleTagClick(tag)}
              className={`relative px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                selectedTags.includes(tag)
                  ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                  : 'bg-gray-100/50 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300 hover:bg-gray-200/50 dark:hover:bg-gray-600/50'
              }`}
            >
              #{tag}
              {selectedTags.includes(tag) && (
                <div className="absolute inset-0 bg-white/20 rounded-xl animate-pulse"></div>
              )}
            </button>
          ))}
        </div>
        {selectedTags.length > 0 && (
          <button
            onClick={() => onTagsChange([])}
            className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors group"
          >
            <X size={14} className="group-hover:rotate-90 transition-transform duration-300" />
            {_('memory.filter.clearTags', 'Clear All Tags')}
          </button>
        )}
      </div>

      {/* Quick Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <button 
          onClick={() => {
            onCharacterChange('all');
            onEmotionChange('all');
            onTagsChange([]);
            onImportanceChange(0);
            onDateRangeChange('all');
          }}
          className="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 shadow-lg"
        >
          🔄 Reset Filters
        </button>
        <button className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
          ✨ Smart Suggestions
        </button>
        <button className="px-4 py-2 bg-gradient-to-r from-indigo-500 to-blue-500 hover:from-indigo-600 hover:to-blue-600 text-white rounded-xl font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
          💾 Save Filters
        </button>
      </div>
    </div>
  );
};

export default MemoryFilters; 