'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslation } from '@/app/i18n/client';

interface SidebarJourneyProgressProps {
  lang: string;
}

const SidebarJourneyProgress: React.FC<SidebarJourneyProgressProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');

  return (
    <div className="px-4 py-2">
      {/* Header with title and View Journey button */}
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
          {t('sidebar.todaysJourney')}
        </h3>
        <Link
          href={`/${lang}/journey`}
          className="px-3 py-1 bg-purple-500 hover:bg-purple-600 text-white text-xs font-medium rounded-lg transition-colors shadow-sm"
        >
          {t('sidebar.viewJourney')}
        </Link>
      </div>

      {/* Progress bar without labels */}
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div className="bg-gradient-to-r from-orange-400 to-orange-500 h-2 rounded-full" style={{width: '20%'}}></div>
      </div>
    </div>
  );
};

export default SidebarJourneyProgress;
