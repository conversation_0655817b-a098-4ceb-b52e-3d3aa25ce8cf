'use client';

import React from 'react';
import { TrendingUp, TrendingDown, Users, ShoppingBag, Target, DollarSign, Activity, Clock, Star, Heart } from 'lucide-react';

interface AnalyticsOverviewProps {
  lang: string;
}

interface MetricCard {
  title: string;
  value: string;
  change: number;
  changeType: 'increase' | 'decrease';
  icon: React.ComponentType<any>;
  color: string;
  description: string;
}

const AnalyticsOverview: React.FC<AnalyticsOverviewProps> = ({ lang }) => {
  // Mock analytics data - in real app this would come from analytics service
  const keyMetrics: MetricCard[] = [
    {
      title: 'Total Users',
      value: '12,847',
      change: 12.5,
      changeType: 'increase',
      icon: Users,
      color: 'from-blue-500 to-indigo-600',
      description: 'Active users this month'
    },
    {
      title: 'Revenue',
      value: '$45,231',
      change: 8.2,
      changeType: 'increase',
      icon: DollarSign,
      color: 'from-green-500 to-emerald-600',
      description: 'Total revenue this month'
    },
    {
      title: 'Store Purchases',
      value: '3,456',
      change: 15.3,
      changeType: 'increase',
      icon: ShoppingBag,
      color: 'from-purple-500 to-pink-600',
      description: 'Items purchased this month'
    },
    {
      title: 'Missions Completed',
      value: '8,923',
      change: 23.1,
      changeType: 'increase',
      icon: Target,
      color: 'from-orange-500 to-red-600',
      description: 'Journey missions completed'
    },
    {
      title: 'Avg Session Time',
      value: '24m 32s',
      change: -2.1,
      changeType: 'decrease',
      icon: Clock,
      color: 'from-cyan-500 to-blue-600',
      description: 'Average user session duration'
    },
    {
      title: 'User Satisfaction',
      value: '4.8/5',
      change: 0.3,
      changeType: 'increase',
      icon: Star,
      color: 'from-yellow-500 to-orange-600',
      description: 'Average user rating'
    }
  ];

  const topPerformingContent = [
    { name: 'Vampire Romance Collection', category: 'Character Arts', purchases: 1234, revenue: '$12,340' },
    { name: 'CEO Business Pack', category: 'Character Series', purchases: 987, revenue: '$9,870' },
    { name: 'MetaVerse Pass', category: 'Subscription', purchases: 456, revenue: '$45,600' },
    { name: 'Moonlit Castle Scene', category: 'Story Arts', purchases: 789, revenue: '$7,890' },
    { name: 'Anniversary Memory Pack', category: 'Memory Arts', purchases: 567, revenue: '$5,670' }
  ];

  const userSegments = [
    { segment: 'New Users (0-7 days)', count: 2847, percentage: 22.1, color: 'bg-blue-500' },
    { segment: 'Active Users (8-30 days)', count: 4521, percentage: 35.2, color: 'bg-green-500' },
    { segment: 'Regular Users (31-90 days)', count: 3456, percentage: 26.9, color: 'bg-purple-500' },
    { segment: 'Veteran Users (90+ days)', count: 2023, percentage: 15.8, color: 'bg-orange-500' }
  ];

  return (
    <div className="space-y-8">
      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {keyMetrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${metric.color} rounded-lg flex items-center justify-center`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-sm font-medium ${
                  metric.changeType === 'increase' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200'
                }`}>
                  {metric.changeType === 'increase' ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : (
                    <TrendingDown className="w-3 h-3" />
                  )}
                  {Math.abs(metric.change)}%
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                {metric.value}
              </h3>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                {metric.title}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {metric.description}
              </p>
            </div>
          );
        })}
      </div>

      {/* Charts and Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Top Performing Content */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            Top Performing Content
          </h3>
          <div className="space-y-4">
            {topPerformingContent.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                    {item.name}
                  </h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {item.category}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-gray-100 text-sm">
                    {item.revenue}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {item.purchases} purchases
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* User Segments */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            User Segments
          </h3>
          <div className="space-y-4">
            {userSegments.map((segment, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {segment.segment}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {segment.count.toLocaleString()} ({segment.percentage}%)
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className={`${segment.color} h-2 rounded-full transition-all duration-300`}
                    style={{ width: `${segment.percentage}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="p-4 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg hover:shadow-lg transition-all">
            <Activity className="w-6 h-6 mb-2" />
            <span className="text-sm font-medium">Export Report</span>
          </button>
          <button className="p-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg hover:shadow-lg transition-all">
            <Target className="w-6 h-6 mb-2" />
            <span className="text-sm font-medium">Set Goals</span>
          </button>
          <button className="p-4 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-lg hover:shadow-lg transition-all">
            <Heart className="w-6 h-6 mb-2" />
            <span className="text-sm font-medium">User Feedback</span>
          </button>
          <button className="p-4 bg-gradient-to-r from-orange-500 to-red-600 text-white rounded-lg hover:shadow-lg transition-all">
            <Star className="w-6 h-6 mb-2" />
            <span className="text-sm font-medium">A/B Tests</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsOverview;
