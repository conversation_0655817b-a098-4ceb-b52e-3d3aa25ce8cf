'use client';

import { useState, useCallback } from 'react';
import toast from 'react-hot-toast';
import type { CharacterFormData, ImportedCharacterData } from '@/types/character-creation';

export const useCharacterImport = (
  setFormData: (data: CharacterFormData | ((prev: CharacterFormData) => CharacterFormData)) => void,
  setCreateMode: (mode: 'simple' | 'detailed') => void,
  setCurrentStep: (step: 'basics' | 'personality' | 'advanced') => void,
  setIsAiGenerated: (generated: boolean) => void
) => {
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importedCharacters, setImportedCharacters] = useState<ImportedCharacterData[]>([]);
  const [scriptFile, setScriptFile] = useState<File | null>(null);

  // Handle batch import files
  const handleImportFiles = useCallback((files: FileList) => {
    const jsonFiles = Array.from(files).filter(file => file.name.endsWith('.json'));

    if (jsonFiles.length === 0) {
      toast.error('Please select .json files only');
      return;
    }

    let processedCount = 0;
    let totalCharacters = 0;
    const importedCharacters: ImportedCharacterData[] = [];

    jsonFiles.forEach((file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const jsonData = JSON.parse(content);

          // Check if it's a single character or character array
          let characters = [];
          if (Array.isArray(jsonData)) {
            // Array of multiple characters
            characters = jsonData;
          } else if (jsonData.characters && Array.isArray(jsonData.characters)) {
            // Object containing characters field
            characters = jsonData.characters;
          } else {
            // Single character object
            characters = [jsonData];
          }

          characters.forEach((cardData: any) => {
            const characterData: ImportedCharacterData = {
              // Basic info
              name: cardData.name || `Imported Character ${totalCharacters + 1}`,
              description: cardData.description || cardData.desc || '',
              appearance: cardData.appearance || cardData.physical_description || '', // Appearance description
              setting: cardData.scenario || cardData.world || '', // World setting

              // Image resources
              characterImage: null,
              avatar: null,
              avatarCrop: null,

              // Dialogue settings
              greetingMessage: cardData.first_mes || cardData.greeting || 'Hello! Nice to meet you!',
              personality: cardData.personality ?
                (Array.isArray(cardData.personality) ? cardData.personality :
                 cardData.personality.split(',').map((p: string) => p.trim())) :
                [],

              // Category info
              gender: cardData.gender || 'Unknown',
              faction: 'anime',
              tags: cardData.tags || 'Imported Character',

              // Publishing settings
              visibility: 'public',

              // Advanced settings
              backgroundStory: cardData.background_story || cardData.scenario || '',
              interactionStyleTags: cardData.mes_example || '',
              voiceIds: '',
              initialMemoriesText: cardData.description || '',
              customPromptPrefix: cardData.system_prompt || '',

              // Knowledge files
              knowledgeFiles: [],
            };

            importedCharacters.push(characterData);
            totalCharacters++;
          });

          processedCount++;

          // When all files are processed
          if (processedCount === jsonFiles.length) {
            if (importedCharacters.length === 1) {
              // Only one character, import directly
              setFormData(importedCharacters[0]);
              setCreateMode('detailed');
              setCurrentStep('essentials');
              setIsAiGenerated(false); // Reset AI generation state
              toast.success('Character imported successfully!');
            } else {
              // Multiple characters, show selection interface
              setImportedCharacters(importedCharacters);
              toast.success(`Found ${totalCharacters} characters from ${jsonFiles.length} files. Please select one to import.`);
            }
          }
        } catch (error) {
          console.error('Failed to parse file:', file.name, error);
          toast.error(`Failed to parse ${file.name}. Please check the format.`);
          processedCount++;
        }
      };
      reader.readAsText(file);
    });
  }, [setFormData, setCreateMode, setCurrentStep, setIsAiGenerated]);

  // Handle single import file (for backward compatibility)
  const handleImportFile = useCallback((file: File) => {
    const fileList = new DataTransfer();
    fileList.items.add(file);
    handleImportFiles(fileList.files);
  }, [handleImportFiles]);

  // Handle script file upload
  const handleScriptFileUpload = useCallback((file: File) => {
    if (file.size > 16 * 1024) { // 16KB limit
      toast.error('Script file size must be less than 16KB');
      return;
    }

    setScriptFile(file);
    
    // Read and process script file
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        
        // Simple script parsing - extract character information
        const lines = content.split('\n');
        let characterName = '';
        let characterDescription = '';
        let characterBackground = '';
        
        // Basic parsing logic
        lines.forEach(line => {
          const trimmedLine = line.trim();
          if (trimmedLine.toLowerCase().includes('name:') || trimmedLine.toLowerCase().includes('character:')) {
            characterName = trimmedLine.split(':')[1]?.trim() || '';
          } else if (trimmedLine.toLowerCase().includes('description:') || trimmedLine.toLowerCase().includes('desc:')) {
            characterDescription = trimmedLine.split(':')[1]?.trim() || '';
          } else if (trimmedLine.toLowerCase().includes('background:') || trimmedLine.toLowerCase().includes('story:')) {
            characterBackground = trimmedLine.split(':')[1]?.trim() || '';
          }
        });

        // If no structured data found, use content as description
        if (!characterName && !characterDescription) {
          characterDescription = content.slice(0, 500); // First 500 characters
          characterName = 'Script Character';
        }

        // Update form data with extracted information
        setFormData(prev => ({
          ...prev,
          name: characterName || prev.name,
          description: characterDescription || prev.description,
          backgroundStory: characterBackground || prev.backgroundStory,
        }));

        toast.success('Script file processed successfully!');
      } catch (error) {
        console.error('Failed to process script file:', error);
        toast.error('Failed to process script file');
      }
    };
    reader.readAsText(file);
  }, [setFormData]);

  // Select character from imported list
  const handleSelectCharacter = useCallback((character: ImportedCharacterData) => {
    setFormData(character);
    setCreateMode('detailed');
    setCurrentStep('basics');
    setImportedCharacters([]);
    setIsAiGenerated(false); // Reset AI generation state
    toast.success(`Character "${character.name}" imported successfully!`);
  }, [setFormData, setCreateMode, setCurrentStep, setIsAiGenerated]);

  // Clear import state
  const clearImportState = useCallback(() => {
    setImportFile(null);
    setImportedCharacters([]);
    setScriptFile(null);
  }, []);

  return {
    // State
    importFile,
    importedCharacters,
    scriptFile,
    
    // Actions
    setImportFile,
    setImportedCharacters,
    handleImportFiles,
    handleImportFile,
    handleScriptFileUpload,
    handleSelectCharacter,
    clearImportState,
  };
};
