'use client';

import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Heart } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import PageLayout from '@/components/PageLayout';
import { useTranslation } from '@/app/i18n/client';

export default function PlaceholderPage() {
  const { lang, slug } = useParams() as { lang: string, slug: string };
  const { t } = useTranslation(lang, 'translation');

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-indigo-900/20 theme-transition">
        <PageLayout title="">
          <div className="text-center space-y-8 py-12 relative">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              <div className="absolute top-20 left-10 w-2 h-2 bg-pink-300 rounded-full animate-ping"></div>
              <div className="absolute top-40 right-20 w-1 h-1 bg-purple-300 rounded-full animate-ping delay-1000"></div>
              <div className="absolute bottom-40 left-20 w-1.5 h-1.5 bg-indigo-300 rounded-full animate-ping delay-2000"></div>
              <div className="absolute bottom-20 right-10 w-2 h-2 bg-pink-300 rounded-full animate-ping delay-3000"></div>
            </div>
            {/* Broken Heart Emoji */}
            <div className="flex justify-center mb-8">
              <div className="relative">
                <div className="text-8xl animate-pulse">
                  💔
                </div>
                {/* Floating hearts animation */}
                <div className="absolute -top-4 -left-4 text-2xl animate-bounce delay-100">
                  💙
                </div>
                <div className="absolute -top-2 -right-6 text-xl animate-bounce delay-300">
                  💜
                </div>
                <div className="absolute -bottom-2 left-2 text-lg animate-bounce delay-500">
                  🖤
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="space-y-6">
              <div className="space-y-2">
                <h1 className="text-4xl md:text-6xl font-bold text-foreground theme-transition">
                  Happiness <span className="text-red-500">Lost</span> In the <span className="text-purple-500">Void</span>
                </h1>
                <div className="text-lg text-foreground/70 theme-transition">
                  <p>The page you're looking for seems to have wandered into the digital abyss...</p>
                </div>
              </div>
              
            </div>

            {/* Action Button */}
            <div className="pt-4">
              <Link
                href={`/${lang}`}
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-semibold rounded-lg text-lg transition-all duration-300 theme-transition hover:scale-105 hover:shadow-lg"
              >
                <Heart className="w-5 h-5 mr-2" />
                Discover My Love
              </Link>
            </div>

          </div>
        </PageLayout>
      </div>
    </MainAppLayout>
  );
}
