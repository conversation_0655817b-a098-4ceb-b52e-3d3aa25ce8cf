"use client";

import { useEffect, useState } from "react";

// This component ensures its children are rendered only on the client side after mounting.
// This can help with hydration mismatches for components that rely heavily on client-side state
// or browser-specific APIs for their initial render.
export default function ClientBody({ children }: { children: React.ReactNode }) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    // Returning null (or a loader) prevents children from rendering during server-side rendering
    // or the initial client-side hydration phase if they might cause a mismatch.
    return null;
  }

  // Once mounted on the client, render the children.
  return <>{children}</>;
}
