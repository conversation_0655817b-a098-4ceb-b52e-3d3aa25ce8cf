'use client';

import React from 'react';
import Link from 'next/link';
import { Settings, ChevronRight, SunMoon, Globe } from 'lucide-react';
import { useDropdown } from '@/hooks/useDropdown';
import { useResponsive } from '@/hooks/useResponsive';
import { getNavigationLinks, getUsefulLinks, communityLinks } from '@/constants/navigation';
import ThemeToggle from '@/components/ThemeToggle';

interface UnifiedSettingsDropdownProps {
  lang: string;
}

const UnifiedSettingsDropdown: React.FC<UnifiedSettingsDropdownProps> = ({ lang }) => {
  const { isOpen, toggle, dropdownRef, activeSubmenu, setActiveSubmenu } = useDropdown();
  const { isMobile } = useResponsive();
  
  const navigationLinks = getNavigationLinks(lang);
  const usefulLinks = getUsefulLinks(lang);

  // Responsive positioning and sizing
  const dropdownClasses = isMobile
    ? "absolute top-full right-0 mt-2 w-72 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-[100] animate-fade-in-down"
    : "absolute top-full right-0 mt-2 lg:mt-4 w-60 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-[100] animate-fade-in-down";

  const iconSize = isMobile ? 20 : 18;
  const buttonSize = isMobile ? 24 : 28;

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggle}
        className="w-10 h-10 flex items-center justify-center text-gray-500 hover:text-indigo-600 transition-colors rounded"
        aria-label="Settings"
      >
        <Settings size={buttonSize} />
      </button>

      {isOpen && (
        <div className={dropdownClasses}>
          <div className="p-2">
            {/* Navigation Links */}
            {navigationLinks.map((link) => {
              const Icon = link.icon;
              return (
                <Link
                  key={link.name}
                  href={link.href!}
                  className="flex items-center gap-3 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 relative"
                >
                  <Icon size={iconSize} />
                  <span>{link.name}</span>
                  {link.hasNotification && (
                    <span className="absolute top-2 right-2 block h-2 w-2 rounded-full bg-red-500" />
                  )}
                </Link>
              );
            })}

            <div className="border-t border-gray-100 dark:border-gray-700 my-1"></div>

            {/* Theme Toggle */}
            <div className="flex items-center justify-between w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md">
              <div className="flex items-center gap-3">
                <SunMoon size={iconSize} />
                <span>Theme</span>
              </div>
              <ThemeToggle />
            </div>

            {/* Language Display */}
            <div className="flex items-center justify-between w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md">
              <div className="flex items-center gap-3">
                <Globe size={iconSize} />
                <span>Language</span>
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">English</span>
            </div>

            <div className="border-t border-gray-100 dark:border-gray-700 my-1"></div>

            {/* Community Links with Submenu */}
            <div className="relative">
              <button
                className="flex items-center justify-between w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                onMouseEnter={() => !isMobile && setActiveSubmenu('community')}
                onMouseLeave={() => !isMobile && setActiveSubmenu(null)}
                onClick={() => isMobile && setActiveSubmenu(activeSubmenu === 'community' ? null : 'community')}
              >
                <div className="flex items-center gap-3">
                  <span>👥</span>
                  <span>Community</span>
                </div>
                <ChevronRight size={16} />
              </button>

              {activeSubmenu === 'community' && (
                <div
                  className={`${
                    isMobile
                      ? "mt-1 w-full bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600"
                      : "absolute right-full top-0 mr-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
                  }`}
                  onMouseEnter={() => !isMobile && setActiveSubmenu('community')}
                  onMouseLeave={() => !isMobile && setActiveSubmenu(null)}
                >
                  <div className="p-2">
                    {communityLinks.map(link => (
                      <a
                        key={link.name}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                          <path d={link.iconPath} />
                        </svg>
                        <span>{link.name}</span>
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Useful Links with Submenu */}
            <div className="relative">
              <button
                className="flex items-center justify-between w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                onMouseEnter={() => !isMobile && setActiveSubmenu('links')}
                onMouseLeave={() => !isMobile && setActiveSubmenu(null)}
                onClick={() => isMobile && setActiveSubmenu(activeSubmenu === 'links' ? null : 'links')}
              >
                <div className="flex items-center gap-3">
                  <span>📄</span>
                  <span>Useful Links</span>
                </div>
                <ChevronRight size={16} />
              </button>

              {activeSubmenu === 'links' && (
                <div
                  className={`${
                    isMobile
                      ? "mt-1 w-full bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600"
                      : "absolute right-full top-0 mr-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
                  }`}
                  onMouseEnter={() => !isMobile && setActiveSubmenu('links')}
                  onMouseLeave={() => !isMobile && setActiveSubmenu(null)}
                >
                  <div className="p-2">
                    {usefulLinks.map(link => {
                      const Icon = link.icon;
                      return (
                        <Link
                          key={link.name}
                          href={link.href!}
                          className="flex items-center gap-3 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          <Icon size={iconSize} />
                          <span>{link.name}</span>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UnifiedSettingsDropdown;
