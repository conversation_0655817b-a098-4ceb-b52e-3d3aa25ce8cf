import { createInstance, i18n } from 'i18next'
import { initReactI18next } from 'react-i18next/initReactI18next'
import resourcesToBackend from 'i18next-resources-to-backend'
import { getOptions, defaultNS } from './settings'

export const initTranslations = async (lang: string, ns: string | string[], i18nInstance?: i18n, resources?: any) => {
  i18nInstance = i18nInstance || createInstance()
  i18nInstance
    .use(initReactI18next)
    .use(resourcesToBackend((language: string, namespace: string) => import(`./locales/${language}/${namespace}.json`)))
  
  if (!resources) {
    // Determine the namespace for the current language
    const namespace = Array.isArray(ns) ? ns[0] : ns;
    await i18nInstance.init(getOptions(lang, namespace));
  }

  return {
    i18n: i18nInstance,
    resources: i18nInstance.services.resourceStore.data,
    t: i18nInstance.t
  }
}

export async function useTranslation(lang: string, ns: string | string[] = 'translation', options: any = {}) {
  const { i18n, t } = await initTranslations(lang, ns)
  return {
    t,
    i18n
  }
} 