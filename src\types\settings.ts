// Settings related types for Alphane.ai
export interface UserSettings {
  // Account & Security
  account: {
    email: string;
    twoFactorEnabled: boolean;
    ageVerificationStatus: 'not_verified' | 'pending' | 'verified';
    linkedAccounts: LinkedAccount[];
    loginHistory: LoginAttempt[];
  };
  
  // AI Interaction Preferences
  aiInteraction: {
    responseSpeed: 'fast' | 'standard' | 'detailed';
    emotionalIntensity: 'subtle' | 'moderate' | 'rich';
    memorySuggestions: boolean;
    bondingNotifications: boolean;
    memoryCapacityPriority: 'basic' | 'enhanced' | 'premium';
    preferredModel: 'standard' | 'gemini_2_5_flash' | 'gemini_2_5_pro';
    autoSaveMemories: boolean;
    contextAwareness: 'basic' | 'enhanced' | 'deep';
    personalityAdaptation: boolean;
    voicePreference: 'text_only' | 'voice_enabled' | 'voice_preferred';
    responseLength: 'concise' | 'balanced' | 'detailed';
    creativityLevel: 'conservative' | 'balanced' | 'creative';
  };
  
  // Privacy & Permissions
  privacy: {
    profileVisibility: 'public' | 'followers_only' | 'private';
    memorySharing: 'disabled' | 'anonymous' | 'full';
    digitalTwinInteraction: 'anyone' | 'followers_only' | 'disabled';
    characterCreationAttribution: boolean;
    dataCollection: boolean;
    analyticsOptIn: boolean;
    shareUsageData: boolean;
    allowPersonalization: boolean;
    cookiePreferences: CookiePreferences;
    searchIndexing: boolean;
    socialMediaSharing: boolean;
    locationTracking: boolean;
  };
  
  // Notifications
  notifications: {
    streakReminders: boolean;
    battlePassProgress: boolean;
    newCharacterReleases: boolean;
    followedCharacterUpdates: boolean;
    promotionalOffers: boolean;
    doNotDisturbStart?: string; // "22:00"
    doNotDisturbEnd?: string; // "08:00"
    pushNotifications: boolean;
    emailNotifications: boolean;
    inAppNotifications: boolean;
    weeklyDigest: boolean;
    maintenanceNotifications: boolean;
    friendActivityNotifications: boolean;
    achievementNotifications: boolean;
    memoryMilestones: boolean;
    bondingLevelUps: boolean;
    taskReminders: boolean;
    premiumExpiryReminders: boolean;
  };
  
  // Display & Content
  display: {
    language: 'en' | 'zh' | 'ja';
    theme: 'auto' | 'light' | 'dark';
    fontSize: 'small' | 'medium' | 'large' | 'extra_large';
    chatBackground: string;
    animationLevel: 'none' | 'reduced' | 'standard' | 'rich';
    contentFilter: boolean;
    regionalization: boolean;
    highContrast: boolean;
    reducedMotion: boolean;
    customCssEnabled: boolean;
    chatBubbleStyle: 'rounded' | 'square' | 'minimal';
    messageTimestamps: boolean;
    compactMode: boolean;
    showTypingIndicators: boolean;
  };
  
  // Gamification Settings
  gamification: {
    achievementAnimations: boolean;
    currencyGainNotifications: boolean;
    taskReminderIntensity: 'low' | 'moderate' | 'high';
    memoryArtStyle: 'anime' | 'realistic' | 'abstract' | 'custom';
    streakMotivation: boolean;
    progressCelebrations: boolean;
    competitiveMode: boolean;
    leaderboardVisibility: 'public' | 'friends' | 'private';
    rewardPreferences: RewardPreferences;
    autoClaimRewards: boolean;
    experienceDisplayMode: 'detailed' | 'simplified' | 'minimal';
    badgeDisplayMode: 'all' | 'favorites' | 'recent';
  };
  
  // Data Management
  dataManagement: {
    autoMemoryBackup: boolean;
    cacheSize: number;
    exportDataConsent: boolean;
    dataRetentionPeriod: number;
    autoCleanup: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
    storageOptimization: boolean;
    compressionEnabled: boolean;
    cloudSyncEnabled: boolean;
    localStorageLimit: number;
    downloadHistory: boolean;
    chatHistoryLimit: number;
  };

  // Premium Features
  premium: {
    creatorToolsEnabled: boolean;
    advancedAnalyticsEnabled: boolean;
    prioritySupportEnabled: boolean;
    exclusiveContentAccess: boolean;
    whisperSpaceAccess: boolean;
    unlimitedFastRequests: boolean;
    enhancedMemoryCapacity: boolean;
    customUIThemes: boolean;
    advancedFilters: boolean;
    betaFeatureAccess: boolean;
    aiModelSelection: boolean;
    customPersonalities: boolean;
  };
}

// Supporting interfaces
export interface LinkedAccount {
  id: string;
  provider: 'google' | 'apple' | 'discord' | 'twitter';
  email: string;
  isVerified: boolean;
  linkedAt: string;
  lastUsed: string;
}

export interface LoginAttempt {
  id: string;
  timestamp: string;
  ip: string;
  location?: string;
  device: string;
  success: boolean;
  method: 'password' | 'oauth' | '2fa';
}

export interface CookiePreferences {
  essential: boolean;
  analytics: boolean;
  marketing: boolean;
  personalization: boolean;
}

export interface RewardPreferences {
  autoOpen: boolean;
  showRarity: boolean;
  celebrationStyle: 'minimal' | 'standard' | 'festive';
  sortBy: 'date' | 'rarity' | 'category';
}

export interface CurrencyBalance {
  alphane: number; // 曦光微尘 (Glimmering Dust)
  endora: number;  // 心悦晶石 (Joy Crystal)
  serotile: number; // 忆境拼图 (Memory Puzzle)
  oxytol: number;  // 羁绊之露 (Bond Dew)
}

export interface SubscriptionInfo {
  type: 'free' | 'alphane_pass' | 'diamond_pass';
  isActive: boolean;
  expiresAt?: string;
  nextBillingDate?: string;
  autoRenew: boolean;
  benefits: string[];
}

export interface ExportDataOptions {
  includeChats: boolean;
  includeCharacters: boolean;
  includeMemories: boolean;
  includeAchievements: boolean;
  includeSettings: boolean;
  format: 'json' | 'csv' | 'pdf';
  dateRange: 'all' | 'last_month' | 'last_year';
}

// Settings state management
export interface SettingsState {
  settings: UserSettings;
  loading: boolean;
  saving: boolean;
  error: string | null;
  hasUnsavedChanges: boolean;
  lastSaved: string | null;
}

// Settings context type
export interface SettingsContextType {
  state: SettingsState;
  updateSetting: (path: string, value: any) => void;
  saveSettings: () => Promise<void>;
  resetSettings: () => void;
  exportData: (options: ExportDataOptions) => Promise<void>;
  importSettings: (data: Partial<UserSettings>) => void;
}

// Settings category
export interface SettingsCategory {
  id: string;
  titleKey: string;
  descriptionKey: string;
  icon: React.ReactNode;
  component: React.ComponentType<SettingsComponentProps>;
  isPremium?: boolean;
  badge?: string;
}

// Settings component props
export interface SettingsComponentProps {
  settings: UserSettings;
  updateSetting: (path: string, value: any) => void;
  lang: string;
  user: any;
  hasUnsavedChanges: boolean;
  isPremiumUser: boolean;
  currencies?: CurrencyBalance;
  subscription?: SubscriptionInfo;
}

// Settings item configuration
export interface SettingsItemConfig {
  id: string;
  labelKey: string;
  descriptionKey?: string;
  type: 'toggle' | 'select' | 'slider' | 'input' | 'button' | 'info' | 'range';
  value?: any;
  options?: SettingsOption[];
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
  isPremium?: boolean;
  dangerous?: boolean;
  disabled?: boolean;
  validation?: (value: any) => boolean | string;
  transform?: (value: any) => any;
  onClick?: () => void;
  onChange?: (value: any) => void;
}

// Settings option for dropdowns
export interface SettingsOption {
  value: string | number;
  label: string;
  description?: string;
  disabled?: boolean;
  isPremium?: boolean;
  icon?: React.ReactNode;
}

// Settings section
export interface SettingsSection {
  id: string;
  titleKey: string;
  descriptionKey?: string;
  icon?: React.ReactNode;
  items: SettingsItemConfig[];
  isPremium?: boolean;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

// Settings form validation
export interface SettingsValidation {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>;
}

// Settings import/export
export interface SettingsExport {
  version: string;
  exportedAt: string;
  userId: string;
  settings: UserSettings;
  metadata: {
    platform: string;
    appVersion: string;
    deviceInfo: string;
  };
}

// Settings theme configuration
export interface SettingsTheme {
  id: string;
  name: string;
  description: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    accent: string;
  };
  isPremium?: boolean;
  previewUrl?: string;
} 