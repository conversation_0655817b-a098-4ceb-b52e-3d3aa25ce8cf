'use client';

import { CookiesProvider } from 'react-cookie';
import { AuthProvider } from './AuthProvider';
import { CreatorModeProvider } from '@/contexts/CreatorModeContext';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <CookiesProvider>
      <AuthProvider>
        <CreatorModeProvider>
          {children}
        </CreatorModeProvider>
      </AuthProvider>
    </CookiesProvider>
  );
}