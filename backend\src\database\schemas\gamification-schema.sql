-- =====================================================
-- User Status and Gamification Data Storage Schema
-- Supporting Journey, Trophies, Analytics, and Stats
-- =====================================================

-- 1. Enhanced User Achievements Table Extension
-- Extend existing user_achievements table for TrophyCard support
ALTER TABLE user_achievements 
ADD COLUMN IF NOT EXISTS rarity VARCHAR(20) DEFAULT 'bronze' CHECK (rarity IN ('bronze', 'silver', 'gold', 'platinum', 'diamond')),
ADD COLUMN IF NOT EXISTS category VARCHAR(50),
ADD COLUMN IF NOT EXISTS progress_current INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS progress_total INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS progress_percentage DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS unlock_conditions JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS rewards JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS is_hidden BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS unlock_order INTEGER,
ADD COLUMN IF NOT EXISTS achievement_metadata JSONB DEFAULT '{}';

-- 2. Enhanced User Journey Table Extension  
-- Extend existing user_journey table for multi-track progression
ALTER TABLE user_journey 
ADD COLUMN IF NOT EXISTS journey_type VARCHAR(20) DEFAULT 'main' CHECK (journey_type IN ('main', 'side', 'seasonal', 'special')),
ADD COLUMN IF NOT EXISTS track_type VARCHAR(20) DEFAULT 'free' CHECK (track_type IN ('free', 'pass', 'diamond')),
ADD COLUMN IF NOT EXISTS current_level INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS current_xp INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS level_xp_required INTEGER DEFAULT 100,
ADD COLUMN IF NOT EXISTS total_xp_earned INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS milestones_unlocked INTEGER[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS rewards_claimed JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS journey_metadata JSONB DEFAULT '{}';

-- 3. User Statistics Table
-- Comprehensive user statistics for StatCard and analytics
CREATE TABLE user_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Activity Statistics
    total_login_days INTEGER DEFAULT 0,
    consecutive_login_days INTEGER DEFAULT 0,
    max_consecutive_login_streak INTEGER DEFAULT 0,
    last_login_date DATE,
    
    -- Interaction Statistics
    total_conversations INTEGER DEFAULT 0,
    total_messages_sent INTEGER DEFAULT 0,
    total_messages_received INTEGER DEFAULT 0,
    avg_conversation_length DECIMAL(8,2) DEFAULT 0.00,
    total_conversation_time INTEGER DEFAULT 0, -- in seconds
    
    -- Character and Story Statistics
    characters_created INTEGER DEFAULT 0,
    characters_liked INTEGER DEFAULT 0,
    stories_completed INTEGER DEFAULT 0,
    stories_started INTEGER DEFAULT 0,
    avg_story_completion_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- Social Statistics
    moments_shared INTEGER DEFAULT 0,
    moments_liked INTEGER DEFAULT 0,
    comments_made INTEGER DEFAULT 0,
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    
    -- Achievement Statistics
    achievements_unlocked INTEGER DEFAULT 0,
    total_achievement_points INTEGER DEFAULT 0,
    rare_achievements_count INTEGER DEFAULT 0,
    
    -- Currency and Purchase Statistics
    total_currency_earned JSONB DEFAULT '{}',
    total_currency_spent JSONB DEFAULT '{}',
    total_purchases_made INTEGER DEFAULT 0,
    total_money_spent DECIMAL(10,2) DEFAULT 0.00,
    
    -- Engagement Metrics
    session_count INTEGER DEFAULT 0,
    avg_session_duration INTEGER DEFAULT 0, -- in seconds
    total_time_spent INTEGER DEFAULT 0, -- in seconds
    feature_usage_stats JSONB DEFAULT '{}',
    
    -- Metadata
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id)
);

-- 4. Daily Missions Table
-- Daily task system for engagement
CREATE TABLE daily_missions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Mission Information
    mission_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN (
        'conversation', 'social', 'creation', 'exploration', 'achievement'
    )),
    
    -- Mission Configuration
    mission_type VARCHAR(30) NOT NULL CHECK (mission_type IN (
        'daily', 'weekly', 'monthly', 'seasonal', 'special'
    )),
    
    -- Requirements and Progress
    requirements JSONB NOT NULL DEFAULT '{}',
    -- Structure: {
    --   target_count: number,
    --   specific_actions?: string[],
    --   time_limit?: number, // in hours
    --   conditions?: object
    -- }
    
    -- Rewards
    rewards JSONB NOT NULL DEFAULT '[]',
    -- Same structure as store_products.rewards
    
    -- Difficulty and Rarity
    difficulty VARCHAR(20) DEFAULT 'easy' CHECK (difficulty IN ('easy', 'medium', 'hard', 'expert')),
    rarity VARCHAR(20) DEFAULT 'common' CHECK (rarity IN ('common', 'uncommon', 'rare', 'epic', 'legendary')),
    
    -- Availability
    is_active BOOLEAN DEFAULT true,
    available_from TIME DEFAULT '00:00:00',
    available_until TIME DEFAULT '23:59:59',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. User Daily Mission Progress Table
-- Track user progress on daily missions
CREATE TABLE user_daily_mission_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    daily_mission_id UUID NOT NULL REFERENCES daily_missions(id) ON DELETE CASCADE,
    
    -- Progress Tracking
    current_progress INTEGER DEFAULT 0,
    target_progress INTEGER NOT NULL,
    is_completed BOOLEAN DEFAULT false,
    is_claimed BOOLEAN DEFAULT false,
    
    -- Timing
    assigned_date DATE DEFAULT CURRENT_DATE,
    completed_at TIMESTAMP,
    claimed_at TIMESTAMP,
    expires_at TIMESTAMP,
    
    -- Progress Details
    progress_data JSONB DEFAULT '{}',
    -- Structure: {
    --   actions_completed: string[],
    --   milestones_reached: number[],
    --   bonus_progress?: number
    -- }
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, daily_mission_id, assigned_date)
);

-- 6. Leaderboards Table
-- Ranking system for various metrics
CREATE TABLE leaderboards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Leaderboard Information
    leaderboard_code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    
    -- Configuration
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN (
        'total_conversations', 'achievement_points', 'story_completions',
        'social_interactions', 'consecutive_days', 'currency_earned'
    )),
    
    -- Time Period
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN (
        'daily', 'weekly', 'monthly', 'yearly', 'all_time'
    )),
    
    -- Ranking Configuration
    max_entries INTEGER DEFAULT 100,
    update_frequency INTEGER DEFAULT 3600, -- in seconds
    
    -- Rewards for Top Positions
    position_rewards JSONB DEFAULT '{}',
    -- Structure: {
    --   "1": [rewards],
    --   "2": [rewards],
    --   "3": [rewards],
    --   "top_10": [rewards],
    --   "top_100": [rewards]
    -- }
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    season_start TIMESTAMP,
    season_end TIMESTAMP,
    
    -- Metadata
    last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. User Leaderboard Entries Table
-- Current leaderboard positions
CREATE TABLE user_leaderboard_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    leaderboard_id UUID NOT NULL REFERENCES leaderboards(id) ON DELETE CASCADE,
    
    -- Ranking Information
    current_rank INTEGER NOT NULL,
    previous_rank INTEGER,
    rank_change INTEGER DEFAULT 0,
    
    -- Score and Metrics
    score DECIMAL(15,2) NOT NULL,
    previous_score DECIMAL(15,2),
    score_change DECIMAL(15,2) DEFAULT 0.00,
    
    -- Additional Data
    entry_data JSONB DEFAULT '{}',
    -- Structure: {
    --   breakdown: object, // detailed score breakdown
    --   achievements: string[], // related achievements
    --   bonus_points?: number
    -- }
    
    -- Timing
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    last_score_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, leaderboard_id, period_start)
);

-- 8. Memorial Events Table
-- Anniversary and special event system for MemorialCard
CREATE TABLE memorial_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    character_id UUID REFERENCES characters(id) ON DELETE CASCADE,
    
    -- Event Information
    event_type VARCHAR(30) NOT NULL CHECK (event_type IN (
        'first_meeting', 'perfect_intimacy', 'perfect_storyline', 
        'anniversary', 'milestone', 'special_moment'
    )),
    
    event_name VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Timing
    original_date TIMESTAMP NOT NULL,
    anniversary_date DATE NOT NULL,
    next_anniversary DATE,
    
    -- Rewards and Recognition
    rewards JSONB DEFAULT '[]',
    -- Same structure as store_products.rewards
    
    is_claimed BOOLEAN DEFAULT false,
    claimed_at TIMESTAMP,
    
    -- Event Metadata
    event_data JSONB DEFAULT '{}',
    -- Structure: {
    --   milestone_details?: object,
    --   story_context?: string,
    --   intimacy_level?: number,
    --   special_achievements?: string[]
    -- }
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 9. User Activity Logs Table
-- Detailed user behavior tracking
CREATE TABLE user_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Activity Information
    activity_type VARCHAR(50) NOT NULL,
    activity_category VARCHAR(30) NOT NULL,
    
    -- Context
    target_id UUID, -- ID of related object (character, story, etc.)
    target_type VARCHAR(30), -- Type of related object
    
    -- Activity Data
    activity_data JSONB DEFAULT '{}',
    -- Flexible structure for different activity types
    
    -- Session Information
    session_id UUID,
    ip_address INET,
    user_agent TEXT,
    
    -- Timing
    duration INTEGER, -- in seconds
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Partition the activity logs table by month for performance
CREATE TABLE user_activity_logs_y2024m01 PARTITION OF user_activity_logs
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE user_activity_logs_y2024m02 PARTITION OF user_activity_logs
FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

-- Add more partitions as needed...

-- 10. Indexes for Performance Optimization
CREATE INDEX idx_user_statistics_user ON user_statistics(user_id);
CREATE INDEX idx_user_statistics_login_streak ON user_statistics(consecutive_login_days);
CREATE INDEX idx_user_statistics_achievements ON user_statistics(achievements_unlocked);

CREATE INDEX idx_daily_missions_type ON daily_missions(mission_type);
CREATE INDEX idx_daily_missions_category ON daily_missions(category);
CREATE INDEX idx_daily_missions_active ON daily_missions(is_active) WHERE is_active = true;

CREATE INDEX idx_user_daily_mission_progress_user_date ON user_daily_mission_progress(user_id, assigned_date);
CREATE INDEX idx_user_daily_mission_progress_completed ON user_daily_mission_progress(is_completed, is_claimed);

CREATE INDEX idx_leaderboards_category ON leaderboards(category);
CREATE INDEX idx_leaderboards_period ON leaderboards(period_type);
CREATE INDEX idx_leaderboards_active ON leaderboards(is_active) WHERE is_active = true;

CREATE INDEX idx_user_leaderboard_entries_user ON user_leaderboard_entries(user_id);
CREATE INDEX idx_user_leaderboard_entries_leaderboard_rank ON user_leaderboard_entries(leaderboard_id, current_rank);
CREATE INDEX idx_user_leaderboard_entries_period ON user_leaderboard_entries(period_start, period_end);

CREATE INDEX idx_memorial_events_user ON memorial_events(user_id);
CREATE INDEX idx_memorial_events_character ON memorial_events(character_id);
CREATE INDEX idx_memorial_events_type ON memorial_events(event_type);
CREATE INDEX idx_memorial_events_anniversary ON memorial_events(anniversary_date);

CREATE INDEX idx_user_activity_logs_user_time ON user_activity_logs(user_id, created_at);
CREATE INDEX idx_user_activity_logs_type ON user_activity_logs(activity_type);
CREATE INDEX idx_user_activity_logs_session ON user_activity_logs(session_id);

-- GIN indexes for JSONB columns
CREATE INDEX idx_user_achievements_unlock_conditions_gin ON user_achievements USING GIN (unlock_conditions);
CREATE INDEX idx_user_achievements_metadata_gin ON user_achievements USING GIN (achievement_metadata);
CREATE INDEX idx_user_journey_metadata_gin ON user_journey USING GIN (journey_metadata);
CREATE INDEX idx_user_statistics_feature_usage_gin ON user_statistics USING GIN (feature_usage_stats);
CREATE INDEX idx_daily_missions_requirements_gin ON daily_missions USING GIN (requirements);
CREATE INDEX idx_user_daily_mission_progress_data_gin ON user_daily_mission_progress USING GIN (progress_data);
CREATE INDEX idx_memorial_events_data_gin ON memorial_events USING GIN (event_data);
CREATE INDEX idx_user_activity_logs_data_gin ON user_activity_logs USING GIN (activity_data);

COMMENT ON TABLE user_statistics IS 'Comprehensive user statistics for analytics and StatCard components';
COMMENT ON TABLE daily_missions IS 'Daily task system for user engagement';
COMMENT ON TABLE user_daily_mission_progress IS 'User progress tracking for daily missions';
COMMENT ON TABLE leaderboards IS 'Ranking system configuration for various metrics';
COMMENT ON TABLE user_leaderboard_entries IS 'Current user positions in leaderboards';
COMMENT ON TABLE memorial_events IS 'Anniversary and special event system for MemorialCard';
COMMENT ON TABLE user_activity_logs IS 'Detailed user behavior tracking and analytics';
