-- =====================================================
-- API Specification Enhancements for Missing Endpoints
-- Based on data_examples structure analysis
-- =====================================================

-- This file documents the missing API endpoints that need to be implemented
-- to support the enhanced schema and data_examples functionality

-- Memory System Endpoints
-- =======================

-- GET /characters/{characterId}/memories
-- Description: Retrieve all memories for a specific character-user pair
-- Parameters:
--   - characterId (path): UUID of the character
--   - userId (query): UUID of the user (optional, defaults to current user)
--   - type (query): Filter by memory type (optional)
--   - importance (query): Filter by minimum importance score (optional)
--   - limit (query): Number of memories to return (default: 50)
--   - offset (query): Offset for pagination (default: 0)
-- Response: Array of memory objects with emotional context and metadata

-- POST /characters/{characterId}/memories
-- Description: Create a new memory for a character-user pair
-- Parameters:
--   - characterId (path): UUID of the character
--   - userId (query): UUID of the user (optional, defaults to current user)
-- Request Body: {
--   "memory_type": "general|relationship|preference|event|emotion",
--   "content": "Memory content text",
--   "summary": "Brief summary for quick recall",
--   "emotional_context": {
--     "primary_emotion": "joy",
--     "intensity": 0.8,
--     "associated_emotions": ["excitement"],
--     "emotional_valence": 0.9
--   },
--   "importance_score": 7,
--   "tags": ["important", "happy"],
--   "session_id": "UUID of related chat session",
--   "memory_metadata": {
--     "source": "user_input",
--     "context_type": "conversation",
--     "confidence_score": 0.9
--   }
-- }
-- Response: Created memory object with ID

-- PUT /characters/{characterId}/memories/{memoryId}
-- Description: Update an existing memory
-- Parameters:
--   - characterId (path): UUID of the character
--   - memoryId (path): UUID of the memory
-- Request Body: Partial memory object with fields to update
-- Response: Updated memory object

-- DELETE /characters/{characterId}/memories/{memoryId}
-- Description: Delete a memory
-- Parameters:
--   - characterId (path): UUID of the character
--   - memoryId (path): UUID of the memory
-- Response: Success confirmation

-- Character Relationship Endpoints
-- ===============================

-- GET /characters/{characterId}/relationships
-- Description: Get relationship data for a character-user pair
-- Parameters:
--   - characterId (path): UUID of the character
--   - userId (query): UUID of the user (optional, defaults to current user)
-- Response: Relationship object with all metrics and history

-- POST /characters/{characterId}/relationships/{userId}/interaction
-- Description: Record a new interaction and update relationship metrics
-- Parameters:
--   - characterId (path): UUID of the character
--   - userId (path): UUID of the user
-- Request Body: {
--   "interaction_type": "conversation|gift|shared_experience|conflict",
--   "interaction_quality": 0.8,
--   "session_id": "UUID of related chat session",
--   "emotional_impact": {
--     "primary_emotion": "joy",
--     "intensity": 0.7
--   },
--   "notes": "Description of interaction"
-- }
-- Response: Updated relationship object

-- Chat Management Endpoints
-- ==========================

-- GET /chats/{chatId}/context
-- Description: Get comprehensive context for a chat session
-- Parameters:
--   - chatId (path): UUID of the chat session
-- Response: {
--   "session": chat_session_object,
--   "messages": [message_array],
--   "character_state": character_dynamic_state_object,
--   "relationship_context": relationship_object,
--   "relevant_memories": [memory_array],
--   "story_context": story_progression_object
-- }

-- POST /chats/{chatId}/memories
-- Description: Extract and create memories from chat messages
-- Parameters:
--   - chatId (path): UUID of the chat session
-- Request Body: {
--   "message_ids": ["uuid1", "uuid2"],
--   "memory_extraction_config": {
--     "importance_threshold": 5,
--     "emotional_significance": true,
--     "relationship_relevance": true
--   }
-- }
-- Response: Array of created memories

-- GET /chats/{chatId}/relationship-progress
-- Description: Get relationship progress within a specific chat
-- Parameters:
--   - chatId (path): UUID of the chat session
-- Response: {
--   "initial_relationship_state": relationship_object,
--   "current_relationship_state": relationship_object,
--   "relationship_changes": {
--     "trust_change": 0.15,
--     "intimacy_change": 0.08,
--     "level_change": 3
--   },
--   "key_moments": [
--     {
--       "message_id": "uuid",
--       "timestamp": "ISO timestamp",
--       "impact_description": "Significant emotional sharing",
--       "metrics_change": {"trust": 0.1, "intimacy": 0.05}
--     }
--   ]
-- }

-- POST /characters/{characterId}/chat/start
-- Description: Start a new chat session with context initialization
-- Parameters:
--   - characterId (path): UUID of the character
-- Request Body: {
--   "story_id": "UUID (optional)",
--   "scene_id": "UUID (optional)",
--   "initial_context": {
--     "user_mood": "curious",
--     "conversation_goals": ["learn_about_character", "build_connection"],
--     "preferred_topics": ["hobbies", "experiences"]
--   }
-- }
-- Response: Created chat session with initial context

-- Story Progression Endpoints
-- ==========================

-- GET /stories/{storyId}/progress
-- Description: Get detailed story progression data
-- Parameters:
--   - storyId (path): UUID of the story
--   - userId (query): UUID of the user (optional, defaults to current user)
-- Response: {
--   "progression": story_progression_object,
--   "current_scene": scene_hierarchical_info_object,
--   "completed_scenes": [scene_array],
--   "character_arcs": {
--     "character_id": {
--       "current_position": "description",
--       "growth_metrics": {"emotional": 0.7, "relationship": 0.5},
--       "key_developments": ["milestone1", "milestone2"]
--     }
--   },
--   "upcoming_choices": [choice_array],
--   "relationship_development": relationship_object
-- }

-- POST /stories/{storyId}/scenes/{sceneId}/complete
-- Description: Mark a scene as completed and trigger progression logic
-- Parameters:
--   - storyId (path): UUID of the story
--   - sceneId (path): UUID of the scene
-- Request Body: {
--   "completion_data": {
--     "user_choices": ["choice1", "choice2"],
--     "emotional_outcome": "positive",
--     "time_spent": 300, // seconds
--     "key_moments": ["significant_event1", "significant_event2"]
--   },
--   "character_reactions": {
--     "character_id": {
--       "emotional_response": "joy",
--       "relationship_impact": 0.1,
--       "memory_created": true
--     }
--   }
-- }
-- Response: Updated progression data and next scene information

-- GET /stories/{storyId}/choices
-- Description: Get available choices in current story context
-- Parameters:
--   - storyId (path): UUID of the story
--   - sceneId (query): UUID of current scene (optional)
-- Response: {
--   "available_choices": [
--     {
--       "choice_id": "uuid",
--       "description": "Choice description",
--       "consequences": {
--         "relationship_impact": {"trust": 0.1, "intimacy": 0.05},
--         "story_branch": "branch_name",
--         "character_reactions": ["positive", "curious"]
--       },
--       "requirements": {"minimum_trust": 0.3},
--       "weight": 0.8
--     }
//    ],
//   "context": {
//     "current_situation": "description",
//     "character_expectations": ["expectation1", "expectation2"],
//     "time_pressure": false
//   }
// }

// POST /stories/{storyId}/choices/{choiceId}/make
// Description: Process a user choice and update story state
// Parameters:
//   - storyId (path): UUID of the story
//   - choiceId (path): UUID of the choice
// Request Body: {
//   "choice_context": {
//     "user_emotion": "determined",
//     "reasoning": "explanation of choice",
//     "additional_factors": ["factor1", "factor2"]
//   },
//   "character_id": "UUID of character involved" (optional)
// }
// Response: {
//   "choice_outcome": {
//     "immediate_consequences": ["consequence1", "consequence2"],
//     "relationship_changes": {"trust": 0.15, "intimacy": 0.08},
//     "story_progression": "next_scene_id",
//     "memories_created": ["memory1_id", "memory2_id"]
//   },
//   "character_responses": {
//     "character_id": {
//       "verbal_response": "dialogue",
//       "emotional_response": "surprised",
//       "action_taken": "description"
//     }
//   }
// }

// Character State Management Endpoints
// ==================================

// GET /characters/{characterId}/state
// Description: Get current dynamic state of a character
// Parameters:
//   - characterId (path): UUID of the character
//   - userId (query): UUID of the user (optional, defaults to current user)
//   - sessionId (query): UUID of chat session (optional)
// Response: character_dynamic_state_object

// POST /characters/{characterId}/state/update
// Description: Update character dynamic state
// Parameters:
//   - characterId (path): UUID of the character
//   - userId (query): UUID of the user (optional, defaults to current user)
// Request Body: {
//   "emotional_changes": {
//     "new_emotion": "joy",
//     "intensity": 0.8,
//     "valence": 0.9
//   },
//   "mental_state_changes": {
//     "energy_level": 0.9,
//     "stress_level": 0.2,
//     "focus_level": 0.7
//   },
//   "contextual_updates": {
//     "current_location": "new location",
//     "immediate_goals": ["goal1", "goal2"]
//   },
//   "triggers": ["trigger1", "trigger2"]
// }
// Response: Updated character dynamic state

// Memory Association Endpoints
// ============================

// GET /characters/{characterId}/memory-network
// Description: Get memory association network for a character
// Parameters:
//   - characterId (path): UUID of the character
//   - userId (query): UUID of the user (optional, defaults to current user)
//   - depth (query): Network depth to return (default: 2)
// Response: {
//   "nodes": [
//     {
//       "id": "memory_id",
//       "label": "memory summary",
//       "type": "general",
//       "importance": 8,
//       "emotion": "joy"
//     }
//   ],
//   "edges": [
//     {
//       "source": "memory1_id",
//       "target": "memory2_id",
//       "type": "emotional",
//       "strength": 0.7,
//       "label": "shared emotional context"
//     }
//   ]
// }

// POST /characters/{characterId}/memories/{memoryId}/associate
// Description: Create association between memories
// Parameters:
//   - characterId (path): UUID of the character
//   - memoryId (path): UUID of the source memory
// Request Body: {
//   "target_memory_id": "UUID of target memory",
//   "association_type": "emotional",
//   "association_strength": 0.7,
//   "context": {
//     "relationship_description": "both involve joy and family",
//     "bidirectional": true
//   }
// }
// Response: Created memory association

// Analytics and Insights Endpoints
// ===============================

// GET /characters/{characterId}/insights
// Description: Get AI-generated insights about character relationships and patterns
// Parameters:
//   - characterId (path): UUID of the character
//   - userId (query): UUID of the user (optional, defaults to current user)
//   - insight_types (query): Array of insight types (optional)
// Response: {
//   "personality_insights": {
//     "dominant_traits": ["trait1", "trait2"],
//     "growth_areas": ["area1", "area2"],
//     "communication_patterns": ["pattern1", "pattern2"]
//   },
//   "relationship_insights": {
//     "relationship_health": 0.85,
//     "growth_trajectory": "positive",
//     "key_strengths": ["strength1", "strength2"],
//     "areas_for_improvement": ["area1", "area2"]
//   },
//   "memory_insights": {
//     "memory_theme_analysis": {
//       "dominant_themes": ["theme1", "theme2"],
//       "emotional_patterns": ["pattern1", "pattern2"]
//     },
//     "learning_progress": {
//       "adaptation_rate": 0.7,
//       "knowledge_acquisition": ["topic1", "topic2"]
//     }
//   }
// }

// GET /characters/{characterId}/conversation-analytics
// Description: Get detailed conversation analytics
// Parameters:
//   - characterId (path): UUID of the character
//   - userId (query): UUID of the user (optional, defaults to current user)
//   - time_range (query): Time range for analysis (optional, default: 30d)
// Response: {
//   "conversation_stats": {
//     "total_conversations": 25,
//     "total_messages": 342,
//     "average_conversation_length": 13.7,
//     "total_conversation_time": 485, // minutes
//     "response_time_avg": 2.3 // seconds
//   },
//   "emotional_analysis": {
//     "dominant_emotions": ["joy", "curiosity", "affection"],
//     "emotional_progression": {
//       "initial_trust": 0.3,
//       "current_trust": 0.8,
//       "emotional_depth_increase": 0.6
//     }
//   },
//   "relationship_development": {
//     "trust_growth_rate": 0.15,
//     "intimacy_growth_rate": 0.08,
//     "key_milestones": ["milestone1", "milestone2"]
//   },
//   "conversation_themes": {
//     "frequent_topics": ["topic1", "topic2"],
//     "topic_evolution": {
//       "early_topics": ["surface_topics"],
//       "current_topics": ["deep_topics"]
//     }
//   }
// }

// Batch Operations Endpoints
// =========================

// POST /characters/{characterId}/memories/batch
// Description: Create multiple memories in a single request
// Parameters:
//   - characterId (path): UUID of the character
// Request Body: {
//   "memories": [
//     {
//       "memory_type": "general",
//       "content": "Memory content",
//       "emotional_context": {...},
//       "importance_score": 7
//     }
//   ]
// }
// Response: Array of created memory objects

// POST /characters/{characterId}/state/batch-update
// Description: Batch update multiple character states
// Parameters:
//   - characterId (path): UUID of the character
// Request Body: {
//   "updates": [
//     {
//       "user_id": "UUID",
//       "emotional_changes": {...},
//       "mental_state_changes": {...}
//     }
//   ]
// }
// Response: Array of updated character state objects

// WebSocket Events for Real-time Updates
// =====================================

// These events should be supported via WebSocket connections:

// Memory Events:
// - memory_created: {character_id, user_id, memory_id, memory_type, importance}
// - memory_updated: {character_id, user_id, memory_id, changes}
// - memory_accessed: {character_id, user_id, memory_id, access_count}

// Relationship Events:
// - relationship_updated: {character_id, user_id, changes}
// - relationship_milestone: {character_id, user_id, milestone_type, description}
// - interaction_recorded: {character_id, user_id, interaction_type, quality}

// State Events:
// - character_state_changed: {character_id, user_id, state_changes}
// - emotional_shift: {character_id, user_id, from_emotion, to_emotion, intensity}

// Chat Events:
// - message_sent: {chat_id, message_id, sender_type, emotional_state}
// - chat_context_updated: {chat_id, context_changes}
// - memory_extracted: {chat_id, memory_ids}

// Story Events:
// - scene_completed: {story_id, scene_id, completion_data}
// - choice_made: {story_id, choice_id, outcome}
// - story_progressed: {story_id, progression_data}

// Error Response Format
// ====================

// All endpoints should return errors in this format:
// {
//   "error": {
//     "code": "ERROR_CODE",
//     "message": "Human-readable error message",
//     "details": {
//       "field": "Specific field error",
//       "validation_error": "Validation details"
//     },
//     "timestamp": "ISO timestamp",
//     "request_id": "Unique request identifier"
//   }
// }

// Common Error Codes:
// - NOT_FOUND: Resource not found
// - VALIDATION_ERROR: Request validation failed
// - AUTHORIZATION_ERROR: User not authorized
// - RATE_LIMIT_EXCEEDED: Too many requests
// - INTERNAL_ERROR: Server internal error
// - CONFLICT_ERROR: Resource conflict (e.g., duplicate relationship)