'use client';

import React, { useState, useEffect } from 'react';
import { 
  ShoppingBag, 
  Star, 
  Sparkles, 
  Crown, 
  Heart, 
  Users, 
  Zap, 
  Gift,
  Clock,
  ArrowRight,
  Check,
  Loader2
} from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { FirstTimePurchaseOffer, MockStoreService } from '@/lib/store-api';
import { useFirstTimePurchase } from '@/contexts/StoreContext';
import { useGiftToast } from '@/components/ui/Toast';

interface FirstTimePurchaseSectionProps {
  lang: string;
  onPurchase?: (offerId: string) => void;
}

const FirstTimePurchaseSection: React.FC<FirstTimePurchaseSectionProps> = ({
  lang,
  onPurchase
}) => {
  const { t } = useTranslation(lang, 'translation');
  const { firstTimePurchaseOffers: offers, markFirstPurchaseComplete } = useFirstTimePurchase();
  const { showPurchaseSuccess, showPurchaseError } = useGiftToast();
  const [loading, setLoading] = useState(false); // Loading handled by context
  const [purchasingOffer, setPurchasingOffer] = useState<string | null>(null);

  const handlePurchase = async (offerId: string) => {
    try {
      setPurchasingOffer(offerId);

      // Find the offer to get its name
      const offer = offers.find(o => o.id === offerId);

      // Simulate purchase process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mark first purchase as made using context
      markFirstPurchaseComplete();

      // Call parent callback if provided
      if (onPurchase) {
        onPurchase(offerId);
      }

      // Show success toast
      showPurchaseSuccess(offer?.name || 'Premium Package');

    } catch (error) {
      console.error('Purchase failed:', error);
      showPurchaseError(error instanceof Error ? error.message : 'Please try again.');
    } finally {
      setPurchasingOffer(null);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'starter_pack': return ShoppingBag;
      case 'character_bundle': return Users;
      case 'premium_subscription': return Crown;
      case 'currency_pack': return Sparkles;
      default: return Gift;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'starter_pack': return 'from-green-400 to-emerald-500';
      case 'character_bundle': return 'from-purple-400 to-pink-500';
      case 'premium_subscription': return 'from-yellow-400 to-orange-500';
      case 'currency_pack': return 'from-blue-400 to-indigo-500';
      default: return 'from-gray-400 to-gray-500';
    }
  };

  const getRewardIcon = (rewardType: string) => {
    switch (rewardType) {
      case 'currency': return Sparkles;
      case 'character': return Heart;
      case 'subscription': return Crown;
      case 'feature': return Zap;
      case 'exclusive': return Star;
      default: return Gift;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
              <Loader2 className="w-5 h-5 text-white animate-spin" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              First-Time Purchase Offers
            </h2>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Loading exclusive offers for new customers...
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
              <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-xl mb-4"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded mb-4"></div>
              <div className="space-y-2 mb-6">
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
              </div>
              <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (offers.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <Check className="w-8 h-8 text-white" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Welcome Back!
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Thank you for being a valued customer. Check out our other store sections for more great deals!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <div className="flex items-center gap-3 mb-2">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
            <ShoppingBag className="w-5 h-5 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            First-Time Purchase Offers
          </h2>
          <div className="flex items-center gap-1 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200 px-2 py-1 rounded-full text-sm font-medium">
            <Zap className="w-3 h-3" />
            New Customer Only
          </div>
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          Exclusive discounts and bonuses available only for your first purchase
        </p>
      </div>

      {/* Special Notice */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0">
            <Star className="w-4 h-4 text-white" />
          </div>
          <div>
            <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-1">
              🎉 Welcome to Alphane Premium!
            </h4>
            <p className="text-sm text-purple-600 dark:text-purple-400">
              These exclusive offers are available only for your first purchase. Once you make a purchase, 
              these deals will no longer be available, so choose wisely!
            </p>
          </div>
        </div>
      </div>

      {/* Offers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {offers.map((offer) => {
          const CategoryIcon = getCategoryIcon(offer.category);
          const categoryColor = getCategoryColor(offer.category);
          const isPurchasing = purchasingOffer === offer.id;
          
          return (
            <div
              key={offer.id}
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-200 group relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className={`absolute top-0 right-0 w-20 h-20 bg-gradient-to-br ${categoryColor} opacity-10 rounded-full -translate-y-4 translate-x-4`} />
              
              {/* Discount Badge */}
              <div className="absolute top-4 right-4 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                -{offer.discount_percentage}%
              </div>
              
              {/* Header */}
              <div className="flex items-start justify-between mb-4 relative z-10">
                <div className={`w-12 h-12 bg-gradient-to-br ${categoryColor} rounded-xl flex items-center justify-center shadow-lg`}>
                  <CategoryIcon className="w-6 h-6 text-white" />
                </div>
                {offer.valid_until && (
                  <div className="flex items-center gap-1 bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-200 px-2 py-1 rounded-full text-xs font-medium">
                    <Clock className="w-3 h-3" />
                    Limited Time
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="mb-6 relative z-10">
                <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">
                  {offer.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {offer.description}
                </p>
                
                {/* Price */}
                <div className="flex items-center gap-2 mb-4">
                  <span className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    ${offer.discounted_price}
                  </span>
                  <span className="text-lg text-gray-500 dark:text-gray-400 line-through">
                    ${offer.original_price}
                  </span>
                  <span className="text-sm font-medium text-green-600 dark:text-green-400">
                    Save ${(offer.original_price - offer.discounted_price).toFixed(2)}
                  </span>
                </div>

                {/* Rewards */}
                <div className="space-y-2">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                    What you get:
                  </h4>
                  {offer.rewards.slice(0, 3).map((reward, index) => {
                    const RewardIcon = getRewardIcon(reward.type);
                    return (
                      <div key={index} className="flex items-center gap-3">
                        <div className="w-5 h-5 bg-gradient-to-br from-blue-400 to-indigo-500 rounded flex items-center justify-center">
                          <RewardIcon className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {reward.name}
                        </span>
                      </div>
                    );
                  })}
                  {offer.rewards.length > 3 && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 ml-8">
                      +{offer.rewards.length - 3} more rewards
                    </div>
                  )}
                </div>
              </div>

              {/* Action Button */}
              <button
                onClick={() => handlePurchase(offer.id)}
                disabled={isPurchasing}
                className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center gap-2 relative z-10 ${
                  isPurchasing
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                    : `bg-gradient-to-r ${categoryColor} text-white hover:shadow-lg hover:shadow-current/30 group-hover:scale-105`
                }`}
              >
                {isPurchasing ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <ShoppingBag className="w-4 h-4" />
                    Purchase Now
                    <ArrowRight className="w-4 h-4" />
                  </>
                )}
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default FirstTimePurchaseSection;
