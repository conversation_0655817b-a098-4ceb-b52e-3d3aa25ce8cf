'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from '@/app/i18n/client';
import MainAppLayout from '@/components/MainAppLayout';
import MemoryStats from '@/components/memory/MemoryStats';
import MemorySearchBar from '@/components/memory/MemorySearchBar';
import MemoryFilters from '@/components/memory/MemoryFilters';
import MemoryCard from '@/components/memory/MemoryCard';
import MemoryDetailModal from '@/components/memory/MemoryDetailModal';
import MemoryEmptyState from '@/components/memory/MemoryEmptyState';

interface ManageMemoryClientPageProps {
  lang: string;
}

// Mock data interface based on the database schema
export interface Memory {
  memory_id_pk: string;
  creator_user_id: string;
  source_character_id: string;
  source_character: {
    id: string;
    name: string;
    avatar: string;
  };
  source_story_id: string;
  source_session_id: string;
  capsule_name: string;
  dialogue_context: string;
  summary: string;
  tags: string[];
  emotion_marker?: 'happy' | 'sad' | 'excited' | 'thoughtful' | 'important';
  importance_score: number;
  created_at: string;
  updated_at: string;
  reference_count?: number;
  artwork_url?: string;
}

// Mock data generator
const generateMockMemories = (capsuleNames: string[], dialogueTopics: string[], summaryTemplates: string[], tagGroups: string[][]): Memory[] => {
  const emotions = ['happy', 'sad', 'excited', 'thoughtful', 'important'] as const;
  const characters = [
    { id: '1', name: 'Luna', avatar: 'https://api.dicebear.com/7.x/adventurer/svg?seed=Luna' },
    { id: '2', name: 'Aria', avatar: 'https://api.dicebear.com/7.x/adventurer/svg?seed=Aria' },
    { id: '3', name: 'Kai', avatar: 'https://api.dicebear.com/7.x/adventurer/svg?seed=Kai' },
  ];
  
  return Array.from({ length: 15 }, (_, i) => ({
    memory_id_pk: `memory-${i + 1}`,
    creator_user_id: 'user-1',
    source_character_id: characters[i % 3].id,
    source_character: characters[i % 3],
    source_story_id: `story-${i + 1}`,
    source_session_id: `session-${i + 1}`,
    capsule_name: capsuleNames[i] || `Memory ${i + 1}`,
    dialogue_context: `This is a conversation about ${dialogueTopics[i % 5]}...`,
    summary: `In this conversation, we discussed ${summaryTemplates[i % 5]}, and the AI companion provided warm responses and deep understanding.`,
    tags: tagGroups[i % 5] || ['general', 'conversation'],
    emotion_marker: emotions[i % emotions.length],
    importance_score: 3 + (i % 3),
    created_at: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
    updated_at: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
    reference_count: Math.floor(Math.random() * 20),
    artwork_url: i % 3 === 0 ? `https://picsum.photos/400/300?random=${i}` : undefined,
  }));
};

const ManageMemoryClientPage: React.FC<ManageMemoryClientPageProps> = ({ lang }) => {
  const { t: _ } = useTranslation(lang, 'translation');
  
  // Get translated mock data
  const capsuleNames = _('memory.mockData.capsuleNames', { returnObjects: true }) as string[];
  const dialogueTopics = _('memory.mockData.dialogueTopics', { returnObjects: true }) as string[];
  const summaryTemplates = _('memory.mockData.summaryTemplates', { returnObjects: true }) as string[];
  const tagGroups = _('memory.mockData.tagGroups', { returnObjects: true }) as string[][];
  
  const [memories] = useState<Memory[]>(generateMockMemories(capsuleNames, dialogueTopics, summaryTemplates, tagGroups));
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCharacter, setSelectedCharacter] = useState<string>('all');
  const [selectedEmotion, setSelectedEmotion] = useState<string>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [importanceFilter, setImportanceFilter] = useState<number>(0);
  const [dateRange, setDateRange] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [selectedMemory, setSelectedMemory] = useState<Memory | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'timeline'>('grid');

  // Extract unique characters and tags for filters
  const uniqueCharacters = useMemo(() => {
    const charactersMap = new Map();
    memories.forEach(memory => {
      if (!charactersMap.has(memory.source_character.id)) {
        charactersMap.set(memory.source_character.id, memory.source_character);
      }
    });
    return Array.from(charactersMap.values());
  }, [memories]);

  const allTags = useMemo(() => {
    const tagsSet = new Set<string>();
    memories.forEach(memory => {
      memory.tags.forEach(tag => tagsSet.add(tag));
    });
    return Array.from(tagsSet);
  }, [memories]);

  // Filter memories based on all criteria
  const filteredMemories = useMemo(() => {
    return memories.filter(memory => {
      // Search query filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = 
          memory.capsule_name.toLowerCase().includes(query) ||
          memory.summary.toLowerCase().includes(query) ||
          memory.dialogue_context.toLowerCase().includes(query) ||
          memory.tags.some(tag => tag.toLowerCase().includes(query));
        if (!matchesSearch) return false;
      }

      // Character filter
      if (selectedCharacter !== 'all' && memory.source_character.id !== selectedCharacter) {
        return false;
      }

      // Emotion filter
      if (selectedEmotion !== 'all' && memory.emotion_marker !== selectedEmotion) {
        return false;
      }

      // Tags filter
      if (selectedTags.length > 0 && !selectedTags.some(tag => memory.tags.includes(tag))) {
        return false;
      }

      // Importance filter
      if (importanceFilter > 0 && memory.importance_score < importanceFilter) {
        return false;
      }

      // Date range filter
      if (dateRange !== 'all') {
        const memoryDate = new Date(memory.created_at);
        const now = new Date();
        const dayMs = 24 * 60 * 60 * 1000;
        
        switch (dateRange) {
          case 'today':
            if (now.getTime() - memoryDate.getTime() > dayMs) return false;
            break;
          case 'week':
            if (now.getTime() - memoryDate.getTime() > 7 * dayMs) return false;
            break;
          case 'month':
            if (now.getTime() - memoryDate.getTime() > 30 * dayMs) return false;
            break;
        }
      }

      return true;
    });
  }, [memories, searchQuery, selectedCharacter, selectedEmotion, selectedTags, importanceFilter, dateRange]);

  const handleMemoryClick = useCallback((memory: Memory) => {
    setSelectedMemory(memory);
  }, []);

  const handleUpdateMemory = useCallback((updatedMemory: Memory) => {
    // TODO: Implement API call to update memory
    console.log('Update memory:', updatedMemory);
    setSelectedMemory(null);
  }, []);

  const handleDeleteMemory = useCallback((memoryId: string) => {
    // TODO: Implement API call to delete memory
    console.log('Delete memory:', memoryId);
    setSelectedMemory(null);
  }, []);

  return (
    <MainAppLayout lang={lang}>
      {/* Enhanced Background with Improved Visual Effects */}
      <div className="min-h-screen relative overflow-hidden">
        {/* Simplified Background */}
        <div className="fixed inset-0 -z-10">
          {/* Clean gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-purple-50/50 dark:from-gray-950 dark:to-purple-950/30"></div>
          
          {/* Minimal decorative elements */}
          <div className="absolute inset-0 opacity-40">
            <div className="absolute top-20 -left-10 w-40 h-40 bg-purple-200 rounded-full mix-blend-multiply filter blur-2xl opacity-60"></div>
            <div className="absolute bottom-20 -right-10 w-40 h-40 bg-pink-200 rounded-full mix-blend-multiply filter blur-2xl opacity-60"></div>
          </div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 py-8">
          {/* Compact Hero Section */}
          <div className="text-center mb-12 relative">
            {/* Main Content */}
            <div className="relative z-10">
              {/* Clean Typography */}
              <div className="space-y-4">
                <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                  <span className="bg-gradient-to-r from-purple-600 via-pink-600 to-rose-600 bg-clip-text text-transparent">
                    {_('memory.title', 'Memory Capsules')}
                  </span>
                </h1>
                
                <div className="max-w-2xl mx-auto">
                  <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
                    {_('memory.subtitle', 'Treasure beautiful moments with your AI companions, making every important memory last forever')}
                  </p>
                </div>

                {/* Compact Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mt-6">
                  <button className="group relative px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 overflow-hidden">
                    <span className="relative z-10 flex items-center gap-2">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-white">
                        <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                      </svg>
                      {_('memory.action.createFirst', 'Create First Memory')}
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-pink-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </button>
                  
                  <button className="group px-6 py-3 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-700 dark:text-gray-300 font-medium rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-white dark:hover:bg-gray-800 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <span className="flex items-center gap-2">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-gray-500">
                        <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19Z" fill="currentColor"/>
                        <path d="M7 7H17V9H7V7ZM7 11H17V13H7V11ZM7 15H14V17H7V15Z" fill="currentColor"/>
                      </svg>
                      {_('memory.action.learnMore', 'Learn More')}
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Compact Stats Container */}
          <div className="relative mb-6">
            <div className="backdrop-blur-lg bg-white/90 dark:bg-gray-900/90 rounded-2xl p-6 shadow-xl border border-gray-200/50 dark:border-gray-700/50">
              <MemoryStats memories={filteredMemories} lang={lang} />
            </div>
          </div>

          {/* Compact Search and Filters */}
          <div className="relative mb-6">
            <div className="backdrop-blur-lg bg-white/90 dark:bg-gray-900/90 rounded-2xl p-6 shadow-xl border border-gray-200/50 dark:border-gray-700/50">
              <div className="space-y-4">
                <MemorySearchBar 
                  value={searchQuery}
                  onChange={setSearchQuery}
                  viewMode={viewMode}
                  onViewModeChange={setViewMode}
                  lang={lang}
                />
                
                <MemoryFilters
                  characters={uniqueCharacters}
                  selectedCharacter={selectedCharacter}
                  onCharacterChange={setSelectedCharacter}
                  selectedEmotion={selectedEmotion}
                  onEmotionChange={setSelectedEmotion}
                  allTags={allTags}
                  selectedTags={selectedTags}
                  onTagsChange={setSelectedTags}
                  importanceFilter={importanceFilter}
                  onImportanceChange={setImportanceFilter}
                  dateRange={dateRange}
                  onDateRangeChange={setDateRange}
                  lang={lang}
                />
              </div>
            </div>
          </div>

          {/* Memory Cards with Enhanced Container */}
          {filteredMemories.length > 0 ? (
            <div className="mt-8">
              {viewMode === 'grid' ? (
                <div className="columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6">
                  {filteredMemories.map((memory, index) => (
                    <div 
                      key={memory.memory_id_pk}
                      className="break-inside-avoid mb-6 animate-fadeInUp"
                      style={{
                        animationDelay: `${index * 100}ms`,
                      }}
                    >
                      <MemoryCard
                        memory={memory}
                        onClick={handleMemoryClick}
                        lang={lang}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                // Enhanced Timeline view
                <div className="relative max-w-4xl mx-auto">
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-purple-400 via-pink-400 to-rose-400 rounded-full shadow-2xl"></div>
                  <div className="space-y-12">
                    {filteredMemories.map((memory, index) => (
                      <div
                        key={memory.memory_id_pk}
                        className={`flex items-center animate-fadeInUp ${
                          index % 2 === 0 ? 'justify-start' : 'justify-end'
                        }`}
                        style={{
                          animationDelay: `${index * 150}ms`,
                        }}
                      >
                        <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8' : 'pl-8'}`}>
                          <MemoryCard
                            memory={memory}
                            onClick={handleMemoryClick}
                            lang={lang}
                          />
                        </div>
                        {/* Enhanced Timeline Dot */}
                        <div className="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full shadow-xl border-4 border-white dark:border-gray-800 z-10">
                          <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-ping opacity-75"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <MemoryEmptyState 
              hasFilters={searchQuery !== '' || selectedCharacter !== 'all' || selectedEmotion !== 'all' || selectedTags.length > 0}
              lang={lang} 
            />
          )}

          {/* Memory Detail Modal */}
          {selectedMemory && (
            <MemoryDetailModal
              memory={selectedMemory}
              onClose={() => setSelectedMemory(null)}
              onUpdate={handleUpdateMemory}
              onDelete={handleDeleteMemory}
              lang={lang}
            />
          )}
        </div>
      </div>

      {/* Minimal CSS Animations */}
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fadeInUp {
          animation: fadeInUp 0.6s ease-out forwards;
        }
      `}</style>
    </MainAppLayout>
  );
};

export default ManageMemoryClientPage; 