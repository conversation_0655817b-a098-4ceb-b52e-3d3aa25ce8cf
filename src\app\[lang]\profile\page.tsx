import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import ProfileRedirectPage from './ProfileRedirectPage';
import MainAppLayout from '@/components/MainAppLayout';

export default async function ProfilePage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;

  return (
    <AuthGuard requireAuth={true}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <MainAppLayout lang={lang}>
          <ProfileRedirectPage lang={lang} />
        </MainAppLayout>
      </Suspense>
    </AuthGuard>
  );
}