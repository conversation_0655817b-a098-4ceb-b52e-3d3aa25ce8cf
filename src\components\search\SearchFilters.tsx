'use client';

import { useTranslation } from '@/app/i18n/client';

export interface SearchFilters {
  sortBy: 'relevance' | 'newest' | 'oldest' | 'popular' | 'trending';
  category: string;
  tags: string[];
  dateRange: string;
  verified: boolean;
  premium: boolean;
}

interface SearchFiltersProps {
  lang: string;
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onResetFilters: () => void;
}

const SearchFiltersComponent: React.FC<SearchFiltersProps> = ({
  lang,
  filters,
  onFiltersChange,
  onResetFilters
}) => {
  const { t } = useTranslation(lang, 'translation');

  const sortOptions = [
    { value: 'relevance', label: t('search.filters.relevance') },
    { value: 'newest', label: t('search.filters.newest') },
    { value: 'oldest', label: t('search.filters.oldest') },
    { value: 'popular', label: t('search.filters.popular') },
    { value: 'trending', label: t('search.filters.trending') }
  ];

  const categories = [
    { value: 'all', label: t('search.categories.all') },
    { value: 'romance', label: t('search.categories.romance') },
    { value: 'adventure', label: t('search.categories.adventure') },
    { value: 'fantasy', label: t('search.categories.fantasy') },
    { value: 'scifi', label: t('search.categories.scifi') },
    { value: 'mystery', label: t('search.categories.mystery') },
    { value: 'slice_of_life', label: t('search.categories.slice_of_life') }
  ];

  const dateRanges = [
    { value: 'all', label: t('search.filters.all') },
    { value: 'today', label: t('search.filters.today') },
    { value: 'thisWeek', label: t('search.filters.thisWeek') },
    { value: 'thisMonth', label: t('search.filters.thisMonth') },
    { value: 'lastMonth', label: t('search.filters.lastMonth') },
    { value: 'thisYear', label: t('search.filters.thisYear') }
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
      <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
        {t('search.advancedFilters')}
      </h3>
      
      {/* Sort By */}
      <div className="mb-4">
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('search.sortBy')}
        </label>
        <select
          value={filters.sortBy}
          onChange={(e) => onFiltersChange({
            ...filters,
            sortBy: e.target.value as SearchFilters['sortBy']
          })}
          className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {sortOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Category */}
      <div className="mb-4">
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('search.filters.category')}
        </label>
        <select
          value={filters.category}
          onChange={(e) => onFiltersChange({
            ...filters,
            category: e.target.value
          })}
          className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {categories.map(category => (
            <option key={category.value} value={category.value}>
              {category.label}
            </option>
          ))}
        </select>
      </div>

      {/* Date Range */}
      <div className="mb-4">
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('search.filters.dateRange')}
        </label>
        <select
          value={filters.dateRange}
          onChange={(e) => onFiltersChange({
            ...filters,
            dateRange: e.target.value
          })}
          className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {dateRanges.map(range => (
            <option key={range.value} value={range.value}>
              {range.label}
            </option>
          ))}
        </select>
      </div>

      {/* Checkboxes */}
      <div className="space-y-3 mb-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={filters.verified}
            onChange={(e) => onFiltersChange({
              ...filters,
              verified: e.target.checked
            })}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
            {t('search.filters.verified')}
          </span>
        </label>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={filters.premium}
            onChange={(e) => onFiltersChange({
              ...filters,
              premium: e.target.checked
            })}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
            {t('search.filters.premium')}
          </span>
        </label>
      </div>

      {/* Reset Filters */}
      <button
        onClick={onResetFilters}
        className="w-full px-4 py-2 text-sm text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
      >
        {t('search.resetFilters')}
      </button>
    </div>
  );
};

export default SearchFiltersComponent; 