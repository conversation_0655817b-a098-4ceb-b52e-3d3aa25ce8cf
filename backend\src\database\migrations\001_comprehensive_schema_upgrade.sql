-- =====================================================
-- Comprehensive PostgreSQL Schema Upgrade Migration
-- Version: 2.0.0
-- Description: Complete schema upgrade for multi-language support,
--              enhanced gamification, store system, and social features
-- =====================================================

-- Migration Metadata
INSERT INTO schema_migrations (version, description, applied_at) 
VALUES ('2.0.0', 'Comprehensive schema upgrade for frontend Card components support', CURRENT_TIMESTAMP)
ON CONFLICT (version) DO NOTHING;

-- =====================================================
-- PHASE 1: CORE EXTENSIONS AND FUNCTIONS
-- =====================================================

-- Ensure required extensions are available
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Create or update the timestamp trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- PHASE 2: CHARACTER SYSTEM ENHANCEMENTS
-- =====================================================

-- Extend existing characters table
DO $$ 
BEGIN
    -- Add new columns to characters table if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'mbti_type') THEN
        ALTER TABLE characters ADD COLUMN mbti_type VARCHAR(4) CHECK (mbti_type ~ '^[IE][NS][FT][JP]$');
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'pov') THEN
        ALTER TABLE characters ADD COLUMN pov VARCHAR(20) DEFAULT 'neutral';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'era') THEN
        ALTER TABLE characters ADD COLUMN era VARCHAR(50);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'region') THEN
        ALTER TABLE characters ADD COLUMN region VARCHAR(100);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'abo_ratio') THEN
        ALTER TABLE characters ADD COLUMN abo_ratio JSONB DEFAULT '{"alpha": 0.33, "beta": 0.34, "omega": 0.33}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'detailed_personality') THEN
        ALTER TABLE characters ADD COLUMN detailed_personality JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'cognitive_model') THEN
        ALTER TABLE characters ADD COLUMN cognitive_model JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'appearance_details') THEN
        ALTER TABLE characters ADD COLUMN appearance_details JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'behavioral_patterns') THEN
        ALTER TABLE characters ADD COLUMN behavioral_patterns JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'characters' AND column_name = 'voice_characteristics') THEN
        ALTER TABLE characters ADD COLUMN voice_characteristics JSONB DEFAULT '{}';
    END IF;
END $$;

-- Create character_localizations table
CREATE TABLE IF NOT EXISTS character_localizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    language_code VARCHAR(10) NOT NULL CHECK (language_code IN ('zh-CN', 'ja-JP', 'en-US')),
    
    -- Basic Information (localized)
    name JSONB NOT NULL DEFAULT '{}',
    background TEXT,
    personality TEXT,
    
    -- Hierarchical Appearance Info
    appear_hierarchical_info JSONB NOT NULL DEFAULT '{}',
    ext_hierarchical_info JSONB NOT NULL DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(character_id, language_code)
);

-- Create character_appearance_details table
CREATE TABLE IF NOT EXISTS character_appearance_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    
    -- Core Physical Identity
    physique JSONB DEFAULT '{}',
    stature JSONB DEFAULT '{}',
    body_ratio JSONB DEFAULT '{}',
    face JSONB DEFAULT '{}',
    hair JSONB DEFAULT '{}',
    eyes JSONB DEFAULT '{}',
    base_species JSONB DEFAULT '{}',
    
    -- Presentation Style
    wardrobe JSONB DEFAULT '{}',
    garments JSONB DEFAULT '{}',
    footwear JSONB DEFAULT '{}',
    adornments JSONB DEFAULT '{}',
    gear JSONB DEFAULT '{}',
    handheld_items JSONB DEFAULT '{}',
    
    -- Behavioral Dynamics
    expression_engine JSONB DEFAULT '{}',
    kinesics JSONB DEFAULT '{}',
    vocalization JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(character_id)
);

-- Create character_personality_profiles table
CREATE TABLE IF NOT EXISTS character_personality_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    
    -- Core Identity Archetype
    species VARCHAR(50) DEFAULT 'Human',
    vocation TEXT,
    social_role TEXT,
    archetypal_story TEXT,
    core_need TEXT,
    core_fear TEXT,
    core_virtue TEXT,
    
    -- Cognitive Model
    openness_score INTEGER CHECK (openness_score >= 1 AND openness_score <= 100),
    conscientiousness_score INTEGER CHECK (conscientiousness_score >= 1 AND conscientiousness_score <= 100),
    extraversion_score INTEGER CHECK (extraversion_score >= 1 AND extraversion_score <= 100),
    agreeableness_score INTEGER CHECK (agreeableness_score >= 1 AND agreeableness_score <= 100),
    neuroticism_score INTEGER CHECK (neuroticism_score >= 1 AND neuroticism_score <= 100),
    
    -- Emotional Spectrum
    emotional_baseline VARCHAR(50),
    emotional_range TEXT,
    emotional_triggers TEXT[],
    coping_mechanisms TEXT[],
    empathy_level VARCHAR(20),
    emotional_expression TEXT,
    
    -- Detailed Personality Data
    personality_details JSONB DEFAULT '{}',
    cognitive_details JSONB DEFAULT '{}',
    emotional_details JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(character_id)
);

-- =====================================================
-- PHASE 3: STORY SYSTEM ENHANCEMENTS
-- =====================================================

-- Extend existing stories table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stories' AND column_name = 'story_rating') THEN
        ALTER TABLE stories ADD COLUMN story_rating VARCHAR(10) DEFAULT 'G';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stories' AND column_name = 'scene_count') THEN
        ALTER TABLE stories ADD COLUMN scene_count INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stories' AND column_name = 'narrative_structure') THEN
        ALTER TABLE stories ADD COLUMN narrative_structure VARCHAR(50) DEFAULT 'linear';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stories' AND column_name = 'world_setting') THEN
        ALTER TABLE stories ADD COLUMN world_setting JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stories' AND column_name = 'character_relationships') THEN
        ALTER TABLE stories ADD COLUMN character_relationships JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stories' AND column_name = 'story_themes') THEN
        ALTER TABLE stories ADD COLUMN story_themes TEXT[];
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stories' AND column_name = 'completion_status') THEN
        ALTER TABLE stories ADD COLUMN completion_status VARCHAR(20) DEFAULT 'draft';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'stories' AND column_name = 'estimated_duration') THEN
        ALTER TABLE stories ADD COLUMN estimated_duration INTEGER;
    END IF;
END $$;

-- Create story_localizations table
CREATE TABLE IF NOT EXISTS story_localizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    language_code VARCHAR(10) NOT NULL CHECK (language_code IN ('zh-CN', 'ja-JP', 'en-US')),
    
    -- Basic Story Information
    story_name VARCHAR(200) NOT NULL,
    story_rating VARCHAR(10) DEFAULT 'G',
    
    -- Core World Structure
    story_core_world JSONB NOT NULL DEFAULT '{}',
    story_narrative_style JSONB NOT NULL DEFAULT '{}',
    story_beats JSONB NOT NULL DEFAULT '[]',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(story_id, language_code)
);

-- Create scene_hierarchical_info table
CREATE TABLE IF NOT EXISTS scene_hierarchical_info (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    scene_index INTEGER NOT NULL,
    language_code VARCHAR(10) NOT NULL CHECK (language_code IN ('zh-CN', 'ja-JP', 'en-US')),
    
    -- Scene Basic Info
    scene_name VARCHAR(200) NOT NULL,
    scene_description TEXT,
    
    -- Four-layer Scene Information
    first_layer_worldview JSONB DEFAULT '{}',
    second_layer_scene JSONB DEFAULT '{}',
    third_layer_antecedent JSONB DEFAULT '{}',
    fourth_layer_character JSONB DEFAULT '{}',
    
    -- Scene Progression
    scene_start TEXT,
    scene_conflict TEXT,
    scene_turning_point TEXT,
    scene_character_arc TEXT,
    scene_relationship_development TEXT,
    scene_target TEXT,
    scene_ending TEXT,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(story_id, scene_index, language_code)
);

-- Create story_progression table
CREATE TABLE IF NOT EXISTS story_progression (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    
    -- Progress Information
    current_scene_index INTEGER DEFAULT 1,
    completed_scenes INTEGER[] DEFAULT '{}',
    total_scenes INTEGER NOT NULL,
    completion_percentage DECIMAL(5,2) DEFAULT 0.00,
    
    -- User Choices and Paths
    story_choices JSONB DEFAULT '{}',
    relationship_points JSONB DEFAULT '{}',
    character_affinity JSONB DEFAULT '{}',
    
    -- Timing Information
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    total_time_spent INTEGER DEFAULT 0,
    
    -- Status
    status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused')),
    
    UNIQUE(user_id, story_id)
);

-- Create story_moments table
CREATE TABLE IF NOT EXISTS story_moments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    moment_id UUID NOT NULL REFERENCES moments(id) ON DELETE CASCADE,
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    scene_index INTEGER,
    
    -- Moment Context
    story_context TEXT,
    emotional_significance TEXT,
    relationship_milestone VARCHAR(50),
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(moment_id, story_id)
);

-- Continue with remaining phases in next file due to length limit...
