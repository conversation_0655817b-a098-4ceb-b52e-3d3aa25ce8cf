'use client';

import React, { useState } from 'react';
import { Users, Heart, Share2, MessageSquare, UserPlus, Gift, ThumbsUp, Star } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { EnhancedAchievement } from '@/types/achievements';
import TrophyCard from '../TrophyCard';

interface TrophySocialTabProps {
  achievements: EnhancedAchievement[];
  lang: string;
  onAchievementClick: (achievement: EnhancedAchievement) => void;
  onClaimReward: (achievementId: string) => void;
}

const TrophySocialTab: React.FC<TrophySocialTabProps> = ({
  achievements,
  lang,
  onAchievementClick,
  onClaimReward
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [activeFilter, setActiveFilter] = useState<'all' | 'community' | 'sharing' | 'friendship' | 'support'>('all');

  // Filter achievements for the Socializers player type (community & sharing)
  const socialAchievements = achievements.filter(a => 
    a.category === 'social'
  );

  const socialCategories = [
    {
      id: 'all',
      label: t('trophies.filters.social.all'),
      icon: Users,
      description: t('trophies.filters.social.allDesc')
    },
    {
      id: 'community',
      label: t('trophies.filters.social.community'),
      icon: MessageSquare,
      description: t('trophies.filters.social.communityDesc')
    },
    {
      id: 'sharing',
      label: t('trophies.filters.social.sharing'),
      icon: Share2,
      description: t('trophies.filters.social.sharingDesc')
    },
    {
      id: 'friendship',
      label: t('trophies.filters.social.friendship'),
      icon: UserPlus,
      description: t('trophies.filters.social.friendshipDesc')
    },
    {
      id: 'support',
      label: t('trophies.filters.social.support'),
      icon: Heart,
      description: t('trophies.filters.social.supportDesc')
    }
  ];

  // Mock categorization based on achievement content
  const categorizeAchievement = (achievement: EnhancedAchievement): string => {
    const name = achievement.name.toLowerCase();
    const desc = achievement.description.toLowerCase();
    
    if (name.includes('friend') || desc.includes('connect') || desc.includes('follow')) {
      return 'friendship';
    }
    if (name.includes('share') || desc.includes('share') || desc.includes('post')) {
      return 'sharing';
    }
    if (name.includes('help') || name.includes('support') || desc.includes('help') || desc.includes('guide')) {
      return 'support';
    }
    if (desc.includes('community') || desc.includes('forum') || desc.includes('discussion')) {
      return 'community';
    }
    return 'community';
  };

  const filteredAchievements = activeFilter === 'all' 
    ? socialAchievements
    : socialAchievements.filter(a => categorizeAchievement(a) === activeFilter);

  const stats = {
    total: socialAchievements.length,
    completed: socialAchievements.filter(a => a.status === 'completed').length,
    inProgress: socialAchievements.filter(a => a.status === 'inProgress').length,
    points: socialAchievements.filter(a => a.status === 'completed').reduce((sum, a) => sum + a.points, 0)
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-purple-500/10 border border-blue-200/20 dark:border-blue-800/20 p-8">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5 backdrop-blur-3xl"></div>
        
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-500 text-white shadow-lg">
              <Users className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {t('trophies.socialTab.title')}
              </h1>
              <p className="text-muted-foreground">{t('trophies.socialTab.subtitle')}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{stats.completed}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.socialTab.connected')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{stats.inProgress}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.socialTab.building')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{stats.total}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.total')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-pink-600 dark:text-pink-400">{stats.points}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.points')}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2">
        {socialCategories.map((category) => {
          const Icon = category.icon;
          const isActive = activeFilter === category.id;
          const categoryCount = category.id === 'all' 
            ? socialAchievements.length 
            : socialAchievements.filter(a => categorizeAchievement(a) === category.id).length;

          return (
            <button
              key={category.id}
              onClick={() => setActiveFilter(category.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                isActive
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                  : 'bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="font-medium">{category.label}</span>
              <span className={`text-xs px-2 py-0.5 rounded-full ${
                isActive 
                  ? 'bg-white/20 text-white' 
                  : 'bg-muted-foreground/20 text-muted-foreground'
              }`}>
                {categoryCount}
              </span>
            </button>
          );
        })}
      </div>

      {/* Social Progress */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200/20 dark:border-blue-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-500 text-white">
              <UserPlus className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.socialTab.communityBuilder.title')}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t('trophies.socialTab.communityBuilder.description')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('trophies.socialTab.communityBuilder.friends')}</span>
              <span className="font-medium">24</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{t('trophies.socialTab.communityBuilder.followers')}</span>
              <span className="font-medium">156</span>
            </div>
          </div>
        </div>

        <div className="rounded-2xl bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border border-indigo-200/20 dark:border-indigo-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-500 text-white">
              <Share2 className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.socialTab.contentSharer.title')}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t('trophies.socialTab.contentSharer.description')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('trophies.socialTab.contentSharer.shares')}</span>
              <span className="font-medium">89</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{t('trophies.socialTab.contentSharer.likesReceived')}</span>
              <span className="font-medium">342</span>
            </div>
          </div>
        </div>

        <div className="rounded-2xl bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200/20 dark:border-purple-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 text-white">
              <Heart className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.socialTab.communityHelper.title')}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t('trophies.socialTab.communityHelper.description')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('trophies.socialTab.communityHelper.helpfulVotes')}</span>
              <span className="font-medium">67</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{t('trophies.socialTab.communityHelper.guidesWritten')}</span>
              <span className="font-medium">3</span>
            </div>
          </div>
        </div>
      </div>

      {/* Social Activity Feed */}
      <div className="rounded-2xl bg-card border border-border p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-500 text-white">
            <MessageSquare className="w-5 h-5" />
          </div>
          <h2 className="text-xl font-bold">{t('trophies.socialTab.socialActivity.title')}</h2>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200/20 dark:border-blue-800/20">
            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-500 text-white">
              <ThumbsUp className="w-5 h-5" />
            </div>
            <div className="flex-1">
              <div className="font-medium">{t('trophies.socialTab.socialActivity.receivedLikes')}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.socialTab.socialActivity.hoursAgo')}</div>
            </div>
            <div className="text-blue-600 dark:text-blue-400 font-medium">+5 pts</div>
          </div>

          <div className="flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border border-indigo-200/20 dark:border-indigo-800/20">
            <div className="p-2 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-500 text-white">
              <UserPlus className="w-5 h-5" />
            </div>
            <div className="flex-1">
              <div className="font-medium">{t('trophies.socialTab.socialActivity.newFollowers')}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.socialTab.socialActivity.dayAgo')}</div>
            </div>
            <div className="text-indigo-600 dark:text-indigo-400 font-medium">+10 pts</div>
          </div>

          <div className="flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200/20 dark:border-purple-800/20">
            <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 text-white">
              <Gift className="w-5 h-5" />
            </div>
            <div className="flex-1">
              <div className="font-medium">{t('trophies.socialTab.socialActivity.helpedNewcomer')}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.socialTab.socialActivity.daysAgo')}</div>
            </div>
            <div className="text-purple-600 dark:text-purple-400 font-medium">+20 pts</div>
          </div>
        </div>
      </div>

      {/* Community Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-4 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200/20 dark:border-blue-800/20">
          <div className="p-3 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-500 text-white mx-auto mb-3 w-fit">
            <MessageSquare className="w-6 h-6" />
          </div>
          <div className="text-lg font-bold">247</div>
          <div className="text-sm text-muted-foreground">{t('trophies.socialTab.communityStats.comments')}</div>
        </div>

        <div className="text-center p-4 rounded-xl bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border border-indigo-200/20 dark:border-indigo-800/20">
          <div className="p-3 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-500 text-white mx-auto mb-3 w-fit">
            <Share2 className="w-6 h-6" />
          </div>
          <div className="text-lg font-bold">89</div>
          <div className="text-sm text-muted-foreground">{t('trophies.socialTab.communityStats.shares')}</div>
        </div>

        <div className="text-center p-4 rounded-xl bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200/20 dark:border-purple-800/20">
          <div className="p-3 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 text-white mx-auto mb-3 w-fit">
            <Heart className="w-6 h-6" />
          </div>
          <div className="text-lg font-bold">456</div>
          <div className="text-sm text-muted-foreground">{t('trophies.socialTab.communityStats.likesGiven')}</div>
        </div>

        <div className="text-center p-4 rounded-xl bg-gradient-to-br from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20 border border-pink-200/20 dark:border-pink-800/20">
          <div className="p-3 rounded-lg bg-gradient-to-br from-pink-500 to-rose-500 text-white mx-auto mb-3 w-fit">
            <Star className="w-6 h-6" />
          </div>
          <div className="text-lg font-bold">12</div>
          <div className="text-sm text-muted-foreground">{t('trophies.socialTab.communityStats.featuredPosts')}</div>
        </div>
      </div>

      {/* Achievements Grid */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {activeFilter === 'all' ? t('trophies.socialTab.allSocialAchievements') : socialCategories.find(c => c.id === activeFilter)?.label}
          </h2>
          <div className="text-sm text-muted-foreground">
            {filteredAchievements.length} {t('trophies.common.achievements')}
          </div>
        </div>

        {filteredAchievements.length > 0 ? (
          <>
            {/* Mobile Layout */}
            <div className="block sm:hidden">
              <div className="columns-2 gap-4" style={{ columnFill: 'balance' }}>
                {filteredAchievements.map((achievement) => (
                  <div key={achievement.id} className="break-inside-avoid mb-4">
                    <TrophyCard
                      achievement={achievement}
                      onClick={() => onAchievementClick(achievement)}
                      onClaimReward={onClaimReward}
                      lang={lang}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Desktop Layout */}
            <div className="hidden sm:block">
              <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-5 lg:gap-6">
                {filteredAchievements.map((achievement) => (
                  <TrophyCard
                    key={achievement.id}
                    achievement={achievement}
                    onClick={() => onAchievementClick(achievement)}
                    onClaimReward={onClaimReward}
                    lang={lang}
                  />
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <div className="p-4 rounded-2xl bg-muted/50 inline-block mb-4">
              <Users className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">{t('trophies.emptyStates.social.title')}</h3>
            <p className="text-muted-foreground">
              {t('trophies.emptyStates.social.description')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrophySocialTab;
