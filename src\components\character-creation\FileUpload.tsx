'use client';

import React, { useRef, DragEvent } from 'react';
import { Upload, FileText, Trash2 } from 'lucide-react';
import toast from 'react-hot-toast';

interface FileUploadProps {
  id: string;
  label: string;
  description?: string;
  files: File[];
  accept?: string;
  multiple?: boolean;
  maxFiles?: number;
  maxSizeKB?: number;
  onFilesChange: (files: File[]) => void;
  supportedFormats?: string[];
  className?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  id,
  label,
  description,
  files,
  accept = ".txt,.md,.json,text/plain,text/markdown,application/json",
  multiple = true,
  maxFiles = 5,
  maxSizeKB = 1024,
  onFilesChange,
  supportedFormats = ['.txt', '.md', '.json'],
  className = ""
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): boolean => {
    // Check file size
    const maxSizeBytes = maxSizeKB * 1024;
    if (file.size > maxSizeBytes) {
      toast.error(`File ${file.name} exceeds size limit (${maxSizeKB}KB)`);
      return false;
    }

    // Check file type
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    const isValidType = supportedFormats.some(format => 
      extension === format || 
      file.type === 'text/plain' || 
      file.type === 'text/markdown' || 
      file.type === 'application/json'
    );
    
    if (!isValidType) {
      toast.error(`File ${file.name} format not supported, please upload ${supportedFormats.join(', ')} format files`);
      return false;
    }

    return true;
  };

  const handleFileSelect = (newFiles: FileList) => {
    const validFiles: File[] = [];
    
    for (let i = 0; i < newFiles.length; i++) {
      const file = newFiles[i];
      
      // Check if file with same name already exists
      if (files.some(existingFile => existingFile.name === file.name)) {
        toast.error(`File ${file.name} already exists`);
        continue;
      }

      // Validate file
      if (validateFile(file)) {
        validFiles.push(file);
      }
    }

    // Check file count limit
    const totalFiles = files.length + validFiles.length;
    if (totalFiles > maxFiles) {
      const allowedCount = Math.max(0, maxFiles - files.length);
      toast.error(`Maximum ${maxFiles} files allowed, ${files.length} selected, can only add ${allowedCount} more`);
      onFilesChange([...files, ...validFiles.slice(0, allowedCount)]);
    } else {
      onFilesChange([...files, ...validFiles]);
    }

    if (validFiles.length > 0) {
      toast.success(`Successfully added ${validFiles.length} files`);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFileSelect(e.target.files);
    }
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleClick = () => {
    inputRef.current?.click();
  };

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    onFilesChange(newFiles);
    toast.success('File removed');
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    return `${(bytes / 1024).toFixed(1)} KB`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Label */}
      <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200">
        {label}
        {description && (
          <span className="text-xs text-gray-500 dark:text-gray-400 font-normal ml-1">
            {description}
          </span>
        )}
      </label>

      {/* Upload Area */}
      <div
        className="border-2 border-dashed border-purple-300 dark:border-purple-700 rounded-lg p-4 text-center hover:border-purple-400 dark:hover:border-purple-600 transition-all cursor-pointer bg-background"
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        <div className="text-purple-600 dark:text-purple-400">
          <Upload className="mx-auto mb-2" size={24} />
          <p className="text-sm font-semibold text-purple-800 dark:text-purple-200 mb-1">
            {multiple ? 'Upload Files' : 'Upload Single File'}
          </p>
          <p className="text-xs text-purple-600 dark:text-purple-400">
            Supports {supportedFormats.join(', ')} formats, max {maxSizeKB}KB
          </p>
          <p className="text-xs text-purple-500 dark:text-purple-400 mt-1">
            Drag files here or click to select
          </p>
        </div>
      </div>

      {/* Hidden Input */}
      <input
        ref={inputRef}
        id={id}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleInputChange}
        className="hidden"
      />

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h5 className="text-sm font-medium text-purple-700 dark:text-purple-300">
            Uploaded Files ({files.length}/{maxFiles})
          </h5>
          <div className="space-y-2">
            {files.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700"
              >
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <FileText size={16} className="text-purple-600 dark:text-purple-400 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <p className="text-sm text-gray-900 dark:text-white font-medium truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => removeFile(index)}
                  className="text-red-500 hover:text-red-700 dark:hover:text-red-300 transition-colors p-1 flex-shrink-0"
                  title="Delete file"
                >
                  <Trash2 size={14} />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Info */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-700">
        <h5 className="font-semibold text-purple-800 dark:text-purple-200 mb-2 text-sm">
          📋 File Upload Instructions:
        </h5>
        <div className="space-y-1 text-xs text-purple-700 dark:text-purple-300">
          <div className="flex items-center gap-2">
            <span>✅</span>
            <span>Supported formats: {supportedFormats.join(', ')}</span>
          </div>
          <div className="flex items-center gap-2">
            <span>✅</span>
            <span>File size: max {maxSizeKB}KB</span>
          </div>
          <div className="flex items-center gap-2">
            <span>✅</span>
            <span>File count: max {maxFiles} files</span>
          </div>
          {multiple && (
            <div className="flex items-center gap-2">
              <span>✅</span>
              <span>Supports multiple selection and drag & drop</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileUpload; 