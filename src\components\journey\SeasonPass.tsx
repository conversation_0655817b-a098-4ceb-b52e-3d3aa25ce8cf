'use client';

import React, { useState } from 'react';
import { Crown, Star, Lock, Zap, Gift, Sparkles, Timer, TrendingUp, Calendar, Target, Trophy } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { MembershipStatus } from './shared/MembershipStatus';

interface SeasonPassProps {
  currentLevel: number;
  isPremiumUser: boolean;
  membershipTier: 'standard' | 'pass' | 'diamond' | 'metaverse';
  onMembershipClick: () => void;
  lang: string;
}

interface PassReward {
  level: number;
  freeReward?: {
    type: 'currency' | 'item' | 'cosmetic';
    name: string;
    amount?: number;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    icon: string;
  };
  premiumReward?: {
    type: 'currency' | 'item' | 'cosmetic' | 'exclusive';
    name: string;
    amount?: number;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    icon: string;
    exclusive?: boolean;
  };
}

// 季节会员推销组件
const SeasonMembershipPromotion: React.FC<{
  isPremiumUser: boolean;
  onUpgradeClick: () => void;
  currentSeason: any;
  lang: string;
}> = ({ isPremiumUser, onUpgradeClick, currentSeason, lang }) => {
  const { t } = useTranslation(lang, 'translation');
  
  // 计算免费vs付费奖励对比 - 针对季节通行证
  const freeSeasonRewards = { xp: 1200, cosmetics: 3, currency: 800 };
  const premiumSeasonRewards = { xp: 3600, cosmetics: 15, currency: 2400, exclusive: 5 };
  const seasonPotentialLoss = (premiumSeasonRewards.xp - freeSeasonRewards.xp) + 
                             (premiumSeasonRewards.currency - freeSeasonRewards.currency);

  if (isPremiumUser) {
    return null; // 付费用户不显示推销
  }

  return (
    <div className="relative bg-gradient-to-r from-yellow-500/15 via-orange-500/15 to-red-500/15 border-2 border-yellow-400/40 rounded-2xl p-6 mb-6">
      {/* 脉冲动画背景 */}
      <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/5 to-orange-400/5 rounded-2xl animate-pulse"></div>
      
      {/* 季节特色装饰 */}
      <div className="absolute top-0 right-0 text-6xl opacity-20 transform rotate-12 translate-x-4 -translate-y-4">
        {currentSeason.icon}
      </div>

      <div className="relative">
        {/* 顶部：升级CTA */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="w-14 h-14 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg animate-bounce">
              <Crown className="w-7 h-7 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-yellow-100 flex items-center gap-2">
                {t('journey.seasonPass.unlockSeasonPass', { season: currentSeason.name })}
                <Sparkles className="w-5 h-5 text-yellow-400 animate-pulse" />
              </h3>
              <p className="text-sm text-yellow-200/90">
                {t('journey.seasonPass.getTripleExp', { exp: 3 })}<span className="font-bold text-yellow-300">3倍经验</span>和<span className="font-bold text-yellow-300">{t('journey.seasonPass.exclusiveSeasonRewards', { season: currentSeason.name })}</span>
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-3xl font-bold text-yellow-300 mb-1 flex items-center gap-1">
              <TrendingUp className="w-6 h-6" />
              +{Math.floor(seasonPotentialLoss / 100)}倍
            </div>
            <div className="text-xs text-yellow-200/80 mb-3">{t('journey.seasonPass.rewardBoost')}</div>
            <button 
              onClick={onUpgradeClick}
              className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-6 py-3 rounded-xl font-bold text-sm hover:scale-105 transition-all shadow-lg hover:shadow-yellow-400/30"
            >
              {t('journey.seasonPass.upgradeNow')}
            </button>
          </div>
        </div>

        {/* 中间：季节特色亮点 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-black/20 rounded-lg p-4 border border-yellow-400/30">
            <div className="text-center">
              <div className="text-2xl mb-2">{currentSeason.icon}</div>
              <div className="text-yellow-300 text-sm font-bold mb-1">{currentSeason.specialFeatures[0]}</div>
              <div className="text-xs text-yellow-200/80">{t('journey.seasonPass.seasonalEffects')}</div>
            </div>
          </div>
          <div className="bg-black/20 rounded-lg p-4 border border-orange-400/30">
            <div className="text-center">
              <div className="text-2xl mb-2">🎁</div>
              <div className="text-orange-300 text-sm font-bold mb-1">{t('journey.seasonPass.exclusiveDecorations')}</div>
              <div className="text-xs text-orange-200/80">{t('journey.seasonPass.passUsersOnly')}</div>
            </div>
          </div>
          <div className="bg-black/20 rounded-lg p-4 border border-red-400/30">
            <div className="text-center">
              <div className="text-2xl mb-2">⚡</div>
              <div className="text-red-300 text-sm font-bold mb-1">{t('journey.seasonPass.tripleExpBonus')}</div>
              <div className="text-xs text-red-200/80">{t('journey.seasonPass.fastUpgrade')}</div>
            </div>
          </div>
        </div>

        {/* 底部：对比数据 */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-black/20 rounded-lg p-3 border border-gray-500/30">
            <div className="text-center">
              <div className="text-gray-400 text-xs mb-1">{t('journey.seasonPass.freePass')}</div>
              <div className="text-gray-300 font-bold">{freeSeasonRewards.xp + freeSeasonRewards.currency} {t('journey.seasonPass.totalRewards')}</div>
              <div className="text-xs text-gray-400 mt-1">{freeSeasonRewards.cosmetics} {t('journey.seasonPass.decorations')}</div>
            </div>
          </div>
          <div className="bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-lg p-3 border border-yellow-400/30">
            <div className="text-center">
              <div className="text-yellow-300 text-xs mb-1">{t('journey.seasonPass.premiumPass')}</div>
              <div className="text-yellow-100 font-bold">{premiumSeasonRewards.xp + premiumSeasonRewards.currency} {t('journey.seasonPass.totalRewards')}</div>
              <div className="text-xs text-green-400 mt-1">{premiumSeasonRewards.cosmetics} {t('journey.seasonPass.decorations')} + {premiumSeasonRewards.exclusive} {t('journey.seasonPass.exclusive')}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const SeasonPass: React.FC<SeasonPassProps> = ({
  currentLevel,
  isPremiumUser,
  membershipTier,
  onMembershipClick,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);

  // 季节信息 Mock 数据
  const getCurrentSeasonData = () => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // 0-11
    
    // 根据月份确定季节
    const seasons = [
      {
        name: t('journey.seasonPass.seasons.winterFantasy'),
        nameEn: 'Winter Fantasy',
        description: t('journey.seasonPass.seasons.winterDescription'),
        theme: 'winter',
        icon: '❄️',
        colors: {
          primary: 'from-blue-400 to-cyan-300',
          secondary: 'from-blue-500/10 to-cyan-500/10',
          accent: 'text-blue-600 dark:text-blue-400'
        },
        specialFeatures: [
          t('journey.seasonPass.seasons.snowEffects'),
          t('journey.seasonPass.seasons.winterExclusiveDecorations'),
          t('journey.seasonPass.seasons.festivalLimitedRewards')
        ]
      },
      {
        name: t('journey.seasonPass.seasons.springAwakening'),
        nameEn: 'Spring Awakening', 
        description: t('journey.seasonPass.seasons.springDescription'),
        theme: 'spring',
        icon: '🌸',
        colors: {
          primary: 'from-green-400 to-pink-300',
          secondary: 'from-green-500/10 to-pink-500/10',
          accent: 'text-green-600 dark:text-green-400'
        },
        specialFeatures: [
          t('journey.seasonPass.seasons.petalFalling'),
          t('journey.seasonPass.seasons.springGrowthBonus'),
          t('journey.seasonPass.seasons.newCharacterUnlock')
        ]
      },
      {
        name: t('journey.seasonPass.seasons.summerCarnival'),
        nameEn: 'Summer Carnival',
        description: t('journey.seasonPass.seasons.summerDescription'),
        theme: 'summer',
        icon: '🌞',
        colors: {
          primary: 'from-yellow-400 to-orange-300',
          secondary: 'from-yellow-500/10 to-orange-500/10',
          accent: 'text-yellow-600 dark:text-yellow-400'
        },
        specialFeatures: [
          t('journey.seasonPass.seasons.sunlightEffects'),
          t('journey.seasonPass.seasons.summerEvents'),
          t('journey.seasonPass.seasons.doubleExperience')
        ]
      },
      {
        name: t('journey.seasonPass.seasons.autumnTales'),
        nameEn: 'Autumn Tales',
        description: t('journey.seasonPass.seasons.autumnDescription'),
        theme: 'autumn',
        icon: '🍂',
        colors: {
          primary: 'from-amber-400 to-red-300',
          secondary: 'from-amber-500/10 to-red-500/10',
          accent: 'text-amber-600 dark:text-amber-400'
        },
        specialFeatures: [
          t('journey.seasonPass.seasons.fallingLeavesAnimation'),
          t('journey.seasonPass.seasons.harvestFestival'),
          t('journey.seasonPass.seasons.thanksgivingThemeRewards')
        ]
      }
    ];

    // 根据月份选择季节 (12-2: 冬, 3-5: 春, 6-8: 夏, 9-11: 秋)
    let seasonIndex;
    if (currentMonth >= 11 || currentMonth <= 1) seasonIndex = 0; // 冬季
    else if (currentMonth >= 2 && currentMonth <= 4) seasonIndex = 1; // 春季  
    else if (currentMonth >= 5 && currentMonth <= 7) seasonIndex = 2; // 夏季
    else seasonIndex = 3; // 秋季

    return seasons[seasonIndex];
  };

  const currentSeason = getCurrentSeasonData();

  // 生成1-100级的通行证奖励数据，步进5
  const generatePassRewards = (): PassReward[] => {
    const rewards: PassReward[] = [];
    const rewardTemplates = [
      {
        freeType: 'currency' as const,
        freeName: t('journey.seasonPass.rewards.coins'),
        freeIcon: '🪙',
        premiumType: 'currency' as const,
        premiumName: t('journey.seasonPass.rewards.diamonds'),
        premiumIcon: '💎'
      },
      {
        freeType: 'item' as const,
        freeName: t('journey.seasonPass.rewards.expAccelerator'),
        freeIcon: '⚡',
        premiumType: 'cosmetic' as const,
        premiumName: t('journey.seasonPass.rewards.starryAvatarFrame'),
        premiumIcon: '🌟'
      },
      {
        freeType: 'currency' as const,
        freeName: t('journey.seasonPass.rewards.coins'),
        freeIcon: '🪙',
        premiumType: 'exclusive' as const,
        premiumName: t('journey.seasonPass.rewards.legendaryTitle'),
        premiumIcon: '👑'
      },
      {
        freeType: 'item' as const,
        freeName: t('journey.seasonPass.rewards.luckyCharm'),
        freeIcon: '🍀',
        premiumType: 'currency' as const,
        premiumName: t('journey.seasonPass.rewards.diamonds'),
        premiumIcon: '💎'
      },
      {
        freeType: 'currency' as const,
        freeName: t('journey.seasonPass.rewards.coins'),
        freeIcon: '🪙',
        premiumType: 'cosmetic' as const,
        premiumName: t('journey.seasonPass.rewards.rainbowChatBubble'),
        premiumIcon: '🌈'
      },
      {
        freeType: 'item' as const,
        freeName: t('journey.seasonPass.rewards.doubleExpCard'),
        freeIcon: '📈',
        premiumType: 'exclusive' as const,
        premiumName: t('journey.seasonPass.rewards.exclusiveBackgroundTheme'),
        premiumIcon: '🎨'
      },
      {
        freeType: 'currency' as const,
        freeName: t('journey.seasonPass.rewards.coins'),
        freeIcon: '🪙',
        premiumType: 'exclusive' as const,
        premiumName: t('journey.seasonPass.rewards.ultimateGloryBadge'),
        premiumIcon: '🏆'
      }
    ];

    const specialRewards = [
      '🎁', '🎉', '🏅', '🔮', '⭐', '💫', '🌟', '✨', '🎯', '🎪',
      '🎨', '🎭', '🎪', '🎀', '🎊', '🎈', '🎆', '🎇', '🎐', '🎑'
    ];

    for (let level = 5; level <= 100; level += 5) {
      const templateIndex = Math.floor((level - 5) / 5) % rewardTemplates.length;
      const template = rewardTemplates[templateIndex];
      
      // 根据等级计算稀有度
      let rarity: 'common' | 'rare' | 'epic' | 'legendary';
      if (level >= 80) rarity = 'legendary';
      else if (level >= 50) rarity = 'epic';
      else if (level >= 20) rarity = 'rare';
      else rarity = 'common';

      // 计算奖励数量（随等级递增）
      const baseAmount = Math.floor(level / 5) * 50 + 50;
      const premiumAmount = Math.floor(level / 10) * 25 + 25;

      rewards.push({
        level,
        freeReward: {
          type: template.freeType,
          name: template.freeName,
          amount: template.freeType === 'currency' ? baseAmount : undefined,
          rarity: level % 25 === 0 ? (rarity === 'common' ? 'rare' : rarity) : rarity,
          icon: template.freeIcon
        },
        premiumReward: {
          type: template.premiumType,
          name: template.premiumName,
          amount: template.premiumType === 'currency' ? premiumAmount : undefined,
          rarity: level % 25 === 0 ? 'legendary' : (level % 10 === 0 ? 'epic' : rarity),
          icon: level % 25 === 0 ? specialRewards[Math.floor(level / 25) - 1] || template.premiumIcon : template.premiumIcon,
          exclusive: template.premiumType === 'exclusive' || level % 25 === 0
        }
      });
    }
    
    return rewards;
  };

  const passRewards = generatePassRewards();

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'from-gray-400 to-gray-600';
      case 'rare': return 'from-blue-400 to-blue-600';
      case 'epic': return 'from-purple-400 to-purple-600';
      case 'legendary': return 'from-yellow-400 to-orange-500';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  const getRarityBorder = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-300 dark:border-gray-700';
      case 'rare': return 'border-blue-300 dark:border-blue-700';
      case 'epic': return 'border-purple-300 dark:border-purple-700';
      case 'legendary': return 'border-yellow-300 dark:border-yellow-700';
      default: return 'border-gray-300 dark:border-gray-700';
    }
  };

  const PassCard = ({ reward, isPremium, level, isUnlocked }: {
    reward?: PassReward['freeReward'] | PassReward['premiumReward'];
    isPremium: boolean;
    level: number;
    isUnlocked: boolean;
  }) => {
    const isClaimable = isUnlocked && (!isPremium || isPremiumUser);
    const isLocked = isPremium && !isPremiumUser;

    return (
      <div className={`relative p-4 rounded-xl border-2 transition-all duration-300 min-w-[120px] overflow-hidden ${
        isClaimable
          ? `${getRarityBorder(reward?.rarity || 'common')} bg-gradient-to-br ${getRarityColor(reward?.rarity || 'common')}/10`
          : isLocked
          ? 'border-red-300 dark:border-red-800 bg-red-50/50 dark:bg-red-900/20'
          : 'border-gray-200 dark:border-gray-800 bg-gray-50/50 dark:bg-gray-900/20'
      }`}>
        {/* 光效动画 - 仅对可领取的奖励显示 */}
        {isClaimable && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shine"></div>
        )}
        {/* 等级标识 */}
        <div className={`absolute -top-3 -left-3 w-10 h-10 rounded-full flex items-center justify-center text-xs font-bold shadow-lg z-10 ${
          isPremium 
            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' 
            : 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white'
        }`}>
          {level}
        </div>

        {/* 锁定标识 */}
        {isLocked && (
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center z-10">
            <Lock className="w-3 h-3 text-white" />
          </div>
        )}

        {/* 专属标识 */}
        {reward && 'exclusive' in reward && reward.exclusive && (
          <div className="absolute top-1 right-1 z-10">
            <Sparkles className="w-4 h-4 text-yellow-500" />
          </div>
        )}

        {/* 奖励内容 */}
        <div className="text-center space-y-2 pt-2">
          <div className="text-2xl">{reward?.icon || '❓'}</div>
          <div className="text-sm font-medium text-foreground">
            {reward?.name || t('journey.seasonPass.emptyReward')}
          </div>
          {reward?.amount && (
            <div className="text-xs text-foreground/60">
              x{reward.amount}
            </div>
          )}
          <div className={`text-xs px-2 py-1 rounded-full ${
            reward?.rarity === 'legendary' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400' :
            reward?.rarity === 'epic' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400' :
            reward?.rarity === 'rare' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400' :
            'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-400'
          }`}>
            {reward?.rarity || 'common'}
          </div>
        </div>

        {/* 领取状态 */}
        {isUnlocked && isClaimable && (
          <button className="absolute inset-0 bg-green-500/20 rounded-xl flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
            <span className="text-green-600 dark:text-green-400 font-bold text-sm">{t('journey.seasonPass.unlocked')}</span>
          </button>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 季节会员推销区域 */}
      <SeasonMembershipPromotion
        isPremiumUser={isPremiumUser}
        onUpgradeClick={onMembershipClick}
        currentSeason={currentSeason}
        lang={lang}
      />

      {/* 季节信息和进度概览 */}
      <div className="relative overflow-hidden rounded-2xl backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 shadow-xl">
        {/* 渐变背景 */}
        <div className={`absolute inset-0 bg-gradient-to-br ${currentSeason.colors.secondary} opacity-50`}></div>
        
        <div className="relative p-6">
          {/* 顶部：季节信息 + 会员状态 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              {/* 季节图标 */}
              <div className={`w-12 h-12 bg-gradient-to-br ${currentSeason.colors.primary} rounded-xl flex items-center justify-center shadow-lg`}>
                <span className="text-2xl">{currentSeason.icon}</span>
              </div>
              <div>
                <h2 className="text-xl font-bold text-foreground">
                  {currentSeason.name}
                </h2>
                <p className="text-foreground/70">{currentSeason.description}</p>
                
                {/* 季节特色功能 */}
                <div className="flex gap-2 mt-2">
                  {currentSeason.specialFeatures.slice(0, 2).map((feature, index) => (
                    <span key={index} className={`text-xs px-2 py-1 rounded-full bg-gradient-to-r ${currentSeason.colors.primary} text-white`}>
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            
            {/* 会员状态 */}
            <MembershipStatus
              membershipTier={membershipTier}
              onUpgradeClick={onMembershipClick}
              lang={lang}
              compact={true}
            />
          </div>

          {/* 进度信息 */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                <Target className="w-5 h-5 text-purple-400" />
                <span className="font-semibold">{t('journey.seasonPass.currentLevel')}: {currentLevel}</span>
              </div>
              <span className="text-sm text-foreground/60">
                {t('journey.seasonPass.nextReward')}: {t('journey.seasonPass.level')} {Math.ceil(currentLevel / 5) * 5}
              </span>
            </div>
            
            <div className="w-full bg-white/20 dark:bg-black/20 rounded-full h-3 overflow-hidden">
              <div 
                className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-1000 relative"
                style={{ width: `${(currentLevel % 5) * 20}%` }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shine"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 奖励轨道区域 */}
      <div className="space-y-4">
        {/* 免费轨道 */}
        <div className="relative overflow-hidden rounded-2xl backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 shadow-xl">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-cyan-500/10 to-blue-500/10 opacity-50"></div>
          
          <div className="relative p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                <Star className="w-5 h-5 text-white" />
              </div>
              <h4 className="text-lg font-bold text-foreground">{t('journey.seasonPass.freeTrack')}</h4>
            </div>

            {/* 横向滚动容器 */}
            <div className="overflow-x-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-blue-500/30 hover:scrollbar-thumb-blue-500/50">
              <div className="flex gap-4 pb-4 pt-4 px-4" style={{ width: `${passRewards.length * 140 + 32}px` }}>
                {passRewards.map((reward) => (
                  <PassCard
                    key={`free-${reward.level}`}
                    reward={reward.freeReward}
                    isPremium={false}
                    level={reward.level}
                    isUnlocked={currentLevel >= reward.level}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 高级轨道 */}
        <div className="relative overflow-hidden rounded-2xl backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 shadow-xl">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-purple-500/10 opacity-50"></div>
          
          <div className="relative p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Crown className="w-5 h-5 text-white" />
                </div>
                <h4 className="text-lg font-bold text-foreground">{t('journey.seasonPass.premiumTrack')}</h4>
              </div>
              
              {isPremiumUser && (
                <div className="px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm rounded-full">
                  {t('journey.seasonPass.activated')}
                </div>
              )}
            </div>

            {/* 横向滚动容器 */}
            <div className="overflow-x-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-purple-500/30 hover:scrollbar-thumb-purple-500/50">
              <div className="flex gap-4 pb-4 pt-4 px-4" style={{ width: `${passRewards.length * 140 + 32}px` }}>
                {passRewards.map((reward) => (
                  <PassCard
                    key={`premium-${reward.level}`}
                    reward={reward.premiumReward}
                    isPremium={true}
                    level={reward.level}
                    isUnlocked={currentLevel >= reward.level}
                  />
                ))}
              </div>
            </div>

            {!isPremiumUser && (
              <div className="mt-6 text-center">
                <button 
                  onClick={onMembershipClick}
                  className="px-8 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-bold hover:scale-105 transition-transform shadow-lg"
                >
                  {t('journey.seasonPass.upgradeToUnlockAll')}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 购买弹窗 */}
      {showPurchaseModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-900 rounded-2xl p-6 max-w-md w-full">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-2">{t('journey.seasonPass.upgradeToPremium')}</h3>
              <p className="text-foreground/70">{t('journey.seasonPass.unlockExclusiveRewards')}</p>
            </div>

            <div className="space-y-4 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
                <span>{t('journey.seasonPass.unlockAllPremiumRewards')}</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
                <span>{t('journey.seasonPass.tripleExperienceBonus')}</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
                <span>{t('journey.seasonPass.exclusiveDecorationsAndTitles')}</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
                <span>{t('journey.seasonPass.priorityCustomerSupport')}</span>
              </div>
            </div>

            <div className="flex gap-3">
              <button 
                onClick={() => setShowPurchaseModal(false)}
                className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                {t('journey.seasonPass.cancel')}
              </button>
              <button 
                onClick={() => {
                  setShowPurchaseModal(false);
                  // 这里应该调用支付接口
                  alert(t('journey.seasonPass.jumpToPayment'));
                }}
                className="flex-1 px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-bold hover:scale-105 transition-transform"
              >
                {t('journey.seasonPass.buyNow')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SeasonPass; 