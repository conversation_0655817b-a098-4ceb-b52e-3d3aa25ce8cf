import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import ManageStoriesClientPage from './ManageStoriesClientPage';

interface ManageStoriesPageProps {
  params: Promise<{ lang: string }>;
}

export default async function ManageStoriesPage({ params }: ManageStoriesPageProps) {
  const { lang } = await params;

  return (
    <AuthGuard requireAuth={true}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <ManageStoriesClientPage lang={lang} />
      </Suspense>
    </AuthGuard>
  );
}

export async function generateMetadata() {
  return {
    title: 'Manage Stories - Alphane',
    description: 'Manage your created stories, view analytics and edit story details',
  };
}