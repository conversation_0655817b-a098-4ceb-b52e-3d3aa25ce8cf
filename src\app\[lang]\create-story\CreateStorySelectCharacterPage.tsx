'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslation } from '@/app/i18n/client';

import { getManagedCharacters, getManagedStories, type ManagedCharacter } from '@/lib/mock-data';
import {
  Plus,
  Search,
  Users,
  BookOpen,
  Sparkles,
  ChevronDown,
  X,
  Tag,
  TrendingUp,
  PenTool,
  Star,
  Zap,
  Filter,
  Grid3X3,
  List,
  ArrowRight,
  Heart,
  Play,
  BadgeCheck
} from 'lucide-react';

interface CreateStorySelectCharacterPageProps {
  lang: string;
}

const CreateStorySelectCharacterPage: React.FC<CreateStorySelectCharacterPageProps> = ({ lang }) => {
  const router = useRouter();
  const { t } = useTranslation(lang, 'translation');
  const [characters, setCharacters] = useState<ManagedCharacter[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'followers' | 'heatScore' | 'trendPercentage' | 'lastEditedAt'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filterStatus, setFilterStatus] = useState<'all' | 'verified' | 'unverified'>('all');
  const [filterGender, setFilterGender] = useState<'all' | 'male' | 'female' | 'non-binary' | 'other'>('all');
  const [filterPOV, setFilterPOV] = useState<'all' | 'first-person' | 'second-person' | 'third-person'>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    // Load user's characters and stories
    const loadData = async () => {
      try {
        // In a real app, this would be an API call to get user's characters and stories
        const userCharacters = getManagedCharacters();
        setCharacters(userCharacters);
      } catch (error) {
        console.error('Failed to load data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);



  // Filter and sort characters
  const filteredAndSortedCharacters = characters.filter(character => {
    const matchesSearch = character.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (character.description?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);
    const matchesStatus = filterStatus === 'all' ||
                         (filterStatus === 'verified' && character.isVerified) ||
                         (filterStatus === 'unverified' && !character.isVerified);
    const matchesGender = filterGender === 'all' || character.gender === filterGender;
    const matchesPOV = filterPOV === 'all' || character.pov === filterPOV;
    const matchesTags = selectedTags.length === 0 || selectedTags.every(tag => character.tags.includes(tag));

    return matchesSearch && matchesStatus && matchesGender && matchesPOV && matchesTags;
  }).sort((a, b) => {
    let aValue: any = a[sortBy];
    let bValue: any = b[sortBy];

    if (sortBy === 'lastEditedAt') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  const handleCharacterSelect = (characterId: string) => {
    router.push(`/${lang}/create-story/${characterId}`);
  };

  // Character Card Component for Grid View
  const CharacterCard = ({ character }: { character: ManagedCharacter }) => {
    const [avatarSrc, setAvatarSrc] = useState(character.character_avatar);

    return (
      <div
        onClick={() => handleCharacterSelect(character.id)}
        className="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-purple-100 dark:border-purple-800/50 hover:shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer"
      >
        {/* Character Avatar */}
        <div className="relative mb-3">
          <div className="w-16 h-16 mx-auto relative">
            <Image
              src={avatarSrc}
              alt={character.name}
              width={64}
              height={64}
              className="w-full h-full rounded-xl object-cover border-2 border-purple-200 dark:border-purple-700"
              unoptimized
              onError={() => setAvatarSrc('https://picsum.photos/400/400')}
            />
            {character.isVerified && (
              <div className="absolute -top-1 -right-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-1">
                <BadgeCheck className="w-3 h-3 text-white" />
              </div>
            )}
          </div>
        </div>

        {/* Character Info */}
        <div className="text-center mb-3">
          <h3 className="text-base font-bold text-gray-900 dark:text-white mb-1 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
            {character.name}
          </h3>
          <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
            {character.description || t('storyCreation.selectCharacter.noDescriptionAvailable')}
          </p>

          {/* Tags */}
          <div className="flex flex-wrap justify-center gap-1 mb-3">
            {character.tags.slice(0, 2).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
            {character.tags.length > 2 && (
              <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                +{character.tags.length - 2}
              </span>
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-1 text-center border-t border-purple-100 dark:border-purple-800/50 pt-3">
          <div>
            <div className="text-sm font-bold text-gray-900 dark:text-white">
              {Math.round(character.followers / 1000)}k
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">{t('storyCreation.selectCharacter.fans')}</div>
          </div>
          <div>
            <div className="text-sm font-bold text-gray-900 dark:text-white">
              {Math.round(character.heatScore / 1000)}k
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">{t('storyCreation.selectCharacter.heatScore')}</div>
          </div>
          <div>
            <div className={`text-sm font-bold ${
              character.trend === 'up' ? 'text-green-600' :
              character.trend === 'down' ? 'text-red-600' : 'text-gray-600'
            }`}>
              {character.trendPercentage > 0 ? '+' : ''}{character.trendPercentage}%
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">{t('storyCreation.selectCharacter.trend')}</div>
          </div>
        </div>

        {/* Action Button */}
        <div className="mt-3 pt-3 border-t border-purple-100 dark:border-purple-800/50">
          <div className="flex items-center justify-center gap-2 text-purple-600 dark:text-purple-400 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors">
            <BookOpen className="w-4 h-4" />
            <span className="text-sm font-medium">{t('storyCreation.selectCharacter.createStory')}</span>
            <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </div>
        </div>
      </div>
    );
  };

  // Character List Item Component for List View
  const CharacterListItem = ({ character }: { character: ManagedCharacter }) => {
    const [avatarSrc, setAvatarSrc] = useState(character.character_avatar);

    return (
      <div
        onClick={() => handleCharacterSelect(character.id)}
        className="group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-3 shadow-lg border border-purple-100 dark:border-purple-800/50 hover:shadow-xl hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 cursor-pointer"
      >
        <div className="flex items-center gap-3">
          {/* Avatar */}
          <div className="relative flex-shrink-0">
            <Image
              src={avatarSrc}
              alt={character.name}
              width={48}
              height={48}
              className="w-12 h-12 rounded-lg object-cover border-2 border-purple-200 dark:border-purple-700"
              unoptimized
              onError={() => setAvatarSrc('https://picsum.photos/400/400')}
            />
            {character.isVerified && (
              <div className="absolute -top-1 -right-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-0.5">
                <BadgeCheck className="w-3 h-3 text-white" />
              </div>
            )}
          </div>

          {/* Character Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="text-base font-bold text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                  {character.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-1 mb-1">
                  {character.description || t('storyCreation.selectCharacter.noDescriptionAvailable')}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-1">
                  {character.tags.slice(0, 3).map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                  {character.tags.length > 3 && (
                    <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                      +{character.tags.length - 3}
                    </span>
                  )}
                </div>
              </div>

              {/* Stats */}
              <div className="flex items-center gap-4 ml-3">
                <div className="text-center">
                  <div className="text-sm font-bold text-gray-900 dark:text-white">
                    {Math.round(character.followers / 1000)}k
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{t('storyCreation.selectCharacter.fans')}</div>
                </div>
                <div className="text-center">
                  <div className="text-sm font-bold text-gray-900 dark:text-white">
                    {Math.round(character.heatScore / 1000)}k
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{t('storyCreation.selectCharacter.heatScore')}</div>
                </div>
                <div className="text-center">
                  <div className={`text-sm font-bold ${
                    character.trend === 'up' ? 'text-green-600' :
                    character.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {character.trendPercentage > 0 ? '+' : ''}{character.trendPercentage}%
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{t('storyCreation.selectCharacter.trend')}</div>
                </div>

                {/* Action Button */}
                <div className="flex items-center gap-2 text-purple-600 dark:text-purple-400 group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors">
                  <BookOpen className="w-4 h-4" />
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Tag management functions
  const handleAddTag = () => {
    if (tagInput.trim() && !selectedTags.includes(tagInput.trim())) {
      setSelectedTags([...selectedTags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setSelectedTags(selectedTags.filter(tag => tag !== tagToRemove));
  };



  // Statistics - Calculate story-related stats
  const stories = getManagedStories();
  const totalStories = stories.length;
  const totalLikes = stories.reduce((sum, story) => sum + story.likes, 0);
  const totalPlays = stories.reduce((sum, story) => sum + story.plays, 0);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10 flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200/20 dark:bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-200/20 dark:bg-pink-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Hero Section */}
        <div className="text-center mb-8">
          <div className="max-w-4xl mx-auto mb-4">
            <div className="flex items-center justify-center gap-4">
              <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-lg">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <div className="text-left">
                <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  {t('storyCreation.selectCharacter.title')}
                </h1>
                <p className="text-base text-gray-600 dark:text-gray-400 mt-1">
                  {t('storyCreation.selectCharacter.subtitle')}
                </p>
              </div>
            </div>
          </div>

          {/* Quick Stats - Full width and flatter */}
          <div className="flex gap-2 mb-6">
            <div className="flex-1 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-purple-100 dark:border-purple-800/50">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex-shrink-0">
                  <BookOpen className="w-5 h-5 text-white" />
                </div>
                <div className="text-left min-w-0">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">{totalStories}</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 truncate">{t('storyCreation.selectCharacter.totalStories')}</div>
                </div>
              </div>
            </div>
            <div className="flex-1 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-purple-100 dark:border-purple-800/50">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-pink-500 to-red-500 rounded-lg flex-shrink-0">
                  <Heart className="w-5 h-5 text-white" />
                </div>
                <div className="text-left min-w-0">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">{Math.round(totalLikes / 1000)}k</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 truncate">{t('storyCreation.selectCharacter.totalLikes')}</div>
                </div>
              </div>
            </div>
            <div className="flex-1 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-purple-100 dark:border-purple-800/50">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex-shrink-0">
                  <Play className="w-5 h-5 text-white" />
                </div>
                <div className="text-left min-w-0">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">{Math.round(totalPlays / 1000)}k</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 truncate">{t('storyCreation.selectCharacter.totalPlays')}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter Controls */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-purple-100 dark:border-purple-800/50 mb-6">
          <div className="flex items-center justify-between gap-3 mb-4">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
              <Search className="w-5 h-5 text-purple-600" />
              {t('storyCreation.selectCharacter.selectYourCharacter')}
            </h2>

            {/* View Mode Toggle */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">{t('storyCreation.selectCharacter.viewMode')}</span>
              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-white dark:bg-gray-600 text-purple-600 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                  }`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list'
                      ? 'bg-white dark:bg-gray-600 text-purple-600 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Search Box */}
          <div className="flex flex-col sm:flex-row gap-3 mb-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder={t('storyCreation.selectCharacter.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
              />
            </div>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center gap-2 px-4 py-2.5 rounded-lg border transition-all ${
                showFilters
                  ? 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700 text-purple-700 dark:text-purple-300'
                  : 'bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
              }`}
            >
              <Filter className="w-4 h-4" />
              <span className="hidden sm:inline">{t('storyCreation.selectCharacter.filters')}</span>
            </button>
          </div>

          {/* Collapsible Filters */}
          {showFilters && (
            <div className="space-y-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 mb-3">
              {/* Filter Row */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                {/* Status Filter */}
                <div className="space-y-1">
                  <label className="text-xs font-medium text-gray-700 dark:text-gray-300">{t('storyCreation.selectCharacter.storyStatus')}</label>
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value as any)}
                    className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="all">{t('storyCreation.selectCharacter.allCharacters')}</option>
                    <option value="verified">{t('storyCreation.selectCharacter.withStories')}</option>
                    <option value="unverified">{t('storyCreation.selectCharacter.withoutStories')}</option>
                  </select>
                </div>

                {/* Gender Filter */}
                <div className="space-y-1">
                  <label className="text-xs font-medium text-gray-700 dark:text-gray-300">{t('storyCreation.selectCharacter.gender')}</label>
                  <select
                    value={filterGender}
                    onChange={(e) => setFilterGender(e.target.value as any)}
                    className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="all">{t('storyCreation.selectCharacter.allGenders')}</option>
                    <option value="male">{t('storyCreation.selectCharacter.male')}</option>
                    <option value="female">{t('storyCreation.selectCharacter.female')}</option>
                    <option value="other">{t('storyCreation.selectCharacter.other')}</option>
                  </select>
                </div>

                {/* POV Filter */}
                <div className="space-y-1">
                  <label className="text-xs font-medium text-gray-700 dark:text-gray-300">{t('storyCreation.selectCharacter.pointOfView')}</label>
                  <select
                    value={filterPOV}
                    onChange={(e) => setFilterPOV(e.target.value as any)}
                    className="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="all">{t('storyCreation.selectCharacter.allPOV')}</option>
                    <option value="first-person">{t('storyCreation.selectCharacter.firstPerson')}</option>
                    <option value="second-person">{t('storyCreation.selectCharacter.secondPerson')}</option>
                    <option value="third-person">{t('storyCreation.selectCharacter.thirdPerson')}</option>
                  </select>
                </div>
              </div>

              {/* Tag Input */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-gray-700 dark:text-gray-300">{t('storyCreation.selectCharacter.filterByTags')}</label>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={14} />
                    <input
                      type="text"
                      placeholder={t('storyCreation.selectCharacter.addTagPlaceholder')}
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                      className="w-full pl-9 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                  <button
                    onClick={handleAddTag}
                    className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg transition-colors"
                  >
                    {t('storyCreation.selectCharacter.add')}
                  </button>
                </div>

                {/* Selected Tags */}
                {selectedTags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedTags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center gap-1 px-2 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200 text-xs rounded-full"
                      >
                        {tag}
                        <button
                          onClick={() => handleRemoveTag(tag)}
                          className="hover:text-purple-600 dark:hover:text-purple-300"
                        >
                          <X size={12} />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Sort Controls */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {filteredAndSortedCharacters.length === 1
                  ? t('storyCreation.selectCharacter.characterFound', { count: filteredAndSortedCharacters.length })
                  : t('storyCreation.selectCharacter.charactersFound', { count: filteredAndSortedCharacters.length })
                }
              </span>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">{t('storyCreation.selectCharacter.sortBy')}</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-2 py-1 text-sm text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="name">{t('storyCreation.selectCharacter.name')}</option>
                <option value="followers">{t('storyCreation.selectCharacter.fans')}</option>
                <option value="heatScore">{t('storyCreation.selectCharacter.heatScore')}</option>
                <option value="trendPercentage">{t('storyCreation.selectCharacter.trend')}</option>
                <option value="lastEditedAt">{t('storyCreation.selectCharacter.lastUpdated')}</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="p-1.5 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors"
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>
          </div>
        </div>

        {/* Character Display */}
        {viewMode === 'grid' ? (
          /* Grid View */
          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredAndSortedCharacters.map((character) => (
              <CharacterCard key={character.id} character={character} />
            ))}
          </div>
        ) : (
          /* List View */
          <div className="space-y-3">
            {filteredAndSortedCharacters.map((character) => (
              <CharacterListItem key={character.id} character={character} />
            ))}
          </div>
        )}

        {/* Empty States */}
        {filteredAndSortedCharacters.length === 0 && characters.length > 0 && (
          <div className="text-center py-16">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-purple-100 dark:border-purple-800/50 max-w-md mx-auto">
              <Search className="mx-auto h-16 w-16 text-purple-400 mb-4" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{t('storyCreation.selectCharacter.noMatchingCharacters')}</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {t('storyCreation.selectCharacter.noMatchingCharactersDesc')}
              </p>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setFilterStatus('all');
                  setFilterGender('all');
                  setFilterPOV('all');
                  setSelectedTags([]);
                }}
                className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-300 font-medium"
              >
                {t('storyCreation.selectCharacter.clearAllFilters')}
              </button>
            </div>
          </div>
        )}

        {characters.length === 0 && (
          <div className="text-center py-16">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-purple-100 dark:border-purple-800/50 max-w-md mx-auto">
              <Users className="mx-auto h-16 w-16 text-purple-400 mb-4" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{t('storyCreation.selectCharacter.noCharactersYet')}</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {t('storyCreation.selectCharacter.noCharactersYetDesc')}
              </p>
              <Link
                href={`/${lang}/create-character`}
                className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all duration-300 font-medium"
              >
                <Plus className="w-5 h-5" />
                {t('storyCreation.selectCharacter.createFirstCharacter')}
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateStorySelectCharacterPage; 