'use client';

import React from 'react';
import { Clock, Calendar, Target, CheckCircle, Flame } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface TaskProgressOverviewProps {
  dailyProgress: {
    completed: number;
    total: number;
    weeklyCompleted: number;
    weeklyTotal: number;
    monthlyCompleted: number;
    monthlyTotal: number;
  };
  streakData: {
    currentStreak: number;
    longestStreak: number;
    nextMilestone: number;
    daysUntilMilestone: number;
  };
  lang: string;
}

const TaskProgressOverview: React.FC<TaskProgressOverviewProps> = ({ 
  dailyProgress, 
  streakData, 
  lang 
}) => {
  const { t } = useTranslation(lang, 'translation');

  const dailyPercentage = (dailyProgress.completed / dailyProgress.total) * 100;
  const weeklyPercentage = (dailyProgress.weeklyCompleted / dailyProgress.weeklyTotal) * 100;
  const monthlyPercentage = (dailyProgress.monthlyCompleted / dailyProgress.monthlyTotal) * 100;

  const progressItems = [
    {
      icon: <Clock className="w-5 h-5 text-blue-500" />,
      label: t('tasks.overview.todayProgress'),
      completed: dailyProgress.completed,
      total: dailyProgress.total,
      percentage: dailyPercentage,
      color: 'blue'
    },
    {
      icon: <Calendar className="w-5 h-5 text-green-500" />,
      label: t('tasks.overview.weeklyProgress'),
      completed: dailyProgress.weeklyCompleted,
      total: dailyProgress.weeklyTotal,
      percentage: weeklyPercentage,
      color: 'green'
    },
    {
      icon: <Target className="w-5 h-5 text-purple-500" />,
      label: t('tasks.overview.monthlyProgress'),
      completed: dailyProgress.monthlyCompleted,
      total: dailyProgress.monthlyTotal,
      percentage: monthlyPercentage,
      color: 'purple'
    }
  ];

  const getProgressColor = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-500';
      case 'green': return 'bg-green-500';
      case 'purple': return 'bg-purple-500';
      default: return 'bg-primary';
    }
  };

  const getBackgroundColor = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-100 dark:bg-blue-900/30';
      case 'green': return 'bg-green-100 dark:bg-green-900/30';
      case 'purple': return 'bg-purple-100 dark:bg-purple-900/30';
      default: return 'bg-muted';
    }
  };

  return (
    <div className="bg-card border border-border rounded-lg p-6 theme-transition">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-foreground flex items-center gap-2">
          <CheckCircle className="w-5 h-5 text-primary" />
          Progress Overview
        </h2>
        <div className="flex items-center gap-2 text-orange-500">
          <Flame className="w-4 h-4" />
          <span className="text-sm font-medium">
            {t('tasks.overview.streakDays', { days: streakData.currentStreak })}
          </span>
        </div>
      </div>

      {/* Progress Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {progressItems.map((item, index) => (
          <div key={index} className="bg-background border border-border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              {item.icon}
              <span className="text-sm font-medium text-foreground">{item.label}</span>
            </div>
            
            <div className="mb-2">
              <div className="flex items-baseline gap-1 mb-1">
                <span className="text-2xl font-bold text-foreground">{item.completed}</span>
                <span className="text-sm text-foreground/70">/ {item.total}</span>
              </div>
              <span className="text-xs text-foreground/60">
                {t('tasks.overview.tasksCompleted', { 
                  completed: item.completed, 
                  total: item.total 
                })}
              </span>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-muted rounded-full h-2 mb-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${getProgressColor(item.color)}`}
                style={{ width: `${Math.min(item.percentage, 100)}%` }}
              />
            </div>

            <div className="text-right">
              <span className="text-xs font-medium text-foreground">
                {item.percentage.toFixed(0)}%
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Stats */}
      <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center">
          <div className="text-lg font-bold text-orange-500">
            {streakData.currentStreak}
          </div>
          <div className="text-xs text-foreground/70">Current Streak</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-green-500">
            {dailyProgress.completed}
          </div>
          <div className="text-xs text-foreground/70">Tasks Today</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-blue-500">
            {streakData.daysUntilMilestone}
          </div>
          <div className="text-xs text-foreground/70">Days to Milestone</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-purple-500">
            {((dailyPercentage + weeklyPercentage + monthlyPercentage) / 3).toFixed(0)}%
          </div>
          <div className="text-xs text-foreground/70">Overall Progress</div>
        </div>
      </div>

      {/* Motivational Message */}
      {dailyPercentage === 100 && (
        <div className="mt-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center gap-2 text-green-800 dark:text-green-200">
            <CheckCircle className="w-5 h-5" />
            <span className="font-medium">
              🎉 {t('tasks.messages.allTasksComplete')}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskProgressOverview; 