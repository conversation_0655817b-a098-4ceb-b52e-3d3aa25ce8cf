'use client';

import { useState, useEffect } from 'react';

export type Breakpoint = 'mobile' | 'tablet' | 'desktop' | 'ultrawide';

export interface ResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isUltrawide: boolean;
  breakpoint: Breakpoint;
  width: number;
}

const getBreakpoint = (width: number): Breakpoint => {
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet';
  if (width < 1536) return 'desktop';
  return 'ultrawide';
};

export const useResponsive = (): ResponsiveState => {
  const [state, setState] = useState<ResponsiveState>(() => {
    // Default to desktop for SSR
    const defaultWidth = 1024;
    const defaultBreakpoint = getBreakpoint(defaultWidth);
    
    return {
      width: defaultWidth,
      breakpoint: defaultBreakpoint,
      isMobile: defaultBreakpoint === 'mobile',
      isTablet: defaultBreakpoint === 'tablet',
      isDesktop: defaultBreakpoint === 'desktop',
      isUltrawide: defaultBreakpoint === 'ultrawide',
    };
  });

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      const breakpoint = getBreakpoint(width);
      
      setState({
        width,
        breakpoint,
        isMobile: breakpoint === 'mobile',
        isTablet: breakpoint === 'tablet',
        isDesktop: breakpoint === 'desktop',
        isUltrawide: breakpoint === 'ultrawide',
      });
    };

    // Set initial value
    updateBreakpoint();

    // Add event listener
    window.addEventListener('resize', updateBreakpoint);
    
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return state;
};

// Utility hook for common responsive patterns
export const useIsMobile = () => {
  const { isMobile } = useResponsive();
  return isMobile;
};

export const useIsDesktop = () => {
  const { isDesktop, isUltrawide } = useResponsive();
  return isDesktop || isUltrawide;
};
