'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Gift, Crown, Star, Sparkles, Lock, CheckCircle } from 'lucide-react';
import { useResponsive } from '@/hooks/useResponsive';
import { useTranslation } from '@/app/i18n/client';

interface RewardCard {
  day: number;
  reward: {
    type: 'currency' | 'cosmetic' | 'item' | 'exclusive';
    name: string;
    amount?: number;
    icon: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
  };
  claimed: boolean;
  available: boolean;
}

interface Track {
  type: 'free' | 'pass' | 'diamond' | 'metaverse';
  name: string;
  isUnlocked: boolean;
  cards: RewardCard[];
}

interface UnifiedTrackGridProps {
  tracks: Track[];
  currentLevel: number;
  onTrackPurchase: (trackType: 'free' | 'pass' | 'diamond' | 'metaverse') => void;
  lang: string;
}

const UnifiedTrackGrid: React.FC<UnifiedTrackGridProps> = ({
  tracks,
  currentLevel,
  onTrackPurchase,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const { breakpoint } = useResponsive();
  const [scrollPosition, setScrollPosition] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  
  const totalCards = 26;

  const getTrackConfig = (trackType: string) => {
    switch (trackType) {
      case 'free':
        return {
          name: t('journey.tracks.free'),
          gradient: 'from-green-400 to-emerald-500',
          bg: 'bg-green-50 dark:bg-green-900/20',
          text: 'text-green-600 dark:text-green-400',
          icon: Gift
        };
      case 'pass':
        return {
          name: t('journey.tracks.premium'),
          gradient: 'from-pink-400 to-rose-500',
          bg: 'bg-pink-50 dark:bg-pink-900/20',
          text: 'text-pink-600 dark:text-pink-400',
          icon: Crown
        };
      case 'diamond':
        return {
          name: t('journey.tracks.diamond'),
          gradient: 'from-blue-400 to-indigo-500',
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          text: 'text-blue-600 dark:text-blue-400',
          icon: Star
        };
      case 'metaverse':
        return {
          name: t('journey.signIn.metaverseTrack'),
          gradient: 'from-purple-400 to-violet-500',
          bg: 'bg-purple-50 dark:bg-purple-900/20',
          text: 'text-purple-600 dark:text-purple-400',
          icon: Sparkles
        };
      default:
        return {
          name: t('journey.tracks.free'),
          gradient: 'from-gray-400 to-gray-500',
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          text: 'text-gray-600 dark:text-gray-400',
          icon: Gift
        };
    }
  };

  const getRarityColors = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'from-gray-400 to-gray-500';
      case 'rare': return 'from-blue-400 to-blue-500';
      case 'epic': return 'from-purple-400 to-purple-500';
      case 'legendary': return 'from-yellow-400 to-orange-500';
      default: return 'from-gray-400 to-gray-500';
    }
  };

  // Handle scroll
  const handleScroll = (scrollLeft: number) => {
    const container = scrollContainerRef.current;
    if (container && container.scrollLeft !== scrollLeft) {
      container.scrollLeft = scrollLeft;
    }
    setScrollPosition(scrollLeft);
  };



  // Handle mouse drag
  const handleMouseDown = (e: React.MouseEvent) => {
    if (breakpoint === 'mobile') return;
    e.preventDefault();
    setIsDragging(true);
    setDragStart(e.clientX);
  };

  // Handle container scroll events
  const handleContainerScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const scrollLeft = e.currentTarget.scrollLeft;
    setScrollPosition(scrollLeft);
  };



  // Handle mouse events
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      e.preventDefault();
      const deltaX = dragStart - e.clientX;
      const newScrollLeft = Math.max(0, scrollPosition + deltaX);
      handleScroll(newScrollLeft);
      setDragStart(e.clientX);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragStart, scrollPosition]);

  const cardSize = breakpoint === 'mobile' ? 'w-20 h-24' : breakpoint === 'tablet' ? 'w-24 h-28' : 'w-28 h-32';

  return (
    <div className="relative rounded-2xl overflow-hidden backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 shadow-xl">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-pink-500/5 to-blue-500/5 opacity-50"></div>
      
      {/* Header */}
      <div className={`relative ${breakpoint === 'mobile' ? 'p-3' : 'p-4'} border-b border-white/10 dark:border-white/5`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className={`${breakpoint === 'mobile' ? 'text-base' : 'text-lg'} font-bold text-foreground`}>{t('journey.signIn.rewardTracks')}</h3>
                          <div className={`${breakpoint === 'mobile' ? 'text-xs' : 'text-sm'} text-foreground/60`}>
                {totalCards} {t('journey.signIn.rewards')}
              </div>
          </div>

        </div>
      </div>

      {/* Unified Grid with Single Scroll Container */}
      <div className="relative">
        <div 
          ref={scrollContainerRef}
          className={`overflow-x-auto transition-all duration-300 ${
            breakpoint === 'mobile' 
              ? 'scrollbar-hide cursor-default momentum-scroll' 
              : `scrollbar-thin scrollbar-track-transparent scrollbar-thumb-purple-500/30 hover:scrollbar-thumb-purple-500/50 ${
                  isDragging ? 'cursor-grabbing scale-[0.98]' : 'cursor-grab hover:scale-[1.01]'
                }`
          }`}
          onScroll={handleContainerScroll}
          onMouseDown={handleMouseDown}
          style={{ 
            scrollbarWidth: breakpoint === 'mobile' ? 'none' : 'thin',
            scrollBehavior: isDragging ? 'auto' : 'smooth',
            WebkitOverflowScrolling: 'touch'
          }}
        >
          <div className={`${breakpoint === 'mobile' ? 'p-2' : 'p-3'} min-w-max touch-manipulation`}>
            {/* Track Headers Row - 压缩高度 */}
            <div className="flex mb-2">
              <div className={`${breakpoint === 'mobile' ? 'w-16' : 'w-20'} flex-shrink-0`}></div>
              <div className={`flex ${breakpoint === 'mobile' ? 'gap-2' : 'gap-3'}`}>
                {Array.from({ length: totalCards }, (_, index) => (
                  <div key={index} className={`${
                    breakpoint === 'mobile' ? 'w-20 h-6' : breakpoint === 'tablet' ? 'w-24 h-7' : 'w-28 h-8'
                  } flex items-center justify-center`}>
                    <span className={`${breakpoint === 'mobile' ? 'text-xs' : 'text-sm'} font-bold text-foreground/70`}>
                      {index + 1}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Track Rows */}
            {tracks.map((track) => {
              const config = getTrackConfig(track.type);

              return (
                <div
                  key={track.type}
                  className={`flex items-center mb-2 last:mb-0 ${
                    !track.isUnlocked ? 'opacity-60' : ''
                  }`}
                >
                  {/* Track Name Column */}
                  <div className={`${breakpoint === 'mobile' ? 'w-16 p-2' : 'w-20 p-3'} ${config.bg} border border-white/10 dark:border-white/5 backdrop-blur-sm flex flex-col items-center justify-center rounded-lg flex-shrink-0`}>
                    <div className={`${breakpoint === 'mobile' ? 'w-6 h-6 mb-1' : 'w-8 h-8 mb-1'} bg-gradient-to-br ${config.gradient} rounded-lg flex items-center justify-center shadow-md relative`}>
                      <config.icon className={`${breakpoint === 'mobile' ? 'w-3 h-3' : 'w-4 h-4'} text-white drop-shadow-sm`} />
                      {!track.isUnlocked && (
                        <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center backdrop-blur-[1px]">
                          <Lock className="w-2 h-2 text-white/90" />
                        </div>
                      )}
                    </div>
                    <span className={`${breakpoint === 'mobile' ? 'text-xs' : 'text-sm'} font-semibold ${config.text} text-center`}>
                      {config.name}
                    </span>
                  </div>

                  {/* Track Cards */}
                  <div className={`flex ${breakpoint === 'mobile' ? 'gap-2 ml-2' : 'gap-3 ml-3'}`}>
                    {track.cards.map((card) => {
                      const rarityGradient = getRarityColors(card.reward.rarity);
                      
                      return (
                        <div
                          key={card.day}
                          className={`group/card relative flex-shrink-0 ${cardSize} rounded-xl border-2 transition-all duration-500 ${
                            !track.isUnlocked
                              ? 'bg-gray-200 dark:bg-gray-800 border-gray-300 dark:border-gray-700 opacity-70'
                              : breakpoint === 'mobile'
                              ? 'active:scale-95'
                              : card.claimed
                              ? `bg-gradient-to-br ${config.bg} border-green-300 dark:border-green-700 shadow-md hover:shadow-xl hover:shadow-green-500/20 hover:scale-110 hover:rotate-1 hover:brightness-110`
                              : card.available
                              ? `bg-gradient-to-br from-white/90 to-white/70 dark:from-gray-800/90 dark:to-gray-900/70 border-purple-300 dark:border-purple-700 shadow-lg hover:shadow-2xl hover:shadow-purple-500/30 animate-pulse hover:scale-110 hover:-rotate-1 hover:brightness-125`
                              : 'bg-white/50 dark:bg-black/50 border-white/30 dark:border-white/20 hover:bg-white/70 dark:hover:bg-black/70 hover:scale-105 hover:shadow-lg hover:brightness-110'
                          }`}
                        >
                          {/* Status indicator */}
                          {!track.isUnlocked ? (
                            <div className="absolute top-1 right-1">
                              <Lock className="w-3 h-3 text-gray-500" />
                            </div>
                          ) : card.claimed ? (
                            <div className="absolute top-1 right-1">
                              <CheckCircle className="w-3 h-3 text-green-500" fill="currentColor" />
                            </div>
                          ) : null}

                          {/* Reward content */}
                          <div className="flex flex-col items-center justify-center h-full p-2">
                            <div className={`${breakpoint === 'mobile' ? 'text-lg' : 'text-xl'} mb-1 transition-all duration-300 ${
                              !track.isUnlocked
                                ? 'grayscale'
                                : 'group-hover/card:scale-125 group-hover/card:drop-shadow-lg'
                            }`}>
                              {card.reward.icon}
                            </div>
                            <div className={`text-xs font-medium text-center leading-tight transition-all duration-300 ${
                              !track.isUnlocked
                                ? 'text-gray-500'
                                : 'text-foreground group-hover/card:text-opacity-90 group-hover/card:font-bold'
                            }`}>
                              {card.reward.amount && (
                                <div className="font-bold group-hover/card:text-purple-600 dark:group-hover/card:text-purple-400 transition-colors duration-300">{card.reward.amount}</div>
                              )}
                              <div className="truncate max-w-full">{card.reward.name}</div>
                            </div>
                          </div>

                          {/* Rarity border glow */}
                          {track.isUnlocked && (
                            <div className={`absolute inset-0 rounded-xl bg-gradient-to-r ${rarityGradient} opacity-20 group-hover/card:opacity-50 group-hover/card:blur-sm transition-all duration-500 pointer-events-none`}></div>
                          )}

                          {/* Enhanced shimmer effect for available cards */}
                          {track.isUnlocked && card.available && (
                            <div className="absolute inset-0 rounded-xl overflow-hidden pointer-events-none">
                              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full animate-shimmer"></div>
                            </div>
                          )}

                          {/* Breathing glow effect for unlocked cards */}
                          {track.isUnlocked && !card.claimed && (
                            <div className={`absolute inset-0 rounded-xl bg-gradient-to-r ${rarityGradient} opacity-0 group-hover/card:opacity-30 group-hover/card:animate-pulse transition-all duration-700 pointer-events-none`}></div>
                          )}

                          {/* Sparkle effect for claimed cards */}
                          {track.isUnlocked && card.claimed && (
                            <div className="absolute inset-0 rounded-xl opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 pointer-events-none">
                              <div className="absolute top-1 right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping"></div>
                              <div className="absolute bottom-1 left-1 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-300"></div>
                              <div className="absolute top-1/2 left-1/2 w-1.5 h-1.5 bg-blue-400 rounded-full animate-ping animation-delay-600"></div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>


    </div>
  );
};

export default UnifiedTrackGrid;
