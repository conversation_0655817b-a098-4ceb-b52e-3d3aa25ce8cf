import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import {
  CharacterFormData,
  SelectedFunction,
  Step,
  StoryboardChapter,
  AvatarCropData
} from '@/types/character';
import { FlashGenerationResult } from '@/types/character-creation';

const initialFormData: CharacterFormData = {
  // Basic information
  name: '',
  description: '',
  publicDescription: '',
  appearance: '',
  setting: '',

  // Image resources
  characterImage: null,
  avatar: null,
  avatarCrop: null,

  // Dialogue settings
  greetingMessage: '',
  personality: [],

  // Category information
  gender: '',
  faction: 'anime',

  // Publishing settings
  visibility: 'public',

  // Advanced settings
  backgroundStory: '',
  interactionStyleTags: '',
  voiceIds: '',
  initialMemoriesText: '',
  customPromptPrefix: '',

  // World book files
  knowledgeFiles: [],
};

const initialStoryboardChapters: StoryboardChapter[] = [
  { id: 1, title: 'Chapter 1: First Meeting', content: '', greetingAfter: '', updateGreeting: false },
  { id: 2, title: 'Chapter 2: Getting Familiar', content: '', greetingAfter: '', updateGreeting: false },
  { id: 3, title: 'Chapter 3: Close Bond', content: '', greetingAfter: '', updateGreeting: false }
];

export const useCharacterCreation = (lang: string) => {
  const router = useRouter();
  
  // Main state
  const [selectedFunction, setSelectedFunction] = useState<SelectedFunction>('flash');
  const [currentStep, setCurrentStep] = useState<Step>('essentials');
  const [formData, setFormData] = useState<CharacterFormData>(initialFormData);

  // Flash function state
  const [oneClickPrompt, setOneClickPrompt] = useState('');
  const [flashResult, setFlashResult] = useState<FlashGenerationResult | null>(null);
  const [regenerateCount, setRegenerateCount] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);

  // Storyboard state
  const [storyboardChapters, setStoryboardChapters] = useState<StoryboardChapter[]>(initialStoryboardChapters);
  const [storyConcept, setStoryConcept] = useState('');
  const [chapterCount, setChapterCount] = useState(3);

  // Image related state
  const [avatarPreview, setAvatarPreview] = useState<string>('');
  const [characterImagePreview, setCharacterImagePreview] = useState<string>('');
  const [originalImageFile, setOriginalImageFile] = useState<File | null>(null);
  const [originalImagePreview, setOriginalImagePreview] = useState<string>('');
  const [isAiGenerated, setIsAiGenerated] = useState(false);

  // Import related state
  const [importedCharacters, setImportedCharacters] = useState<any[]>([]);
  const [scriptFile, setScriptFile] = useState<File | null>(null);
  
  // 步骤导航
  const nextStep = useCallback(() => {
    const steps: Step[] = ['essentials', 'behavior', 'finalize'];
    const currentIndex = steps.findIndex(step => step === currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  }, [currentStep]);

  const prevStep = useCallback(() => {
    const steps: Step[] = ['essentials', 'behavior', 'finalize'];
    const currentIndex = steps.findIndex(step => step === currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  }, [currentStep]);

  // Step validation
  const isStepComplete = useCallback((step: Step): boolean => {
    switch (step) {
      case 'essentials':
        return !!(formData.characterImage && formData.avatar && formData.appearance.trim() &&
                 formData.name.trim() && formData.description.trim() && formData.gender.trim());
      case 'behavior':
        return true; // Second step is optional
      case 'finalize':
        return true; // Third step is optional
      default:
        return false;
    }
  }, [formData]);

  // Flash generation
  const handleOneClickGenerate = useCallback(async () => {
    setIsGenerating(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate AI generation

      const isRandom = !oneClickPrompt.trim();
      const prompt = isRandom ? 'random character' : oneClickPrompt;

      // Simulate generation result
      const mockResult: FlashGenerationResult = {
        name: isRandom ?
          ['Luna', 'Aria', 'Zara', 'Nova', 'Echo'][Math.floor(Math.random() * 5)] :
          (prompt.toLowerCase().includes('wizard') ? 'Merlin' :
           prompt.toLowerCase().includes('warrior') ? 'Valeria' : 'Aurora'),

        personality: isRandom ?
          'Mysterious and introspective on the surface, but deeply caring and protective of those close to them. Has a playful side that emerges around trusted friends.' :
          `Based on your description: ${prompt.slice(0, 100)}... Generated personality traits that complement the concept.`,

        appearance: isRandom ?
          'Tall and elegant with silver hair that seems to shimmer in moonlight. Deep violet eyes that hold ancient wisdom. Wears flowing robes with intricate star patterns.' :
          `Physical appearance generated from your concept: ${prompt.slice(0, 50)}... Detailed visual description.`,

        behavior: isRandom ?
          'Speaks in measured tones, often pausing to consider words carefully. Gestures gracefully when explaining complex concepts. Has a habit of stargazing when deep in thought.' :
          'Behavioral patterns and mannerisms that align with the character concept you provided.',

        knowledge: isRandom ?
          'Expert in celestial magic, ancient languages, and interdimensional theory. Knowledgeable about herbs, potions, and mystical artifacts. Well-versed in historical events across multiple realms.' :
          'Knowledge base and skills generated to support the character concept and background.',

        greetings: isRandom ?
          'Hello there! I sense something special about you... Are you perhaps interested in learning about the mysteries of the cosmos?' :
          `Greeting generated based on your concept: ${prompt.slice(0, 30)}...`,

        storyboard: isRandom ?
          'In a world where magic and mystery intertwine, your character encounters someone special during a chance meeting. What begins as curiosity quickly develops into a meaningful connection as they discover shared interests and complementary personalities. Through conversations and shared experiences, they learn to trust each other and find comfort in their growing bond. The story explores themes of friendship, understanding, and the magic that happens when two souls connect on a deeper level.' :
          `A ~100 token story based on your character concept: ${prompt.slice(0, 30)}... The narrative explores the development of a meaningful relationship between your character and someone they meet, showcasing their personality traits and creating an engaging backdrop for future interactions.`
      };

      setFlashResult(mockResult);
      toast.success('Character generated successfully! Review the preview below.');
    } catch (error) {
      console.error('Generation failed:', error);
      toast.error('Generation failed, please try again');
    } finally {
      setIsGenerating(false);
    }
  }, [oneClickPrompt]);

  // Avatar crop complete
  const handleAvatarCropComplete = useCallback((croppedImage: File, cropData: AvatarCropData) => {
    const avatarUrl = URL.createObjectURL(croppedImage);
    setAvatarPreview(avatarUrl);
    setFormData(prev => ({
      ...prev,
      avatar: croppedImage,
      avatarCrop: cropData
    }));
    toast.success('Avatar set successfully!');
  }, []);

  // Character image crop complete
  const handleCharacterImageCropComplete = useCallback((croppedImage: File) => {
    const imageUrl = URL.createObjectURL(croppedImage);
    setCharacterImagePreview(imageUrl);
    setFormData(prev => ({
      ...prev,
      characterImage: croppedImage
    }));
    toast.success('Character image cropped successfully!');
  }, []);

  // Form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const submitData = new FormData();

      // Required parameters
      submitData.append('name', formData.name);
      submitData.append('description', formData.description);
      submitData.append('public_description', formData.publicDescription);
      submitData.append('setting', formData.setting || `${formData.name} lives in a world full of stories...`);
      submitData.append('greeting_message', formData.greetingMessage);
      submitData.append('gender', formData.gender);

      // Opening message templates (required)
      const openingTemplates = JSON.stringify([{
        template_id: 'default',
        content: formData.greetingMessage,
        priority: 1,
        conditions: null
      }]);
      submitData.append('opening_message_templates', openingTemplates);

      // Personality tags (required)
      const personalityTags = formData.personality.length > 0
        ? JSON.stringify(formData.personality.map(trait => ({ tag_name: trait, weight: 0.8 })))
        : JSON.stringify([{ tag_name: 'friendly', weight: 0.8 }]);
      submitData.append('personality_tags', personalityTags);

      // Content tags (required) - 映射前端的交互风格标签到后端的内容标签
      submitData.append('content_tags', formData.interactionStyleTags || 'daily,warm');

      // Interaction style tags (optional) - 保持原有的交互风格标签
      if (formData.interactionStyleTags) {
        submitData.append('interaction_style_tags', formData.interactionStyleTags);
      }

      // Image files (required)
      if (formData.avatar) {
        submitData.append('avatar', formData.avatar);
      }

      // Character image (required)
      if (formData.characterImage) {
        submitData.append('image', formData.characterImage);
      }

      // Avatar crop info (optional)
      if (formData.avatarCrop) {
        submitData.append('avatar_crop_info', JSON.stringify(formData.avatarCrop));
      }

      // World book files (optional)
      if (formData.knowledgeFiles.length > 0) {
        formData.knowledgeFiles.forEach((file) => {
          submitData.append('knowledge_files', file);
        });
      }

      // Optional parameters
      if (formData.visibility) {
        submitData.append('visibility', formData.visibility);
      }

      // Advanced settings (optional)
      if (formData.backgroundStory) {
        submitData.append('background_story', formData.backgroundStory);
      }
      if (formData.interactionStyleTags) {
        submitData.append('interaction_style_tags', formData.interactionStyleTags);
      }
      if (formData.voiceIds) {
        submitData.append('voice_ids', formData.voiceIds);
      }
      if (formData.initialMemoriesText) {
        submitData.append('initial_memories_text', formData.initialMemoriesText);
      }
      if (formData.customPromptPrefix) {
        submitData.append('custom_prompt_prefix', formData.customPromptPrefix);
      }

      // 前端新增字段 - 需要后端支持
      if (formData.appearance) {
        submitData.append('appearance', formData.appearance);
      }
      if (formData.faction) {
        submitData.append('faction', formData.faction);
      }

      console.log('Submitting character data to /character/create');

      // TODO: Call actual API
      // const response = await fetch('/api/character/create', {
      //   method: 'POST',
      //   body: submitData
      // });
      // const result = await response.json();
      // router.push(`/${lang}/character/${result.character_id}`);

      // Temporary simulation of success
      router.push(`/${lang}/character/new-character-id`);
      
    } catch (error) {
      console.error('Failed to create character:', error);
      toast.error('Failed to create character, please try again');
    }
  }, [formData, router, lang]);

  return {
    // State
    selectedFunction,
    currentStep,
    formData,
    oneClickPrompt,
    flashResult,
    regenerateCount,
    isGenerating,
    storyboardChapters,
    storyConcept,
    chapterCount,
    avatarPreview,
    characterImagePreview,
    originalImageFile,
    originalImagePreview,
    isAiGenerated,
    importedCharacters,
    scriptFile,

    // Setters
    setSelectedFunction,
    setCurrentStep,
    setFormData,
    setOneClickPrompt,
    setFlashResult,
    setRegenerateCount,
    setStoryboardChapters,
    setStoryConcept,
    setChapterCount,
    setAvatarPreview,
    setCharacterImagePreview,
    setOriginalImageFile,
    setOriginalImagePreview,
    setIsAiGenerated,
    setImportedCharacters,
    setScriptFile,

    // Methods
    nextStep,
    prevStep,
    isStepComplete,
    handleOneClickGenerate,
    handleAvatarCropComplete,
    handleCharacterImageCropComplete,
    handleSubmit,
  };
};