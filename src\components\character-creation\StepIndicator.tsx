'use client';

import React from 'react';
import { Check } from 'lucide-react';
import { Step, StepConfig } from '@/types/character';

interface StepIndicatorProps {
  steps: StepConfig[];
  currentStep: Step;
  onStepChange: (step: Step) => void;
  isStepComplete: (step: Step) => boolean;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  steps,
  currentStep,
  onStepChange,
  isStepComplete
}) => {
  const stepIndex = steps.findIndex(step => step.id === currentStep);

  return (
    <div className="bg-white dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl border border-gray-200 dark:border-gray-700 p-4 md:p-6">
      {/* Mobile step progress bar */}
      <div className="md:hidden mb-4">
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
          <span>Step {stepIndex + 1} / {steps.length}</span>
          <span>{Math.round(((stepIndex + 1) / steps.length) * 100)}%</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((stepIndex + 1) / steps.length) * 100}%` }}
          />
        </div>
        <p className="text-center text-lg font-semibold text-gray-900 dark:text-white mt-3">
          {steps[stepIndex].label}
        </p>
        <p className="text-center text-sm text-gray-600 dark:text-gray-400">
          {steps[stepIndex].description}
        </p>
      </div>

      {/* Desktop step indicator */}
      <div className="hidden md:flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className="flex items-center">
              <button
                onClick={() => onStepChange(step.id)}
                disabled={index > stepIndex && !isStepComplete(steps[index - 1]?.id)}
                className={`flex items-center justify-center w-10 h-10 rounded-full transition-all disabled:cursor-not-allowed ${
                  currentStep === step.id
                    ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg scale-110'
                    : isStepComplete(step.id)
                    ? 'bg-green-500 text-white hover:bg-green-600'
                    : index <= stepIndex
                    ? 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-400 dark:hover:bg-gray-500'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500'
                }`}
              >
                {isStepComplete(step.id) && currentStep !== step.id ? (
                  <Check size={16} />
                ) : (
                  <step.icon size={16} />
                )}
              </button>
              <div className="ml-3">
                <p className={`text-sm font-semibold transition-colors ${
                  currentStep === step.id ? 'text-indigo-600 dark:text-indigo-400' : 'text-gray-600 dark:text-gray-400'
                }`}>
                  {step.label}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">{step.description}</p>
              </div>
            </div>
            {index < steps.length - 1 && (
              <div className={`w-12 h-0.5 mx-4 transition-colors ${
                stepIndex > index ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'
              }`} />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default StepIndicator; 