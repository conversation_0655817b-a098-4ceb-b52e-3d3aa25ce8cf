-- =====================================================
-- Social and UGC Content Data Storage Schema
-- Supporting social interactions, moments, and UGC
-- =====================================================

-- 1. Enhanced Moments Table Extension
-- Extend existing moments table for rich UGC content
ALTER TABLE moments 
ADD COLUMN IF NOT EXISTS moment_type VARCHAR(30) DEFAULT 'general' CHECK (moment_type IN (
    'general', 'milestone', 'achievement', 'story_highlight', 'character_showcase', 'memory_share'
)),
ADD COLUMN IF NOT EXISTS mood VARCHAR(50),
ADD COLUMN IF NOT EXISTS visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'followers', 'private')),
ADD COLUMN IF NOT EXISTS media_urls JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS location_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS interaction_stats JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS content_metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS featured_until TIMESTAMP;

-- 2. Social Interactions Table
-- Comprehensive social interaction tracking
CREATE TABLE social_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    target_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Interaction Type and Target
    interaction_type VARCHAR(30) NOT NULL CHECK (interaction_type IN (
        'like', 'unlike', 'share', 'comment', 'follow', 'unfollow', 
        'bookmark', 'report', 'block', 'mention', 'react'
    )),
    
    target_type VARCHAR(30) NOT NULL CHECK (target_type IN (
        'moment', 'character', 'story', 'comment', 'user', 'memory_capsule'
    )),
    target_id UUID NOT NULL,
    
    -- Interaction Data
    interaction_data JSONB DEFAULT '{}',
    -- Structure varies by type:
    -- like: {emotion?: string}
    -- comment: {content: string, parent_comment_id?: uuid}
    -- share: {platform?: string, message?: string}
    -- react: {reaction_type: string, intensity?: number}
    
    -- Context
    context_data JSONB DEFAULT '{}',
    -- Structure: {
    --   source_page?: string,
    --   referrer?: string,
    --   session_id?: string,
    --   device_type?: string
    -- }
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Comments Table
-- Detailed comment system for moments and other content
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Comment Target
    target_type VARCHAR(30) NOT NULL CHECK (target_type IN (
        'moment', 'character', 'story', 'memory_capsule', 'comment'
    )),
    target_id UUID NOT NULL,
    
    -- Comment Content
    content TEXT NOT NULL,
    content_type VARCHAR(20) DEFAULT 'text' CHECK (content_type IN ('text', 'rich_text', 'media')),
    
    -- Threading
    parent_comment_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    thread_depth INTEGER DEFAULT 0,
    
    -- Interaction Stats
    likes_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    
    -- Moderation
    is_edited BOOLEAN DEFAULT false,
    edited_at TIMESTAMP,
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP,
    moderation_status VARCHAR(20) DEFAULT 'approved' CHECK (moderation_status IN (
        'pending', 'approved', 'rejected', 'flagged'
    )),
    
    -- Metadata
    comment_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. Content Moderation Table
-- Safety and quality control system
CREATE TABLE content_moderation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Content Information
    content_type VARCHAR(30) NOT NULL CHECK (content_type IN (
        'moment', 'comment', 'character', 'story', 'user_profile', 'memory_capsule'
    )),
    content_id UUID NOT NULL,
    content_owner_id UUID NOT NULL REFERENCES users(id),
    
    -- Moderation Details
    moderation_type VARCHAR(30) NOT NULL CHECK (moderation_type IN (
        'automated_scan', 'user_report', 'manual_review', 'appeal'
    )),
    
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'approved', 'rejected', 'requires_review', 'appealed'
    )),
    
    -- Violation Information
    violation_types TEXT[] DEFAULT '{}',
    -- Examples: ['inappropriate_content', 'spam', 'harassment', 'copyright']
    
    severity_level VARCHAR(20) DEFAULT 'low' CHECK (severity_level IN (
        'low', 'medium', 'high', 'critical'
    )),
    
    -- Actions Taken
    actions_taken JSONB DEFAULT '[]',
    -- Array of: {action: string, timestamp: datetime, moderator_id?: uuid}
    
    -- Review Information
    reviewer_id UUID REFERENCES users(id),
    review_notes TEXT,
    automated_flags JSONB DEFAULT '{}',
    
    -- Reporter Information (for user reports)
    reporter_id UUID REFERENCES users(id),
    report_reason VARCHAR(100),
    report_details TEXT,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP
);

-- 5. Trending Content Table
-- Algorithm-driven content discovery
CREATE TABLE trending_content (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Content Information
    content_type VARCHAR(30) NOT NULL CHECK (content_type IN (
        'moment', 'character', 'story', 'user', 'hashtag'
    )),
    content_id UUID NOT NULL,
    
    -- Trending Metrics
    trend_score DECIMAL(10,4) NOT NULL DEFAULT 0.0000,
    engagement_rate DECIMAL(8,4) DEFAULT 0.0000,
    velocity_score DECIMAL(8,4) DEFAULT 0.0000, -- Rate of engagement increase
    
    -- Time-based Metrics
    views_1h INTEGER DEFAULT 0,
    views_24h INTEGER DEFAULT 0,
    views_7d INTEGER DEFAULT 0,
    
    interactions_1h INTEGER DEFAULT 0,
    interactions_24h INTEGER DEFAULT 0,
    interactions_7d INTEGER DEFAULT 0,
    
    -- Trending Categories
    trending_category VARCHAR(50) NOT NULL CHECK (trending_category IN (
        'viral', 'rising', 'hot', 'new', 'controversial', 'quality'
    )),
    
    -- Geographic and Demographic Data
    trending_regions TEXT[] DEFAULT '{}',
    target_demographics JSONB DEFAULT '{}',
    
    -- Algorithm Data
    algorithm_version VARCHAR(20) DEFAULT 'v1.0',
    calculation_factors JSONB DEFAULT '{}',
    -- Structure: {
    --   engagement_weight: number,
    --   recency_weight: number,
    --   quality_weight: number,
    --   diversity_weight: number
    -- }
    
    -- Status and Timing
    is_active BOOLEAN DEFAULT true,
    trending_since TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    
    -- Metadata
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. User Social Stats Table
-- Aggregated social metrics for users
CREATE TABLE user_social_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Follower Statistics
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    mutual_followers_count INTEGER DEFAULT 0,
    
    -- Content Statistics
    moments_count INTEGER DEFAULT 0,
    moments_likes_received INTEGER DEFAULT 0,
    moments_shares_received INTEGER DEFAULT 0,
    moments_comments_received INTEGER DEFAULT 0,
    
    -- Engagement Statistics
    total_likes_given INTEGER DEFAULT 0,
    total_comments_made INTEGER DEFAULT 0,
    total_shares_made INTEGER DEFAULT 0,
    
    -- Quality Metrics
    avg_moment_engagement DECIMAL(8,4) DEFAULT 0.0000,
    content_quality_score DECIMAL(6,3) DEFAULT 0.000,
    influence_score DECIMAL(8,4) DEFAULT 0.0000,
    
    -- Activity Metrics
    daily_active_streak INTEGER DEFAULT 0,
    weekly_active_streak INTEGER DEFAULT 0,
    last_activity_date DATE,
    
    -- Trending and Recognition
    trending_moments_count INTEGER DEFAULT 0,
    featured_moments_count INTEGER DEFAULT 0,
    viral_moments_count INTEGER DEFAULT 0,
    
    -- Time-based Metrics
    stats_period_start DATE NOT NULL,
    stats_period_end DATE NOT NULL,
    
    -- Metadata
    last_calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, stats_period_start)
);

-- 7. Hashtags Table
-- Hashtag system for content discovery
CREATE TABLE hashtags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Hashtag Information
    tag VARCHAR(100) UNIQUE NOT NULL,
    normalized_tag VARCHAR(100) NOT NULL, -- lowercase, no special chars
    
    -- Usage Statistics
    usage_count INTEGER DEFAULT 0,
    unique_users_count INTEGER DEFAULT 0,
    
    -- Trending Information
    is_trending BOOLEAN DEFAULT false,
    trending_score DECIMAL(8,4) DEFAULT 0.0000,
    trending_since TIMESTAMP,
    
    -- Category and Metadata
    category VARCHAR(50),
    language_code VARCHAR(10),
    
    -- Moderation
    is_blocked BOOLEAN DEFAULT false,
    blocked_reason TEXT,
    
    -- Metadata
    first_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. Content Hashtags Junction Table
-- Many-to-many relationship between content and hashtags
CREATE TABLE content_hashtags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Content Information
    content_type VARCHAR(30) NOT NULL CHECK (content_type IN (
        'moment', 'character', 'story', 'comment'
    )),
    content_id UUID NOT NULL,
    
    -- Hashtag
    hashtag_id UUID NOT NULL REFERENCES hashtags(id) ON DELETE CASCADE,
    
    -- Context
    position_in_content INTEGER, -- Position where hashtag appears
    context_snippet TEXT, -- Surrounding text for context
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(content_type, content_id, hashtag_id)
);

-- 9. Indexes for Performance Optimization
CREATE INDEX idx_social_interactions_user ON social_interactions(user_id);
CREATE INDEX idx_social_interactions_target ON social_interactions(target_type, target_id);
CREATE INDEX idx_social_interactions_type ON social_interactions(interaction_type);
CREATE INDEX idx_social_interactions_created ON social_interactions(created_at);

CREATE INDEX idx_comments_target ON comments(target_type, target_id);
CREATE INDEX idx_comments_user ON comments(user_id);
CREATE INDEX idx_comments_parent ON comments(parent_comment_id);
CREATE INDEX idx_comments_moderation ON comments(moderation_status);

CREATE INDEX idx_content_moderation_content ON content_moderation(content_type, content_id);
CREATE INDEX idx_content_moderation_status ON content_moderation(status);
CREATE INDEX idx_content_moderation_reviewer ON content_moderation(reviewer_id);
CREATE INDEX idx_content_moderation_reporter ON content_moderation(reporter_id);

CREATE INDEX idx_trending_content_type_score ON trending_content(content_type, trend_score DESC);
CREATE INDEX idx_trending_content_category ON trending_content(trending_category);
CREATE INDEX idx_trending_content_active ON trending_content(is_active) WHERE is_active = true;

CREATE INDEX idx_user_social_stats_user ON user_social_stats(user_id);
CREATE INDEX idx_user_social_stats_period ON user_social_stats(stats_period_start, stats_period_end);
CREATE INDEX idx_user_social_stats_influence ON user_social_stats(influence_score DESC);

CREATE INDEX idx_hashtags_tag ON hashtags(normalized_tag);
CREATE INDEX idx_hashtags_trending ON hashtags(is_trending) WHERE is_trending = true;
CREATE INDEX idx_hashtags_usage ON hashtags(usage_count DESC);

CREATE INDEX idx_content_hashtags_content ON content_hashtags(content_type, content_id);
CREATE INDEX idx_content_hashtags_hashtag ON content_hashtags(hashtag_id);

-- GIN indexes for JSONB columns
CREATE INDEX idx_moments_interaction_stats_gin ON moments USING GIN (interaction_stats);
CREATE INDEX idx_moments_content_metadata_gin ON moments USING GIN (content_metadata);
CREATE INDEX idx_social_interactions_data_gin ON social_interactions USING GIN (interaction_data);
CREATE INDEX idx_comments_metadata_gin ON comments USING GIN (comment_metadata);
CREATE INDEX idx_content_moderation_actions_gin ON content_moderation USING GIN (actions_taken);
CREATE INDEX idx_trending_content_factors_gin ON trending_content USING GIN (calculation_factors);

-- 10. Triggers for Updated Timestamps
CREATE TRIGGER social_interactions_updated_at 
BEFORE UPDATE ON social_interactions 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER comments_updated_at 
BEFORE UPDATE ON comments 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER content_moderation_updated_at 
BEFORE UPDATE ON content_moderation 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER user_social_stats_updated_at 
BEFORE UPDATE ON user_social_stats 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMENT ON TABLE social_interactions IS 'Comprehensive social interaction tracking for all content types';
COMMENT ON TABLE comments IS 'Detailed comment system with threading and moderation';
COMMENT ON TABLE content_moderation IS 'Safety and quality control system for user-generated content';
COMMENT ON TABLE trending_content IS 'Algorithm-driven content discovery and trending system';
COMMENT ON TABLE user_social_stats IS 'Aggregated social metrics for users and influence scoring';
COMMENT ON TABLE hashtags IS 'Hashtag system for content discovery and categorization';
COMMENT ON TABLE content_hashtags IS 'Many-to-many relationship between content and hashtags';
