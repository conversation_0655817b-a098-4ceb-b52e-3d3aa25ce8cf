'use client';

import { useTranslation } from '@/app/i18n/client';
import { Search, Users, BookOpen, User, Brain } from 'lucide-react';

export type SearchTab = 'all' | 'characters' | 'stories' | 'users' | 'memories';

interface SearchTabsProps {
  lang: string;
  activeTab: SearchTab;
  onTabChange: (tab: SearchTab) => void;
  resultCounts?: {
    all?: number;
    characters?: number;
    stories?: number;
    users?: number;
    memories?: number;
  };
}

const SearchTabs: React.FC<SearchTabsProps> = ({
  lang,
  activeTab,
  onTabChange,
  resultCounts = {}
}) => {
  const { t } = useTranslation(lang, 'translation');

  const tabs = [
    {
      id: 'all' as SearchTab,
      label: t('search.tabs.all'),
      icon: Search,
      count: resultCounts.all
    },
    {
      id: 'characters' as SearchTab,
      label: t('search.tabs.characters'),
      icon: Users,
      count: resultCounts.characters
    },
    {
      id: 'stories' as SearchTab,
      label: t('search.tabs.stories'),
      icon: BookOpen,
      count: resultCounts.stories
    },
    {
      id: 'users' as SearchTab,
      label: t('search.tabs.users'),
      icon: User,
      count: resultCounts.users
    },
    {
      id: 'memories' as SearchTab,
      label: t('search.tabs.memories'),
      icon: Brain,
      count: resultCounts.memories
    }
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-1 shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex overflow-x-auto">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          const isActive = activeTab === tab.id;
          
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap ${
                isActive
                  ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <Icon className="w-4 h-4" />
              {tab.label}
              {typeof tab.count === 'number' && tab.count > 0 && (
                <span className={`ml-1 px-2 py-0.5 rounded-full text-xs font-medium ${
                  isActive
                    ? 'bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                }`}>
                  {tab.count > 999 ? '999+' : tab.count}
                </span>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default SearchTabs; 