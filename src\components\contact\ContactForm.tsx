'use client';

import React, { useState } from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Send, Loader2 } from 'lucide-react';
import ContactTypeSelector from './ContactTypeSelector';

interface ContactFormProps {
  onSubmit: (formData: ContactFormData) => Promise<void>;
  lang: string;
}

export interface ContactFormData {
  type: string;
  email: string;
  title: string;
  content: string;
}

const ContactForm: React.FC<ContactFormProps> = ({ onSubmit, lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [formData, setFormData] = useState<ContactFormData>({
    type: 'reportBug',
    email: '',
    title: '',
    content: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<ContactFormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<ContactFormData> = {};
    
    // Email validation (optional but must be valid if provided)
    if (formData.email.trim() && !isValidEmail(formData.email)) {
      newErrors.email = t('contact.form.email.invalid');
    }
    
    if (!formData.title.trim()) {
      newErrors.title = t('contact.form.validation.titleRequired');
    }
    
    if (!formData.content.trim()) {
      newErrors.content = t('contact.form.validation.contentRequired');
    } else if (formData.content.length > 1000) {
      newErrors.content = t('contact.form.validation.contentTooLong');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name as keyof ContactFormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleTypeChange = (type: string) => {
    setFormData(prev => ({
      ...prev,
      type
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Contact Type Selector */}
      <ContactTypeSelector
        selectedType={formData.type}
        onTypeChange={handleTypeChange}
        lang={lang}
      />

      {/* Email Input */}
      <div>
        <label htmlFor="email" className="block text-sm font-medium mb-2 text-foreground">
          {t('contact.form.email.label')}
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleInputChange}
          className={`w-full px-4 py-3 border rounded-lg bg-background text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all ${
            errors.email ? 'border-destructive' : 'border-border'
          }`}
          placeholder={t('contact.form.email.placeholder')}
          disabled={isSubmitting}
        />
        {errors.email && (
          <p className="mt-1 text-sm text-destructive">{errors.email}</p>
        )}
      </div>

      {/* Title Input */}
      <div>
        <label htmlFor="title" className="block text-sm font-medium mb-2 text-foreground">
          {t('contact.form.title.label')}
        </label>
        <input
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={handleInputChange}
          className={`w-full px-4 py-3 border rounded-lg bg-background text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all ${
            errors.title ? 'border-destructive' : 'border-border'
          }`}
          placeholder={t('contact.form.title.placeholder')}
          disabled={isSubmitting}
        />
        {errors.title && (
          <p className="mt-1 text-sm text-destructive">{errors.title}</p>
        )}
      </div>

      {/* Content Textarea */}
      <div>
        <label htmlFor="content" className="block text-sm font-medium mb-2 text-foreground">
          {t('contact.form.content.label')}
        </label>
        <textarea
          id="content"
          name="content"
          value={formData.content}
          onChange={handleInputChange}
          maxLength={1000}
          rows={8}
          className={`w-full px-4 py-3 border rounded-lg bg-background text-foreground placeholder-foreground/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all resize-none ${
            errors.content ? 'border-destructive' : 'border-border'
          }`}
          placeholder={t('contact.form.content.placeholder')}
          disabled={isSubmitting}
        />
        <div className="flex justify-between items-center mt-2">
          {errors.content && (
            <p className="text-sm text-destructive">{errors.content}</p>
          )}
          <div className="text-sm text-foreground/50 ml-auto">
            {t('contact.form.characterCount', { count: formData.content.length })}
          </div>
        </div>
      </div>

      {/* Email Notice */}
      {!formData.email.trim() && (
        <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
          <p className="text-sm text-orange-700 dark:text-orange-300">
            💡 {t('contact.form.email.required')}
          </p>
        </div>
      )}

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isSubmitting || !formData.title || !formData.content}
        className="w-full bg-primary text-primary-foreground py-3 px-6 rounded-lg hover:bg-primary/90 disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center space-x-2"
      >
        {isSubmitting ? (
          <>
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>{t('contact.form.submitting')}</span>
          </>
        ) : (
          <>
            <Send className="w-5 h-5" />
            <span>{t('contact.form.submit')}</span>
          </>
        )}
      </button>
    </form>
  );
};

export default ContactForm; 