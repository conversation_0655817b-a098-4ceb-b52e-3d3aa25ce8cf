'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { <PERSON><PERSON><PERSON><PERSON>, Brain, Sparkles, Heart, Clock, Volume2, Zap, Cpu } from 'lucide-react';
import SettingItem from './SettingItem';
import ToggleSwitch from './ToggleSwitch';
import <PERSON><PERSON>ield from './SelectField';
import type { SettingsComponentProps } from '@/types/settings';

const AIInteractionSettings: React.FC<SettingsComponentProps> = ({
  settings,
  updateSetting,
  lang,
  user,
  hasUnsavedChanges,
  isPremiumUser
}) => {
  const { t } = useTranslation(lang, 'translation');

  const responseSpeedOptions = [
    { value: 'fast', label: t('settings.categories.aiInteraction.responseSpeedOptions.fast') },
    { value: 'standard', label: t('settings.categories.aiInteraction.responseSpeedOptions.standard') },
    { value: 'detailed', label: t('settings.categories.aiInteraction.responseSpeedOptions.detailed') }
  ];

  const emotionalIntensityOptions = [
    { value: 'subtle', label: t('settings.categories.aiInteraction.emotionalIntensityOptions.subtle') },
    { value: 'moderate', label: t('settings.categories.aiInteraction.emotionalIntensityOptions.moderate') },
    { value: 'rich', label: t('settings.categories.aiInteraction.emotionalIntensityOptions.rich') }
  ];

  const memoryCapacityOptions = [
    { value: 'basic', label: t('settings.categories.aiInteraction.memoryCapacityOptions.basic') },
    { value: 'enhanced', label: t('settings.categories.aiInteraction.memoryCapacityOptions.enhanced') },
    { value: 'premium', label: t('settings.categories.aiInteraction.memoryCapacityOptions.premium'), isPremium: true }
  ];

  const modelOptions = [
    { value: 'standard', label: t('settings.categories.aiInteraction.preferredModelOptions.standard') },
    { value: 'gemini_2_5_flash', label: t('settings.categories.aiInteraction.preferredModelOptions.gemini_2_5_flash') },
    { value: 'gemini_2_5_pro', label: t('settings.categories.aiInteraction.preferredModelOptions.gemini_2_5_pro'), isPremium: true }
  ];

  const contextAwarenessOptions = [
    { value: 'basic', label: t('settings.categories.aiInteraction.contextAwarenessOptions.basic') },
    { value: 'enhanced', label: t('settings.categories.aiInteraction.contextAwarenessOptions.enhanced') },
    { value: 'deep', label: t('settings.categories.aiInteraction.contextAwarenessOptions.deep'), isPremium: true }
  ];

  const voicePreferenceOptions = [
    { value: 'text_only', label: t('settings.categories.aiInteraction.voicePreferenceOptions.text_only') },
    { value: 'voice_enabled', label: t('settings.categories.aiInteraction.voicePreferenceOptions.voice_enabled') },
    { value: 'voice_preferred', label: t('settings.categories.aiInteraction.voicePreferenceOptions.voice_preferred'), isPremium: true }
  ];

  const responseLengthOptions = [
    { value: 'concise', label: t('settings.categories.aiInteraction.responseLengthOptions.concise') },
    { value: 'balanced', label: t('settings.categories.aiInteraction.responseLengthOptions.balanced') },
    { value: 'detailed', label: t('settings.categories.aiInteraction.responseLengthOptions.detailed') }
  ];

  const creativityLevelOptions = [
    { value: 'conservative', label: t('settings.categories.aiInteraction.creativityLevelOptions.conservative') },
    { value: 'balanced', label: t('settings.categories.aiInteraction.creativityLevelOptions.balanced') },
    { value: 'creative', label: t('settings.categories.aiInteraction.creativityLevelOptions.creative') }
  ];

  return (
    <div className="space-y-6">
      {/* Response Settings */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <MessageCircle className="w-4 h-4" />
          Response Settings
        </h4>

        <SettingItem
          label={t('settings.categories.aiInteraction.responseSpeed')}
          description={t('settings.categories.aiInteraction.responseSpeedDesc')}
        >
          <SelectField
            value={settings.aiInteraction.responseSpeed}
            onChange={(value) => updateSetting('aiInteraction.responseSpeed', value)}
            options={responseSpeedOptions}
            className="w-48"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.aiInteraction.responseLength')}
          description={t('settings.categories.aiInteraction.responseLengthDesc')}
        >
          <SelectField
            value={settings.aiInteraction.responseLength}
            onChange={(value) => updateSetting('aiInteraction.responseLength', value)}
            options={responseLengthOptions}
            className="w-48"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.aiInteraction.creativityLevel')}
          description={t('settings.categories.aiInteraction.creativityLevelDesc')}
        >
          <SelectField
            value={settings.aiInteraction.creativityLevel}
            onChange={(value) => updateSetting('aiInteraction.creativityLevel', value)}
            options={creativityLevelOptions}
            className="w-48"
          />
        </SettingItem>
      </div>

      {/* Emotional Intelligence */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Heart className="w-4 h-4" />
          Emotional Intelligence
        </h4>

        <SettingItem
          label={t('settings.categories.aiInteraction.emotionalIntensity')}
          description={t('settings.categories.aiInteraction.emotionalIntensityDesc')}
        >
          <SelectField
            value={settings.aiInteraction.emotionalIntensity}
            onChange={(value) => updateSetting('aiInteraction.emotionalIntensity', value)}
            options={emotionalIntensityOptions}
            className="w-48"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.aiInteraction.personalityAdaptation')}
          description={t('settings.categories.aiInteraction.personalityAdaptationDesc')}
        >
          <ToggleSwitch
            checked={settings.aiInteraction.personalityAdaptation}
            onChange={(checked) => updateSetting('aiInteraction.personalityAdaptation', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.aiInteraction.bondingNotifications')}
          description={t('settings.categories.aiInteraction.bondingNotificationsDesc')}
        >
          <ToggleSwitch
            checked={settings.aiInteraction.bondingNotifications}
            onChange={(checked) => updateSetting('aiInteraction.bondingNotifications', checked)}
          />
        </SettingItem>
      </div>

      {/* Memory & Context */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Brain className="w-4 h-4" />
          Memory & Context
        </h4>

        <SettingItem
          label={t('settings.categories.aiInteraction.memoryCapacity')}
          description={t('settings.categories.aiInteraction.memoryCapacityDesc')}
        >
          <SelectField
            value={settings.aiInteraction.memoryCapacityPriority}
            onChange={(value) => updateSetting('aiInteraction.memoryCapacityPriority', value)}
            options={memoryCapacityOptions}
            className="w-48"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.aiInteraction.contextAwareness')}
          description={t('settings.categories.aiInteraction.contextAwarenessDesc')}
        >
          <SelectField
            value={settings.aiInteraction.contextAwareness}
            onChange={(value) => updateSetting('aiInteraction.contextAwareness', value)}
            options={contextAwarenessOptions}
            className="w-48"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.aiInteraction.memorySuggestions')}
          description={t('settings.categories.aiInteraction.memorySuggestionsDesc')}
        >
          <ToggleSwitch
            checked={settings.aiInteraction.memorySuggestions}
            onChange={(checked) => updateSetting('aiInteraction.memorySuggestions', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.aiInteraction.autoSaveMemories')}
          description={t('settings.categories.aiInteraction.autoSaveMemoriesDesc')}
        >
          <ToggleSwitch
            checked={settings.aiInteraction.autoSaveMemories}
            onChange={(checked) => updateSetting('aiInteraction.autoSaveMemories', checked)}
          />
        </SettingItem>
      </div>

      {/* AI Model & Voice */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Cpu className="w-4 h-4" />
          AI Model & Voice
        </h4>

        <SettingItem
          label={t('settings.categories.aiInteraction.preferredModel')}
          description={t('settings.categories.aiInteraction.preferredModelDesc')}
        >
          <SelectField
            value={settings.aiInteraction.preferredModel}
            onChange={(value) => updateSetting('aiInteraction.preferredModel', value)}
            options={modelOptions}
            className="w-48"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.aiInteraction.voicePreference')}
          description={t('settings.categories.aiInteraction.voicePreferenceDesc')}
        >
          <SelectField
            value={settings.aiInteraction.voicePreference}
            onChange={(value) => updateSetting('aiInteraction.voicePreference', value)}
            options={voicePreferenceOptions}
            className="w-48"
          />
        </SettingItem>
      </div>

      {/* Premium Features Preview */}
      {!isPremiumUser && (
        <div className="mt-6 p-4 bg-romantic-gradient/10 border border-primary/30 rounded-lg">
          <h5 className="font-medium text-primary flex items-center gap-2 mb-3">
            <Sparkles className="w-4 h-4" />
            Premium AI Features
          </h5>

          <div className="space-y-2 text-sm text-foreground/70">
            <p>• Advanced AI models (Gemini 2.5 Pro)</p>
            <p>• Deep context understanding</p>
            <p>• Voice interaction preferences</p>
            <p>• Unlimited memory capacity</p>
            <p>• Custom personality adaptation</p>
          </div>
          
          <button
            onClick={() => {
              // TODO: Navigate to upgrade page
              alert('Upgrade to Diamond Pass to unlock premium AI features!');
            }}
            className="mt-3 px-4 py-2 bg-romantic-gradient text-primary-foreground rounded-lg hover:opacity-90 transition-opacity text-sm font-medium"
          >
            Upgrade to Diamond Pass
          </button>
        </div>
      )}
    </div>
  );
};

export default AIInteractionSettings; 