'use client';

import { FC, useState, useEffect } from 'react';
import { User, MessageCircle, Settings } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';

// Import edit character component
import EditCharacterCreation from '@/components/character-creation/EditCharacterCreation';

// Import hooks
import { useCharacterEdit } from '@/hooks/character-creation/useCharacterEdit';
import { useImageUpload } from '@/hooks/character-creation/useImageUpload';

// Import types
import type { 
  Step, 
  StepConfig,
  CharacterFormData 
} from '@/types/character-creation';

interface EditCharacterClientPageProps {
  lang: string;
  characterId: string;
}

const EditCharacterClientPage: FC<EditCharacterClientPageProps> = ({ lang, characterId }) => {
  const { t } = useTranslation(lang, 'translation');
  
  const [currentStep, setCurrentStep] = useState<Step>('basics');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Use custom hooks
  const characterEdit = useCharacterEdit();
  const imageUpload = useImageUpload(characterEdit.formData, characterEdit.setFormData);

  // Load character data
  useEffect(() => {
    const loadCharacter = async () => {
      try {
        setIsLoading(true);
        await characterEdit.loadCharacterData(characterId);
      } catch (error) {
        console.error('Failed to load character:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCharacter();
  }, [characterId, characterEdit]);

  // Handle save changes
  const handleSaveChanges = async (): Promise<string> => {
    return await characterEdit.updateCharacter(characterId);
  };

  // Step configuration
  const steps: readonly StepConfig[] = [
    { id: 'basics', label: t('characterCreation.steps.basics.title'), icon: User, description: t('characterCreation.steps.basics.description') },
    { id: 'personality', label: t('characterCreation.steps.personality.title'), icon: MessageCircle, description: t('characterCreation.steps.personality.description') },
    { id: 'advanced', label: t('characterCreation.steps.advanced.title'), icon: Settings, description: t('characterCreation.steps.advanced.description') },
  ] as const;

  // Loading state
  if (isLoading) {
    return (
      <MainAppLayout lang={lang}>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10 flex items-center justify-center">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">{t('common.loading')}</p>
          </div>
        </div>
      </MainAppLayout>
    );
  }

  // Main content renderer
  const renderMainContent = () => (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-rose-600 bg-clip-text text-transparent">
          {t('characterEdit.title')}
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          {t('characterEdit.subtitle')}
        </p>
      </div>

      {/* Edit Character Content */}
      <div className="animate-in slide-in-from-bottom-4 duration-500">
        <EditCharacterCreation
          currentStep={currentStep}
          formData={characterEdit.formData}
          setFormData={characterEdit.setFormData}
          onStepChange={setCurrentStep}
          onSubmit={handleSaveChanges}
          isStepComplete={characterEdit.isStepComplete}
          steps={steps}
          lang={lang}
        />
      </div>
    </div>
  );

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200/20 dark:bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-200/20 dark:bg-pink-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Main Content */}
          {renderMainContent()}
        </div>
      </div>
    </MainAppLayout>
  );
};

export default EditCharacterClientPage; 