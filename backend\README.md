# Alphane Backend

Modern Node.js backend for the Alphane AI character interaction platform.

## Architecture

This backend follows a modular architecture with clear separation of concerns:

- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic and data processing
- **Models**: Database models and schema definitions  
- **Middleware**: Authentication, validation, and request processing
- **Routes**: API endpoint definitions
- **Utils**: Shared utilities and helpers
- **Config**: Environment and application configuration

## Technology Stack

- Node.js with Express.js
- PostgreSQL with Prisma ORM
- Redis for caching and sessions
- Socket.IO for real-time communication
- JWT for authentication
- AI integration for character interactions

## Getting Started

1. Install dependencies: `npm install`
2. Set up environment variables: `cp .env.example .env`
3. Run database migrations: `npm run db:migrate`
4. Start development server: `npm run dev`

## API Documentation

API documentation is available at `/api/docs` when running the server.