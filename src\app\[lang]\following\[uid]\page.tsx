import { Suspense } from 'react';
import FollowingClientPage from './FollowingClientPage';
import { characters } from '@/lib/mock-data';
import MainAppLayout from '@/components/MainAppLayout';
import { getUserByUid, getUserFollowing } from '@/lib/auth-api';
import { notFound } from 'next/navigation';

// Mock following data
const generateFollowingData = () => {
  return characters.slice(0, 4).map((character, index) => ({
    id: character.id,
    name: character.name,
    avatar: character.character_avatar,
    username: `@${character.name.toLowerCase().replace(' ', '_')}`,
    followerCount: Math.floor(Math.random() * 10000) + 100,
    bio: `Creator of amazing AI characters and interactive stories. Building the future of digital companionship.`,
    joinedDate: `${Math.floor(Math.random() * 365) + 1} days ago`,
    isVerified: Math.random() > 0.5, // 50% chance of being verified
    lastActive: `${Math.floor(Math.random() * 24) + 1}h ago`,
    mutualFollowers: Math.floor(Math.random() * 50) + 5,
  }));
};

export default async function FollowingPage({
  params
}: {
  params: Promise<{ lang: string; uid: string }>
}) {
  const { lang, uid } = await params;

  // For now, we'll use mock data since server-side API calls need proper setup
  let userData = {
    _id: uid,
    uid: uid,
    name: `User ${uid.slice(-4)}`,
    email: `user${uid.slice(-4)}@example.com`,
    avatar: `https://i.pravatar.cc/160?u=${uid}`,
    follow_count: Math.floor(Math.random() * 1000) + 50,
  };

  let followingList = generateFollowingData();

  const userInfo = {
    name: userData?.name || 'Unknown User',
    avatar: userData?.avatar || 'https://i.pravatar.cc/160?u=profile',
    followingCount: userData?.follow_count || followingList.length,
  };

  return (
    <Suspense fallback={
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
      </div>
    }>
      <MainAppLayout lang={lang} title={`${userInfo.name}'s Following`}>
        <FollowingClientPage
          lang={lang}
          uid={uid}
          userInfo={userInfo}
          following={followingList}
        />
      </MainAppLayout>
    </Suspense>
  );
}
