@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar utilities */
@layer utilities {
  .scrollbar-hide {
    /* Hide scrollbar for Chrome, Safari and Opera */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  /* Custom thin scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(168, 85, 247, 0.3) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    height: 6px;
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(168, 85, 247, 0.3);
    border-radius: 3px;
    transition: background 0.3s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(168, 85, 247, 0.5);
  }

  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thumb-purple-500\/30::-webkit-scrollbar-thumb {
    background: rgba(168, 85, 247, 0.3);
  }

  .scrollbar-thumb-purple-500\/50::-webkit-scrollbar-thumb {
    background: rgba(168, 85, 247, 0.5);
  }

  /* Custom slider styling */
  .slider-thumb {
    background: transparent;
  }

  .slider-thumb::-webkit-slider-thumb {
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #a855f7, #ec4899);
    cursor: grab;
    border: 3px solid white;
    box-shadow: 0 3px 12px rgba(168, 85, 247, 0.4);
    transition: all 0.3s ease;
  }

  .slider-thumb::-webkit-slider-thumb:hover {
    transform: scale(1.15);
    box-shadow: 0 6px 20px rgba(168, 85, 247, 0.5);
    cursor: grab;
  }

  .slider-thumb::-webkit-slider-thumb:active {
    transform: scale(1.05);
    cursor: grabbing;
    box-shadow: 0 4px 16px rgba(168, 85, 247, 0.6);
  }

  .slider-thumb::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #a855f7, #ec4899);
    cursor: grab;
    border: 3px solid white;
    box-shadow: 0 3px 12px rgba(168, 85, 247, 0.4);
    transition: all 0.3s ease;
  }

  .slider-thumb::-moz-range-thumb:hover {
    transform: scale(1.15);
    box-shadow: 0 6px 20px rgba(168, 85, 247, 0.5);
  }

  .slider-thumb::-moz-range-thumb:active {
    transform: scale(1.05);
    cursor: grabbing;
  }

  /* Touch optimization for mobile interactions */
  .touch-manipulation {
    touch-action: pan-x;
    -webkit-overflow-scrolling: touch;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    scroll-behavior: smooth;
    overscroll-behavior-x: contain;
  }

  /* Smooth momentum scrolling for mobile */
  .momentum-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    overscroll-behavior-x: contain;
  }

  /* Drag cursor states */
  .cursor-grab {
    cursor: grab;
  }

  .cursor-grabbing {
    cursor: grabbing;
  }

  .active\:cursor-grabbing:active {
    cursor: grabbing;
  }

  /* Animation delay utilities */
  .animation-delay-300 {
    animation-delay: 300ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  /* Enhanced card hover effects */
  .card-breathe {
    animation: breathe 2s ease-in-out infinite;
  }

  @keyframes breathe {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    50% {
      transform: scale(1.02);
      box-shadow: 0 8px 16px rgba(168, 85, 247, 0.2);
    }
  }

  /* Floating animation for special cards */
  .card-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-2px);
    }
  }
}

@layer base {
  :root {
    /* Romantic Purple-Pink Theme - Light Mode */
    /* Background: Pure white gradient */
    --background: 255 255 255;
    --background-gradient: linear-gradient(135deg, rgb(255, 255, 255) 0%, rgb(254, 254, 254) 50%, rgb(253, 253, 253) 100%);

    /* Text colors: Purple-pink gradient tones */
    --foreground: 124 58 237; /* Deep purple for main text */
    --foreground-secondary: 139 92 246; /* Medium purple for secondary text */
    --foreground-muted: 167 139 250; /* Light purple for muted text */

    /* Card colors: Subtle purple-pink tints */
    --card: 255 255 255;
    --card-foreground: 124 58 237;
    --card-secondary: 251 245 255; /* Very light purple tint */

    /* Popover colors */
    --popover: 255 255 255;
    --popover-foreground: 124 58 237;

    /* Primary: Purple-pink gradient */
    --primary: 139 92 246; /* Main purple */
    --primary-pink: 236 72 153; /* Main pink */
    --primary-foreground: 255 255 255;
    --primary-gradient: linear-gradient(135deg, rgb(139, 92, 246) 0%, rgb(236, 72, 153) 100%);

    /* Secondary: Light purple-pink */
    --secondary: 221 214 254; /* Very light purple */
    --secondary-pink: 251 207 232; /* Very light pink */
    --secondary-foreground: 124 58 237;
    --secondary-gradient: linear-gradient(135deg, rgb(221, 214, 254) 0%, rgb(251, 207, 232) 100%);

    /* Muted: Subtle purple-pink */
    --muted: 245 243 255; /* Extremely light purple */
    --muted-foreground: 147 51 234; /* Medium purple */

    /* Accent: Bright purple-pink */
    --accent: 168 85 247; /* Bright purple */
    --accent-pink: 244 114 182; /* Bright pink */
    --accent-foreground: 255 255 255;
    --accent-gradient: linear-gradient(135deg, rgb(168, 85, 247) 0%, rgb(244, 114, 182) 100%);

    /* Functional colors with purple-pink tints */
    --destructive: 219 39 119; /* Deep pink for errors */
    --destructive-foreground: 255 255 255;
    --success: 147 51 234; /* Purple for success */
    --success-foreground: 255 255 255;
    --warning: 245 158 11; /* Gold for warnings */
    --warning-foreground: 255 255 255;

    /* UI elements */
    --border: 221 214 254; /* Light purple border */
    --input: 255 255 255;
    --ring: 139 92 246; /* Purple focus ring */
    --logo: 139 92 246;
    --icon: 124 58 237;
    --header-foreground: 124 58 237;

    /* Radius */
    --radius: 0.5rem;
  }

  .dark {
    /* Romantic Purple-Pink Theme - Dark Mode */
    /* Background: Black to titanium gray gradient */
    --background: 0 0 0;
    --background-gradient: linear-gradient(135deg, rgb(0, 0, 0) 0%, rgb(10, 10, 10) 50%, rgb(26, 26, 26) 100%);

    /* Text colors: Same purple-pink gradient tones (consistent across themes) */
    --foreground: 167 139 250; /* Light purple for main text in dark mode */
    --foreground-secondary: 196 181 253; /* Lighter purple for secondary text */
    --foreground-muted: 139 92 246; /* Medium purple for muted text */

    /* Card colors: Dark background with purple-pink accents */
    --card: 0 0 0;
    --card-foreground: 167 139 250;
    --card-secondary: 17 12 28; /* Very dark purple tint */

    /* Popover colors */
    --popover: 0 0 0;
    --popover-foreground: 167 139 250;

    /* Primary: Same purple-pink gradient (consistent) */
    --primary: 139 92 246;
    --primary-pink: 236 72 153;
    --primary-foreground: 255 255 255;
    --primary-gradient: linear-gradient(135deg, rgb(139, 92, 246) 0%, rgb(236, 72, 153) 100%);

    /* Secondary: Dark purple-pink */
    --secondary: 30 27 75; /* Dark purple */
    --secondary-pink: 45 7 28; /* Dark pink */
    --secondary-foreground: 196 181 253;
    --secondary-gradient: linear-gradient(135deg, rgb(30, 27, 75) 0%, rgb(45, 7, 28) 100%);

    /* Muted: Dark subtle purple-pink */
    --muted: 17 12 28; /* Very dark purple */
    --muted-foreground: 139 92 246;

    /* Accent: Same bright purple-pink (consistent) */
    --accent: 168 85 247;
    --accent-pink: 244 114 182;
    --accent-foreground: 255 255 255;
    --accent-gradient: linear-gradient(135deg, rgb(168, 85, 247) 0%, rgb(244, 114, 182) 100%);

    /* Functional colors: Same as light mode (consistent) */
    --destructive: 219 39 119;
    --destructive-foreground: 255 255 255;
    --success: 147 51 234;
    --success-foreground: 255 255 255;
    --warning: 245 158 11;
    --warning-foreground: 255 255 255;

    /* UI elements */
    --border: 30 27 75; /* Dark purple border */
    --input: 0 0 0;
    --ring: 139 92 246;
    --logo: 167 139 250;
    --icon: 196 181 253;
    --header-foreground: 167 139 250;
  }

  /* Font settings */
  .font-japanese {
    font-family: var(--font-jk-maru), var(--font-quicksand), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  }

  .font-english {
    font-family: var(--font-quicksand), var(--font-jk-maru), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  }


}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply text-foreground;
    background: var(--background-gradient);
    transition: background 0.3s ease, color 0.3s ease;
    min-height: 100vh;
  }

  html {
    transition: all 0.3s ease;
  }

  /* Enhanced romantic atmosphere */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-gradient);
    z-index: -1;
    pointer-events: none;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(var(--foreground) / 0.2);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgb(var(--foreground) / 0.3);
  }

  /* Masonry layout with precise padding/gap calculations */
  /* Formula: Total spacing = (N+1) × 8px, CSS columns auto-distributes remaining width */
  .masonry-container {
    padding: 8px;
    column-gap: 8px;
    column-fill: balance;
    width: 100%;
    box-sizing: border-box;
    /* Force full width utilization */
    overflow-x: hidden;
    /* Ensure columns fill the entire container */
    column-width: auto;
    /* Prevent column width constraints */
    max-width: none;
  }

  /* Base: 2 columns - Explicit width calculation with floor rounding */
  .masonry-container.cols-2 {
    column-count: 2;
    column-width: calc((100% - 24px) / 2 - 1px); /* (2+1)*8=24px spacing, -1px for floor */
  }

  /* Medium screens: 3 columns - Explicit width calculation with floor rounding */
  @media (min-width: 768px) {
    .masonry-container.md\:cols-3 {
      column-count: 3;
      column-width: calc((100% - 32px) / 3 - 1px); /* (3+1)*8=32px spacing, -1px for floor */
    }
  }

  /* Large screens: 3 columns (maintain 3 columns until 1280px) */
  @media (min-width: 1024px) {
    .masonry-container.lg\:cols-3 {
      column-count: 3;
      column-width: calc((100% - 32px) / 3 - 1px); /* Keep 3 columns */
    }
  }

  /* Extra large screens: 4 columns - Explicit width calculation with floor rounding */
  @media (min-width: 1280px) {
    .masonry-container.xl\:cols-4 {
      column-count: 4;
      column-width: calc((100% - 40px) / 4 - 1px); /* (4+1)*8=40px spacing, -1px for floor */
    }
  }

  /* 2XL screens: 5 columns - Explicit width calculation with floor rounding */
  @media (min-width: 1536px) {
    .masonry-container.2xl\:cols-5 {
      column-count: 5;
      column-width: calc((100% - 48px) / 5 - 1px); /* (5+1)*8=48px spacing, -1px for floor */
    }
  }

  /* Ensure proper item spacing and prevent overflow */
  .masonry-container > * {
    break-inside: avoid;
    margin-bottom: 8px;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
  }

  /* Enhanced specificity for darker text for descriptions */
  .dark [class*="text-foreground/"],
  .dark [class*="text-card-foreground/"] {
    color: rgb(255 255 255 / 0.7) !important;
  }

  /* Hide scrollbar for horizontal scroll containers */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Theme transition utility */
@layer utilities {
  .theme-transition {
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Override hardcoded colors to use romantic purple-pink theme */
@layer utilities {
  /* Blue color overrides - map to primary purple */
  .text-blue-600,
  .text-blue-700,
  .text-blue-500,
  .text-indigo-600,
  .text-indigo-500 {
    color: rgb(var(--primary)) !important;
  }

  .hover\:text-blue-600:hover,
  .hover\:text-blue-700:hover,
  .hover\:text-blue-500:hover,
  .hover\:text-indigo-600:hover,
  .hover\:text-indigo-500:hover {
    color: rgb(var(--accent)) !important;
  }

  .bg-blue-600,
  .bg-blue-700,
  .bg-blue-500,
  .bg-indigo-600,
  .bg-indigo-500 {
    background-color: rgb(var(--primary)) !important;
  }

  .bg-blue-100,
  .bg-indigo-100 {
    background-color: rgb(var(--secondary)) !important;
  }

  /* Purple color overrides - map to accent purple-pink */
  .text-purple-600,
  .text-purple-700,
  .text-purple-500,
  .text-purple-400 {
    color: rgb(var(--accent)) !important;
  }

  .bg-purple-600,
  .bg-purple-700,
  .bg-purple-500 {
    background-color: rgb(var(--accent)) !important;
  }

  .bg-purple-100,
  .bg-purple-50 {
    background-color: rgb(var(--secondary)) !important;
  }

  /* Pink color overrides - map to primary pink */
  .text-pink-600,
  .text-pink-700,
  .text-pink-500,
  .text-pink-400 {
    color: rgb(var(--primary-pink)) !important;
  }

  .bg-pink-600,
  .bg-pink-700,
  .bg-pink-500 {
    background-color: rgb(var(--primary-pink)) !important;
  }

  .bg-pink-100,
  .bg-pink-50 {
    background-color: rgb(var(--secondary-pink)) !important;
  }

  /* Yellow/Gold overrides - keep for warnings but adjust tone */
  .text-yellow-500,
  .text-yellow-600 {
    color: rgb(var(--warning)) !important;
  }

  .bg-yellow-100 {
    background-color: rgb(251 245 255) !important; /* Light purple tint */
  }

  /* Green overrides - map to success purple */
  .text-green-500,
  .text-green-600,
  .text-emerald-600,
  .text-emerald-400 {
    color: rgb(var(--success)) !important;
  }

  .bg-green-100,
  .bg-emerald-100 {
    background-color: rgb(var(--secondary)) !important;
  }

  /* White text overrides */
  .text-white {
    color: rgb(255 255 255) !important;
  }

  /* Gray overrides - map to muted purple tones */
  .text-gray-500,
  .text-gray-600 {
    color: rgb(var(--muted-foreground)) !important;
  }

  .text-gray-400 {
    color: rgb(var(--foreground-muted)) !important;
  }

  .bg-gray-100,
  .bg-gray-50 {
    background-color: rgb(var(--muted)) !important;
  }

  .bg-gray-800,
  .bg-gray-900 {
    background-color: rgb(var(--card-secondary)) !important;
  }

  .border-gray-200,
  .border-gray-300 {
    border-color: rgb(var(--border)) !important;
  }

  .border-gray-700,
  .border-gray-800 {
    border-color: rgb(var(--border)) !important;
  }

  /* Romantic theme utilities */
  .bg-romantic-gradient {
    background: var(--primary-gradient) !important;
  }

  .bg-romantic-secondary {
    background: var(--secondary-gradient) !important;
  }

  .bg-romantic-accent {
    background: var(--accent-gradient) !important;
  }

  .text-romantic {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .border-romantic {
    border-color: rgb(var(--primary)) !important;
  }

  .shadow-romantic {
    box-shadow: 0 10px 25px -3px rgba(139, 92, 246, 0.1), 0 4px 6px -2px rgba(236, 72, 153, 0.05) !important;
  }

  .shadow-romantic-lg {
    box-shadow: 0 25px 50px -12px rgba(139, 92, 246, 0.25), 0 10px 20px -5px rgba(236, 72, 153, 0.1) !important;
  }

  /* System color utilities */
  .text-system {
    color: rgb(var(--foreground));
  }

  .text-system-muted {
    color: rgb(var(--muted-foreground));
  }

  .text-system-primary {
    color: rgb(var(--primary));
  }

  .bg-system {
    background-color: rgb(var(--background));
  }

  .bg-system-card {
    background-color: rgb(var(--card));
  }

  .bg-system-primary {
    background-color: rgb(var(--primary));
  }

  .border-system {
    border-color: rgb(var(--border));
  }

  /* Glass morphism utilities */
  .glass {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-strong {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .glass-card {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .glass-nav {
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    background: rgba(255, 255, 255, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.25);
  }

  /* Dark mode glass adjustments */
  .dark .glass {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .dark .glass-strong {
    background: rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .dark .glass-card {
    background: rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .dark .glass-nav {
    background: rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.12);
  }

  /* Featured tabs glass effect - semi-transparent with strong blur */
  .glass-featured-tabs {
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .dark .glass-featured-tabs {
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced neon reflection effects for tabs */
  .tab-neon-reflection {
    position: relative;
    overflow: hidden;
  }

  .tab-neon-reflection::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(168, 85, 247, 0.3) 25%,
      rgba(236, 72, 153, 0.5) 50%,
      rgba(168, 85, 247, 0.3) 75%,
      transparent 100%);
    animation: neonPulse 3s ease-in-out infinite;
  }

  .tab-neon-reflection.active::after {
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 25%,
      rgba(255, 255, 255, 0.6) 50%,
      rgba(255, 255, 255, 0.4) 75%,
      transparent 100%);
    animation: neonShine 2s ease-in-out infinite;
  }

  /*
   * Z-Index Hierarchy (SOTA Implementation)
   * =====================================
   * z-[100]: Header dropdown menus, critical overlays (UnifiedSettingsDropdown)
   * z-[90]:  Header bar (ResponsiveHeader)
   * z-[80]:  Featured tabs navigation (EnhancedTabNavigation)
   * z-[70]:  Modals, search overlays (SearchOverlay, modals)
   * z-[60]:  Notifications, toasts
   * z-[50]:  Secondary overlays
   * z-[40]:  Sticky elements
   * z-[30]:  Floating elements
   * z-[20]:  Elevated cards
   * z-[10]:  Hover effects, badges
   * z-[1-9]: Basic layering
   * z-[0]:   Default layer
   */

  /* Animation utilities */
  .animate-gentle-bounce {
    animation: gentle-bounce 2s ease-in-out infinite;
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
  }

  .animate-scale-hover {
    transition: transform 0.3s ease;
  }

  .animate-scale-hover:hover {
    transform: scale(1.05);
  }

  @keyframes gentle-bounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-4px);
    }
  }

  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Hero section utilities */
  .hero-gradient {
    background: linear-gradient(135deg, rgb(var(--primary)) 0%, rgb(var(--primary-pink)) 100%);
  }

  .hero-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, transparent 100%);
  }

  .hero-content {
    position: relative;
    z-index: 10;
  }

  /* Special utility for true green color (bypasses theme overrides) */
  .text-true-green {
    color: #10b981 !important;
  }

  .text-true-red {
    color: #ef4444 !important;
  }

  /* Standardized character counter styling for create-character components */
  .character-counter {
    @apply text-sm font-medium;
    color: #10b981; /* Default green */
  }

  .character-counter.over-limit {
    color: #ef4444 !important; /* Red for over-limit */
  }

  /* Special utility for silver hover (bypasses theme overrides) */
  .hover\:bg-silver:hover {
    background-color: #c0c0c0 !important;
  }

  /* Button glow effect for romantic theme */
  .btn-glow {
    position: relative;
    overflow: hidden;
    background: var(--primary-gradient);
    box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
    transition: all 0.3s ease;
  }

  .btn-glow:hover {
    box-shadow: 0 12px 32px rgba(139, 92, 246, 0.6);
    transform: translateY(-2px);
  }

  .btn-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .btn-glow:hover::before {
    left: 100%;
  }

  /* Achievement animations */
  .achievement {
    position: relative;
    transition: all 0.3s ease;
  }

  .achievement:hover {
    transform: scale(1.1) rotateY(10deg);
  }

  .achievement.rare {
    filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
  }

  /* Progress bar animation */
  .progress-bar-animated {
    animation: progressGlow 2s ease-in-out infinite alternate;
  }

  @keyframes progressGlow {
    from {
      box-shadow: 0 0 5px rgba(236, 72, 153, 0.5);
    }
    to {
      box-shadow: 0 0 15px rgba(236, 72, 153, 0.8), 0 0 25px rgba(139, 92, 246, 0.4);
    }
  }

  /* Gradient text effect */
  .gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Float animation */
  .float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Bond ring animation */
  .bond-ring::before {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: conic-gradient(from var(--bond-angle, 0deg),
      transparent 0deg,
      rgba(139, 92, 246, 0.8) 45deg,
      rgba(236, 72, 153, 0.8) 90deg,
      transparent 135deg);
    animation: bondRotate 4s linear infinite;
  }

  @keyframes bondRotate {
    to { transform: rotate(360deg); }
  }

  /* Card hover effects */
  .card-hover {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .card-hover:hover {
    transform: translateY(-8px) scale(1.02);
  }

  /* Fade in up animation */
  .animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Tab content visibility */
  .tab-content {
    display: none;
  }

  .tab-content.active {
    display: block;
    animation: fadeInUp 0.3s ease-out;
  }

  /* Glass morphism effects */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.4);
  }

  .glass-strong {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.6);
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-strong {
    background: rgba(0, 0, 0, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* Enhanced sticky masking effects */
  .sticky-mask {
    position: relative;
    background: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.98) 0%,
      rgba(255, 255, 255, 0.98) 85%,
      rgba(255, 255, 255, 0.95) 95%,
      rgba(255, 255, 255, 0.9) 100%
    );
    backdrop-filter: blur(24px) saturate(180%);
    -webkit-backdrop-filter: blur(24px) saturate(180%);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      0 8px 32px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .dark .sticky-mask {
    background: linear-gradient(
      to bottom,
      rgba(17, 24, 39, 0.98) 0%,
      rgba(17, 24, 39, 0.98) 85%,
      rgba(17, 24, 39, 0.95) 95%,
      rgba(17, 24, 39, 0.9) 100%
    );
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.3),
      0 8px 32px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Function selector enhanced effects */
  .function-selector-gradient {
    background: linear-gradient(
      135deg,
      #ec4899 0%,
      #8b5cf6 25%,
      #a855f7 50%,
      #ec4899 75%,
      #f43f5e 100%
    );
    background-size: 200% 200%;
    animation: gradientShift 8s ease-in-out infinite;
  }

  @keyframes gradientShift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* Enhanced button interactions */
  .function-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .function-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s;
  }

  .function-button:hover::before {
    left: 100%;
  }

  .function-button:active {
    transform: scale(0.98);
  }

  /* Chapter current animation - subtle glow effect */
  .chapter-current {
    position: relative;
    animation: subtleGlow 3s ease-in-out infinite;
  }

  @keyframes subtleGlow {
    0%, 100% {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    50% {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 20px rgba(59, 130, 246, 0.3);
    }
  }
}

/* Achievement system utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation delays for decorative elements */
.delay-500 {
  animation-delay: 0.5s;
}

.delay-1000 {
  animation-delay: 1s;
}

/* Custom scrollbar for modal */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--foreground) / 0.3);
}

/* 成就页面自定义动画 */
@layer utilities {
  /* 慢速旋转动画 */
  .animate-spin-slow {
    animation: spin 6s linear infinite;
  }

  /* 闪烁动画 */
  .animate-twinkle {
    animation: twinkle 2s ease-in-out infinite;
  }

  /* 光波效果动画 */
  .animate-shimmer {
    animation: shimmer 3s ease-in-out infinite;
  }

  /* 渐变径向背景 */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }



  /* 增强的hover效果 */
  .hover-glow:hover {
    box-shadow: 0 10px 40px rgba(139, 92, 246, 0.4), 0 0 20px rgba(236, 72, 153, 0.2);
    transform: translateY(-2px);
  }

  /* 成就卡片特效 */
  .achievement-glow {
    position: relative;
    overflow: hidden;
  }

  .achievement-glow::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .achievement-glow:hover::after {
    left: 100%;
  }

  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-success {
    animation: success 0.6s ease-in-out;
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-12deg);
  }
  100% {
    transform: translateX(200%) skewX(-12deg);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.6), 0 0 30px rgba(236, 72, 153, 0.3);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes success {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Journey页面动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-8px) rotate(-3deg); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced neon animations for tabs */
@keyframes neonPulse {
  0%, 100% {
    opacity: 0.3;
    filter: blur(1px);
  }
  50% {
    opacity: 0.8;
    filter: blur(0px);
  }
}

@keyframes neonShine {
  0%, 100% {
    opacity: 0.6;
    transform: scaleX(1);
    filter: blur(0px);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.1);
    filter: blur(0px) brightness(1.2);
  }
}

@keyframes bounce-in {
  0% { 
    opacity: 0; 
    transform: scale(0.3) translateY(20px); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.05) translateY(-5px); 
  }
  70% { 
    transform: scale(0.9) translateY(0px); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1) translateY(0px); 
  }
}

@keyframes pulse-scale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 3s ease-in-out infinite 1.5s;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-shine {
  animation: shine 1.5s infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-pulse-scale {
  animation: pulse-scale 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
}

/* 紧迫感效果 */
.urgent-glow {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
  animation: pulse 2s infinite;
}

.critical-glow {
  box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
  animation: pulse 1s infinite;
}

/* VIP特效 */
.vip-border {
  position: relative;
  overflow: hidden;
}

.vip-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 215, 0, 0.4),
    transparent
  );
  transition: left 0.5s;
}

.vip-border:hover::before {
  left: 100%;
}

/* Journey页面动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-8px) rotate(-3deg); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes bounce-in {
  0% { 
    opacity: 0; 
    transform: scale(0.3) translateY(20px); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.05) translateY(-5px); 
  }
  70% { 
    transform: scale(0.9) translateY(0px); 
  }
  100% { 
    opacity: 1; 
    transform: scale(1) translateY(0px); 
  }
}

@keyframes pulse-scale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 3s ease-in-out infinite 1.5s;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-shine {
  animation: shine 1.5s infinite;
}

.animate-bounce-in {
  animation: bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-pulse-scale {
  animation: pulse-scale 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
}

/* 紧迫感效果 */
.urgent-glow {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
  animation: pulse 2s infinite;
}

.critical-glow {
  box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
  animation: pulse 1s infinite;
}