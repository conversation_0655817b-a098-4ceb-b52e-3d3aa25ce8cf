import { useState, useCallback } from 'react';
import type { CharacterFormData, Step } from '@/types/character-creation';

// 默认的空表单数据
const defaultFormData: CharacterFormData = {
  name: '',
  nameOrigin: '',
  description: '',
  personality: '',
  appearance: '',
  setting: '',
  settings: '',
  characterImage: null,
  avatar: null,
  avatarCrop: null,
  greetingMessage: '',
  personalityTags: [],
  gender: '',
  pov: '',
  faction: 'anime',
  visibility: 'public',
  backgroundStory: '',
  interactionStyleTags: '',
  voiceIds: '',
  facialExpressions: '',
  bodyLanguage: '',
  relationshipWithPlayer: '',
  initialMemoriesText: '',
  customPromptPrefix: '',
  personalityTraits: '',
  personalityMind: '',
  personalityEmotion: '',
  goodAt: [],
  badAt: [],
  knowledgeFiles: [],
};

export const useCharacterEdit = (initialData?: Partial<CharacterFormData>) => {
  const [formData, setFormData] = useState<CharacterFormData>({
    ...defaultFormData,
    ...initialData
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // 验证步骤是否完成
  const isStepComplete = useCallback((step: Step): boolean => {
    switch (step) {
      case 'basics':
        return !!(
          formData.name?.trim() &&
          formData.appearance?.trim() &&
          formData.personality?.trim() &&
          formData.gender &&
          formData.pov
        );
      case 'personality':
        return !!(
          formData.greetingMessage?.trim() &&
          formData.backgroundStory?.trim()
        );
      case 'advanced':
        return true; // Advanced step is always considered complete
      default:
        return false;
    }
  }, [formData]);

  // 更新角色数据
  const updateCharacter = useCallback(async (characterId: string): Promise<string> => {
    try {
      setIsSubmitting(true);
      
      // TODO: 实际实现时替换为真实的API调用
      console.log('Updating character:', characterId, formData);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      return characterId;
    } catch (error) {
      console.error('Failed to update character:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  }, [formData]);

  // 加载角色数据
  const loadCharacterData = useCallback(async (characterId: string): Promise<void> => {
    try {
      // TODO: 实际实现时替换为真实的API调用
      console.log('Loading character data:', characterId);
      
      // 模拟API调用和数据加载
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 这里应该用从API获取的数据更新formData
      // const characterData = await api.getCharacter(characterId);
      // setFormData({ ...defaultFormData, ...characterData });
    } catch (error) {
      console.error('Failed to load character data:', error);
      throw error;
    }
  }, []);

  return {
    formData,
    setFormData,
    isStepComplete,
    updateCharacter,
    loadCharacterData,
    isSubmitting,
  };
}; 