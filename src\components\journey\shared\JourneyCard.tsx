import React from 'react';
import { LucideIcon } from 'lucide-react';

interface JourneyCardProps {
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  glowEffect?: boolean;
  vipGlow?: boolean;
  animated?: boolean;
}

interface JourneyCardHeaderProps {
  icon: LucideIcon;
  title: string;
  subtitle?: string;
  iconGradient?: string;
  actions?: React.ReactNode;
}

interface JourneyCardStatsProps {
  stats: Array<{
    label: string;
    value: string | number;
    color?: string;
    sublabel?: string;
    trend?: string;
  }>;
  columns?: 2 | 3 | 4;
}

// 主卡片组件
export const JourneyCard: React.FC<JourneyCardProps> = ({
  children,
  className = '',
  gradient = 'from-purple-500/10 via-pink-500/10 to-purple-500/10',
  glowEffect = false,
  vipGlow = false,
  animated = true
}) => {
  return (
    <div className={`relative overflow-hidden rounded-2xl backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 shadow-xl ${animated ? 'transition-all duration-300 hover:scale-[1.02]' : ''} ${className}`}>
      {/* 背景渐变 */}
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-50 ${animated ? 'animate-gradient' : ''}`}></div>

      {/* VIP光效 */}
      {vipGlow && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-400/10 to-transparent animate-shimmer"></div>
      )}

      {/* 发光效果 */}
      {glowEffect && (
        <div className="absolute inset-0 rounded-2xl blur-sm bg-gradient-to-r from-purple-400/20 via-pink-400/20 to-purple-400/20 animate-pulse -z-10"></div>
      )}

      {/* 装饰粒子 */}
      {animated && (
        <>
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-pink-400/20 to-transparent rounded-full -translate-y-10 translate-x-10 animate-float"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-purple-400/20 to-transparent rounded-full translate-y-8 -translate-x-8 animate-float-delayed"></div>
        </>
      )}

      {/* 内容 */}
      <div className="relative p-6">
        {children}
      </div>
    </div>
  );
};

// 卡片头部组件
export const JourneyCardHeader: React.FC<JourneyCardHeaderProps> = ({
  icon: Icon,
  title,
  subtitle,
  iconGradient = 'from-purple-500 to-pink-500',
  actions
}) => {
  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <div className={`w-12 h-12 bg-gradient-to-br ${iconGradient} rounded-xl flex items-center justify-center shadow-lg`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-foreground">{title}</h3>
          {subtitle && <p className="text-foreground/70">{subtitle}</p>}
        </div>
      </div>
      {actions && (
        <div className="flex gap-2">
          {actions}
        </div>
      )}
    </div>
  );
};

// 统计数据网格组件
export const JourneyCardStats: React.FC<JourneyCardStatsProps> = ({
  stats,
  columns = 4
}) => {
  const gridCols = {
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-2 md:grid-cols-4'
  };

  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'purple':
        return 'bg-purple-50/50 dark:bg-purple-900/20 border-purple-200/30 dark:border-purple-800/30 text-purple-600 dark:text-purple-400';
      case 'pink':
        return 'bg-pink-50/50 dark:bg-pink-900/20 border-pink-200/30 dark:border-pink-800/30 text-pink-600 dark:text-pink-400';
      case 'blue':
        return 'bg-blue-50/50 dark:bg-blue-900/20 border-blue-200/30 dark:border-blue-800/30 text-blue-600 dark:text-blue-400';
      case 'green':
        return 'bg-green-50/50 dark:bg-green-900/20 border-green-200/30 dark:border-green-800/30 text-green-600 dark:text-green-400';
      case 'orange':
        return 'bg-orange-50/50 dark:bg-orange-900/20 border-orange-200/30 dark:border-orange-800/30 text-orange-600 dark:text-orange-400';
      case 'yellow':
        return 'bg-yellow-50/50 dark:bg-yellow-900/20 border-yellow-200/30 dark:border-yellow-800/30 text-yellow-600 dark:text-yellow-400';
      default:
        return 'bg-gray-50/50 dark:bg-gray-900/20 border-gray-200/30 dark:border-gray-800/30 text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className={`grid ${gridCols[columns]} gap-4`}>
      {stats.map((stat, index) => (
        <div 
          key={index}
          className={`text-center p-4 rounded-xl border hover:scale-105 transition-all ${getColorClasses(stat.color)}`}
        >
          <div className="text-3xl font-bold mb-1">
            {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
          </div>
          <div className="text-xs text-foreground/60">{stat.label}</div>
          {stat.sublabel && (
            <div className="text-xs mt-1 font-medium">{stat.sublabel}</div>
          )}
        </div>
      ))}
    </div>
  );
}; 