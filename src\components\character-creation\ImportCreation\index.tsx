'use client';

import React, { useRef } from 'react';
import { Upload, FileText } from 'lucide-react';
import type { ImportCreationProps } from '@/types/character-creation';
import CharacterSelector from './CharacterSelector';
import { useTranslation } from '@/app/i18n/client';

const ImportCreation: React.FC<ImportCreationProps> = ({
  importFile,
  setImportFile,
  importedCharacters,
  setImportedCharacters,
  onImportFiles,
  onSelectCharacter,
  scriptFile,
  onScriptFileUpload,
  lang = 'en'
}) => {
  const { t } = useTranslation(lang, 'translation');
  const importInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="text-center">
        <h3 className="text-2xl font-bold text-rose-800 dark:text-rose-200 mb-2">{t('characterCreation.import.title')}</h3>
        <p className="text-gray-600 dark:text-gray-400">{t('characterCreation.import.subtitle')}</p>
      </div>

      {/* SillyTavern Character Cards Upload */}
      <div className="bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 rounded-2xl p-6 border border-rose-200 dark:border-rose-700 shadow-lg">
        <h4 className="text-xl font-bold text-rose-800 dark:text-rose-200 mb-4 flex items-center gap-2">
          {t('characterCreation.import.sillyTavern')}
        </h4>
        <div
          className="border-2 border-dashed border-rose-300 dark:border-rose-700 rounded-xl p-8 text-center hover:border-rose-400 dark:hover:border-rose-600 transition-all cursor-pointer bg-white dark:bg-gray-800/50 hover:bg-rose-50 dark:hover:bg-rose-900/30"
          onClick={() => importInputRef.current?.click()}
        >
          {importFile ? (
            <div className="text-rose-700 dark:text-rose-300">
              <FileText className="mx-auto mb-4 text-rose-500" size={48} />
              <p className="text-lg font-semibold text-rose-800 dark:text-rose-200 mb-2">{importFile.name}</p>
              <p className="text-sm text-rose-600 dark:text-rose-400 mb-4">{t('characterCreation.import.uploadArea.fileSelected')}</p>
              <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 rounded-full text-green-700 dark:text-green-300 font-medium border border-green-200 dark:border-green-700">
                {t('characterCreation.import.uploadArea.readyToImport')}
              </div>
            </div>
          ) : (
            <div className="text-rose-600 dark:text-rose-400">
              <Upload className="mx-auto mb-4 text-rose-500" size={48} />
              <p className="text-lg font-semibold text-rose-800 dark:text-rose-200 mb-2">{t('characterCreation.import.uploadArea.title')}</p>
              <p className="text-sm text-rose-600 dark:text-rose-400 mb-4">{t('characterCreation.import.uploadArea.subtitle')}</p>
              <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-rose-100 to-pink-100 dark:from-rose-900/50 dark:to-pink-900/50 rounded-full text-rose-700 dark:text-rose-300 font-medium border border-rose-200 dark:border-rose-700">
                {t('characterCreation.import.uploadArea.button')}
              </div>
            </div>
          )}
          <input
            ref={importInputRef}
            type="file"
            accept=".json"
            multiple
            onChange={(e) => e.target.files && onImportFiles(e.target.files)}
            className="hidden"
          />
        </div>
      </div>

      {/* Script File Upload */}
      <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-700 shadow-lg">
        <h4 className="text-xl font-bold text-blue-800 dark:text-blue-200 mb-4 flex items-center gap-2">
          {t('characterCreation.import.scriptFiles.title')}
        </h4>
        <div
          className="border-2 border-dashed border-blue-300 dark:border-blue-700 rounded-xl p-8 text-center hover:border-blue-400 dark:hover:border-blue-600 transition-all cursor-pointer bg-white dark:bg-gray-800/50 hover:bg-blue-50 dark:hover:bg-blue-900/30"
          onDrop={(e) => {
            e.preventDefault();
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].name.endsWith('.txt')) {
              onScriptFileUpload(files[0]);
            }
          }}
          onDragOver={(e) => e.preventDefault()}
          onClick={() => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.txt';
            input.onchange = (e) => {
              const file = (e.target as HTMLInputElement).files?.[0];
              if (file) onScriptFileUpload(file);
            };
            input.click();
          }}
        >
          {scriptFile ? (
            <div className="text-blue-700 dark:text-blue-300">
              <FileText className="mx-auto mb-4 text-blue-500" size={48} />
              <p className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">{scriptFile.name}</p>
              <p className="text-sm text-blue-600 dark:text-blue-400">{t('characterCreation.import.scriptFiles.uploaded')}</p>
            </div>
          ) : (
            <div className="text-blue-600 dark:text-blue-400">
              <Upload className="mx-auto mb-4 text-blue-500" size={48} />
              <p className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">{t('characterCreation.import.scriptFiles.uploadTitle')}</p>
              <p className="text-sm text-blue-600 dark:text-blue-400">{t('characterCreation.import.scriptFiles.uploadSubtitle')}</p>
            </div>
          )}
        </div>
      </div>

      {/* Import Features Info */}
      <div className="bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-2xl p-6 border border-violet-200 dark:border-violet-700 shadow-lg">
        <h4 className="text-xl font-bold text-violet-800 dark:text-violet-200 mb-4 flex items-center gap-2">
          {t('characterCreation.import.importFeatures.title')}
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-violet-200 dark:border-violet-700">
            <h5 className="font-semibold text-violet-700 dark:text-violet-300 mb-3 flex items-center gap-2">
              {t('characterCreation.import.importFeatures.characterCards')}
            </h5>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-green-500 text-lg">✅</span>
                <span className="text-sm text-gray-700 dark:text-gray-300">{t('characterCreation.import.importFeatures.multipleFiles')}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-500 text-lg">✅</span>
                <span className="text-sm text-gray-700 dark:text-gray-300">{t('characterCreation.import.importFeatures.singleFileMultiple')}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-500 text-lg">✅</span>
                <span className="text-sm text-gray-700 dark:text-gray-300">{t('characterCreation.import.importFeatures.sillyTavernSupport')}</span>
              </div>
            </div>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-violet-200 dark:border-violet-700">
            <h5 className="font-semibold text-violet-700 dark:text-violet-300 mb-3 flex items-center gap-2">
              {t('characterCreation.import.scriptFiles.title')}
            </h5>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-green-500 text-lg">✅</span>
                <span className="text-sm text-gray-700 dark:text-gray-300">{t('characterCreation.import.importFeatures.txtSupport')}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-500 text-lg">✅</span>
                <span className="text-sm text-gray-700 dark:text-gray-300">{t('characterCreation.import.importFeatures.autoExtract')}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-500 text-lg">✅</span>
                <span className="text-sm text-gray-700 dark:text-gray-300">{t('characterCreation.import.importFeatures.fillBackground')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Character Selection Interface */}
      {importedCharacters.length > 1 && (
        <div className="bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-2xl p-6 border border-emerald-200 dark:border-emerald-700 shadow-lg">
          <h4 className="text-xl font-bold text-emerald-800 dark:text-emerald-200 mb-4 flex items-center gap-2">
            {t('characterCreation.import.selectCharacter.title')}
          </h4>
          <CharacterSelector
            characters={importedCharacters}
            onSelectCharacter={onSelectCharacter}
          />
        </div>
      )}
    </div>
  );
};

export default ImportCreation;
