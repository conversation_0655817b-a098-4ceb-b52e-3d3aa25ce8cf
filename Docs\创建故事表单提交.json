{
  // ===== 基础故事信息 =====
  "title": "神秘学院的奇幻冒险",
  "description": "一个关于魔法学院学生探索未知力量的奇幻故事",
  "genre": "fantasy",
  "tags": ["魔法", "冒险", "友情", "成长"],
  "openingMessage": "欢迎来到艾瑟拉魔法学院，这里充满了神秘与奇迹。你准备好开始你的魔法之旅了吗？",
  "estimatedDuration": "medium",
  "backgroundSetting": "在艾瑟拉大陆的魔法学院中展开的冒险故事",
  "coverImage": null, // File对象
  "coverImageUrl": "https://example.com/cover.jpg",

  // ===== 世界设定 (WorldSetting) =====
  "worldSetting": {
    // 基础信息
    "storyName": "艾瑟拉学院传说",
    "coverImage": null, // File对象

    // 快速设置 - 通用设置
    "worldOverview": "艾瑟拉大陆是一个充满魔法能量的世界，这里有古老的魔法学院、神秘的森林和强大的魔法生物。",
    "storyBackground": "在魔法复苏的时代，年轻的魔法师们在学院中学习古老的咒语，同时面临着来自黑暗势力的威胁。",

    // 核心设置
    "coreConflict": "阻止古老黑魔法的复苏，保护学院和大陆的和平",

    // 背景细节
    "historicalEra": "魔法复兴纪元",
    "geographicEnvironment": "浮空岛屿上的魔法学院",
    "mainRaces": "人类、精灵、矮人、半身人",

    // 高级设置
    "physicsRules": "high-fantasy",
    "physicsRulesCustom": "", // 当选择custom时填写
    "supernaturalElements": "魔法能量遍布世界，可以通过咒语、魔法道具和天赋能力来操控",
    "socialPoliticalSystem": "学院制度：由魔法议会管理的教育机构，各学院之间既合作又竞争",
    "economicFoundation": "以魔法水晶为货币，贸易基于魔法道具和知识交换",
    "techLevel": "magic-tech-fusion",
    "techLevelCustom": "", // 当选择custom时填写
    "timeBackground": "魔法复苏后的300年，古老的魔法知识重新被发现"
  },

  // ===== 章节设置 (Chapters) =====
  "chapters": [
    {
      "id": "chapter-1690123456789-1",
      "title": "初入学院",
      "description": "初次踏入魔法学院的大门",
      "content": "你站在艾瑟拉学院的大门前，感受着空气中弥漫的魔法能量...",
      "order": 1,
      "chapterType": "main",
      "parentChapter": null,
      "branchLetter": null,
      "unlockCondition": "",
      "backgroundSetting": "魔法学院的入学大厅，充满了古老而神秘的氛围",
      
      // 章节完成效果
      "completionEffects": {
        "bondPointsChange": 10,
        "greetingChange": "欢迎回到学院！今天的课程还顺利吗？",
        "characterMoodChange": "兴奋且好奇",
        "unlockedFeatures": ["学院地图", "基础魔法"],
        "customEffects": "解锁学院探索功能"
      },

      // 选择项
      "choices": [
        {
          "id": "choice-1",
          "text": "主动与其他新生交流",
          "description": "结识新朋友，了解学院生活",
          "consequences": "好感度+5",
          "nextChapter": "chapter-2a",
          "requirements": [],
          "effectPreview": "获得友谊线索"
        },
        {
          "id": "choice-2", 
          "text": "独自探索学院",
          "description": "安静地观察周围环境",
          "consequences": "获得隐藏信息",
          "nextChapter": "chapter-2b",
          "requirements": [],
          "effectPreview": "发现神秘线索"
        }
      ],
      "nextChapters": ["chapter-2a", "chapter-2b"],

      // 背景图片设置
      "backgroundImage": null, // File对象
      "backgroundImageUrl": "https://example.com/chapter1-bg.jpg",
      "useWorldSettingImage": false,

      // 目标设定 (Objectives) - 环境层
      "environment": "魔法学院的宏大入学大厅，飘浮着发光的魔法符文",
      "timeElements": {
        "season": "秋季",
        "timeOfDay": "黄昏",
        "duration": "30分钟",
        "specialDate": "开学第一天"
      },
      "spatialElements": {
        "location": "艾瑟拉学院主楼大厅",
        "atmosphere": "神秘而庄严",
        "keyObjects": "漂浮的魔法水晶、古老的雕像、魔法门"
      },
      "environmentalElements": {
        "weather": "晴朗",
        "lighting": "魔法水晶的柔和光芒",
        "sounds": "低声的咒语吟唱、脚步声回响",
        "scents": "古老羊皮纸和魔法香料的味道",
        "temperature": "温和，略带魔法能量的温暖"
      },

      // 前因层
      "macroHistory": "魔法复苏后，学院重新开放招生，这是三百年来规模最大的一次",
      "characterPast": "从普通家庭中觉醒魔法天赋的少年/少女",
      "immediateTrigger": "收到学院录取通知书后的第一天到校",

      // 主观设定 (Subjectives) - 角色心理层
      "mentalModel": {
        "coreValues": "知识、友谊、正义",
        "thinkingMode": "好奇且理性的探索型思维",
        "decisionLogic": "基于直觉和道德原则做出判断"
      },
      "emotionalBaseline": {
        "displayedEmotion": "兴奋和紧张",
        "hiddenEmotion": "对未知的不安",
        "emotionalIntensity": 75,
        "emotionalStability": 60
      },
      "memorySystem": {
        "triggeredMemories": "童年时期第一次展现魔法天赋的记忆",
        "emotionalMemories": "家人的期望和祝福",
        "knowledgePriority": "学习魔法的基础知识"
      },

      // 互动层
      "dialogueStrategy": {
        "initiative": 65,
        "listeningRatio": 70,
        "questioningStyle": "充满好奇心的提问",
        "responseSpeed": "thoughtful" // 深思熟虑的
      },
      "relationshipDynamics": {
        "initialGoodwill": 60,
        "trustLevel": 50,
        "intimacyLevel": "初识阶段",
        "powerRelation": "学生与导师/同学的平等关系"
      },
      "goalOrientation": {
        "sceneGoal": "成功完成入学手续，适应学院环境",
        "displayedIntent": "努力学习魔法知识",
        "hiddenIntent": "证明自己的价值和能力",
        "successCriteria": "获得导师和同学的认可"
      }
    },
    
    // 更多章节...
    {
      "id": "chapter-1690123456789-2",
      "title": "魔法启蒙", 
      "description": "第一堂魔法课的体验",
      "content": "...",
      "order": 2,
      "chapterType": "main",
      // ... 其他字段类似
    }
  ],

  // ===== 分支设置 =====
  "branches": [
    {
      "id": "branch-1",
      "fromChapter": "chapter-1",
      "toChapter": "chapter-2a",
      "condition": "选择主动交流",
      "title": "友谊之路",
      "description": "通过与他人交流建立友谊"
    }
  ],

  // ===== 解锁条件 =====
  "unlockConditions": "完成角色创建教程",

  // ===== 奖励系统 =====
  "rewards": [
    {
      "id": "reward-1",
      "name": "学院新生奖励",
      "type": "currency",
      "amount": 100,
      "icon": "✨",
      "description": "完成入学获得的Alphane奖励"
    }
  ],

  // ===== 奖励分配系统 =====
  "rewardAllocation": {
    "totalBudget": 500,
    "usedBudget": 250,
    "allocations": [
      {
        "id": "alloc-1",
        "type": {
          "id": "alphane",
          "name": "Alphane",
          "icon": "✨",
          "cost": 1,
          "description": "Glimmering Dust",
          "color": "bg-yellow-100 text-yellow-700"
        },
        "amount": 100,
        "cost": 1,
        "totalCost": 100,
        "customText": ""
      },
      {
        "id": "alloc-2", 
        "type": {
          "id": "bond_points",
          "name": "Bond Points", 
          "icon": "💖",
          "cost": 5,
          "description": "Affection points",
          "color": "bg-pink-100 text-pink-700"
        },
        "amount": 30,
        "cost": 5,
        "totalCost": 150,
        "customText": ""
      }
    ]
  },

  // ===== 成就系统 =====
  "achievements": [
    {
      "id": "achievement-1",
      "title": "学院新星",
      "description": "成功完成入学并获得导师认可",
      "icon": "🌟",
      "rarity": "common",
      "unlockCondition": "完成第一章节并选择积极选项"
    }
  ]
}