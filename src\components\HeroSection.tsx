'use client';

import React from 'react';
import Image from 'next/image';

interface StatItem {
  label: string;
  value: string | number;
  icon?: React.ComponentType<{ className?: string }>;
}

interface HeroSectionProps {
  backgroundImage?: string;
  title: string;
  subtitle?: string;
  description?: string;
  avatar?: string;
  avatarSize?: 'sm' | 'md' | 'lg' | 'xl';
  stats?: StatItem[];
  actions?: React.ReactNode;
  children?: React.ReactNode;
  height?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'profile' | 'character';
  className?: string;
}

const HeroSection: React.FC<HeroSectionProps> = ({
  backgroundImage,
  title,
  subtitle,
  description,
  avatar,
  avatarSize = 'lg',
  stats = [],
  actions,
  children,
  height = 'lg',
  variant = 'default',
  className = ''
}) => {
  const getHeightClass = () => {
    switch (height) {
      case 'sm': return 'h-[250px] md:h-[300px]';
      case 'md': return 'h-[320px] md:h-[400px]';
      case 'lg': return 'h-[380px] md:h-[480px]';
      case 'xl': return 'h-[450px] md:h-[600px]';
      default: return 'h-[380px] md:h-[480px]';
    }
  };

  const getAvatarSize = () => {
    switch (avatarSize) {
      case 'sm': return 'w-16 h-16 sm:w-20 sm:h-20';
      case 'md': return 'w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28';
      case 'lg': return 'w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-40 lg:h-40';
      case 'xl': return 'w-28 h-28 sm:w-32 sm:h-32 md:w-36 md:h-36 lg:w-44 lg:h-44';
      default: return 'w-24 h-24 sm:w-28 sm:h-28 md:w-32 md:h-32 lg:w-40 lg:h-40';
    }
  };

  return (
    <section className={`relative w-full ${getHeightClass()} bg-romantic-gradient overflow-hidden ${className}`}>
      {/* Background Image */}
      {backgroundImage && (
        <Image
          src={backgroundImage}
          alt="hero background"
          fill
          className="object-cover"
          unoptimized
        />
      )}
      
      {/* Gradient overlay - More subtle on mobile */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/85 via-black/50 to-black/20 md:from-black/80 md:via-black/40 md:to-transparent" />

      {/* Mobile Layout */}
      <div className="relative z-10 h-full flex flex-col md:hidden">
        {/* Top section with basic info */}
        <div className="flex-1 flex items-end p-4 pb-2">
          <div className="flex items-center gap-4 w-full">
            {/* Avatar */}
            {avatar && (
              <div className="relative flex-shrink-0">
                <div className={`${getAvatarSize()} rounded-full border-3 border-white/70 relative shadow-xl bg-white/10 backdrop-blur-sm p-1`}>
                  <div className="w-full h-full rounded-full overflow-hidden">
                    <Image
                      src={avatar}
                      alt={title}
                      width={160}
                      height={160}
                      className="w-full h-full object-cover"
                      unoptimized
                    />
                  </div>
                  {/* Level badge for character variant */}
                  {variant === 'character' && (
                    <div className="absolute -bottom-1 -right-1 bg-romantic-gradient text-white text-xs px-2 py-1 rounded-full font-bold border-2 border-white shadow-lg">
                      Lv.5
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Basic Info */}
            <div className="text-white flex-1 min-w-0">
              <div className="flex items-start justify-between mb-1">
                <h1 className="text-xl font-bold truncate flex-1">
                  {title}
                </h1>
                {/* Toggle button in top-right */}
                {children && (
                  <div className="flex-shrink-0 ml-2">
                    {children}
                  </div>
                )}
              </div>
              {subtitle && (
                <p className="text-sm opacity-90 mb-2">
                  {subtitle}
                </p>
              )}
              {description && (
                <p className="text-sm opacity-85 line-clamp-2">
                  {description}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Bottom section with stats */}
        {stats.length > 0 && (
          <div className="bg-black/20 backdrop-blur-sm mx-4 mb-4 rounded-xl p-3">
            <div className="grid grid-cols-3 gap-2 text-center">
              {stats.slice(0, 6).map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <div key={index} className="min-w-0">
                    <div className="text-base font-bold text-white flex items-center justify-center gap-1 mb-1">
                      {Icon && <Icon className="w-3 h-3" />}
                      <span className="truncate">{stat.value}</span>
                    </div>
                    <div className="text-xs text-white/80 truncate">{stat.label}</div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Actions on mobile */}
        {actions && (
          <div className="px-4 pb-4">
            {actions}
          </div>
        )}
      </div>

      {/* Desktop Layout (unchanged) */}
      <div className="hidden md:flex relative z-10 p-4 md:p-6 items-end gap-4 md:gap-6 w-full h-full">
        {/* Avatar Section */}
        {avatar && (
          <div className="relative flex-shrink-0">
            <div className={`${getAvatarSize()} rounded-full border-4 border-white/60 relative shadow-lg bg-white/10 backdrop-blur-sm p-1`}>
              <div className="w-full h-full rounded-full overflow-hidden">
                <Image
                  src={avatar}
                  alt={title}
                  width={160}
                  height={160}
                  className="w-full h-full object-cover"
                  unoptimized
                />
              </div>
              {/* Optional level badge for character variant */}
              {variant === 'character' && (
                <div className="absolute -bottom-1 -right-1 bg-romantic-gradient text-white text-xs sm:text-sm px-2 py-1 rounded-full font-bold border-2 border-white shadow-lg">
                  Lv.5
                </div>
              )}
            </div>
          </div>
        )}

        {/* Content Section */}
        <div className="text-white flex-1 min-w-0">
          {/* Title and Subtitle */}
          <div className="mb-3 md:mb-4">
            <div className="flex items-start justify-between mb-2">
              <h1 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold truncate flex-1">
                {title}
              </h1>
              {/* Toggle button in top-right */}
              {children && (
                <div className="flex-shrink-0 ml-4">
                  {children}
                </div>
              )}
            </div>
            {subtitle && (
              <p className="text-xs sm:text-sm opacity-90 mb-2">
                {subtitle}
              </p>
            )}
            {description && (
              <p className="text-xs sm:text-sm opacity-90 line-clamp-2 md:line-clamp-3 max-w-md">
                {description}
              </p>
            )}
          </div>

          {/* Actions */}
          {actions && (
            <div className="mb-3 md:mb-4">
              {actions}
            </div>
          )}

          {/* Stats Grid */}
          {stats.length > 0 && (
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 md:gap-4 text-center">
              {stats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <div key={index} className="min-w-0">
                    <div className="text-lg sm:text-xl md:text-2xl font-bold text-white flex items-center justify-center gap-1">
                      {Icon && <Icon className="w-4 h-4 sm:w-5 sm:h-5" />}
                      {stat.value}
                    </div>
                    <div className="text-xs text-white/80 truncate">{stat.label}</div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
