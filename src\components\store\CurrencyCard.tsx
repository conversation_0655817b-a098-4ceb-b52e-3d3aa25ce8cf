'use client';

import React, { useState } from 'react';
import { Star, ArrowRight, Check, Loader2 } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

export interface CurrencyItem {
  id: string;
  name: string;
  description: string;
  uses: string;
  icon: React.ComponentType<any>;
  color: string;
  balance: number;
  exchangeRate: string;
  diamondCost: number;
}

export interface DiamondPackage {
  name: string;
  price: number;
  amount: number;
  bonus?: number;
  popular?: boolean;
  discount?: number;
  limited?: boolean;
  bestValue?: boolean;
}

interface CurrencyCardProps {
  currency: CurrencyItem;
  onExchange?: (currency: CurrencyItem, amount: number) => void;
  isLoading?: boolean;
  variant?: 'exchange' | 'display';
  lang: string;
}

interface DiamondPackageCardProps {
  package: DiamondPackage;
  onPurchase?: (pkg: DiamondPackage) => void;
  isLoading?: boolean;
  lang: string;
}

export const CurrencyCard: React.FC<CurrencyCardProps> = ({
  currency,
  onExchange,
  isLoading = false,
  variant = 'display',
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [exchangeAmount, setExchangeAmount] = useState<number>(1);
  const Icon = currency.icon;

  const handleExchange = () => {
    if (onExchange && exchangeAmount > 0) {
      onExchange(currency, exchangeAmount);
    }
  };

  const calculateReceived = () => {
    return Math.floor(exchangeAmount / currency.diamondCost);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 group p-4 md:p-6 min-h-[400px] flex flex-col hover:border-blue-300 dark:hover:border-blue-600">

      {/* Header with tag-like styling - matching FeaturedCard */}
      <div className="flex items-start justify-between mb-3 sm:mb-4">
        <div className="flex flex-wrap items-center gap-1 sm:gap-2 flex-1 mr-2">
          <div className="inline-flex items-center gap-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs font-medium">
            <Icon className="w-3 h-3" />
            {t('store.currencyCard.exchangeCurrency')}
          </div>
        </div>
        {variant === 'display' && (
          <div className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-2 py-1 rounded-full text-xs font-bold flex-shrink-0">
            {currency.balance.toLocaleString()}
          </div>
        )}
      </div>

      {/* Content - Flexible with consistent layout */}
      <div className="flex-1 flex flex-col">
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
            {currency.name}
          </h3>
          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-3 mb-4">
            {currency.description}
          </p>

          {/* Usage info with icon */}
          <div className="flex items-center gap-2 mb-4">
            <div className={`w-8 h-8 bg-gradient-to-br ${currency.color} rounded-lg flex items-center justify-center flex-shrink-0 shadow-lg`}>
              <Icon className="w-4 h-4 text-white" />
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-500 line-clamp-2">
              <strong className="text-gray-700 dark:text-gray-300">{t('store.currencyCard.uses')}:</strong> {currency.uses}
            </div>
          </div>

          {/* Current Balance for display variant */}
          {variant === 'display' && (
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('store.currencyCard.currentBalance')}</span>
                <span className="text-lg font-bold text-blue-700 dark:text-blue-300">
                  {currency.balance.toLocaleString()}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Exchange Rate - Fixed at bottom */}
        <div className="mt-auto">
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800/50 dark:to-blue-900/20 border border-gray-200 dark:border-gray-600 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('store.currencyCard.exchangeRate')}</span>
              <span className="text-sm text-blue-600 dark:text-blue-400 font-semibold">
                {currency.exchangeRate}
              </span>
            </div>
            
            {variant === 'exchange' && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="flex-1">
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                      {t('store.currencyCard.endoraToSpend')}
                    </label>
                    <input
                      type="number"
                      min="1"
                      value={exchangeAmount}
                      onChange={(e) => setExchangeAmount(Math.max(1, parseInt(e.target.value) || 1))}
                      className="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-background text-foreground text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>
                  <div className="text-center">
                    <ArrowRight className="w-4 h-4 text-gray-400 mx-2" />
                  </div>
                  <div className="flex-1">
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                      {t('store.currencyCard.youllReceive')}
                    </label>
                    <div className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-sm font-medium text-gray-900 dark:text-gray-100">
                      {calculateReceived().toLocaleString()}
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={handleExchange}
                  disabled={isLoading || exchangeAmount <= 0}
                  className={`w-full py-2.5 px-4 rounded-lg font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2 ${
                    !isLoading && exchangeAmount > 0
                      ? `bg-gradient-to-r ${currency.color} text-white hover:shadow-lg hover:shadow-blue-500/30 transform hover:scale-105`
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      {t('store.currencyCard.processing')}
                    </>
                  ) : (
                    <>
                      <Icon className="w-4 h-4" />
                      {t('store.currencyCard.exchangeNow')}
                      <ArrowRight className="w-4 h-4" />
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export const DiamondPackageCard: React.FC<DiamondPackageCardProps> = ({
  package: pkg,
  onPurchase,
  isLoading = false,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const handlePurchase = () => {
    if (onPurchase) {
      onPurchase(pkg);
    }
  };

  // Calculate value proposition
  const baseValue = pkg.amount / pkg.price;
  const totalValue = (pkg.amount + (pkg.bonus || 0)) / pkg.price;
  const valueIncrease = pkg.bonus ? Math.round(((totalValue - baseValue) / baseValue) * 100) : 0;

  return (
    <div className={`relative bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 group p-4 md:p-6 min-h-[400px] flex flex-col ${
      pkg.popular || pkg.bestValue
        ? 'ring-2 ring-yellow-300 dark:ring-yellow-600 shadow-lg shadow-yellow-500/20 transform hover:scale-105'
        : 'hover:border-yellow-300 dark:hover:border-yellow-600'
    }`}>

      {/* Header with badges and discount - matching FeaturedCard style */}
      <div className="flex items-start justify-between mb-3 sm:mb-4">
        <div className="flex flex-wrap items-center gap-1 sm:gap-2 flex-1 mr-2">
          {pkg.popular && (
            <div className="inline-flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900/50 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full text-xs font-medium">
              <Star className="w-3 h-3" />
              {t('store.currencyCard.popularChoice')}
            </div>
          )}
          {pkg.bestValue && (
            <div className="inline-flex items-center gap-1 bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
              <Check className="w-3 h-3" />
              {t('store.currencyCard.bestValue')}
            </div>
          )}
          {pkg.limited && (
            <div className="inline-flex items-center gap-1 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200 px-2 py-1 rounded-full text-xs font-medium animate-pulse">
              <Star className="w-3 h-3" />
              {t('store.currencyCard.limitedTime')}
            </div>
          )}
        </div>
        {pkg.discount && (
          <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold flex-shrink-0">
            {t('store.currencyCard.discount', { discount: pkg.discount })}
          </div>
        )}
      </div>

      {/* Content - Flexible with consistent layout */}
      <div className="flex-1 flex flex-col">
        <div className="flex-1">
          <h3 className="text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-yellow-600 dark:group-hover:text-yellow-400 transition-colors line-clamp-2">
            {pkg.name}
          </h3>

          {/* Price display */}
          <div className="flex items-center gap-2 mb-4">
            <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center flex-shrink-0 shadow-lg">
              <Star className="w-4 h-4 text-white" fill="currentColor" />
            </div>
            <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">
              ${pkg.price}
            </div>
          </div>

          {/* Diamond breakdown */}
          <div className="space-y-2 mb-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('store.currencyCard.baseEndora')}
              </span>
              <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                {pkg.amount.toLocaleString()}
              </span>
            </div>

            {pkg.bonus && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-green-600 dark:text-green-400">
                  {t('store.currencyCard.bonusEndora')}
                </span>
                <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                  +{pkg.bonus.toLocaleString()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Fixed Bottom Section */}
        <div className="mt-auto space-y-4">
          {/* Total value highlight */}
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('store.currencyCard.totalEndora')}
              </span>
              <span className="text-lg font-bold text-yellow-700 dark:text-yellow-300">
                {(pkg.amount + (pkg.bonus || 0)).toLocaleString()}
              </span>
            </div>
            {pkg.bonus && (
              <div className="text-xs text-green-600 dark:text-green-400">
                {t('store.currencyCard.moreValue', { percentage: valueIncrease, endoraPerDollar: Math.round(totalValue) })}
              </div>
            )}
          </div>

          {/* Action button */}
          <button
            onClick={handlePurchase}
            disabled={isLoading}
            className={`w-full py-2.5 px-4 rounded-lg font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2 ${
              !isLoading
                ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:shadow-lg hover:shadow-yellow-500/30 hover:from-yellow-500 hover:to-orange-600 transform hover:scale-105'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
            }`}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                {t('store.currencyCard.processing')}
              </>
            ) : (
              <>
                <Star className="w-4 h-4" fill="currentColor" />
                {t('store.currencyCard.purchaseNow')}
                <ArrowRight className="w-4 h-4" />
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CurrencyCard;
