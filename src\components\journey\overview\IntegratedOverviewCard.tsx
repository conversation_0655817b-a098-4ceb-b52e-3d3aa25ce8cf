import React from 'react';
import { TrendingUp, Zap, Target, Award } from 'lucide-react';
import { JourneyCard } from '../shared/JourneyCard';
import { ProgressRing } from '../shared/ProgressRing';
import { useTranslation } from '@/app/i18n/client';

interface IntegratedOverviewCardProps {
  currentLevel: number;
  membershipTier: 'standard' | 'pass' | 'diamond' | 'metaverse';
  progressPercentage: number;
  getLevelName: (level: number) => string;
  currentTrophies: number;
  currentRank: number;
  dailyProgress: number;
  currentStreak: number;
  onSettings?: () => void;
  lang: string;
}

export const IntegratedOverviewCard: React.FC<IntegratedOverviewCardProps> = ({
  currentLevel,
  membershipTier,
  progressPercentage,
  getLevelName,
  currentTrophies,
  currentRank,
  dailyProgress,
  currentStreak,
  onSettings,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');

  return (
    <JourneyCard
      gradient="from-purple-600/15 via-pink-600/15 to-indigo-600/15"
      animated={true}
      className="relative overflow-hidden"
    >
      {/* 游戏化背景装饰 */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-yellow-400/10 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-cyan-400/10 to-transparent rounded-full translate-y-12 -translate-x-12"></div>

      {/* 主要内容区域 - 紧凑布局 */}
      <div className="relative space-y-4">
        {/* 等级与进度 - 水平布局 */}
        <div className="flex items-center gap-4 md:gap-6">
          {/* 进度环 - 更小尺寸 */}
          <div className="flex-shrink-0">
            <div className="relative">
              <ProgressRing
                progress={progressPercentage}
                size="md"
                label={`${currentLevel}`}
              />
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                <Zap className="w-3 h-3 text-white" />
              </div>
            </div>
          </div>

          {/* 等级信息与进度条 - 紧凑版 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg md:text-xl font-bold text-foreground truncate">
                {t(getLevelName(currentLevel))}
              </h3>
              <span className="text-sm font-bold text-purple-400 shrink-0">
                {progressPercentage}%
              </span>
            </div>
            
            {/* 简化的进度条 */}
            <div className="relative">
              <div className="w-full bg-white/15 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-1000 ease-out relative overflow-hidden"
                  style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shine"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 核心统计 - 网格布局 */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          {/* 奖杯数 */}
          <div className="bg-gradient-to-br from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-3 text-center group hover:scale-105 transition-all">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Award className="w-4 h-4 text-yellow-400" />
              <span className="text-lg font-bold text-yellow-400">{currentTrophies.toLocaleString()}</span>
            </div>
            <div className="text-xs text-foreground/60">{t('journey.overview.totalTrophies')}</div>
            <div className="text-xs text-green-400 mt-1">+12 {t('journey.overview.today')}</div>
          </div>

          {/* 排名 */}
          <div className="bg-gradient-to-br from-pink-500/20 to-purple-500/20 border border-pink-500/30 rounded-lg p-3 text-center group hover:scale-105 transition-all">
            <div className="flex items-center justify-center gap-1 mb-1">
              <TrendingUp className="w-4 h-4 text-pink-400" />
              <span className="text-lg font-bold text-pink-400">#{currentRank}</span>
            </div>
            <div className="text-xs text-foreground/60">{t('journey.overview.globalRank')}</div>
            <div className="text-xs text-green-400 mt-1">+23</div>
          </div>

          {/* 日常进度 */}
          <div className="bg-gradient-to-br from-blue-500/20 to-cyan-500/20 border border-blue-500/30 rounded-lg p-3 text-center group hover:scale-105 transition-all">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Target className="w-4 h-4 text-blue-400" />
              <span className="text-lg font-bold text-blue-400">{dailyProgress}%</span>
            </div>
            <div className="text-xs text-foreground/60">{t('journey.overview.dailyGoals')}</div>
            <div className="text-xs text-foreground/50 mt-1">2/3 {t('journey.overview.completed')}</div>
          </div>

          {/* 连击 */}
          <div className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-lg p-3 text-center group hover:scale-105 transition-all">
            <div className="flex items-center justify-center gap-1 mb-1">
              <span className="text-sm">🔥</span>
              <span className="text-lg font-bold text-green-400">{currentStreak}</span>
            </div>
            <div className="text-xs text-foreground/60">{t('journey.overview.currentStreak')}</div>
            <div className="text-xs text-orange-400 mt-1">{t('journey.overview.onFire')}</div>
          </div>
        </div>

        {/* 底部快速统计 - 更紧凑 */}
        <div className="flex items-center p-3 bg-white/5 border border-white/10 rounded-lg">
          <div className="flex-1 text-center">
            <div className="text-sm font-bold text-indigo-400">15%</div>
            <div className="text-xs text-foreground/60">{t('journey.overview.topPercentile')}</div>
          </div>
          <div className="w-px h-8 bg-white/20"></div>
          <div className="flex-1 text-center">
            <div className="text-sm font-bold text-purple-400">+156</div>
            <div className="text-xs text-foreground/60">{t('journey.overview.weeklyGain')}</div>
          </div>
          <div className="w-px h-8 bg-white/20"></div>
          <div className="flex-1 text-center">
            <div className="text-sm font-bold text-pink-400">12</div>
            <div className="text-xs text-foreground/60">{t('journey.overview.bestStreak')}</div>
          </div>
        </div>
      </div>
    </JourneyCard>
  );
}; 