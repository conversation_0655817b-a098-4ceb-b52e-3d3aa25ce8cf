import { Request, Response } from 'express';
import { AuthService } from '../services/AuthService';
import { UserService } from '../services/UserService';
import { validationResult } from 'express-validator';

export class AuthController {
  private authService: AuthService;
  private userService: UserService;

  constructor() {
    this.authService = new AuthService();
    this.userService = new UserService();
  }

  /**
   * Login or register user
   * POST /api/v1/auth
   */
  public async authenticate(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { email, password, loginMethod = 'password' } = req.body;

      if (loginMethod === 'password') {
        // Login with password
        const result = await this.authService.loginWithEmailAndPassword(email, password);
        
        if (result.success) {
          res.json({
            success: true,
            message: 'Login successful',
            data: {
              user: result.user,
              token: result.token,
              refreshToken: result.refreshToken
            }
          });
        } else {
          res.status(401).json({
            success: false,
            message: result.message || 'Invalid credentials'
          });
        }
      } else {
        // Register new user
        const result = await this.authService.registerWithEmailAndPassword(email, password);
        
        if (result.success) {
          res.status(201).json({
            success: true,
            message: 'Registration successful',
            data: {
              user: result.user,
              token: result.token,
              refreshToken: result.refreshToken
            }
          });
        } else {
          res.status(400).json({
            success: false,
            message: result.message || 'Registration failed'
          });
        }
      }
    } catch (error) {
      console.error('Authentication error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Send OTP for verification
   * POST /api/v1/auth/send-otp
   */
  public async sendOTP(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { email } = req.body;
      const result = await this.authService.sendOTP(email);

      if (result.success) {
        res.json({
          success: true,
          message: 'OTP sent successfully',
          data: {
            otpExpiry: result.otpExpiry
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message || 'Failed to send OTP'
        });
      }
    } catch (error) {
      console.error('OTP sending error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Verify OTP and login/register
   * POST /api/v1/auth/verify-otp
   */
  public async verifyOTP(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { email, otp } = req.body;
      const result = await this.authService.verifyOTP(email, otp);

      if (result.success) {
        res.json({
          success: true,
          message: 'OTP verified successfully',
          data: {
            user: result.user,
            token: result.token,
            refreshToken: result.refreshToken
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message || 'Invalid OTP'
        });
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Refresh access token
   * POST /api/v1/auth/refresh
   */
  public async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;
      
      if (!refreshToken) {
        res.status(400).json({
          success: false,
          message: 'Refresh token is required'
        });
        return;
      }

      const result = await this.authService.refreshToken(refreshToken);

      if (result.success) {
        res.json({
          success: true,
          message: 'Token refreshed successfully',
          data: {
            token: result.token,
            refreshToken: result.refreshToken
          }
        });
      } else {
        res.status(401).json({
          success: false,
          message: result.message || 'Invalid refresh token'
        });
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Logout user
   * POST /api/v1/auth/logout
   */
  public async logout(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;
      
      if (refreshToken) {
        await this.authService.logout(refreshToken);
      }

      res.json({
        success: true,
        message: 'Logged out successfully'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Get current user profile
   * GET /api/v1/auth/me
   */
  public async getCurrentUser(req: Request, res: Response): Promise<void> {
    try {
      const userId = (req as import('../middleware/auth').AuthenticatedRequest).user.id;
      const user = await this.userService.getUserById(userId);

      if (user) {
        res.json({
          success: true,
          data: user
        });
      } else {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }
    } catch (error) {
      console.error('Get current user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}