'use client';

import React from 'react';
import { Flame, Gem, Puzzle, Droplet, LucideIcon } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface CurrencyDisplayProps {
  value: number | undefined;
  type: 'alphane' | 'endora' | 'serotile' | 'oxytol';
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  t?: (key: string) => string; // 可选的翻译函数
}

const currencyConfig = {
  alphane: {
    icon: Flame,
    color: 'romantic-primary',
    borderColor: 'border-romantic-primary/20 dark:border-romantic-primary/30',
    bgColor: 'bg-romantic-gradient',
    bgColorSolid: 'bg-romantic-primary'
  },
  endora: {
    icon: Gem,
    color: 'romantic-accent',
    borderColor: 'border-romantic-accent/20 dark:border-romantic-accent/30',
    bgColor: 'bg-romantic-accent',
    bgColorSolid: 'bg-romantic-accent'
  },
  serotile: {
    icon: Puzzle,
    color: 'romantic-pink',
    borderColor: 'border-romantic-pink/20 dark:border-romantic-pink/30',
    bgColor: 'bg-romantic-accent-gradient',
    bgColorSolid: 'bg-romantic-pink'
  },
  oxytol: {
    icon: Droplet,
    color: 'romantic-accent-pink',
    borderColor: 'border-romantic-accent-pink/20 dark:border-romantic-accent-pink/30',
    bgColor: 'bg-gradient-to-br from-romantic-accent-pink to-romantic-pink',
    bgColorSolid: 'bg-romantic-accent-pink'
  }
};

const CurrencyDisplay: React.FC<CurrencyDisplayProps> = ({ 
  value, 
  type, 
  className = '', 
  size = 'md',
  t 
}) => {
  const config = currencyConfig[type];
  const Icon = config.icon;
  
  // Get currency name, prioritize translation function, otherwise use fallback names
  const getCurrencyName = () => {
    if (t) {
      return t(`tokens.${type}`);
    }
    // Fallback names
    const fallbackNames = {
      alphane: 'Glimmering Dust',
      endora: 'Joy Crystal',
      serotile: 'Memory Puzzle',
      oxytol: 'Bond Dew'
    };
    return fallbackNames[type];
  };
  
  const sizeClasses = {
    sm: {
      container: 'p-2',
      icon: 'w-6 h-6',
      iconSize: 12,
      text: 'text-xs'
    },
    md: {
      container: 'p-3',
      icon: 'w-8 h-8',
      iconSize: 16,
      text: 'text-sm'
    },
    lg: {
      container: 'p-4',
      icon: 'w-10 h-10',
      iconSize: 20,
      text: 'text-base'
    }
  };
  
  const sizes = sizeClasses[size];

  return (
    <div 
      className={`bg-white dark:bg-black/30 backdrop-blur-sm border ${config.borderColor} rounded-lg ${sizes.container} text-center ${className}`}
      title={`${getCurrencyName()}: ${formatCurrency(value)}`}
    >
      <div className={`${sizes.icon} ${config.bgColor} rounded-full flex items-center justify-center mx-auto mb-2 shadow-lg`}>
        <Icon className="text-white" size={sizes.iconSize} />
      </div>
      <p className={`${sizes.text} font-bold text-gray-800 dark:text-gray-100`}>
        {formatCurrency(value)}
      </p>
    </div>
  );
};

export default CurrencyDisplay; 