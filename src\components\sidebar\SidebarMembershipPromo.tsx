'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslation } from '@/app/i18n/client';

interface SidebarMembershipPromoProps {
  lang: string;
}

const SidebarMembershipPromo: React.FC<SidebarMembershipPromoProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');

  return (
    <div className="bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl p-4 text-white">
      <h4 className="font-bold mb-1">{t('common.promoTitle')}</h4>
      <p className="text-xs opacity-90 mb-3">{t('common.promoSubtitle')}</p>
      <Link
        href={`/${lang}/store`}
        className="block w-full bg-white text-purple-600 font-semibold py-2 px-4 rounded-lg text-sm text-center hover:bg-silver dark:hover:bg-gray-800 transition-colors"
      >
        {t('common.upgradeNow')}
      </Link>
    </div>
  );
};

export default SidebarMembershipPromo;
