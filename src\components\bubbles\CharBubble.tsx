'use client';

import { FC } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { RefreshCw, Play } from 'lucide-react';

interface CharBubbleProps {
  message: {
    text: string;
    voice?: {
      duration: string;
      url?: string;
    };
  };
  character: {
    id?: string;
    character_avatar?: string;
    name?: string;
  };
}

const CharBubble: FC<CharBubbleProps> = ({ message, character }) => {
  const params = useParams() as { lang?: string };
  const lang = params?.lang ?? 'en';
  return (
    <div className="flex flex-col items-start w-full">
      <div className="flex items-end gap-1.5 justify-start">
        {/* Avatar */}
        {character.character_avatar ? (
          <Link href={`/${lang}/character/${character.id ?? ''}`} prefetch={false}>
            <Image 
              src={character.character_avatar.replace(/'/g, "")} 
              alt={character.name || 'avatar'} 
              width={40} 
              height={40} 
              className="rounded-full w-10 h-10" 
              unoptimized
            />
          </Link>
        ) : (
          <div className="w-10 h-10 rounded-full bg-gray-700" />
        )}
        
        {/* Main Bubble Content */}
        <div className="max-w-xs md:max-w-md rounded-2xl shadow-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-none relative">
          {/* Voice indicator in top-left corner */}
          {message.voice && (
            <div className="absolute -top-3 -left-3 bg-green-500 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1 shadow-md">
              <Play size={10} className="fill-white" />
              <span>{message.voice.duration}</span>
            </div>
          )}
          {/* Message Text */}
          <div className="px-4 py-3">
            <p className="text-sm">{message.text}</p>
          </div>
        </div>
      </div>
      
      {/* Metadata Row (Below Bubble) */}
      <div className="flex items-center gap-3 mt-1.5 ml-12 text-white">
        <span className="text-xs">A few seconds ago</span>
        <button title="Regenerate" className="hover:opacity-80 transition-opacity">
          <RefreshCw size={14} className="text-white" />
        </button>
      </div>
    </div>
  );
};

export default CharBubble; 