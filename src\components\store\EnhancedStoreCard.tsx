import React from 'react';
import { LucideIcon } from 'lucide-react';

interface EnhancedStoreCardProps {
  children: React.ReactNode;
  className?: string;
  gradient?: string;
  glowEffect?: boolean;
  vipGlow?: boolean;
  animated?: boolean;
  neonReflection?: boolean;
}

interface StoreCardHeaderProps {
  icon: LucideIcon;
  title: string;
  subtitle?: string;
  iconGradient?: string;
  actions?: React.ReactNode;
}

interface StoreCardStatsProps {
  stats: Array<{
    label: string;
    value: string | number;
    color?: string;
    sublabel?: string;
    trend?: string;
  }>;
  columns?: 2 | 3 | 4;
}

// Enhanced Store Card Component (ISFJ Style - Warm, Detailed, Caring)
export const EnhancedStoreCard: React.FC<EnhancedStoreCardProps> = ({
  children,
  className = '',
  gradient = 'from-purple-500/10 via-pink-500/10 to-purple-500/10',
  glowEffect = false,
  vipGlow = false,
  animated = true,
  neonReflection = true
}) => {
  return (
    <div className={`relative overflow-hidden rounded-2xl backdrop-blur-xl bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10 shadow-xl ${animated ? 'transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl' : ''} ${className}`}>
      {/* Background gradient with enhanced warmth */}
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-50 ${animated ? 'animate-gradient' : ''}`}></div>

      {/* VIP golden shimmer effect */}
      {vipGlow && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-400/10 to-transparent animate-shimmer"></div>
      )}

      {/* Enhanced glow effect with ISFJ warmth */}
      {glowEffect && (
        <div className="absolute inset-0 rounded-2xl blur-sm bg-gradient-to-r from-purple-400/20 via-pink-400/20 to-purple-400/20 animate-pulse -z-10"></div>
      )}

      {/* Neon reflection at bottom */}
      {neonReflection && (
        <>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-purple-400/50 to-transparent animate-shimmer"></div>
          <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-purple-500/30 via-pink-500/50 to-purple-500/30 animate-pulse"></div>
        </>
      )}

      {/* Caring decorative particles */}
      {animated && (
        <>
          <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-pink-400/20 to-transparent rounded-full -translate-y-10 translate-x-10 animate-float"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-purple-400/20 to-transparent rounded-full translate-y-8 -translate-x-8 animate-float-delayed"></div>
          {/* Additional warm sparkles */}
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-yellow-400/30 rounded-full animate-twinkle"></div>
          <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-pink-400/40 rounded-full animate-twinkle" style={{ animationDelay: '1s' }}></div>
        </>
      )}

      {/* Content with enhanced padding for comfort */}
      <div className="relative p-6">
        {children}
      </div>
    </div>
  );
};

// Store Card Header Component
export const StoreCardHeader: React.FC<StoreCardHeaderProps> = ({
  icon: Icon,
  title,
  subtitle,
  iconGradient = 'from-purple-500 to-pink-500',
  actions
}) => {
  return (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <div className={`w-12 h-12 bg-gradient-to-br ${iconGradient} rounded-xl flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-300`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
        <div>
          <h3 className="text-xl font-bold text-foreground">{title}</h3>
          {subtitle && <p className="text-foreground/70 text-sm">{subtitle}</p>}
        </div>
      </div>
      {actions && (
        <div className="flex gap-2">
          {actions}
        </div>
      )}
    </div>
  );
};

// Enhanced Statistics Grid Component
export const StoreCardStats: React.FC<StoreCardStatsProps> = ({
  stats,
  columns = 4
}) => {
  const gridCols = {
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-2 md:grid-cols-4'
  };

  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'purple':
        return 'bg-purple-50/50 dark:bg-purple-900/20 border-purple-200/30 dark:border-purple-800/30 text-purple-600 dark:text-purple-400 hover:bg-purple-100/50 dark:hover:bg-purple-900/30';
      case 'pink':
        return 'bg-pink-50/50 dark:bg-pink-900/20 border-pink-200/30 dark:border-pink-800/30 text-pink-600 dark:text-pink-400 hover:bg-pink-100/50 dark:hover:bg-pink-900/30';
      case 'blue':
        return 'bg-blue-50/50 dark:bg-blue-900/20 border-blue-200/30 dark:border-blue-800/30 text-blue-600 dark:text-blue-400 hover:bg-blue-100/50 dark:hover:bg-blue-900/30';
      case 'green':
        return 'bg-green-50/50 dark:bg-green-900/20 border-green-200/30 dark:border-green-800/30 text-green-600 dark:text-green-400 hover:bg-green-100/50 dark:hover:bg-green-900/30';
      case 'orange':
        return 'bg-orange-50/50 dark:bg-orange-900/20 border-orange-200/30 dark:border-orange-800/30 text-orange-600 dark:text-orange-400 hover:bg-orange-100/50 dark:hover:bg-orange-900/30';
      case 'yellow':
        return 'bg-yellow-50/50 dark:bg-yellow-900/20 border-yellow-200/30 dark:border-yellow-800/30 text-yellow-600 dark:text-yellow-400 hover:bg-yellow-100/50 dark:hover:bg-yellow-900/30';
      default:
        return 'bg-gray-50/50 dark:bg-gray-900/20 border-gray-200/30 dark:border-gray-800/30 text-gray-600 dark:text-gray-400 hover:bg-gray-100/50 dark:hover:bg-gray-900/30';
    }
  };

  return (
    <div className={`grid ${gridCols[columns]} gap-4`}>
      {stats.map((stat, index) => (
        <div 
          key={index}
          className={`text-center p-4 rounded-xl border hover:scale-105 transition-all duration-300 cursor-pointer ${getColorClasses(stat.color)}`}
        >
          <div className="text-3xl font-bold mb-1">
            {typeof stat.value === 'number' ? stat.value.toLocaleString() : stat.value}
          </div>
          <div className="text-xs text-foreground/60">{stat.label}</div>
          {stat.sublabel && (
            <div className="text-xs mt-1 font-medium">{stat.sublabel}</div>
          )}
          {stat.trend && (
            <div className="text-xs mt-1 text-green-500 font-medium">{stat.trend}</div>
          )}
        </div>
      ))}
    </div>
  );
};

// Enhanced Button Component for Store
export const StoreButton: React.FC<{
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
  neonGlow?: boolean;
}> = ({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  onClick,
  disabled = false,
  neonGlow = true
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600';
      case 'secondary':
        return 'bg-white/10 dark:bg-black/10 text-foreground border border-white/20 hover:bg-white/20 dark:hover:bg-black/20';
      case 'accent':
        return 'bg-gradient-to-r from-amber-500 to-orange-500 text-white hover:from-amber-600 hover:to-orange-600';
      default:
        return 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'md':
        return 'px-4 py-2 text-base';
      case 'lg':
        return 'px-6 py-3 text-lg';
      default:
        return 'px-4 py-2 text-base';
    }
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        relative overflow-hidden rounded-lg font-semibold transition-all duration-300
        ${getVariantClasses()}
        ${getSizeClasses()}
        ${neonGlow ? 'hover:shadow-lg hover:shadow-current/30' : ''}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105 active:scale-95'}
        ${className}
      `}
    >
      {/* Neon glow effect */}
      {neonGlow && !disabled && (
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent animate-pulse"></div>
      )}
      
      {/* Shimmer effect on hover */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-700 ease-out"></div>
      
      <span className="relative z-10">{children}</span>
    </button>
  );
};
