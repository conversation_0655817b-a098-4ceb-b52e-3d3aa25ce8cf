import { Achievement, AchievementStats } from '@/types/achievements';

export const mockAchievements: Achievement[] = [
  // Beginner Achievements
  {
    id: 'first-steps',
    name: 'First Steps',
    description: 'Complete your first conversation with an AI character',
    category: 'beginner',
    rarity: 'bronze',
    status: 'completed',
    currentProgress: 10,
    maxProgress: 10,
    points: 50,
    requirements: ['Send 10 messages in a single conversation'],
    rewards: [
      {
        type: 'currency',
        name: 'Glimmering Dust',
        description: 'Basic currency for platform activities',
        amount: 100,
        currencyType: 'alphane'
      },
      {
        type: 'badge',
        name: 'Newcomer Badge',
        description: 'A special badge for new users'
      }
    ],
    earnedDate: new Date('2024-01-15'),
    tips: 'Start chatting with any character to begin your journey!'
  },
  {
    id: 'conversationalist',
    name: 'Conversationalist',
    description: 'Master of engaging conversations',
    category: 'interaction',
    rarity: 'silver',
    status: 'inProgress',
    currentProgress: 67,
    maxProgress: 100,
    points: 150,
    requirements: ['Complete 100 total conversations', 'Maintain average conversation length of 15+ messages'],
    rewards: [
      {
        type: 'currency',
        name: '<PERSON> Crystal',
        description: 'Premium currency for advanced features',
        amount: 50,
        currencyType: 'endora'
      },
      {
        type: 'title',
        name: 'Conversation Master',
        description: 'Special title display on your profile'
      }
    ],
    tips: 'Explore different characters and topics to expand your social skills'
  },
  {
    id: 'memory-keeper',
    name: 'Memory Keeper',
    description: 'Guardian of precious moments',
    category: 'interaction',
    rarity: 'gold',
    status: 'inProgress',
    currentProgress: 23,
    maxProgress: 50,
    points: 250,
    requirements: ['Create 50 memory capsules', 'Have memories referenced by AI 20+ times'],
    rewards: [
      {
        type: 'item',
        name: 'Memory Vault Expansion',
        description: 'Increase your memory storage capacity'
      },
      {
        type: 'currency',
        name: 'Memory Puzzle',
        description: 'Special collectible currency',
        amount: 10,
        currencyType: 'serotile'
      }
    ],
    tips: 'Save meaningful conversations as memory capsules'
  },
  {
    id: 'character-creator',
    name: 'Character Creator',
    description: 'Bring imagination to life',
    category: 'creation',
    rarity: 'gold',
    status: 'completed',
    currentProgress: 1,
    maxProgress: 1,
    points: 300,
    requirements: ['Create your first character', 'Character receives 10+ interactions'],
    rewards: [
      {
        type: 'privilege',
        name: 'Creator Tools Access',
        description: 'Unlock advanced character creation features'
      },
      {
        type: 'currency',
        name: 'Joy Crystal',
        description: 'Premium currency reward',
        amount: 100,
        currencyType: 'endora'
      }
    ],
    earnedDate: new Date('2024-01-20'),
    tips: 'Use the character creation tool to design unique personalities'
  },
  {
    id: 'socialite',
    name: 'Socialite',
    description: 'Building bonds across the community',
    category: 'social',
    rarity: 'silver',
    status: 'inProgress',
    currentProgress: 12,
    maxProgress: 20,
    points: 200,
    requirements: ['Follow 20 different creators', 'Like 50 character cards', 'Share 5 characters'],
    rewards: [
      {
        type: 'badge',
        name: 'Community Champion',
        description: 'Shows your active participation in the community'
      },
      {
        type: 'currency',
        name: 'Bond Dew',
        description: 'Special currency for relationship building',
        amount: 75,
        currencyType: 'oxytol'
      }
    ],
    tips: 'Discover amazing creators in the community section'
  },
  {
    id: 'streak-master',
    name: 'Streak Master',
    description: 'Consistency is key to greatness',
    category: 'special',
    rarity: 'platinum',
    status: 'inProgress',
    currentProgress: 15,
    maxProgress: 30,
    points: 500,
    requirements: ['Maintain a 30-day interaction streak', 'Complete daily tasks for 30 consecutive days'],
    rewards: [
      {
        type: 'item',
        name: 'Eternal Flame Badge',
        description: 'A magnificent badge that shows your dedication'
      },
      {
        type: 'privilege',
        name: 'Streak Master Perks',
        description: 'Exclusive bonuses and faster progress'
      }
    ],
    tips: 'Log in daily and complete at least one conversation'
  },
  {
    id: 'explorer',
    name: 'Explorer',
    description: 'Adventurer of infinite possibilities',
    category: 'interaction',
    rarity: 'silver',
    status: 'inProgress',
    currentProgress: 18,
    maxProgress: 25,
    points: 175,
    requirements: ['Chat with 25 different characters', 'Try characters from 5 different genres'],
    rewards: [
      {
        type: 'currency',
        name: 'Memory Puzzle',
        description: 'Collectible exploration reward',
        amount: 5,
        currencyType: 'serotile'
      },
      {
        type: 'badge',
        name: 'World Wanderer',
        description: 'Badge for exploring diverse characters'
      }
    ],
    tips: 'Try characters from different genres and personalities'
  },
  {
    id: 'storyteller',
    name: 'Storyteller',
    description: 'Weaver of compelling narratives',
    category: 'creation',
    rarity: 'gold',
    status: 'locked',
    currentProgress: 0,
    maxProgress: 5,
    points: 400,
    requirements: ['Create and publish 5 stories', 'Stories receive 50+ total plays', 'Average story rating 4.0+'],
    rewards: [
      {
        type: 'title',
        name: 'Master Storyteller',
        description: 'Prestigious title for narrative creators'
      },
      {
        type: 'item',
        name: 'Storyteller\'s Quill',
        description: 'Legendary item for story creators'
      }
    ],
    tips: 'Use the story creation tool to craft engaging narratives'
  },
  {
    id: 'perfectionist',
    name: 'Perfectionist',
    description: 'Excellence in every detail',
    category: 'creation',
    rarity: 'diamond',
    status: 'locked',
    currentProgress: 0,
    maxProgress: 100,
    points: 750,
    requirements: ['Receive 100 likes on your characters', 'Maintain 4.5+ average rating', 'Featured in community highlights'],
    rewards: [
      {
        type: 'privilege',
        name: 'Diamond Creator Status',
        description: 'Elite creator privileges and recognition'
      },
      {
        type: 'currency',
        name: 'Joy Crystal',
        description: 'Substantial premium currency reward',
        amount: 500,
        currencyType: 'endora'
      }
    ],
    tips: 'Focus on character quality and unique personalities'
  },
  {
    id: 'legendary-creator',
    name: 'Legendary Creator',
    description: 'A master recognized by all',
    category: 'creation',
    rarity: 'legendary',
    status: 'locked',
    currentProgress: 0,
    maxProgress: 10000,
    points: 2000,
    requirements: ['Achieve 10,000 total character interactions', 'Have 3+ characters in top 100', 'Mentor 5+ new creators'],
    rewards: [
      {
        type: 'privilege',
        name: 'Legendary Creator Hall of Fame',
        description: 'Eternal recognition in the platform\'s hall of fame'
      },
      {
        type: 'item',
        name: 'Crown of Creation',
        description: 'The ultimate achievement item'
      },
      {
        type: 'currency',
        name: 'All Currencies Bundle',
        description: 'Massive reward of all currency types',
        amount: 1000
      }
    ],
    tips: 'Create characters that resonate with the community',
    isSecret: true
  },
  // Collection Achievements
  {
    id: 'collector-bronze',
    name: 'Bronze Collector',
    description: 'Start your collection journey',
    category: 'collection',
    rarity: 'bronze',
    status: 'completed',
    currentProgress: 5,
    maxProgress: 5,
    points: 100,
    requirements: ['Collect 5 memory puzzles', 'Save 10 favorite characters'],
    rewards: [
      {
        type: 'badge',
        name: 'Collector\'s Start',
        description: 'Your first step into collecting'
      }
    ],
    earnedDate: new Date('2024-01-18'),
    tips: 'Start collecting memory puzzles and saving favorite characters'
  },
  {
    id: 'hidden-achievement',
    name: 'Secret Achievement',
    description: 'A mysterious accomplishment awaits discovery',
    category: 'special',
    rarity: 'legendary',
    status: 'locked',
    currentProgress: 0,
    maxProgress: 1,
    points: 1000,
    requirements: ['???'],
    rewards: [
      {
        type: 'item',
        name: 'Mystery Box',
        description: '???'
      }
    ],
    isSecret: true,
    tips: 'Sometimes the greatest achievements come from unexpected actions...'
  }
];

export const mockAchievementStats: AchievementStats = {
  totalAchievements: mockAchievements.length,
  completed: mockAchievements.filter(a => a.status === 'completed').length,
  inProgress: mockAchievements.filter(a => a.status === 'inProgress').length,
  locked: mockAchievements.filter(a => a.status === 'locked').length,
  totalPoints: mockAchievements
    .filter(a => a.status === 'completed')
    .reduce((sum, a) => sum + a.points, 0),
  globalRank: 1247
}; 