'use client';

import React, { useState } from 'react';
import { Crown, Star, Zap, Gift, Shield, Clock, Check, Sparkles, ChevronRight, X, Gem, Globe } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { MembershipTier } from './MembershipStatus';

interface MembershipTierData {
  level: MembershipTier;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  color: string;
  gradient: string;
  icon: React.ComponentType<any>;
  features: string[];
  exclusiveFeatures: string[];
  maxUsers?: number;
  currentUsers?: number;
}

interface MembershipPlansProps {
  currentMembership: MembershipTier;
  lang: string;
  onUpgrade?: (tierLevel: MembershipTier) => void;
  onClose?: () => void;
}

const MembershipPlans: React.FC<MembershipPlansProps> = ({
  currentMembership,
  lang,
  onUpgrade,
  onClose
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [selectedPlan, setSelectedPlan] = useState<'monthly' | 'yearly'>('monthly');

  const membershipTiers: MembershipTierData[] = [
    {
      level: 'standard',
      name: t('journey.membership.freeVersion'),
      monthlyPrice: 0,
      yearlyPrice: 0,
      color: 'gray',
      gradient: 'from-gray-400 to-gray-600',
      icon: Shield,
      features: [
        t('journey.membership.basicChatFeatures'),
        t('journey.membership.dailyAiInteractions'),
        t('journey.membership.communityAccess')
      ],
      exclusiveFeatures: []
    },
    {
      level: 'pass',
      name: t('journey.membership.passMember'),
      monthlyPrice: 29,
      yearlyPrice: 290,
      color: 'blue',
      gradient: 'from-blue-400 to-blue-600',
      icon: Crown,
      features: [
        t('journey.membership.unlimitedAiChat'),
        t('journey.membership.prioritySupport'),
        t('journey.membership.advancedAnalytics'),
        t('journey.membership.customThemes')
      ],
      exclusiveFeatures: [
        t('journey.membership.doubleExperience'),
        t('journey.membership.passExclusiveBadge')
      ]
    },
    {
      level: 'diamond',
      name: t('journey.membership.diamondMember'),
      monthlyPrice: 59,
      yearlyPrice: 590,
      color: 'cyan',
      gradient: 'from-cyan-400 to-cyan-600',
      icon: Gem,
      features: [
        t('journey.membership.allPassFeatures'),
        t('journey.membership.advancedCharacterSlots'),
        t('journey.membership.exclusiveContentAccess'),
        t('journey.membership.earlyAccessFeatures')
      ],
      exclusiveFeatures: [
        t('journey.membership.tripleExperience'),
        t('journey.membership.diamondExclusiveAvatar'),
        t('journey.membership.monthlyRewardPackage')
      ]
    },
    {
      level: 'metaverse',
      name: t('journey.membership.metaverseMember'),
      monthlyPrice: 99,
      yearlyPrice: 990,
      color: 'purple',
      gradient: 'from-purple-400 to-purple-600',
      icon: Globe,
      features: [
        t('journey.membership.allDiamondFeatures'),
        t('journey.membership.unlimitedCharacterSlots'),
        t('journey.membership.metaverseExclusiveContent'),
        t('journey.membership.prioritySupport')
      ],
      exclusiveFeatures: [
        t('journey.membership.tripleExperience'),
        t('journey.membership.metaverseExclusiveAvatar'),
        t('journey.membership.quarterlyRewardPackage')
      ],
      maxUsers: 500,
      currentUsers: 127
    }
  ];

  const getCurrentTier = () => membershipTiers.find(tier => tier.level === currentMembership) || membershipTiers[0];
  const getNextTier = () => {
    const currentIndex = membershipTiers.findIndex(tier => tier.level === currentMembership);
    return membershipTiers[currentIndex + 1];
  };

  const calculateSavings = (tier: MembershipTierData) => {
    const monthlyCost = tier.monthlyPrice * 12;
    const savings = monthlyCost - tier.yearlyPrice;
    const savingsPercent = Math.round((savings / monthlyCost) * 100);
    return { savings, savingsPercent };
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="relative w-full max-w-6xl max-h-[90vh] overflow-y-auto bg-white/10 dark:bg-black/10 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-white/10">
        {/* Close button */}
        {onClose && (
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 w-10 h-10 bg-black/20 hover:bg-black/40 rounded-full flex items-center justify-center transition-colors"
          >
            <X className="w-6 h-6 text-white" />
          </button>
        )}

        <div className="p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center gap-3 mb-4">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Crown className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-foreground">{t('journey.membership.currentPlan')}</h1>
                <p className="text-foreground/70">{t('journey.membership.upgradeTo')}</p>
              </div>
            </div>

            {/* Current Status */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-full">
              <Star className="w-5 h-5 text-blue-400" />
              <span className="text-blue-400 font-medium">
                {t('journey.membership.currentPlan')}: {getCurrentTier().name}
              </span>
            </div>
          </div>

          {/* Plan Toggle */}
          <div className="flex justify-center mb-8">
            <div className="bg-black/20 p-1 rounded-xl">
              <button
                onClick={() => setSelectedPlan('monthly')}
                className={`px-6 py-2 rounded-lg font-medium transition-all ${
                  selectedPlan === 'monthly'
                    ? 'bg-white text-black dark:bg-white dark:text-black'
                    : 'text-foreground/70 hover:text-foreground'
                }`}
              >
                {t('journey.membership.monthly')}
              </button>
              <button
                onClick={() => setSelectedPlan('yearly')}
                className={`px-6 py-2 rounded-lg font-medium transition-all ${
                  selectedPlan === 'yearly'
                    ? 'bg-white text-black dark:bg-white dark:text-black'
                    : 'text-foreground/70 hover:text-foreground'
                }`}
              >
                {t('journey.membership.yearly')}
                <span className="ml-2 px-2 py-1 bg-green-500 text-white text-xs rounded-full">
                  {t('journey.membership.save')} 17%
                </span>
              </button>
            </div>
          </div>

          {/* Membership Tiers Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {membershipTiers.map((tier, index) => {
              const isCurrentTier = tier.level === currentMembership;
              const isUpgrade = membershipTiers.findIndex(t => t.level === tier.level) > membershipTiers.findIndex(t => t.level === currentMembership);
              const price = selectedPlan === 'monthly' ? tier.monthlyPrice : tier.yearlyPrice;
              const { savings, savingsPercent } = calculateSavings(tier);
              const IconComponent = tier.icon;

              return (
                <div
                  key={tier.level}
                  className={`relative overflow-hidden rounded-2xl backdrop-blur-xl border shadow-xl transition-all duration-300 hover:scale-105 ${
                    isCurrentTier
                      ? 'bg-gradient-to-br from-blue-500/20 to-purple-500/20 border-blue-500/50 ring-2 ring-blue-500/30'
                      : isUpgrade
                      ? 'bg-white/10 dark:bg-black/10 border-white/20 dark:border-white/10 hover:border-white/40'
                      : 'bg-white/5 dark:bg-black/5 border-white/10 dark:border-white/5'
                  }`}
                >
                  {/* Tier highlight for premium tiers */}
                  {tier.level !== 'standard' && (
                    <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${tier.gradient}`}></div>
                  )}

                  {/* Limited availability indicator */}
                  {tier.maxUsers && (
                    <div className="absolute top-4 right-4">
                      <div className="px-2 py-1 bg-red-500/20 border border-red-500/30 rounded-full text-red-400 text-xs font-medium">
                        {tier.currentUsers}/{tier.maxUsers}
                      </div>
                    </div>
                  )}

                  <div className="p-6">
                    {/* Tier Icon */}
                    <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br ${tier.gradient} rounded-2xl flex items-center justify-center shadow-lg`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>

                    {/* Tier Info */}
                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-foreground mb-2">{tier.name}</h3>
                      
                      {tier.level !== 'standard' && (
                        <div className="mb-4">
                          <div className="text-3xl font-bold text-foreground">
                            ¥{price}
                          </div>
                          <div className="text-sm text-foreground/60">
                            {selectedPlan === 'monthly' ? t('journey.membership.monthly') : t('journey.membership.yearly')}
                          </div>
                          {selectedPlan === 'yearly' && (tier.level as MembershipTier) !== 'standard' && (
                            <div className="text-xs text-green-400 mt-1">
                              {t('journey.membership.save')} ¥{savings} ({savingsPercent}%)
                            </div>
                          )}
                        </div>
                      )}

                      {isCurrentTier && (
                        <div className="inline-flex items-center gap-1 px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full text-blue-400 text-sm font-medium mb-4">
                          <Check className="w-4 h-4" />
                          {t('journey.membership.currentPlan')}
                        </div>
                      )}
                    </div>

                    {/* Features */}
                    <div className="space-y-3 mb-6">
                      {tier.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-2 text-sm">
                          <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                          <span className="text-foreground/80">{feature}</span>
                        </div>
                      ))}
                      
                      {tier.exclusiveFeatures.map((feature, featureIndex) => (
                        <div key={`exclusive-${featureIndex}`} className="flex items-center gap-2 text-sm">
                          <Star className="w-4 h-4 text-yellow-400 flex-shrink-0" />
                          <span className="text-yellow-400 font-medium">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* Action Button */}
                    {(tier.level as MembershipTier) !== 'standard' && (
                      <button
                        onClick={() => onUpgrade?.(tier.level)}
                        disabled={isCurrentTier}
                        className={`w-full py-3 px-4 rounded-xl font-medium transition-all flex items-center justify-center gap-2 ${
                          isCurrentTier
                            ? 'bg-gray-500/20 text-gray-400 cursor-not-allowed'
                            : isUpgrade
                            ? `bg-gradient-to-r ${tier.gradient} text-white hover:scale-105 shadow-lg hover:shadow-xl`
                            : 'bg-white/10 text-foreground/60 cursor-not-allowed'
                        }`}
                      >
                        {isCurrentTier ? (
                          t('journey.membership.currentPlan')
                        ) : isUpgrade ? (
                          <>
                            {t('journey.membership.upgradeTo')} {tier.name}
                            <ChevronRight className="w-4 h-4" />
                          </>
                        ) : (
                          t('journey.membership.close')
                        )}
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Next Tier Preview */}
          {getNextTier() && (
            <div className="mt-8 p-6 bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-2xl">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-foreground mb-1">
                    {t('journey.membership.upgradeTo')} {getNextTier()!.name}
                  </h3>
                  <p className="text-foreground/70 text-sm">
                    {t('journey.membership.unlockExclusiveRewards')}: {getNextTier()!.exclusiveFeatures.join(', ')}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-purple-400">
                    ¥{selectedPlan === 'monthly' ? getNextTier()!.monthlyPrice : getNextTier()!.yearlyPrice}
                  </div>
                  <div className="text-sm text-foreground/60">
                    {selectedPlan === 'monthly' ? t('journey.membership.monthly') : t('journey.membership.yearly')}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MembershipPlans; 