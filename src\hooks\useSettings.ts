'use client';

import { useState, useEffect, useCallback } from 'react';
import { useTheme } from 'next-themes';
import toast from 'react-hot-toast';
import type { UserSettings, SettingsState, SettingsContextType, ExportDataOptions } from '@/types/settings';
import { useAuthContext } from '@/components/AuthProvider';

// Mock default settings
const defaultSettings: UserSettings = {
  account: {
    email: '',
    twoFactorEnabled: false,
    ageVerificationStatus: 'not_verified',
    linkedAccounts: [],
    loginHistory: []
  },
  aiInteraction: {
    responseSpeed: 'standard',
    emotionalIntensity: 'moderate',
    memorySuggestions: true,
    bondingNotifications: true,
    memoryCapacityPriority: 'basic',
    preferredModel: 'standard',
    autoSaveMemories: true,
    contextAwareness: 'enhanced',
    personalityAdaptation: true,
    voicePreference: 'text_only',
    responseLength: 'balanced',
    creativityLevel: 'balanced'
  },
  privacy: {
    profileVisibility: 'followers_only',
    memorySharing: 'anonymous',
    digitalTwinInteraction: 'followers_only',
    characterCreationAttribution: false,
    dataCollection: true,
    analyticsOptIn: true,
    shareUsageData: true,
    allowPersonalization: true,
    cookiePreferences: {
      essential: true,
      analytics: true,
      marketing: false,
      personalization: true
    },
    searchIndexing: false,
    socialMediaSharing: true,
    locationTracking: false
  },
  notifications: {
    streakReminders: true,
    battlePassProgress: true,
    newCharacterReleases: true,
    followedCharacterUpdates: true,
    promotionalOffers: false,
    pushNotifications: true,
    emailNotifications: true,
    inAppNotifications: true,
    weeklyDigest: true,
    maintenanceNotifications: true,
    friendActivityNotifications: true,
    achievementNotifications: true,
    memoryMilestones: true,
    bondingLevelUps: true,
    taskReminders: true,
    premiumExpiryReminders: true,
    doNotDisturbStart: '22:00',
    doNotDisturbEnd: '08:00'
  },
  display: {
    language: 'en',
    theme: 'auto',
    fontSize: 'medium',
    chatBackground: 'default',
    animationLevel: 'standard',
    contentFilter: true,
    regionalization: true,
    highContrast: false,
    reducedMotion: false,
    customCssEnabled: false,
    chatBubbleStyle: 'rounded',
    messageTimestamps: true,
    compactMode: false,
    showTypingIndicators: true
  },
  gamification: {
    achievementAnimations: true,
    currencyGainNotifications: true,
    taskReminderIntensity: 'moderate',
    memoryArtStyle: 'anime',
    streakMotivation: true,
    progressCelebrations: true,
    competitiveMode: false,
    leaderboardVisibility: 'friends',
    rewardPreferences: {
      autoOpen: true,
      showRarity: true,
      celebrationStyle: 'standard',
      sortBy: 'date'
    },
    autoClaimRewards: false,
    experienceDisplayMode: 'detailed',
    badgeDisplayMode: 'all'
  },
  dataManagement: {
    autoMemoryBackup: true,
    cacheSize: 0,
    exportDataConsent: false,
    dataRetentionPeriod: 365,
    autoCleanup: true,
    backupFrequency: 'weekly',
    storageOptimization: true,
    compressionEnabled: true,
    cloudSyncEnabled: true,
    localStorageLimit: 1000,
    downloadHistory: true,
    chatHistoryLimit: 10000
  },
  premium: {
    creatorToolsEnabled: false,
    advancedAnalyticsEnabled: false,
    prioritySupportEnabled: false,
    exclusiveContentAccess: false,
    whisperSpaceAccess: false,
    unlimitedFastRequests: false,
    enhancedMemoryCapacity: false,
    customUIThemes: false,
    advancedFilters: false,
    betaFeatureAccess: false,
    aiModelSelection: false,
    customPersonalities: false
  }
};

export const useSettings = (): SettingsContextType => {
  const { setTheme, theme } = useTheme();
  const { user } = useAuthContext();
  
  const [state, setState] = useState<SettingsState>({
    settings: defaultSettings,
    loading: true,
    saving: false,
    error: null,
    hasUnsavedChanges: false,
    lastSaved: null
  });

  // Load settings from localStorage and user data
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));
        
        // Load from localStorage
        const savedSettings = localStorage.getItem('alphane_user_settings');
        let userSettings = defaultSettings;
        
        if (savedSettings) {
          try {
            const parsedSettings = JSON.parse(savedSettings);
            userSettings = { ...defaultSettings, ...parsedSettings };
          } catch (error) {
            console.error('Error parsing saved settings:', error);
          }
        }
        
        // Override with user data if available
        if (user) {
          userSettings.account.email = user.email || '';
          userSettings.account.twoFactorEnabled = (user as any).twoFactorEnabled || false;
          userSettings.account.ageVerificationStatus = user.age_verification_status || 'not_verified';
          
          // Update premium settings based on user subscription
          if (user.alphane_pass_details?.is_active) {
            const isDiamond = (user.alphane_pass_details as any).plan === 'diamond_pass';
            userSettings.premium = {
              ...userSettings.premium,
              creatorToolsEnabled: isDiamond,
              advancedAnalyticsEnabled: isDiamond,
              prioritySupportEnabled: true,
              exclusiveContentAccess: true,
              whisperSpaceAccess: isDiamond,
              unlimitedFastRequests: isDiamond,
              enhancedMemoryCapacity: isDiamond,
              customUIThemes: isDiamond,
              advancedFilters: isDiamond,
              betaFeatureAccess: isDiamond,
              aiModelSelection: isDiamond,
              customPersonalities: isDiamond
            };
          }
        }

        setState(prev => ({ 
          ...prev, 
          settings: userSettings,
          loading: false,
          lastSaved: localStorage.getItem('alphane_settings_last_saved')
        }));
        
        // Sync with current theme from next-themes, don't override it
        // Only apply if the saved theme is different from current theme
        if (userSettings.display.theme !== theme && userSettings.display.theme !== 'auto') {
          setTheme(userSettings.display.theme);
        }
      } catch (error) {
        console.error('Error loading settings:', error);
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: 'Failed to load settings' 
        }));
      }
    };

    loadSettings();
  }, [user, setTheme, theme]);

  // Sync theme changes from ThemeToggle back to settings
  useEffect(() => {
    if (theme && theme !== state.settings.display.theme) {
      const validTheme = theme === 'system' ? 'auto' : theme as 'auto' | 'light' | 'dark';
      setState(prev => ({
        ...prev,
        settings: {
          ...prev.settings,
          display: {
            ...prev.settings.display,
            theme: validTheme
          }
        }
      }));
    }
  }, [theme, state.settings.display.theme]);

  // Save settings to localStorage and API
  const saveSettings = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, saving: true, error: null }));

      // Save to localStorage
      localStorage.setItem('alphane_user_settings', JSON.stringify(state.settings));
      const now = new Date().toISOString();
      localStorage.setItem('alphane_settings_last_saved', now);

      // Apply theme setting only when saving from DisplaySettings
      // This ensures theme toggle button changes aren't overridden
      setTheme(state.settings.display.theme);

      // TODO: Save to API when backend is ready
      // await saveUserSettings(state.settings);

      setState(prev => ({ 
        ...prev, 
        saving: false, 
        hasUnsavedChanges: false,
        lastSaved: now
      }));

      toast.success('Settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      setState(prev => ({ 
        ...prev, 
        saving: false, 
        error: 'Failed to save settings' 
      }));
      toast.error('Error saving settings. Please try again.');
    }
  }, [state.settings, setTheme]);

  // Update a specific setting
  const updateSetting = useCallback((path: string, value: any) => {
    setState(prev => {
      const keys = path.split('.');
      const newSettings = { ...prev.settings };
      
      // Navigate to the nested property
      let current: any = newSettings;
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      // Set the value
      current[keys[keys.length - 1]] = value;

      return {
        ...prev,
        settings: newSettings,
        hasUnsavedChanges: true
      };
    });
  }, []);

  // Reset settings to defaults
  const resetSettings = useCallback(() => {
    setState(prev => ({
      ...prev,
      settings: defaultSettings,
      hasUnsavedChanges: true
    }));
    toast.success('Settings reset to defaults');
  }, []);

  // Export data
  const exportData = useCallback(async (options?: ExportDataOptions) => {
    try {
      const exportOptions: ExportDataOptions = {
        includeChats: true,
        includeCharacters: true,
        includeMemories: true,
        includeAchievements: true,
        includeSettings: true,
        format: 'json',
        dateRange: 'all',
        ...options
      };

      // TODO: Implement actual export when backend is ready
      // For now, just show a success message
      toast.success('Data export requested. You\'ll receive an email when ready.');
      
      // Mock export - in reality this would be an API call
      console.log('Export requested with options:', exportOptions);
    } catch (error) {
      console.error('Error exporting data:', error);
      toast.error('Error requesting data export. Please try again.');
    }
  }, []);

  // Import settings
  const importSettings = useCallback((data: Partial<UserSettings>) => {
    setState(prev => ({
      ...prev,
      settings: { ...prev.settings, ...data },
      hasUnsavedChanges: true
    }));
    toast.success('Settings imported successfully');
  }, []);

  return {
    state,
    updateSetting,
    saveSettings,
    resetSettings,
    exportData,
    importSettings
  };
}; 