'use client';

import React from 'react';
import Image from 'next/image';
import { X, Heart, MessageCircle, Share2, MoreHorizontal } from 'lucide-react';

export interface ChatMessage {
  id: number;
  side: 'left' | 'right';
  avatar: string;
  name: string;
  text: string;
  timestamp: string;
  type?: 'text' | 'image' | 'emoji';
}

export interface ChatRecord {
  id: string;
  title: string;
  backgroundImage?: string;
  participants: {
    id: string;
    name: string;
    avatar: string;
  }[];
  messages: ChatMessage[];
  createdAt: string;
  aspectRatio?: number;
  stats: {
    likes: number;
    comments: number;
    shares: number;
  };
}

interface MemoryCapsuleProps {
  chatRecord: ChatRecord;
  isOpen: boolean;
  onClose: () => void;
  onLike?: () => void;
  onComment?: () => void;
  onShare?: () => void;
}

const MemoryCapsule: React.FC<MemoryCapsuleProps> = ({
  chatRecord,
  isOpen,
  onClose,
  onLike,
  onComment,
  onShare
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="rounded-2xl w-full max-w-2xl max-h-[90vh] flex flex-col shadow-2xl overflow-hidden">
        {/* Background Image Container - covers entire area */}
        <div className="relative flex-1 flex flex-col">
          {/* Background Image */}
          <Image
            src={chatRecord.backgroundImage || 'https://picsum.photos/800/400'}
            alt={chatRecord.title}
            fill
            className="absolute top-0 left-0 w-full h-full object-cover"
            unoptimized
            onError={() => {}}
          />

          {/* Gradient overlay - stronger at bottom for content readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/30 to-transparent" />

          {/* Close button - top right */}
          <div className="relative z-10 flex justify-end p-4">
            <button
              onClick={onClose}
              className="p-2 rounded-full bg-black/30 hover:bg-black/50 transition-colors backdrop-blur-sm"
            >
              <X size={20} className="text-white" />
            </button>
          </div>

          {/* Chat Messages */}
          <div className="relative z-10 flex-1 overflow-y-auto px-4 space-y-4">
          {chatRecord.messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.side === 'right' ? 'flex-row-reverse' : ''} items-start gap-3`}
            >
              <Image
                src={message.avatar}
                alt={message.name}
                width={40}
                height={40}
                className="w-10 h-10 rounded-full flex-shrink-0"
              />
              <div className={`max-w-[75%] ${message.side === 'right' ? 'text-right' : ''}`}>
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm font-medium text-white/90">
                    {message.name}
                  </span>
                  <span className="text-xs text-white/60">
                    {message.timestamp}
                  </span>
                </div>
                <div
                  className={`rounded-2xl px-4 py-2 text-sm whitespace-pre-wrap ${
                    message.side === 'right'
                      ? 'bg-blue-500 text-white rounded-br-md'
                      : 'bg-white/90 backdrop-blur-sm text-gray-900 rounded-bl-md'
                  }`}
                >
                  {message.text}
                </div>
              </div>
            </div>
          ))}
          </div>

          {/* Bottom overlay with header info and actions */}
          <div className="relative z-10 p-4 space-y-4">
            {/* Header info */}
            <div className="flex items-center gap-3">
              <div className="flex -space-x-2">
                {chatRecord.participants.map((participant, index) => (
                  <Image
                    key={participant.id}
                    src={participant.avatar}
                    alt={participant.name}
                    width={40}
                    height={40}
                    className="w-10 h-10 rounded-full border-2 border-white/60"
                    style={{ zIndex: chatRecord.participants.length - index }}
                    unoptimized
                    onError={() => {}}
                  />
                ))}
              </div>
              <div className="flex-1 min-w-0">
                <h2 className="font-semibold text-lg text-white mb-1">
                  {chatRecord.title}
                </h2>
                <div className="flex items-center gap-2">
                  <p className="text-sm text-white/80">
                    {chatRecord.participants.map(p => p.name).join(' & ')}
                  </p>
                  <span className="text-xs text-white/60">•</span>
                  <span className="text-xs text-white/60">
                    {chatRecord.createdAt}
                  </span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button
                  onClick={onLike}
                  className="flex items-center gap-2 px-3 py-2 rounded-lg bg-black/20 hover:bg-black/40 transition-colors backdrop-blur-sm"
                >
                  <Heart size={18} className="text-white/90" />
                  <span className="text-sm text-white/90">
                    {chatRecord.stats.likes}
                  </span>
                </button>
                <button
                  onClick={onComment}
                  className="flex items-center gap-2 px-3 py-2 rounded-lg bg-black/20 hover:bg-black/40 transition-colors backdrop-blur-sm"
                >
                  <MessageCircle size={18} className="text-white/90" />
                  <span className="text-sm text-white/90">
                    {chatRecord.stats.comments}
                  </span>
                </button>
                <button
                  onClick={onShare}
                  className="flex items-center gap-2 px-3 py-2 rounded-lg bg-black/20 hover:bg-black/40 transition-colors backdrop-blur-sm"
                >
                  <Share2 size={18} className="text-white/90" />
                  <span className="text-sm text-white/90">
                    {chatRecord.stats.shares}
                  </span>
                </button>
              </div>
              <button className="p-2 rounded-lg bg-black/20 hover:bg-black/40 transition-colors backdrop-blur-sm">
                <MoreHorizontal size={18} className="text-white/90" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemoryCapsule;
