'use client';

import React from 'react';
import { QrCode } from 'lucide-react';

interface QRShareButtonProps {
  shareUrl?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
  variant?: 'default' | 'solid';
}

const QRShareButton: React.FC<QRShareButtonProps> = ({ 
  shareUrl, 
  size = 'md', 
  className = '',
  onClick,
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6 p-1',
    md: 'w-8 h-8 p-1.5',
    lg: 'w-10 h-10 p-2'
  };
  
  const iconSize = {
    sm: 14,
    md: 16,
    lg: 20
  };

  const variantClasses = variant === 'solid'
    ? 'bg-white/20 hover:bg-white/30 text-white'
    : 'bg-white/10 hover:bg-white/20 text-white/80 hover:text-white';

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Default QR generation logic
      const url = shareUrl || window.location.href;
      console.log('Generate QR code for:', url);
      // TODO: Implement actual QR code generation/modal
      alert(`QR Code for: ${url}`);
    }
  };

  return (
    <button
      onClick={handleClick}
      className={`
        flex items-center justify-center rounded-full transition-all duration-200
        ${variantClasses}
        ${sizeClasses[size]}
        ${className}
      `}
      title="Share QR Code"
    >
      <QrCode size={iconSize[size]} />
    </button>
  );
};

export default QRShareButton;
