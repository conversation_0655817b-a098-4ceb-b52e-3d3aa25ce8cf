import { Metadata } from 'next';
import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import ManageCharactersClientPage from './ManageCharactersClientPage';

interface ManageCharactersPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export const metadata: Metadata = {
  title: 'Manage Characters - Alphane',
  description: 'Manage your AI characters with analytics and insights',
};

export default async function ManageCharactersPage({ params }: ManageCharactersPageProps) {
  const { lang } = await params;
  
  return (
    <AuthGuard requireAuth={true}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <ManageCharactersClientPage lang={lang} />
      </Suspense>
    </AuthGuard>
  );
}
