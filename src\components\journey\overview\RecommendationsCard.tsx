import React from 'react';
import { Sparkles, Target, Crown, ChevronRight, Zap, Users } from 'lucide-react';
import { JourneyCard, JourneyCardHeader } from '../shared/JourneyCard';
import { useTranslation } from '@/app/i18n/client';

interface Recommendation {
  type: 'challenge' | 'upgrade' | 'feature' | 'social';
  title: string;
  description: string;
  reward: string;
  difficulty: 'easy' | 'medium' | 'hard' | 'premium';
  matchScore: number;
  actionLabel?: string;
}

interface RecommendationsCardProps {
  recommendations: Recommendation[];
  onRecommendationClick?: (recommendation: Recommendation) => void;
  lang: string;
}

export const RecommendationsCard: React.FC<RecommendationsCardProps> = ({
  recommendations,
  onRecommendationClick,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');

  const getRecommendationIcon = (type: Recommendation['type']) => {
    switch (type) {
      case 'challenge': return Target;
      case 'upgrade': return Crown;
      case 'feature': return Sparkles;
      case 'social': return Users;
      default: return Zap;
    }
  };

  const getDifficultyColor = (difficulty: Recommendation['difficulty']) => {
    switch (difficulty) {
      case 'easy': return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'medium': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'hard': return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'premium': return 'text-purple-400 bg-purple-500/20 border-purple-500/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const getDifficultyLabel = (difficulty: Recommendation['difficulty']) => {
    switch (difficulty) {
      case 'easy': return t('search.recommendations.easy');
      case 'medium': return t('search.recommendations.medium');
      case 'hard': return t('search.recommendations.hard');
      case 'premium': return t('search.recommendations.premium');
      default: return '';
    }
  };

  return (
    <JourneyCard gradient="from-emerald-500/10 via-green-500/10 to-emerald-500/10">
      <JourneyCardHeader
        icon={Sparkles}
        iconGradient="from-green-500 to-emerald-500"
        title={t('journey.overview.personalizedRecommendations')}
        subtitle={t('journey.overview.basedOnYourActivity')}
      />

      <div className="space-y-2">
        {recommendations.map((rec, index) => {
          const IconComponent = getRecommendationIcon(rec.type);
          
          return (
            <div
              key={index}
              onClick={() => onRecommendationClick?.(rec)}
              className="flex items-center gap-3 p-3 bg-white/10 border border-white/20 rounded-lg hover:bg-white/20 transition-all cursor-pointer group hover:scale-[1.01]"
            >
              {/* 推荐类型图标 - 更小 */}
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform shrink-0">
                <IconComponent className="w-5 h-5 text-white" />
              </div>

              {/* 推荐内容 - 紧凑布局 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-foreground text-sm truncate">{rec.title}</h4>
                  <span className={`px-2 py-0.5 rounded-full text-xs font-medium border ${getDifficultyColor(rec.difficulty)} shrink-0`}>
                    {getDifficultyLabel(rec.difficulty)}
                  </span>
                </div>
                <p className="text-xs text-foreground/60 line-clamp-1 mb-1">{rec.description}</p>
                
                {/* 奖励和匹配度 - 一行显示 */}
                <div className="flex items-center justify-between">
                  <div className="text-xs font-medium text-green-400">
                    🎁 {rec.reward}
                  </div>
                  <div className="text-xs text-purple-400">
                    {rec.matchScore}% {t('journey.overview.match')}
                  </div>
                </div>
              </div>

              {/* 简化的匹配度指示器 */}
              <div className="flex items-center gap-2 shrink-0">
                <div className="w-8 h-8 relative">
                  <svg className="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
                    <circle
                      cx="16"
                      cy="16"
                      r="14"
                      fill="none"
                      stroke="#374151"
                      strokeWidth="2"
                    />
                    <circle
                      cx="16"
                      cy="16"
                      r="14"
                      fill="none"
                      stroke="#10B981"
                      strokeWidth="2"
                      strokeDasharray={`${rec.matchScore * 0.88}, 88`}
                      className="transition-all duration-500"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs font-bold text-green-400">{rec.matchScore}</span>
                  </div>
                </div>
                <ChevronRight className="w-4 h-4 text-foreground/40 group-hover:text-foreground/70 group-hover:translate-x-1 transition-all" />
              </div>
            </div>
          );
        })}
      </div>

      {/* 更多推荐按钮 - 更紧凑 */}
      <div className="mt-4 text-center">
        <button className="px-4 py-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-lg text-green-400 font-medium hover:scale-105 transition-all text-sm">
          <Sparkles className="w-4 h-4 inline mr-2" />
          {t('journey.overview.moreRecommendations')}
        </button>
      </div>
    </JourneyCard>
  );
}; 