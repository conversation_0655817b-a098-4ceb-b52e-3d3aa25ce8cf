'use client';

import React from 'react';
import { ChevronDown } from 'lucide-react';

interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  isPremium?: boolean;
}

interface SelectFieldProps {
  value: string | number;
  onChange: (value: string | number) => void;
  options: SelectOption[];
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

const SelectField: React.FC<SelectFieldProps> = ({
  value,
  onChange,
  options,
  disabled = false,
  placeholder = 'Select an option...',
  className = ''
}) => {
  return (
    <div className={`relative ${className}`}>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className={`
          appearance-none w-full px-3 py-2 pr-8 
          bg-background border border-border rounded-lg 
          text-foreground text-sm
          focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary
          hover:border-border/80 transition-colors
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        {placeholder && !value && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
            className={`
              ${option.disabled ? 'opacity-50' : ''}
              ${option.isPremium ? 'font-medium' : ''}
            `}
          >
            {option.label}
            {option.isPremium && ' (Premium)'}
          </option>
        ))}
      </select>
      
      <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
        <ChevronDown className="w-4 h-4 text-foreground/60" />
      </div>
    </div>
  );
};

export default SelectField; 