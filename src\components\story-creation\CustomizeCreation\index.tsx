'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Settings, Layers, Target, Brain } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryCustomizeCreationProps, StoryStep } from '@/types/story-creation';
import WorldSettingStep from './WorldSettingStep';
import StoryFlowStep from './StoryFlowStep';
import ObjectivesSubjectivesStep from './ObjectivesSubjectivesStep';

const CustomizeCreation: React.FC<StoryCustomizeCreationProps & { lang: string }> = ({
  currentStep,
  formData,
  setFormData,
  onStepChange,
  onSubmit,
  isStepComplete,
  steps,
  characterId,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const isLastStep = currentStepIndex === steps.length - 1;
  const isFirstStep = currentStepIndex === 0;

  // Shared selectedChapter state across all steps
  const [selectedChapter, setSelectedChapter] = useState<string | null>(
    formData.chapters.length > 0 ? formData.chapters[0].id : null
  );

  // Update selectedChapter when chapters change (e.g., when a new chapter is added)
  useEffect(() => {
    if (!selectedChapter && formData.chapters.length > 0) {
      setSelectedChapter(formData.chapters[0].id);
    }
  }, [formData.chapters, selectedChapter]);

  const handleNext = () => {
    if (!isLastStep) {
      const nextStep = steps[currentStepIndex + 1];
      onStepChange(nextStep.id as StoryStep);
    } else {
      onSubmit();
    }
  };

  const handlePrevious = () => {
    if (!isFirstStep) {
      const prevStep = steps[currentStepIndex - 1];
      onStepChange(prevStep.id as StoryStep);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'worldSetting':
        return (
          <WorldSettingStep
            formData={formData}
            setFormData={setFormData}
            lang={lang}
            onStepChange={onStepChange}
            onSubmit={onSubmit}
          />
        );
      case 'storyFlow':
        return (
          <StoryFlowStep
            formData={formData}
            setFormData={setFormData}
            lang={lang}
            onStepChange={onStepChange}
            onSubmit={onSubmit}
            selectedChapter={selectedChapter}
            setSelectedChapter={setSelectedChapter}
          />
        );
      case 'objectivesSubjectives':
        return (
          <ObjectivesSubjectivesStep
            formData={formData}
            setFormData={setFormData}
            lang={lang}
            onStepChange={onStepChange}
            onSubmit={onSubmit}
            selectedChapter={selectedChapter}
            setSelectedChapter={setSelectedChapter}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-purple-200/50 dark:border-purple-700/50 rounded-2xl p-6 shadow-xl">
      <div className="space-y-6">
        {/* 3-Stage Progress Bar */}
        <div className="w-full max-w-2xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = step.id === currentStep;
              const isCompleted = isStepComplete(step.id as StoryStep);
              const isPast = index < currentStepIndex;

              return (
                <div key={step.id} className="flex flex-col items-center flex-1">
                  <button
                    onClick={() => onStepChange(step.id as StoryStep)}
                    className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 mb-2 ${
                      isActive
                        ? 'border-purple-500 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 shadow-lg scale-110'
                        : isCompleted || isPast
                        ? 'border-green-500 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 hover:scale-105'
                        : 'border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:scale-105'
                    }`}
                  >
                    <Icon size={18} />
                  </button>
                  <span className={`text-xs font-medium text-center ${
                    isActive
                      ? 'text-purple-700 dark:text-purple-300'
                      : isCompleted || isPast
                      ? 'text-green-700 dark:text-green-300'
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {step.label}
                  </span>
                </div>
              );
            })}
          </div>

          {/* Progress Bar */}
          <div className="relative">
            <div className="w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className="h-3 bg-gradient-to-r from-purple-500 via-pink-500 to-rose-500 rounded-full transition-all duration-500 ease-out"
                style={{
                  width: `${((currentStepIndex + 1) / steps.length) * 100}%`
                }}
              />
            </div>
          </div>
        </div>

        {/* Step Content */}
        <div className="animate-in slide-in-from-bottom-4 duration-500">
          {renderStepContent()}
        </div>


      </div>
    </div>
  );
};

export default CustomizeCreation; 