'use client';

import { useState, useEffect, useCallback } from 'react';

interface HeaderOffsetState {
  headerHeight: number;
  topOffset: string;
  isCalculated: boolean;
}

export const useHeaderOffset = (additionalOffset: number = 0): HeaderOffsetState => {
  const [state, setState] = useState<HeaderOffsetState>({
    headerHeight: 0,
    topOffset: '3.5rem', // Default fallback
    isCalculated: false
  });

  const calculateOffset = useCallback(() => {
    if (typeof window === 'undefined') return;

    // Try to find the header element
    const headerElement = document.querySelector('header') ||
                          document.querySelector('[data-header]') ||
                          document.querySelector('.header') ||
                          document.querySelector('nav[class*="h-14"], nav[class*="h-16"]') ||
                          document.querySelector('[class*="sticky"][class*="top-0"]');

    let calculatedHeight = 0;
    
    if (headerElement) {
      const rect = headerElement.getBoundingClientRect();
      calculatedHeight = rect.height;
    } else {
      // Fallback to responsive values based on screen size
      const isDesktop = window.innerWidth >= 1024;
      calculatedHeight = isDesktop ? 64 : 56; // h-16 : h-14
    }

    const totalOffset = calculatedHeight + additionalOffset;
    
    setState({
      headerHeight: calculatedHeight,
      topOffset: `${totalOffset}px`,
      isCalculated: true
    });
  }, [additionalOffset]);

  useEffect(() => {
    // Initial calculation
    calculateOffset();

    // Set up resize observer for more accurate tracking
    let resizeObserver: ResizeObserver | null = null;
    
    if (typeof window !== 'undefined' && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(() => {
        calculateOffset();
      });

      // Observe header element if it exists
      const headerElement = document.querySelector('header') || 
                            document.querySelector('[data-header]');
      
      if (headerElement) {
        resizeObserver.observe(headerElement);
      }
    }

    // Fallback resize listener
    const handleResize = () => {
      calculateOffset();
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [calculateOffset]);

  // Recalculate when DOM is ready
  useEffect(() => {
    const timer = setTimeout(() => {
      calculateOffset();
    }, 100);

    return () => clearTimeout(timer);
  }, [calculateOffset]);

  return state;
};

export default useHeaderOffset;
