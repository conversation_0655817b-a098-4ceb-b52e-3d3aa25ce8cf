'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import type { NotificationTab } from './NotificationTabs';

interface NotificationEmptyStateProps {
  lang: string;
  activeTab: NotificationTab;
}

const NotificationEmptyState: React.FC<NotificationEmptyStateProps> = ({
  lang,
  activeTab
}) => {
  const { t } = useTranslation(lang, 'translation');

  const getEmptyStateEmoji = (tab: NotificationTab) => {
    switch (tab) {
      case 'all':
        return '📭';
      case 'system':
        return '📢';
      case 'social':
        return '👥';
      case 'profile':
        return '👤';
      case 'subscription':
        return '💳';
      default:
        return '📭';
    }
  };

  const getEmptyStateTitle = (tab: NotificationTab) => {
    if (tab === 'all') {
      return t('notifications.emptyState.title', { category: '' });
    }
    return t('notifications.emptyState.title', { 
      category: t(`notifications.${tab}`) 
    });
  };

  return (
    <div className="text-center py-16 space-y-4">
      <div className="text-6xl text-foreground/30">
        {getEmptyStateEmoji(activeTab)}
      </div>
      <h3 className="text-xl font-semibold text-foreground">
        {getEmptyStateTitle(activeTab)}
      </h3>
      <p className="text-foreground/70 max-w-md mx-auto">
        {t(`notifications.emptyState.${activeTab}`)}
      </p>
    </div>
  );
};

export default NotificationEmptyState; 