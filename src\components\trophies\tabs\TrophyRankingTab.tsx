'use client';

import React, { useState } from 'react';
import { TrendingUp, Crown, Trophy, Medal, Zap, Target, Award, Star, Flame } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { EnhancedAchievement } from '@/types/achievements';
import TrophyCard from '../TrophyCard';

interface TrophyRankingTabProps {
  achievements: EnhancedAchievement[];
  lang: string;
  onAchievementClick: (achievement: EnhancedAchievement) => void;
  onClaimReward: (achievementId: string) => void;
}

const TrophyRankingTab: React.FC<TrophyRankingTabProps> = ({
  achievements,
  lang,
  onAchievementClick,
  onClaimReward
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [activeFilter, setActiveFilter] = useState<'all' | 'competitive' | 'elite' | 'legendary' | 'seasonal'>('all');

  // Filter achievements for the Killers player type (competition & rankings)
  const rankingAchievements = achievements.filter(a => 
    a.category === 'special' || a.rarity === 'platinum' || a.rarity === 'diamond' || a.rarity === 'legendary'
  );

  const rankingCategories = [
    {
      id: 'all',
      label: t('trophies.filters.ranking.all'),
      icon: TrendingUp,
      description: t('trophies.filters.ranking.allDesc')
    },
    {
      id: 'competitive',
      label: t('trophies.filters.ranking.competitive'),
      icon: Trophy,
      description: t('trophies.filters.ranking.competitiveDesc')
    },
    {
      id: 'elite',
      label: t('trophies.filters.ranking.elite'),
      icon: Crown,
      description: t('trophies.filters.ranking.eliteDesc')
    },
    {
      id: 'legendary',
      label: t('trophies.filters.ranking.legendary'),
      icon: Star,
      description: t('trophies.filters.ranking.legendaryDesc')
    },
    {
      id: 'seasonal',
      label: t('trophies.filters.ranking.seasonal'),
      icon: Flame,
      description: t('trophies.filters.ranking.seasonalDesc')
    }
  ];

  // Mock categorization based on achievement content
  const categorizeAchievement = (achievement: EnhancedAchievement): string => {
    const name = achievement.name.toLowerCase();
    const desc = achievement.description.toLowerCase();
    
    if (achievement.rarity === 'legendary' || name.includes('legendary')) {
      return 'legendary';
    }
    if (achievement.rarity === 'diamond' || achievement.rarity === 'platinum') {
      return 'elite';
    }
    if (name.includes('season') || desc.includes('season') || desc.includes('limited')) {
      return 'seasonal';
    }
    if (desc.includes('compete') || desc.includes('rank') || desc.includes('leaderboard')) {
      return 'competitive';
    }
    return 'competitive';
  };

  const filteredAchievements = activeFilter === 'all' 
    ? rankingAchievements
    : rankingAchievements.filter(a => categorizeAchievement(a) === activeFilter);

  const stats = {
    total: rankingAchievements.length,
    completed: rankingAchievements.filter(a => a.status === 'completed').length,
    inProgress: rankingAchievements.filter(a => a.status === 'inProgress').length,
    points: rankingAchievements.filter(a => a.status === 'completed').reduce((sum, a) => sum + a.points, 0)
  };

  // Mock leaderboard data
  const leaderboardData = [
    { rank: 1, name: 'You', points: 2847, badge: 'Champion' },
    { rank: 2, name: 'AlexTheGreat', points: 2756, badge: 'Master' },
    { rank: 3, name: 'StarSeeker', points: 2689, badge: 'Expert' },
    { rank: 4, name: 'MoonDancer', points: 2634, badge: 'Expert' },
    { rank: 5, name: 'DragonHeart', points: 2598, badge: 'Expert' }
  ];

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-rose-500/10 via-pink-500/10 to-fuchsia-500/10 border border-rose-200/20 dark:border-rose-800/20 p-8">
        <div className="absolute inset-0 bg-gradient-to-br from-rose-500/5 via-pink-500/5 to-fuchsia-500/5 backdrop-blur-3xl"></div>
        
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-rose-500 to-fuchsia-500 text-white shadow-lg">
              <TrendingUp className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-rose-600 to-fuchsia-600 bg-clip-text text-transparent">
                {t('trophies.rankingTab.title')}
              </h1>
              <p className="text-muted-foreground">{t('trophies.rankingTab.subtitle')}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-rose-600 dark:text-rose-400">{stats.completed}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.rankingTab.conquered')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-pink-600 dark:text-pink-400">{stats.inProgress}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.rankingTab.competing')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-fuchsia-600 dark:text-fuchsia-400">{stats.total}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.total')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{stats.points}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.points')}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Current Ranking Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="rounded-2xl bg-gradient-to-br from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 border border-yellow-200/20 dark:border-yellow-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-yellow-500 to-amber-500 text-white">
              <Crown className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.rankingTab.globalRank.title')}</h3>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">#1</div>
            <div className="text-sm text-muted-foreground mb-3">{t('trophies.rankingTab.globalRank.outOf')}</div>
            <div className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 text-sm font-medium">
              <Crown className="w-4 h-4" />
              {t('trophies.rankingTab.globalRank.champion')}
            </div>
          </div>
        </div>

        <div className="rounded-2xl bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 border border-rose-200/20 dark:border-rose-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-rose-500 to-pink-500 text-white">
              <Trophy className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.rankingTab.seasonRank.title')}</h3>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-rose-600 dark:text-rose-400 mb-2">#3</div>
            <div className="text-sm text-muted-foreground mb-3">{t('trophies.rankingTab.seasonRank.season')}</div>
            <div className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-rose-100 dark:bg-rose-900/30 text-rose-700 dark:text-rose-300 text-sm font-medium">
              <Medal className="w-4 h-4" />
              {t('trophies.rankingTab.seasonRank.elite')}
            </div>
          </div>
        </div>

        <div className="rounded-2xl bg-gradient-to-br from-purple-50 to-fuchsia-50 dark:from-purple-900/20 dark:to-fuchsia-900/20 border border-purple-200/20 dark:border-purple-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-fuchsia-500 text-white">
              <Zap className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.rankingTab.powerLevel.title')}</h3>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">9,847</div>
            <div className="text-sm text-muted-foreground mb-3">{t('trophies.rankingTab.powerLevel.description')}</div>
            <div className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 text-sm font-medium">
              <Star className="w-4 h-4" />
              {t('trophies.rankingTab.powerLevel.legendary')}
            </div>
          </div>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2">
        {rankingCategories.map((category) => {
          const Icon = category.icon;
          const isActive = activeFilter === category.id;
          const categoryCount = category.id === 'all' 
            ? rankingAchievements.length 
            : rankingAchievements.filter(a => categorizeAchievement(a) === category.id).length;

          return (
            <button
              key={category.id}
              onClick={() => setActiveFilter(category.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                isActive
                  ? 'bg-gradient-to-r from-rose-500 to-fuchsia-500 text-white shadow-lg'
                  : 'bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="font-medium">{category.label}</span>
              <span className={`text-xs px-2 py-0.5 rounded-full ${
                isActive 
                  ? 'bg-white/20 text-white' 
                  : 'bg-muted-foreground/20 text-muted-foreground'
              }`}>
                {categoryCount}
              </span>
            </button>
          );
        })}
      </div>

      {/* Leaderboard */}
      <div className="rounded-2xl bg-card border border-border p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 rounded-lg bg-gradient-to-br from-rose-500 to-fuchsia-500 text-white">
            <Trophy className="w-5 h-5" />
          </div>
          <h2 className="text-xl font-bold">{t('trophies.leaderboard.title')}</h2>
        </div>
        
        <div className="space-y-3">
          {leaderboardData.map((player, index) => (
            <div
              key={player.rank}
              className={`flex items-center gap-4 p-4 rounded-xl transition-all duration-300 ${
                player.name === 'You'
                  ? 'bg-gradient-to-r from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 border border-rose-200/20 dark:border-rose-800/20 shadow-lg'
                  : 'bg-muted/50 hover:bg-muted'
              }`}
            >
              <div className={`flex items-center justify-center w-10 h-10 rounded-lg font-bold ${
                player.rank === 1 
                  ? 'bg-gradient-to-br from-yellow-500 to-amber-500 text-white'
                  : player.rank === 2
                  ? 'bg-gradient-to-br from-gray-400 to-gray-500 text-white'
                  : player.rank === 3
                  ? 'bg-gradient-to-br from-amber-600 to-orange-600 text-white'
                  : 'bg-muted text-muted-foreground'
              }`}>
                {player.rank <= 3 ? (
                  player.rank === 1 ? <Crown className="w-5 h-5" /> :
                  player.rank === 2 ? <Medal className="w-5 h-5" /> :
                  <Award className="w-5 h-5" />
                ) : (
                  player.rank
                )}
              </div>
              
              <div className="flex-1">
                <div className={`font-medium ${player.name === 'You' ? 'text-rose-600 dark:text-rose-400' : ''}`}>
                  {player.name}
                </div>
                <div className="text-sm text-muted-foreground">{player.badge}</div>
              </div>
              
              <div className="text-right">
                <div className="font-bold">{player.points.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">{t('trophies.rankingTab.leaderboard.points')}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Competitive Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="text-center p-4 rounded-xl bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 border border-rose-200/20 dark:border-rose-800/20">
          <div className="p-3 rounded-lg bg-gradient-to-br from-rose-500 to-pink-500 text-white mx-auto mb-3 w-fit">
            <Trophy className="w-6 h-6" />
          </div>
          <div className="text-lg font-bold">47</div>
          <div className="text-sm text-muted-foreground">{t('trophies.rankingTab.competitiveStats.competitionsWon')}</div>
        </div>

        <div className="text-center p-4 rounded-xl bg-gradient-to-br from-pink-50 to-fuchsia-50 dark:from-pink-900/20 dark:to-fuchsia-900/20 border border-pink-200/20 dark:border-pink-800/20">
          <div className="p-3 rounded-lg bg-gradient-to-br from-pink-500 to-fuchsia-500 text-white mx-auto mb-3 w-fit">
            <Crown className="w-6 h-6" />
          </div>
          <div className="text-lg font-bold">12</div>
          <div className="text-sm text-muted-foreground">{t('trophies.rankingTab.competitiveStats.titlesEarned')}</div>
        </div>

        <div className="text-center p-4 rounded-xl bg-gradient-to-br from-fuchsia-50 to-purple-50 dark:from-fuchsia-900/20 dark:to-purple-900/20 border border-fuchsia-200/20 dark:border-fuchsia-800/20">
          <div className="p-3 rounded-lg bg-gradient-to-br from-fuchsia-500 to-purple-500 text-white mx-auto mb-3 w-fit">
            <Star className="w-6 h-6" />
          </div>
          <div className="text-lg font-bold">156</div>
          <div className="text-sm text-muted-foreground">{t('trophies.rankingTab.competitiveStats.daysAtTop')}</div>
        </div>

        <div className="text-center p-4 rounded-xl bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 border border-purple-200/20 dark:border-purple-800/20">
          <div className="p-3 rounded-lg bg-gradient-to-br from-purple-500 to-indigo-500 text-white mx-auto mb-3 w-fit">
            <Flame className="w-6 h-6" />
          </div>
          <div className="text-lg font-bold">89</div>
          <div className="text-sm text-muted-foreground">{t('trophies.rankingTab.competitiveStats.winStreak')}</div>
        </div>
      </div>

      {/* Achievements Grid */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {activeFilter === 'all' ? t('trophies.rankingTab.allRankingAchievements') : rankingCategories.find(c => c.id === activeFilter)?.label}
          </h2>
          <div className="text-sm text-muted-foreground">
            {filteredAchievements.length} {t('trophies.common.achievements')}
          </div>
        </div>

        {filteredAchievements.length > 0 ? (
          <>
            {/* Mobile Layout */}
            <div className="block sm:hidden">
              <div className="columns-2 gap-4" style={{ columnFill: 'balance' }}>
                {filteredAchievements.map((achievement) => (
                  <div key={achievement.id} className="break-inside-avoid mb-4">
                    <TrophyCard
                      achievement={achievement}
                      onClick={() => onAchievementClick(achievement)}
                      onClaimReward={onClaimReward}
                      lang={lang}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Desktop Layout */}
            <div className="hidden sm:block">
              <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-5 lg:gap-6">
                {filteredAchievements.map((achievement) => (
                  <TrophyCard
                    key={achievement.id}
                    achievement={achievement}
                    onClick={() => onAchievementClick(achievement)}
                    onClaimReward={onClaimReward}
                    lang={lang}
                  />
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <div className="p-4 rounded-2xl bg-muted/50 inline-block mb-4">
              <TrendingUp className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">{t('trophies.emptyStates.ranking.title')}</h3>
            <p className="text-muted-foreground">
              {t('trophies.emptyStates.ranking.description')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrophyRankingTab;
