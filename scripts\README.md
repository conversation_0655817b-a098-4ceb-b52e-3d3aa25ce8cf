# 🚀 CodeAct - 项目代码统计工具

CodeAct 是一个专业的代码统计工具，用于分析项目中的真实代码行数，排除注释、空行和文档文件，提供准确的代码量统计。

## ✨ 特性

- 📊 **精确统计**: 区分代码行、注释行和空行
- 🎯 **多语言支持**: 支持 TypeScript、JavaScript、CSS、JSON 等
- 📁 **智能过滤**: 自动排除 node_modules、文档、配置文件
- 📄 **多格式报告**: 生成控制台、JSON 和 Markdown 格式报告
- 🔧 **灵活配置**: 支持自定义输出文件和目录

## 🚀 快速开始

### 基本用法

```bash
# 分析整个项目
node scripts/codeact.js

# 分析指定目录
node scripts/codeact.js src

# 查看帮助
node scripts/codeact.js --help
```

### 高级用法

```bash
# 只生成控制台报告，不生成文件
node scripts/codeact.js --no-json --no-markdown

# 自定义报告文件名
node scripts/codeact.js --json-output my-stats.json --md-output my-stats.md

# 分析src目录并生成自定义报告
node scripts/codeact.js src --md-output src-analysis.md
```

## 📊 报告示例

### 控制台输出
```
🚀 CodeAct - 项目代码统计报告
==================================================

📊 总体统计:
  文件总数: 278
  总行数: 60,944
  代码行数: 54,990
  注释行数: 1,306
  空行数: 4,648
  代码占比: 90.2%

📁 按文件类型统计:
  .tsx   | 文件: 237 | 代码行: 39,555 (71.9%)
  .json  | 文件:   4 | 代码行:  9,242 (16.8%)
  .ts    | 文件:  34 | 代码行:  4,948 (9.0%)
  .css   | 文件:   1 | 代码行:    731 (1.3%)
  .js    | 文件:   2 | 代码行:    514 (0.9%)

🎯 核心指标:
  有效代码行数: 54,990
  平均每文件代码行数: 198
```

### 生成的文件
- `codeact-report.json` - 详细的JSON格式统计数据
- `codeact-report.md` - 美观的Markdown格式报告

## ⚙️ 配置说明

### 支持的文件类型
- TypeScript: `.ts`, `.tsx`
- JavaScript: `.js`, `.jsx`
- 样式文件: `.css`, `.scss`, `.sass`, `.less`
- 数据文件: `.json`
- 标记语言: `.html`, `.htm`

### 自动排除的目录
- `node_modules` - 依赖包
- `.git` - Git版本控制
- `.next`, `dist`, `build` - 构建输出
- `Docs`, `docs` - 文档目录
- `.vscode`, `.idea` - IDE配置

### 自动排除的文件
- 配置文件: `package.json`, `tsconfig.json`, `next.config.js` 等
- 锁文件: `package-lock.json`, `yarn.lock`, `bun.lock`
- 文档文件: `README.md`, `*.md`

## 🎯 使用场景

1. **项目评估**: 快速了解项目代码规模
2. **开发进度**: 跟踪代码增长情况
3. **技术债务**: 分析注释和代码比例
4. **团队报告**: 生成专业的统计报告
5. **代码审查**: 了解各语言文件分布

## 📈 输出指标说明

- **代码行数**: 实际的可执行代码行数
- **注释行数**: 包含注释的行数
- **空行数**: 空白行数量
- **代码占比**: 代码行占总行数的百分比
- **平均每文件代码行数**: 代码密度指标

## 🔧 自定义扩展

如需添加新的文件类型或修改排除规则，可以编辑 `scripts/codeact.js` 中的配置：

```javascript
// 添加新的文件扩展名
this.codeExtensions.add('.vue');

// 添加排除目录
this.excludeDirs.add('temp');

// 添加排除文件
this.excludeFiles.add('custom-config.js');
```

---

*CodeAct - 让代码统计更简单、更准确* 🎯
