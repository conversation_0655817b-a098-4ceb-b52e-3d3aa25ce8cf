# Alphane.ai 前端项目开发计划 (V1.0)

## 一、项目技术选型

*   **框架**: Next.js (App Router)
*   **UI 组件库**: shadcn/ui (基于 Tailwind CSS)
*   **状态管理**: Zustand
*   **API 文档参考**: [`Docs/APIdocs.md`](Docs/APIdocs.md)
*   **功能设计参考**: [`Docs/UI设计/design.md`](Docs/UI设计/design.md)

## 二、项目页面路径规划 (Next.js App Router)

*   **认证系统 (`/app/auth`)**
    *   `/(auth)/login/page.tsx` (登录/注册页)
    *   `/(auth)/forgot-password/page.tsx` (忘记密码页)
    *   `/(auth)/reset-password/page.tsx` (重置密码页)
*   **欢迎与引导 (`/app`)**
    *   `/welcome/page.tsx` (新手引导流程页)
*   **主应用 (底部Tab导航)**
    *   `/app/page.tsx` (首页)
    *   `/app/chat/page.tsx` (聊天列表页)
    *   `/app/chat/[sessionId]/page.tsx` (具体聊天界面)
    *   `/app/create/page.tsx` (创建中心页)
        *   `/app/create/character/page.tsx` (创建/编辑角色页)
        *   `/app/create/story/page.tsx` (创建/编辑故事线页)
    *   `/app/me/page.tsx` (我的/个人中心页)
*   **侧边抽屉导航及其他页面 (`/app`)**
    *   `/app/tasks/page.tsx` (任务中心页)
    *   `/app/journey/page.tsx` (心灵之旅页)
    *   `/app/memory-capsules/page.tsx` (记忆胶囊管理页)
    *   `/app/creator-center/page.tsx` (创作者中心页)
    *   `/app/settings/page.tsx` (设置页)
    *   `/app/character/[characterId]/page.tsx` (角色详情页)
    *   `/app/story/[storyId]/page.tsx` (故事线详情页)
    *   `/app/achievements/page.tsx` (成就徽章页)
    *   `/app/store/page.tsx` (商店付费页)
    *   `/app/notifications/page.tsx` (通知中心页)
*   **启动页**: 通过根布局 `layout.tsx` 结合加载状态实现。

## 三、项目导航结构 (Mermaid Diagram)

```mermaid
graph TD
    Splash[启动页] --> AuthCheck{登录状态检查};
    AuthCheck -- 未登录 --> LoginReg[登录/注册页 /auth/login];
    AuthCheck -- 已登录 --> MainApp[主应用 /];
    LoginReg --> WelcomeGuide[/welcome 新手引导];
    WelcomeGuide --> MainApp;
    LoginReg --> MainApp;

    MainApp -- 底部Tab --> HomePage[首页 /];
    MainApp -- 底部Tab --> ChatListPage[/chat 聊天列表];
    MainApp -- 底部Tab --> CreatePage[/create 创建中心];
    MainApp -- 底部Tab --> MePage[/me 个人中心];

    ChatListPage --> ChatInterface[/chat/[sessionId] 聊天界面];
    CreatePage --> CreateCharacterPage[/create/character 创建角色];
    CreatePage --> CreateStoryPage[/create/story 创建故事线];

    MainApp -- 侧边导航/链接 --> TasksPage[/tasks 任务中心];
    MainApp -- 侧边导航/链接 --> JourneyPage[/journey 心灵之旅];
    MainApp -- 侧边导航/链接 --> MemoryCapsulesPage[/memory-capsules 记忆胶囊];
    MainApp -- 侧边导航/链接 --> CreatorCenterPage[/creator-center 创作者中心];
    MainApp -- 侧边导航/链接 --> SettingsPage[/settings 设置];
    MainApp -- 侧边导航/链接 --> AchievementsPage[/achievements 成就];
    MainApp -- 侧边导航/链接 --> StorePage[/store 商店];
    MainApp -- 侧边导航/链接 --> NotificationsPage[/notifications 通知];

    subgraph "详情页面"
        CharacterDetailsPage[/character/[characterId] 角色详情];
        StoryDetailsPage[/story/[storyId] 故事线详情];
    end

    HomePage --> CharacterDetailsPage;
    ChatInterface --> CharacterDetailsPage;
    StoryDetailsPage --> ChatInterface;
```

## 四、开发计划和 ToDo List

**阶段 0: 项目初始化与基础架构**
*   [ ] 1. **Next.js 项目初始化**: 使用 `create-next-app` 搭建项目骨架。
*   [ ] 2. **项目结构配置**: 建立清晰的目录结构 (components, lib, styles, etc.)。
*   [ ] 3. **UI 库集成**: 集成 **shadcn/ui** 并配置 Tailwind CSS。
*   [ ] 4. **基础布局实现**: 创建主布局组件，包含底部 Tab 导航和侧边抽屉导航的框架。
*   [ ] 5. **状态管理方案选型与集成**: 集成 **Zustand** 作为主要状态管理库。
*   [ ] 6. **API 请求模块封装**: 创建一个统一的API服务模块，用于处理所有后端接口的请求和响应。

**阶段 1: MVP 核心功能 (P0 优先级) - 按游玩流程调整**

*   **1. 用户认证系统** (`design.md` 1, `APIdocs.md` 1)
    *   [ ] 1.1 实现启动页逻辑。
    *   [ ] 1.2 **登录/注册页 (`/auth/login`)**: UI实现, 对接OTP和登录/注册API。
    *   [ ] 1.3 **用户状态管理**: 对接获取用户信息API，使用Zustand管理状态。
*   **2. 主页系统 (`/`)** (`design.md` 2, `APIdocs.md` 2.1, 3.4, 3.6, 11.4)
    *   [ ] 2.1 **首页核心布局**: 顶部状态栏, Tab切换 (推荐角色, 最近聊天, 发现)。
    *   [ ] 2.2 **首页数据对接**:
        *   [ ] 用户状态: [`GET /user/me`](Docs/APIdocs.md:267)。
        *   [ ] 推荐角色: [`GET /character/recommend`](Docs/APIdocs.md:2120)。
        *   [ ] 最近聊天: [`GET /character/self/chat`](Docs/APIdocs.md:2489)。
        *   [ ] 发现页标签: [`GET /tag/mainpage`](Docs/APIdocs.md:5640)。
*   **3. 角色创建与查看** (`design.md` 10, `APIdocs.md` 3)
    *   [ ] 3.1 **创建/编辑角色页 (`/create/character`)**:
        *   [ ] UI 实现。
        *   [ ] 对接创建角色接口 ([`POST /character/create`](Docs/APIdocs.md:1467))。
        *   [ ] 对接修改角色接口 ([`PATCH /character/{character_id}`](Docs/APIdocs.md:1586))。
    *   [ ] 3.2 **角色详情页 (`/character/[characterId]`)**:
        *   [ ] UI 实现。
        *   [ ] 对接获取角色信息接口 ([`GET /character/{character_id}`](Docs/APIdocs.md:1516))。
*   **4. 故事线创建与查看** (`APIdocs.md` 4)
    *   [ ] 4.1 **创建/编辑故事线页 (`/create/story`)**:
        *   [ ] UI 实现。
        *   [ ] 对接创建故事线接口 ([`POST /story/create`](Docs/APIdocs.md:2680))。
    *   [ ] 4.2 **故事线详情页 (`/story/[storyId]`)**:
        *   [ ] UI 实现。
        *   [ ] 对接获取故事线详情接口 ([`GET /story/{story_id}`](Docs/APIdocs.md:2782))。
*   **5. 基础聊天功能** (`design.md` 3, `APIdocs.md` 5)
    *   [ ] 5.1 **聊天界面 (`/chat/[sessionId]`)**: UI 实现, 消息显示区、输入栏布局。
    *   [ ] 5.2 **消息交互**: 对接发送消息接口 ([`POST /completion/chat`](Docs/APIdocs.md:3245)), 停止生成接口 ([`POST /completion/stop`](Docs/APIdocs.md:3306))。
    *   [ ] 5.3 **会话历史**: 对接获取会话历史接口 ([`GET /session/story/latest/{story_id}`](Docs/APIdocs.md:3618) 或 [`GET /session/{session_id}`](Docs/APIdocs.md:3671))。
*   **6. AI 记忆胶囊基础版** (`design.md` 4, `APIdocs.md` 9)
    *   [ ] 6.1 **记忆胶囊管理页 (`/memory-capsules`)**: UI 实现, 列表展示、创建入口。
    *   [ ] 6.2 **记忆操作**: 对接创建记忆 ([`POST /memory/create`](Docs/APIdocs.md:5109)), 获取用户记忆 ([`GET /memory/me`](Docs/APIdocs.md:5150)), 获取角色记忆 ([`GET /memory/character/{id}`](Docs/APIdocs.md:203))。
*   **7. 简化任务系统** (`design.md` 5, `APIdocs.md` 2.3)
    *   [ ] 7.1 **任务中心页 (`/tasks`)**: UI 实现, 任务列表展示、进度显示。
    *   [ ] 7.2 **任务交互**: 对接获取任务列表 ([`GET /user/tasks`](Docs/APIdocs.md:824)), 领取任务奖励 ([`POST /user/tasks/{task_id}/claim-reward`](Docs/APIdocs.md:866))。
    *   [ ] 7.3 **Streak 系统**: 前端展示, 对接领取里程碑奖励 ([`POST /user/streak/claim-milestone`](Docs/APIdocs.md:595))。
*   **8. 基础付费系统** (`design.md` 9, `APIdocs.md` 14)
    *   [ ] 8.1 **商店/付费页 (`/store`)**: UI 实现, 对接获取订阅产品列表 ([`GET /payment/subscription-products`](Docs/APIdocs.md:5710))。
    *   [ ] 8.2 **支付流程**: 对接代币充值 ([`POST /payment/recharge`](Docs/UI设计/design.md:586)), 兑换码充值 ([`POST /redeem/{code}`](Docs/APIdocs.md:5670))。

**阶段 2: 早期重要功能 (P1 优先级)**
*   [ ] **完整游戏化系统**: 完善任务体系 (周常/月常)、成就系统 (`/achievements` 对接 [`GET /user/achievements`](Docs/APIdocs.md:679))、心灵之旅系统 (`/journey` 对接 [`GET /user/journey`](Docs/APIdocs.md:723) 等)。
*   [ ] **羁绊系统**: 聊天界面和角色详情页展示，对接羁绊相关接口 ([`GET /character/{character_id}/bond-details`](Docs/APIdocs.md:1909) 等)。
*   [ ] **记忆碎片画图系统** (`design.md` 7):
    *   [ ] 实现画图界面。
    *   [ ] 对接记忆画作生成接口 ([`POST /memory-art/generate`](Docs/UI设计/design.md:471))。
    *   [ ] 实现画作收藏和展示 (对接 [`GET /memory-art/user-collection`](Docs/UI设计/design.md:472), [`GET /memory-art/gallery`](Docs/UI设计/design.md:475))。
*   [ ] **创作者工具优化**: AI辅助生成角色信息、开场白、故事背景等 ([`POST /character/ai-generate-profile`](Docs/APIdocs.md:1643) 等)，角色模板功能。
*   [ ] **其他P1功能**: 忘记密码、第三方登录、修改用户信息、故事线编辑、草稿箱等。

**阶段 3 & 4: 体验完善 (P2) 与未来扩展 (P3)**
*   [ ] 后续根据 `design.md` 和 `APIdocs.md` 中 P2 和 P3 级别的功能，逐步规划和实现。