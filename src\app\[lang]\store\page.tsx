import StoreClientPage from './StoreClientPage';
import { Suspense } from 'react';

interface StorePageProps {
  params: Promise<{
    lang: string;
  }>;
}

function StoreLoadingFallback() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-slate-900 dark:to-gray-900">
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    </div>
  );
}

export default async function StorePage({ params }: StorePageProps) {
  const { lang } = await params;

  return (
    <Suspense fallback={<StoreLoadingFallback />}>
      <StoreClientPage lang={lang} />
    </Suspense>
  );
} 