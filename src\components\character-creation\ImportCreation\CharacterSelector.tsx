'use client';

import React from 'react';
import type { ImportedCharacterData } from '@/types/character-creation';

interface CharacterSelectorProps {
  characters: ImportedCharacterData[];
  onSelectCharacter: (character: ImportedCharacterData) => void;
}

const CharacterSelector: React.FC<CharacterSelectorProps> = ({
  characters,
  onSelectCharacter,
}) => {
  return (
    <div className="space-y-4">
      <h5 className="font-semibold text-emerald-700 dark:text-emerald-300 mb-3 flex items-center gap-2">
        🎭 Select Character to Import ({characters.length} found)
      </h5>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-h-80 overflow-y-auto">
        {characters.map((character, index) => (
          <button
            key={index}
            onClick={() => onSelectCharacter(character)}
            className="text-left p-4 bg-white dark:bg-gray-800 rounded-xl border border-emerald-200 dark:border-emerald-700 hover:border-emerald-400 dark:hover:border-emerald-500 transition-all hover:shadow-lg transform hover:scale-[1.02] duration-200"
          >
            <h6 className="font-semibold text-emerald-800 dark:text-emerald-200 mb-2">
              {character.name}
            </h6>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
              {character.description || 'No description available'}
            </p>
            <div className="flex flex-wrap gap-2 mb-3">
              {character.personality.slice(0, 3).map((trait, traitIndex) => (
                <span
                  key={traitIndex}
                  className="inline-block px-2 py-1 bg-gradient-to-r from-emerald-100 to-teal-100 dark:from-emerald-900/50 dark:to-teal-900/50 text-emerald-700 dark:text-emerald-300 rounded-full text-xs font-medium border border-emerald-200 dark:border-emerald-700"
                >
                  {trait}
                </span>
              ))}
              {character.personality.length > 3 && (
                <span className="inline-block px-2 py-1 bg-gradient-to-r from-gray-100 to-slate-100 dark:from-gray-900/50 dark:to-slate-900/50 text-gray-600 dark:text-gray-400 rounded-full text-xs font-medium border border-gray-200 dark:border-gray-700">
                  +{character.personality.length - 3} more
                </span>
              )}
            </div>
            <div className="text-xs text-emerald-600 dark:text-emerald-400 flex items-center gap-2">
              <span>👤 {character.gender}</span>
              <span>•</span>
              <span>🏷️ {character.faction}</span>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default CharacterSelector;
