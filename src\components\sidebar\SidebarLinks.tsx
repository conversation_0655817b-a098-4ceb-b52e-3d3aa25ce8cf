'use client';

import React from 'react';
import Link from 'next/link';
import { FileText, Shield, Map, Mail } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface SidebarLinksProps {
  lang: string;
}

const SidebarLinks: React.FC<SidebarLinksProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  
  const usefulLinks = [
    {
      name: t('sidebar.termsOfUse'),
      href: `/${lang}/terms`,
      icon: FileText
    },
    {
      name: t('sidebar.privacyPolicy'),
      href: `/${lang}/privacy`,
      icon: Shield
    },
    {
      name: t('sidebar.sitemap'),
      href: `/${lang}/sitemap`,
      icon: Map
    },
    {
      name: t('sidebar.contactUs'),
      href: `/${lang}/contact`,
      icon: Mail
    }
  ];

  return (
    <div className="px-4 py-2">
      <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
        {t('sidebar.usefulLinks')}
      </h3>
      <div className="flex flex-wrap gap-2">
        {usefulLinks.map(link => (
          <Link
            key={link.name}
            href={link.href}
            className="flex items-center gap-1.5 px-2 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
          >
            <link.icon size={12} />
            <span>{link.name}</span>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default SidebarLinks;
