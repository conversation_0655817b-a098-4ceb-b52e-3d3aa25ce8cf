'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import {
  Star,
  Play,
  Eye,
  Edit3,
  Trash2,
  MoreHorizontal,
  BookO<PERSON>,
  Clock,
  TrendingUp
} from 'lucide-react';
import Link from 'next/link';
import { ManagedStory } from '@/lib/mock-data';

interface StoryManageRowProps {
  lang: string;
  story: ManagedStory;
}

const StoryManageRow: React.FC<StoryManageRowProps> = ({ lang, story }) => {
  const router = useRouter();
  const [showActions, setShowActions] = useState(false);
  const [coverSrc, setCoverSrc] = useState(story.coverImage);
  const actionsRef = useRef<HTMLDivElement>(null);

  // Smart tag count calculation
  const getResponsiveTagCount = () => {
    // Dynamically determine the number of tags to display based on screen width
    // Using CSS classes for control, actual display determined by CSS media queries
    return story.tags.slice(0, 3);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (actionsRef.current && !actionsRef.current.contains(event.target as Node)) {
        setShowActions(false);
      }
    };

    if (showActions) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showActions]);

  const getStatusColor = (status: 'draft' | 'published' | 'archived') => {
    switch (status) {
      case 'published':
        return 'text-green-600 dark:text-green-400';
      case 'draft':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'archived':
        return 'text-gray-600 dark:text-gray-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getDifficultyColor = (difficulty: 'easy' | 'normal' | 'hard') => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-600 dark:text-green-400';
      case 'normal':
        return 'text-blue-600 dark:text-blue-400';
      case 'hard':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const handleEdit = () => {
    router.push(`/${lang}/edit-story/${story.id}`);
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality
    if (confirm(`Are you sure you want to permanently delete "${story.title}"? This action cannot be undone.`)) {
      console.log('Delete story:', story.id);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  return (
    <tr className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
      {/* Story Info column - always visible, compact layout on mobile */}
      <td className="px-1 sm:px-2 lg:px-4 py-3">
        <div className="flex items-center gap-2">
          <div className="relative flex-shrink-0">
            <Image
              src={coverSrc}
              alt={story.title}
              width={48}
              height={36}
              className="w-12 h-9 lg:w-16 lg:h-12 rounded border-2 border-gray-200 dark:border-gray-600 object-cover"
              unoptimized
              onError={() => setCoverSrc('https://picsum.photos/400/300')}
            />
            {story.isFeature && (
              <div className="absolute -top-1 -right-1">
                <Star size={10} className="lg:w-3 lg:h-3 text-yellow-500 bg-white dark:bg-gray-800 rounded-full" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <Link
              href={`/${lang}/story/${story.id}`}
              className="font-medium text-sm text-gray-900 dark:text-white hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors block truncate"
            >
              {story.title}
            </Link>
            <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 mt-0.5">
              <BookOpen size={10} />
              <span>{story.characterName}</span>
            </div>
            <div className="flex flex-wrap gap-1 mt-1">
              {/* Mobile shows 1 tag, sm+ shows 2 tags */}
              {story.tags.slice(0, 2).map((tag, index) => (
                <span
                  key={index}
                  className={`inline-block px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded ${
                    index === 0 ? '' : 'hidden sm:inline-block'
                  }`}
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </td>

      {/* Actions column - always visible, compact on mobile */}
      <td className="px-1 sm:px-2 lg:px-4 py-3 text-center">
        <div className="flex items-center justify-center gap-1">
          <button
            onClick={handleEdit}
            className="p-1.5 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            title="Edit Story"
          >
            <Edit3 size={12} className="lg:w-3.5 lg:h-3.5" />
          </button>
          <button
            onClick={handleDelete}
            className="p-1.5 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
            title="Delete Story"
          >
            <Trash2 size={12} className="lg:w-3.5 lg:h-3.5" />
          </button>
        </div>
      </td>

      {/* Plays column - visible on mobile */}
      <td className="px-1 sm:px-2 lg:px-4 py-3 text-center">
        <div className="flex items-center justify-center gap-1">
          <Play size={10} className="lg:w-3 lg:h-3 text-purple-500" />
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            {formatNumber(story.plays)}
          </span>
        </div>
      </td>

      {/* Likes column - visible on mobile */}
      <td className="px-1 sm:px-2 lg:px-4 py-3 text-center">
        <div className="flex items-center justify-center gap-1">
          <Eye size={10} className="lg:w-3 lg:h-3 text-green-500" />
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            {formatNumber(story.likes)}
          </span>
        </div>
      </td>

      {/* Rating column - visible on mobile */}
      <td className="px-1 sm:px-2 lg:px-4 py-3 text-center">
        <div className="flex items-center justify-center gap-1">
          <Star size={10} className="lg:w-3 lg:h-3 text-yellow-500 fill-current" />
          <span className="text-xs font-medium text-gray-900 dark:text-white">
            {story.rating.toFixed(1)}
          </span>
        </div>
      </td>
    </tr>
  );
};

export default StoryManageRow;
