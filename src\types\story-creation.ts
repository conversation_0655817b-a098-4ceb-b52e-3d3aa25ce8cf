// Story Creation Types

// World Setting Interface
export interface WorldSetting {
  // Basic Info
  storyName: string;
  coverImage?: File;
  coverImageUrl?: string;

  // Quick Setup - 通用设置
  worldOverview: string;
  storyBackground: string;

  // Basic Settings - 基础设置
  historicalEra: string;
  geographicEnvironment: string;
  mainRaces: string;
  coreConflict: string;

  // Advanced Settings - 高级设置
  physicsRules: string;
  physicsRulesCustom?: string;
  supernaturalElements: string;
  socialPoliticalSystem: string; // 合并后的字段
  economicFoundation: string;
  techLevel: string;
  techLevelCustom?: string;
  timeBackground: string;

  // 保持向后兼容（废弃字段，但保留以防数据丢失）
  socialForm?: string;
  politicalStructure?: string;
}

export interface StoryFormData {
  // Basic info
  title: string;
  description: string;
  coverImage?: File;
  coverImageUrl?: string;
  genre: string;
  tags: string[];

  // Detail info
  openingMessage: string;
  estimatedDuration: string;
  backgroundSetting: string;

  // World setting
  worldSetting: WorldSetting;

  // Chapter info
  chapters: StoryChapter[];
  branches: StoryBranch[];

  // Advanced settings
  unlockConditions: string;
  rewards: StoryReward[];
  rewardAllocation: StoryRewardAllocation; // 新的奖励分配系统
  achievements: StoryAchievement[];
}

// Enhanced Chapter Structure based on charstoryscene.tsx
export interface TimeElements {
  season: string;
  timeOfDay: string;
  duration: string;
  specialDate: string;
}

export interface SpatialElements {
  location: string;
  atmosphere: string;
  keyObjects: string;
}

export interface EnvironmentalElements {
  weather: string;
  lighting: string;
  sounds: string;
  scents: string;
  temperature: string;
}

export interface MentalModel {
  coreValues: string;
  thinkingMode: string;
  decisionLogic: string;
}

export interface EmotionalBaseline {
  displayedEmotion: string;
  hiddenEmotion: string;
  emotionalIntensity: number;
  emotionalStability: number;
}

export interface MemorySystem {
  triggeredMemories: string;
  emotionalMemories: string;
  knowledgePriority: string;
}

export interface DialogueStrategy {
  initiative: number;
  listeningRatio: number;
  questioningStyle: string;
  responseSpeed: string;
}

export interface RelationshipDynamics {
  initialGoodwill: number;
  trustLevel: number;
  intimacyLevel: string;
  powerRelation: string;
}

export interface GoalOrientation {
  sceneGoal: string;
  displayedIntent: string;
  hiddenIntent: string;
  successCriteria: string;
}

export interface StoryChapter {
  id: string;
  title: string;
  description: string;
  content: string;
  order: number;
  chapterType: 'main' | 'branch'; // 主章节或分支章节
  parentChapter?: string; // 父章节ID（分支章节使用）
  branchLetter?: string; // 分支字母 A, B, C...
  unlockCondition?: string;
  backgroundSetting: string; // 章节背景设定
  completionEffects: ChapterCompletionEffects; // 完成后的变化
  choices?: StoryChoice[];
  nextChapters?: string[]; // 可能的下一章节ID列表

  // Background Image Settings
  backgroundImage?: File;
  backgroundImageUrl?: string;
  useWorldSettingImage?: boolean; // 是否沿用world setting的图片

  // Enhanced fields for objectives (Scene + Antecedent layers)
  environment?: string;
  timeElements?: TimeElements;
  spatialElements?: SpatialElements;
  environmentalElements?: EnvironmentalElements;
  macroHistory?: string;
  characterPast?: string;
  immediateTrigger?: string;

  // Enhanced fields for subjectives (Character Psychology + Interaction layers)
  mentalModel?: MentalModel;
  emotionalBaseline?: EmotionalBaseline;
  memorySystem?: MemorySystem;
  dialogueStrategy?: DialogueStrategy;
  relationshipDynamics?: RelationshipDynamics;
  goalOrientation?: GoalOrientation;
}

export interface ChapterCompletionEffects {
  bondPointsChange: number; // 好感度积分变化 (+10, -5, 0)
  greetingChange?: string; // 问候语变化
  characterMoodChange?: string; // 角色心情变化
  unlockedFeatures?: string[]; // 解锁的功能
  customEffects?: string; // 其他自定义效果
}

export interface StoryBranch {
  id: string;
  fromChapter: string;
  toChapter: string;
  condition: string;
  title: string;
  description: string;
}

export interface StoryChoice {
  id: string;
  text: string;
  description: string;
  consequences: string; // 触发条件 (e.g., Bond level ≥ 3, Has completed previous task...)
  nextChapter: string; // 指向的下一章节ID
  requirements?: string[]; // 选择此项的条件
  effectPreview?: string; // 选择效果预览
}

export interface StoryReward {
  id: string;
  name: string;
  type: 'currency' | 'item' | 'achievement';
  amount: number;
  icon: string;
  description: string;
}

// 新的奖励分配系统
export interface StoryRewardAllocation {
  totalBudget: number; // 总预算积分
  usedBudget: number; // 已使用积分
  allocations: RewardAllocation[];
}

export interface RewardAllocation {
  id: string;
  type: RewardType;
  amount: number; // 分配的数量
  cost: number; // 单个成本
  totalCost: number; // 总成本
  customText?: string; // 自定义文字（用于徽章等）
}

export interface RewardType {
  id: string;
  name: string;
  icon: string;
  cost: number; // 每个奖励的积分成本
  description: string;
  color: string; // 显示颜色
  maxAmount?: number; // 最大分配数量（可选）
}

// 预定义的奖励类型
export const REWARD_TYPES: RewardType[] = [
  {
    id: 'alphane',
    name: 'Alphane',
    icon: '✨',
    cost: 1,
    description: 'Glimmering Dust',
    color: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
  },
  {
    id: 'bond_points',
    name: 'Bond Points',
    icon: '💖',
    cost: 5,
    description: 'Affection points',
    color: 'bg-pink-100 text-pink-700 dark:bg-pink-900/30 dark:text-pink-300'
  },
  {
    id: 'endora',
    name: 'Endora',
    icon: '💎',
    cost: 10,
    description: 'Joy Crystal',
    color: 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
  },
  {
    id: 'puzzle',
    name: 'Memory Puzzle',
    icon: '🧩',
    cost: 50,
    description: 'Memory Puzzle piece',
    color: 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
  },
  {
    id: 'custom_badge',
    name: 'Custom Badge',
    icon: '🏆',
    cost: 100,
    description: 'Custom achievement badge',
    color: 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300',
    maxAmount: 1
  },
  {
    id: 'bond_level_up',
    name: 'Bond Level Up',
    icon: '⭐',
    cost: 200,
    description: 'Direct bond level increase',
    color: 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300',
    maxAmount: 2 // 最多只能分配2个
  }
];

export interface StoryAchievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlockCondition: string;
}

// Story Creation Step Types
export type StoryStep = 'worldSetting' | 'storyFlow' | 'objectivesSubjectives';

export interface StoryStepConfig {
  id: StoryStep;
  label: string;
  icon: any;
  description: string;
}

// Function Selection Types
export type StorySelectedFunction = 'flash' | 'customize';

// Component Props
export interface StoryFunctionSelectorProps {
  selectedFunction: StorySelectedFunction;
  onFunctionSelect: (func: StorySelectedFunction) => void;
}

export interface StoryCustomizeCreationProps {
  currentStep: StoryStep;
  formData: StoryFormData;
  setFormData: (data: StoryFormData | ((prev: StoryFormData) => StoryFormData)) => void;
  onStepChange: (step: StoryStep) => void;
  onSubmit: () => void;
  isStepComplete: (step: StoryStep) => boolean;
  steps: readonly StoryStepConfig[];
  characterId: string;
}

export interface StoryFlashCreationProps {
  onSubmit: (concept: string) => void;
  isLoading?: boolean;
}

export interface StoryStepProps {
  formData: StoryFormData;
  setFormData: (data: StoryFormData | ((prev: StoryFormData) => StoryFormData)) => void;
}

export interface StoryBasicsStepProps extends StoryStepProps {
  onImageUpload?: (file: File) => void;
}

export interface StoryDetailsStepProps extends StoryStepProps {}

export interface StoryChaptersStepProps extends StoryStepProps {
  onSubmit: () => void;
  onStepChange?: (step: StoryStep) => void;
}

// Client Page Props
export interface CreateStoryClientPageProps {
  lang: string;
  characterId?: string;
}

// Flash Generation
export interface FlashStoryData {
  concept: string;
  generatedStory: Partial<StoryFormData>;
}

// Validation
export interface StoryValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} 