'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';

interface CharacterGenreCreatorProps {
  gender: string;
  pov: string;
  onGenderChange: (gender: string) => void;
  onPovChange: (pov: string) => void;
  className?: string;
  lang?: string;
}

const CharacterGenreCreator: React.FC<CharacterGenreCreatorProps> = ({
  gender,
  pov,
  onGenderChange,
  onPovChange,
  className = '',
  lang = 'en'
}) => {
  const { t } = useTranslation(lang, 'translation');
  
  const genderOptions = [
    { value: 'female', label: t('characterCreation.filters.female') },
    { value: 'male', label: t('characterCreation.filters.male') },
    { value: 'other', label: t('characterCreation.filters.other') }
  ];

  const povOptions = [
    { value: 'female', label: t('characterCreation.filters.femalePOV') },
    { value: 'male', label: t('characterCreation.filters.malePOV') },
    { value: 'other', label: t('characterCreation.filters.otherPOV') }
  ];

  const FilterSection = ({
    title,
    options,
    selectedValue,
    onChange
  }: {
    title: string;
    options: { value: string; label: string }[];
    selectedValue: string;
    onChange: (value: string) => void;
  }) => (
    <div className="space-y-3">
      <h5 className="text-sm font-semibold text-purple-700 dark:text-purple-300">{title}</h5>
      <div className="flex gap-2 flex-wrap">
        {options.map((option) => (
          <button
            key={option.value}
            onClick={() => onChange(option.value)}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
              selectedValue === option.value
                ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg scale-105'
                : 'bg-purple-100 dark:bg-purple-800/50 text-purple-700 dark:text-purple-300 hover:bg-purple-200 dark:hover:bg-purple-700/50 border border-purple-300 dark:border-purple-600 hover:scale-105'
            }`}
          >
            {option.label}
          </button>
        ))}
      </div>
    </div>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      <FilterSection
        title={t('characterCreation.filters.gender')}
        options={genderOptions}
        selectedValue={gender}
        onChange={onGenderChange}
      />
      <FilterSection
        title={t('characterCreation.filters.pointOfView')}
        options={povOptions}
        selectedValue={pov}
        onChange={onPovChange}
      />
    </div>
  );
};

export default CharacterGenreCreator;
