'use client';

import { FC } from 'react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';
import ChatRow from '@/components/ChatRow';

interface ChatsClientPageProps {
  lang: string;
  initialChats: any[];
}

const ChatsClientPage: FC<ChatsClientPageProps> = ({ lang, initialChats }) => {
  const { t } = useTranslation(lang, 'translation');

  return (
    <MainAppLayout lang={lang}>
      <div className="w-full bg-background text-foreground theme-transition">
        <div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {initialChats.map((chat) => (
              <ChatRow
                key={chat.character.id}
                lang={lang}
                character={chat.character}
                lastMessage={chat.lastMessage}
              />
            ))}
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
};

export default ChatsClientPage; 