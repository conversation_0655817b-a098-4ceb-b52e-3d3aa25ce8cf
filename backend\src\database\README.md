# Database Architecture & Schema Documentation

## Overview

This document provides comprehensive documentation for the Alphane AI character platform's PostgreSQL database schema, including entity relationships, constraints, and design decisions.

## Database Structure

### Core Entities

#### Users & Authentication
- **users**: Core user accounts and authentication data
- **user_profiles**: Extended profile information and preferences
- **follows**: User following relationships (many-to-many)

#### Characters & Content
- **characters**: AI characters created by users
- **stories**: Interactive stories linked to characters
- **story_chapters**: Individual chapters within stories
- **character_likes**: User likes for characters
- **story_likes**: User likes for stories
- **story_ratings**: User ratings and reviews for stories

#### Chat System
- **chats**: Chat sessions between users and characters
- **chat_messages**: Individual messages within chat sessions
- **memory_capsules**: Long-term memory storage for character interactions

#### Gamification & Progress
- **user_achievements**: Achievement and trophy system
- **user_journey**: User progression and season data
- **user_story_progress**: Progress tracking through stories

#### Economy & Transactions
- **user_currencies**: Virtual currency balances (coins, gems, tokens, hearts)
- **transactions**: Complete transaction history and audit trail

#### Social Features
- **moments**: User-generated content and social posts
- **notifications**: User notification system

## Entity Relationships

### Primary Relationships

```
users (1) ──── (N) characters
users (1) ──── (N) stories
users (1) ──── (N) chats
users (1) ──── (N) memory_capsules
users (1) ──── (N) user_achievements
users (1) ──── (1) user_journey
users (1) ──── (N) user_currencies
users (1) ──── (N) transactions
users (1) ──── (N) notifications
users (1) ──── (N) moments

characters (1) ──── (N) stories
characters (1) ──── (N) chats
characters (1) ──── (N) memory_capsules
characters (1) ──── (N) character_likes

stories (1) ──── (N) story_chapters
stories (1) ──── (N) chats
stories (1) ──── (N) story_likes
stories (1) ──── (N) story_ratings
stories (1) ──── (N) user_story_progress

chats (1) ──── (N) chat_messages
chats (1) ──── (N) memory_capsules

story_chapters (1) ──── (N) chats
story_chapters (1) ──── (N) user_story_progress
```

### Junction Tables (Many-to-Many)
- **follows**: users ↔ users (follower_id, following_id)
- **character_likes**: users ↔ characters
- **story_likes**: users ↔ stories
- **story_ratings**: users ↔ stories (with rating data)

## Key Design Decisions

### 1. UUID Primary Keys
All tables use UUID primary keys for:
- Better security (non-sequential)
- Distributed system compatibility
- Easier data migration and replication

### 2. JSONB for Flexible Data
Used JSONB columns for:
- **personality_traits**: Dynamic character attributes
- **ai_model_config**: Flexible AI configuration
- **world_setting**: Story world parameters
- **metadata**: Extensible data storage

### 3. Array Types
PostgreSQL arrays used for:
- **tags**: String arrays for categorization
- **interests**: User interest arrays
- **media_urls**: Multiple media file URLs
- **completed_chapters**: Story progress tracking

### 4. Soft Deletion Strategy
- **is_archived**: For chats (user-controlled)
- **expires_at**: For memory_capsules and notifications
- Foreign key constraints use CASCADE or SET NULL appropriately

### 5. Performance Optimizations
- **GIN indexes** on JSONB and array columns
- **Composite indexes** on frequently queried combinations
- **Partial indexes** on boolean filters
- **Btree indexes** on timestamp columns for time-based queries

## Database Constraints

### Check Constraints
```sql
-- Rating validation
ALTER TABLE story_ratings ADD CONSTRAINT valid_rating 
CHECK (rating >= 1 AND rating <= 5);

-- Importance score validation  
ALTER TABLE memory_capsules ADD CONSTRAINT valid_importance 
CHECK (importance_score >= 1 AND importance_score <= 10);

-- Currency balance validation
ALTER TABLE user_currencies ADD CONSTRAINT non_negative_balance 
CHECK (balance >= 0);

-- Enum-like constraints
ALTER TABLE notifications ADD CONSTRAINT valid_priority 
CHECK (priority IN ('low', 'normal', 'high', 'urgent'));
```

### Unique Constraints
```sql
-- Prevent duplicate follows
ALTER TABLE follows ADD CONSTRAINT unique_follow 
UNIQUE (follower_id, following_id);

-- Prevent duplicate likes
ALTER TABLE character_likes ADD CONSTRAINT unique_character_like 
UNIQUE (user_id, character_id);

-- One rating per user per story
ALTER TABLE story_ratings ADD CONSTRAINT unique_story_rating 
UNIQUE (user_id, story_id);

-- One currency balance per type per user
ALTER TABLE user_currencies ADD CONSTRAINT unique_user_currency 
UNIQUE (user_id, currency_type);
```

### Foreign Key Relationships
- **CASCADE**: Used for dependent data (profiles, messages, transactions)
- **SET NULL**: Used for optional references (story context in chats)
- **RESTRICT**: Used to prevent deletion of referenced core entities

## Indexing Strategy

### Primary Indexes
- All UUID primary keys have automatic unique indexes
- Foreign key columns have dedicated indexes

### Performance Indexes
```sql
-- Character discovery
CREATE INDEX idx_characters_public_genre ON characters (is_public, genre);
CREATE INDEX idx_characters_featured ON characters (is_featured) WHERE is_featured = true;

-- Story browsing
CREATE INDEX idx_stories_character_public ON stories (character_id, is_public);
CREATE INDEX idx_stories_rating ON stories (rating_avg DESC, rating_count DESC);

-- Chat performance
CREATE INDEX idx_chats_user_recent ON chats (user_id, last_message_at DESC);
CREATE INDEX idx_chat_messages_recent ON chat_messages (chat_id, created_at DESC);

-- Memory retrieval
CREATE INDEX idx_memory_user_char_importance ON memory_capsules 
(user_id, character_id, importance_score DESC);

-- Transaction history
CREATE INDEX idx_transactions_user_recent ON transactions (user_id, created_at DESC);
```

### Search Indexes
```sql
-- Full-text search preparation
CREATE INDEX idx_characters_tags_gin ON characters USING GIN (tags);
CREATE INDEX idx_stories_tags_gin ON stories USING GIN (tags);
CREATE INDEX idx_moments_tags_gin ON moments USING GIN (tags);
```

## Data Types & Storage

### Text Storage
- **VARCHAR** with limits for controlled fields (usernames, titles)
- **TEXT** for long-form content (descriptions, messages, stories)
- **JSONB** for structured but flexible data

### Numeric Types
- **INTEGER** for counts, scores, durations
- **DECIMAL(3,2)** for ratings (0.00-5.00)
- **DECIMAL(5,2)** for percentages (0.00-100.00)

### Temporal Data
- **TIMESTAMP** with timezone for all datetime fields
- **DATE** for date-only fields (birth_date, activity dates)
- Automatic **created_at** and **updated_at** with triggers

### Boolean Logic
- Explicit boolean columns for status flags
- Default values set appropriately
- Partial indexes on commonly filtered boolean fields

## Security Considerations

### Access Control
- Row-level security policies for user data isolation
- API-level permissions based on user roles
- Sensitive data encryption at rest

### Data Validation
- Input validation at application layer
- Database constraints as safety net
- Prepared statements prevent SQL injection

### Audit Trail
- **transactions** table provides complete financial audit
- **created_at**/**updated_at** timestamps on all entities
- Message edit tracking in **chat_messages**

## Backup & Recovery

### Backup Strategy
- Daily full backups with point-in-time recovery
- Transaction log shipping for high availability
- Regular backup restoration testing

### Data Retention
- **memory_capsules**: Configurable expiration
- **notifications**: Auto-cleanup after 90 days
- **transactions**: Permanent retention for audit
- **chat_messages**: Long-term retention with archival strategy

## Migration Strategy

### Schema Versioning
- All schema changes tracked in migrations
- Rollback procedures for each migration
- Testing on staging before production deployment

### Data Migration
- Zero-downtime deployment strategies
- Backward compatibility maintenance
- Gradual rollout for major changes

This database design supports:
- High-performance real-time chat
- Complex character AI interactions
- Scalable user progression systems
- Comprehensive analytics and reporting
- Flexible content management
- Robust financial transaction tracking