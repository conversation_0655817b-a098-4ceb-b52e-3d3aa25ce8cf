'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { User, Palette } from 'lucide-react';

interface OtherUserModeToggleProps {
  lang: string;
  currentPage: 'profile' | 'creator-profile';
  uid: string; // Required UID for the other user
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const OtherUserModeToggle: React.FC<OtherUserModeToggleProps> = ({
  lang,
  currentPage,
  uid,
  size = 'md',
  className = ''
}) => {
  const router = useRouter();

  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base'
  };

  const iconSize = {
    sm: 14,
    md: 16,
    lg: 20
  };

  const handleToggle = () => {
    // Navigate to the opposite page for the other user
    // This does NOT affect the global creator mode state
    if (currentPage === 'profile') {
      router.push(`/${lang}/creator-profile/${uid}`);
    } else {
      router.push(`/${lang}/profile/${uid}`);
    }
  };

  const isCreatorMode = currentPage === 'creator-profile';

  return (
    <button
      onClick={handleToggle}
      className={`
        flex items-center justify-center rounded-full transition-all duration-200
        ${isCreatorMode 
          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600' 
          : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
        }
        ${sizeClasses[size]}
        p-1.5
        ${className}
      `}
      title={isCreatorMode ? 'View User Profile' : 'View Creator Profile'}
    >
      {isCreatorMode ? (
        <Palette size={iconSize[size]} />
      ) : (
        <User size={iconSize[size]} />
      )}
    </button>
  );
};

export default OtherUserModeToggle;
