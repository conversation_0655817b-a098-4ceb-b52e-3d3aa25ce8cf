'use client';

import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import {
  BadgeCheck,
  QrCode,
  Edit3,
  Trash2,
  MoreHorizontal,
  Eye,
  Users
} from 'lucide-react';
import Link from 'next/link';
import { ManagedCharacter } from '@/lib/mock-data';

interface CharacterManageRoleProps {
  lang: string;
  character: ManagedCharacter;
  onClick?: () => void;
}

const CharacterManageRole: React.FC<CharacterManageRoleProps> = ({ lang, character, onClick }) => {
  const [avatarSrc, setAvatarSrc] = useState(character.character_avatar);
  const [showActions, setShowActions] = useState(false);
  const actionsRef = useRef<HTMLDivElement>(null);

  // Smart time formatting
  const formatTimeResponsive = (timeStr: string) => {
    if (timeStr.includes('week')) {
      const weeks = parseInt(timeStr);
      return {
        full: timeStr,
        short: `${weeks}w`,
        hide: false
      };
    } else if (timeStr.includes('day')) {
      const days = parseInt(timeStr);
      return {
        full: timeStr,
        short: `${days}d`,
        hide: false
      };
    }
    return {
      full: timeStr,
      short: timeStr,
      hide: true // Other formats hidden on smallest screens
    };
  };

  // Smart tag count calculation
  const getResponsiveTagCount = () => {
    // Dynamically determine the number of tags to display based on screen width
    // Using CSS classes for control, actual display determined by CSS media queries
    return character.tags.slice(0, 3);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (actionsRef.current && !actionsRef.current.contains(event.target as Node)) {
        setShowActions(false);
      }
    };

    if (showActions) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showActions]);



  const getTrendColor = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'text-green-600 dark:text-green-400';
      case 'down':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const handleEdit = () => {
    window.location.href = `/${lang}/edit-character/${character.id}`;
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality
    if (confirm(`Are you sure you want to permanently delete "${character.name}"? This action cannot be undone.`)) {
      console.log('Delete character:', character.id);
    }
  };

  const generateQRCode = () => {
    // TODO: Implement QR code generation functionality
    console.log('Generate QR code for character:', character.id);
  };

  return (
    <tr
      className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
      onClick={onClick}
    >
      {/* Name column - always visible, compact layout on mobile */}
      <td className="px-1 sm:px-2 lg:px-4 py-3">
        <div className="flex items-center gap-2">
          <div className="relative flex-shrink-0">
            <Image
              src={avatarSrc}
              alt={character.name}
              width={32}
              height={32}
              className="w-8 h-8 lg:w-10 lg:h-10 rounded-full border-2 border-gray-200 dark:border-gray-600 object-cover"
              unoptimized
              onError={() => setAvatarSrc('https://picsum.photos/400/400')}
            />
            {character.isVerified && (
              <div className="absolute -top-1 -right-1">
                <BadgeCheck size={10} className="lg:w-3 lg:h-3 text-yellow-500 bg-white dark:bg-gray-800 rounded-full" />
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <Link
              href={`/${lang}/character/${character.id}`}
              className="font-medium text-sm text-gray-900 dark:text-white hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors block truncate"
            >
              {character.name}
            </Link>
            <div className="flex flex-wrap gap-1 mt-1">
              {/* Mobile shows 1 tag, sm+ shows 2 tags */}
              {character.tags.slice(0, 2).map((tag, index) => (
                <span
                  key={index}
                  className={`inline-block px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded ${
                    index === 0 ? '' : 'hidden sm:inline-block'
                  }`}
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </td>

      {/* Actions column - always visible, compact on mobile */}
      <td className="px-1 sm:px-2 lg:px-4 py-3 text-center">
        <div className="flex items-center justify-center gap-1">
          <button
            onClick={handleEdit}
            className="p-1.5 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            title="Edit Character"
          >
            <Edit3 size={12} className="lg:w-3.5 lg:h-3.5" />
          </button>
          <button
            onClick={handleDelete}
            className="p-1.5 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
            title="Delete Character"
          >
            <Trash2 size={12} className="lg:w-3.5 lg:h-3.5" />
          </button>

          {/* More actions dropdown menu */}
          <div className="relative" ref={actionsRef}>
            <button
              onClick={() => setShowActions(!showActions)}
              className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              title="More actions"
            >
              <MoreHorizontal size={12} className="lg:w-3.5 lg:h-3.5" />
            </button>

            {showActions && (
              <div className="absolute right-0 top-full mt-1 w-32 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10">
                <div className="py-1">
                  <Link
                    href={`/${lang}/character/${character.id}`}
                    className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Eye size={12} />
                    <span>View</span>
                  </Link>
                  <button
                    onClick={generateQRCode}
                    className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <QrCode size={12} />
                    <span>QR</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </td>

      {/* Trend column - visible on mobile, logo only in header */}
      <td className="px-1 sm:px-2 lg:px-4 py-3 text-center">
        <span className={`text-sm font-medium ${getTrendColor(character.trend)}`}>
          {character.trendPercentage > 0 ? '+' : ''}{character.trendPercentage}%
        </span>
      </td>

      {/* Heat column - visible on mobile, no logo in content */}
      <td className="px-1 sm:px-2 lg:px-4 py-3 text-center">
        <span className="font-medium text-sm text-gray-900 dark:text-white">
          {Math.round(character.heatScore / 1000)}k
        </span>
      </td>

      {/* Fans column - visible on mobile, no logo in content */}
      <td className="px-1 sm:px-2 lg:px-4 py-3 text-center">
        <span className="font-medium text-sm text-gray-900 dark:text-white">
          {Math.round(character.followers / 1000)}k
        </span>
      </td>
    </tr>
  );
};

export default CharacterManageRole;
