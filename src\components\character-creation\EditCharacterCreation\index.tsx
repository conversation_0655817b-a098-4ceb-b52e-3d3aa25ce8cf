'use client';

import React, { useState } from 'react';
import { User, MessageCircle, ArrowLeft, ArrowRight } from 'lucide-react';
import type { CustomizeCreationProps, Step } from '@/types/character-creation';
import BasicsStep from '../CustomizeCreation/BasicsStep';
import PersonalityStep from '../CustomizeCreation/PersonalityStep';
import AdvancedStep from '../CustomizeCreation/AdvancedStep';
import { useTranslation } from '@/app/i18n/client';

// 修改接口类型，使 onSubmit 返回 Promise<string>
interface EditCharacterCreationProps extends Omit<CustomizeCreationProps, 'onSubmit'> {
  onSubmit: () => Promise<string>;
}

const EditCharacterCreation: React.FC<EditCharacterCreationProps> = ({
  currentStep,
  formData,
  setFormData,
  onStepChange,
  onSubmit,
  isStepComplete,
  steps,
  lang = 'en'
}) => {
  const { t } = useTranslation(lang, 'translation');
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const isLastStep = currentStepIndex === steps.length - 1;
  const isFirstStep = currentStepIndex === 0;
  const [characterId, setCharacterId] = useState<string | undefined>();
  const [isSaving, setIsSaving] = useState(false);

  const handleNext = async () => {
    if (!isLastStep) {
      const nextStep = steps[currentStepIndex + 1];
      onStepChange(nextStep.id as Step);
    } else {
      // 在提交时处理保存角色的逻辑
      try {
        setIsSaving(true);
        const id = await onSubmit();
        if (id) {
          setCharacterId(id);
        }
      } catch (error) {
        console.error('Failed to save character:', error);
      } finally {
        setIsSaving(false);
      }
    }
  };

  const handlePrevious = () => {
    if (!isFirstStep) {
      const prevStep = steps[currentStepIndex - 1];
      onStepChange(prevStep.id as Step);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'basics':
        return (
          <BasicsStep
            formData={formData}
            setFormData={setFormData}
            lang={lang}
          />
        );
      case 'personality':
        return (
          <PersonalityStep
            formData={formData}
            setFormData={setFormData}
            lang={lang}
          />
        );
      case 'advanced':
        return (
          <AdvancedStep
            formData={formData}
            setFormData={setFormData}
            characterId={characterId}
            lang={lang}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-purple-200/50 dark:border-purple-700/50 rounded-2xl p-6 shadow-xl">
      <div className="space-y-6">
        {/* 3-Stage Progress Bar */}
        <div className="w-full max-w-2xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = step.id === currentStep;
              const isCompleted = isStepComplete(step.id as Step);
              const isPast = index < currentStepIndex;

              return (
                <div key={step.id} className="flex flex-col items-center flex-1">
                  <button
                    onClick={() => onStepChange(step.id as Step)}
                    className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 mb-2 ${
                      isActive
                        ? 'border-purple-500 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 shadow-lg scale-110'
                        : isCompleted || isPast
                        ? 'border-green-500 bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 hover:scale-105'
                        : 'border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:scale-105'
                    }`}
                  >
                    <Icon size={18} />
                  </button>
                  <span className={`text-xs font-medium text-center ${
                    isActive
                      ? 'text-purple-700 dark:text-purple-300'
                      : isCompleted || isPast
                      ? 'text-green-700 dark:text-green-300'
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {step.label}
                  </span>
                </div>
              );
            })}
          </div>

          {/* Progress Bar */}
          <div className="relative">
            <div className="w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className="h-3 bg-gradient-to-r from-purple-500 via-pink-500 to-rose-500 rounded-full transition-all duration-500 ease-out"
                style={{
                  width: `${((currentStepIndex + 1) / steps.length) * 100}%`
                }}
              />
            </div>
          </div>
        </div>

        {/* Step Content */}
        <div className="animate-in slide-in-from-bottom-4 duration-500">
          {renderStepContent()}
        </div>

        {/* Navigation Buttons */}
        {currentStep !== 'advanced' && (
          <div className="flex justify-between items-center pt-6 border-t border-purple-200/50 dark:border-purple-700/50">
            <button
              type="button"
              onClick={handlePrevious}
              disabled={isFirstStep}
              className="flex items-center gap-2 px-6 py-3 border-2 border-purple-300 dark:border-purple-700 text-purple-700 dark:text-purple-300 rounded-xl hover:bg-purple-50 dark:hover:bg-purple-900/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium"
            >
              <ArrowLeft size={18} />
              {t('characterEdit.navigation.previous')}
            </button>

            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-4 py-2 rounded-full">
              <span className="font-medium">{t('characterEdit.navigation.stepOf', { current: currentStepIndex + 1, total: steps.length })}</span>
            </div>

            <button
              type="button"
              onClick={handleNext}
              disabled={!isStepComplete(currentStep) || isSaving}
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium shadow-lg hover:shadow-xl"
            >
              {isSaving 
                ? t('characterEdit.savingChanges')
                : currentStep === 'personality' || isLastStep 
                  ? t('characterEdit.navigation.finish') 
                  : t('characterEdit.navigation.next')
              }
              {!isSaving && currentStep !== 'personality' && !isLastStep && <ArrowRight size={18} />}
            </button>
          </div>
        )}

        {/* Step Completion Status */}
        {currentStep !== 'advanced' && (
          <div className="text-center">
            <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium ${
              isStepComplete(currentStep)
                ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                : 'bg-amber-100 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300'
            }`}>
              <span className="text-base">
                {isStepComplete(currentStep) ? '✅' : '⚠️'}
              </span>
              <span>
                {isStepComplete(currentStep)
                  ? t('characterEdit.navigation.stepComplete')
                  : t('characterEdit.navigation.stepIncomplete')
                }
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EditCharacterCreation; 