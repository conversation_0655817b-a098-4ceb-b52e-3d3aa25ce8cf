'use client';

import { useState, useRef, useCallback } from 'react';
import toast from 'react-hot-toast';
import type { 
  CharacterFormData, 
  AvatarAspectRatio, 
  CharacterImageAspectRatio, 
  CropStage 
} from '@/types/character-creation';

export const useImageUpload = (
  formData: CharacterFormData,
  setFormData: (data: CharacterFormData | ((prev: CharacterFormData) => CharacterFormData)) => void
) => {
  const [avatarPreview, setAvatarPreview] = useState<string>('');
  const [characterImagePreview, setCharacterImagePreview] = useState<string>('');
  const [originalImageFile, setOriginalImageFile] = useState<File | null>(null);
  const [originalImagePreview, setOriginalImagePreview] = useState<string>('');
  const [showAvatarCrop, setShowAvatarCrop] = useState(false);
  const [showCharacterImageCrop, setShowCharacterImageCrop] = useState(false);
  const [showRatioSelection, setShowRatioSelection] = useState(false);
  const [avatarAspectRatio, setAvatarAspectRatio] = useState<AvatarAspectRatio>('1:1');
  const [characterImageAspectRatio, setCharacterImageAspectRatio] = useState<CharacterImageAspectRatio>('5:6');
  const [cropStage, setCropStage] = useState<CropStage>('character');
  const [isAiGenerated, setIsAiGenerated] = useState(false);

  const characterImageInputRef = useRef<HTMLInputElement>(null);

  // Mock function for generating appearance description
  const generateAppearanceDescription = useCallback((imageFile: File) => {
    // Simulate AI analyzing image to generate appearance description
    const appearanceDescriptions = [
      "Has flowing long hair, clear and bright eyes, wearing elegant clothing, overall exuding a gentle and mysterious temperament.",
      "Short and neat hair, determined expression, simple and elegant clothing, giving a reliable and trustworthy impression.",
      "Fluffy curly hair, sweet smile, wearing colorful clothing, full of vitality and youthful energy.",
      "Silver-white long hair, deep eyes, wearing gorgeous formal wear, radiating noble and elegant temperament.",
      "Black straight hair, gentle expression, wearing simple daily clothing, giving a warm and approachable feeling."
    ];

    // Select a description based on filename or randomly
    const randomIndex = Math.floor(Math.random() * appearanceDescriptions.length);
    const generatedDescription = appearanceDescriptions[randomIndex];

    // Simulate generation process with delay
    setTimeout(() => {
      setFormData(prev => ({
        ...prev,
        appearance: generatedDescription
      }));
      setIsAiGenerated(true);
      toast.success('Appearance description generated automatically!');
    }, 1500);
  }, [setFormData]);

  // Handle character portrait upload
  const handleCharacterImageUpload = useCallback((file: File) => {
    if (file) {
      // Store original image file and preview
      setOriginalImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setOriginalImagePreview(result);

        // Directly show character image cropper with ratio selection inside
        setCropStage('character');
        setCharacterImageAspectRatio('5:6'); // Default ratio
        setShowCharacterImageCrop(true);
        toast.success('Image uploaded successfully! Please crop your character image.');
      };
      reader.readAsDataURL(file);
    }
  }, []);

  // Handle avatar crop completion
  const handleAvatarCropComplete = useCallback((croppedImage: File, cropData: { x: number; y: number; width: number; height: number }) => {
    const avatarUrl = URL.createObjectURL(croppedImage);
    setAvatarPreview(avatarUrl);
    setFormData(prev => ({ 
      ...prev, 
      avatar: croppedImage,
      avatarCrop: cropData
    }));
    setShowAvatarCrop(false);
    setCropStage('character'); // Reset crop stage
    toast.success('Avatar set successfully! All image cropping completed.');
  }, [setFormData]);

  // Handle ratio selection confirmation
  const handleRatioSelectionConfirm = useCallback(() => {
    setShowRatioSelection(false);
    setCropStage('character');
    setShowCharacterImageCrop(true);
    toast.success('Starting character image cropping.');
  }, []);

  // Handle character image crop completion
  const handleCharacterImageCropComplete = useCallback((croppedImage: File, cropData: { x: number; y: number; width: number; height: number }) => {
    const imageUrl = URL.createObjectURL(croppedImage);
    setCharacterImagePreview(imageUrl);
    setFormData(prev => ({ 
      ...prev, 
      characterImage: croppedImage
    }));
    setShowCharacterImageCrop(false);
    
    // After character image cropping, automatically enter avatar cropping mode
    setCropStage('avatar');
    setAvatarAspectRatio('1:1'); // Avatar fixed to 1:1 ratio
    setShowAvatarCrop(true);
    toast.success('Character image cropping completed! Now please crop the avatar from the same image.');
  }, [setFormData]);

  // Handle crop cancellation
  const handleCancelCrop = useCallback(() => {
    setShowAvatarCrop(false);
    setShowCharacterImageCrop(false);
    setShowRatioSelection(false);
    setCropStage('character');
  }, []);

  // Trigger character image input
  const triggerCharacterImageInput = useCallback(() => {
    characterImageInputRef.current?.click();
  }, []);

  return {
    // State
    avatarPreview,
    characterImagePreview,
    originalImageFile,
    originalImagePreview,
    showAvatarCrop,
    showCharacterImageCrop,
    showRatioSelection,
    avatarAspectRatio,
    characterImageAspectRatio,
    cropStage,
    isAiGenerated,
    
    // Refs
    characterImageInputRef,
    
    // Actions
    setAvatarAspectRatio,
    setCharacterImageAspectRatio,
    setShowRatioSelection,
    setIsAiGenerated,
    generateAppearanceDescription,
    handleCharacterImageUpload,
    handleAvatarCropComplete,
    handleRatioSelectionConfirm,
    handleCharacterImageCropComplete,
    handleCancelCrop,
    triggerCharacterImageInput,
  };
};
