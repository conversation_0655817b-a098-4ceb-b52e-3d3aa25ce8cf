# Component Architecture Documentation

## Project Overview
Alphane AI - Component architecture documentation for the AI character chat application, documenting the current project's component structure and design.

## Current Component Architecture

### 1. Layout Components

#### ResponsiveHeader Component
- **File**: `src/components/common/ResponsiveHeader.tsx`
- **Function**: Unified responsive header that adapts to mobile and desktop layouts
- **Reuse Scenarios**: Site-wide universal header component

#### MainAppLayout Component
- **File**: `src/components/MainAppLayout.tsx`
- **Function**: Main application layout container, integrating Header and page content
- **Reuse Scenarios**: Site-wide universal layout

#### PageLayout Component
- **File**: `src/components/PageLayout.tsx`
- **Function**: Page layout container
- **Reuse Scenarios**: Specific page layouts

#### BottomNavBar Component
- **File**: `src/components/BottomNavBar.tsx`
- **Function**: Bottom navigation bar, main navigation for mobile
- **Reuse Scenarios**: Mobile global navigation

#### UserInfoBar Component
- **File**: `src/components/UserInfoBar.tsx`
- **Function**: User information display bar
- **Reuse Scenarios**: User status display area

### 2. Character Components

#### CharacterCard Component
- **File**: `src/components/CharacterCard.tsx`
- **Function**: Character card display, including avatar, information, tags, etc.
- **Reuse Scenarios**: Character lists, recommendation pages, search results

#### CharacterManageRole Component
- **File**: `src/components/CharacterManageRole.tsx`
- **Function**: Character management interface component
- **Reuse Scenarios**: Character management pages

### 3. Chat Components

#### ChatRow Component
- **File**: `src/components/ChatRow.tsx`
- **Function**: Chat row display component
- **Reuse Scenarios**: Chat lists

#### CharBubble Component
- **File**: `src/components/bubbles/CharBubble.tsx`
- **Function**: Character chat bubble
- **Reuse Scenarios**: Chat interface

#### UserBubble Component
- **File**: `src/components/bubbles/UserBubble.tsx`
- **Function**: User chat bubble
- **Reuse Scenarios**: Chat interface

### 4. Content Display Components

#### MomentCard Component
- **File**: `src/components/MomentCard.tsx`
- **Function**: Dynamic card display
- **Reuse Scenarios**: Dynamic lists, moment display

#### MemoryCard Component
- **File**: `src/components/MemoryCard.tsx`
- **Function**: Memory card display
- **Reuse Scenarios**: Memory lists

#### MemoryCapsule Component
- **File**: `src/components/MemoryCapsule.tsx`
- **Function**: Memory capsule component
- **Reuse Scenarios**: Memory-related functions

#### PlanCard Component
- **File**: `src/components/PlanCard.tsx`
- **Function**: Plan/subscription card display
- **Reuse Scenarios**: Subscription pages, plan display

### 5. UI Base Components

#### SearchOverlay Component
- **File**: `src/components/SearchOverlay.tsx`
- **Function**: Search overlay
- **Reuse Scenarios**: Search functionality

#### Tooltip Component
- **File**: `src/components/Tooltip.tsx`
- **Function**: Tooltip component
- **Reuse Scenarios**: Places that need tooltip information

#### ThemeToggle Component
- **File**: `src/components/ThemeToggle.tsx`
- **Function**: Theme toggle button
- **Reuse Scenarios**: Settings pages, navigation bar

### 6. System Components

#### Providers Component
- **File**: `src/components/Providers.tsx`
- **Function**: Global state provider
- **Reuse Scenarios**: Application root component

#### ThemeProvider Component
- **File**: `src/components/ThemeProvider.tsx`
- **Function**: Theme provider
- **Reuse Scenarios**: Theme system

## Component Design Principles

### 1. Single Responsibility
Each component has clear responsibilities and functional boundaries, making it easy to understand and maintain.

### 2. Reusability
Component design considers multi-scenario reuse, adapting to different needs through props configuration.

### 3. Type Safety
All components have complete TypeScript type definitions, ensuring type safety.

### 4. Responsive Design
Components support responsive layouts for both mobile and desktop.

## Component Dependencies

```
MainAppLayout
├── ResponsiveHeader (Unified)
├── BottomNavBar (Mobile)
└── Page Content
    ├── CharacterCard
    ├── MomentCard
    ├── MemoryCard
    ├── ChatRow
    └── Bubbles
        ├── CharBubble
        └── UserBubble
```

## Development Standards

### 1. File Naming
- Component files use PascalCase naming
- Functionally related components are placed in the same directory

### 2. Component Structure
- Use functional components and React Hooks
- Export components along with related type definitions
- Add appropriate comments and documentation

### 3. Style Management
- Use Tailwind CSS for style management
- Support dark/light theme switching
- Responsive design first

## Future Expansion Plans

1. **Component Library Enhancement**: Continue adding more general UI components
2. **State Management**: Introduce state management solutions as needed
3. **Test Coverage**: Add unit tests for key components
4. **Documentation**: Establish component usage documentation and examples
5. **Performance Optimization**: Component lazy loading and performance monitoring

## Usage Examples

```tsx
import MainAppLayout from '@/components/MainAppLayout';
import CharacterCard from '@/components/CharacterCard';
import MomentCard from '@/components/MomentCard';

// Usage in pages
<MainAppLayout lang={lang}>
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {characters.map(character => (
      <CharacterCard key={character.id} character={character} />
    ))}
  </div>
</MainAppLayout>
```

This component architecture provides clear structure and good maintainability for the Alphane AI project.