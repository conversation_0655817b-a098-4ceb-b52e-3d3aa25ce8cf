'use client';

import { useEffect, useState } from 'react';
import { useAuthContext } from '@/components/AuthProvider';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';

function cx(...classes: (string | false | undefined | null)[]) {
  return classes.filter(Boolean).join(' ');
}

interface AuthOverlayProps {
  lang: string;
}

type Mode = 'login' | 'register';

const AuthOverlay: React.FC<AuthOverlayProps> = ({ lang }) => {
  const { t: _ } = useTranslation(lang, 'translation');
  const [isVisible, setIsVisible] = useState(false);
  const [mode, setMode] = useState<Mode>('login');
  const { isAuthenticated } = useAuthContext();
  const router = useRouter();

  useEffect(() => {
    // trigger slide-in after mount (client side)
    const id = requestAnimationFrame(() => setIsVisible(true));
    return () => cancelAnimationFrame(id);
  }, []);

  useEffect(() => {
    // 如果用户已登录，自动跳转到首页
    if (isAuthenticated) {
      router.push(`/${lang}`);
    }
  }, [isAuthenticated, lang, router]);

  const toggleMode = () => setMode((m) => (m === 'login' ? 'register' : 'login'));

  return (
    <div
      className={cx(
        'fixed inset-0 z-50 overflow-y-auto flex justify-end bg-black/40 backdrop-blur-sm transition-opacity',
        isVisible ? 'opacity-100' : 'opacity-0'
      )}
    >
      {/* Sliding panel */}
      <div
        className={cx(
          'relative w-full sm:max-w-md h-full bg-white dark:bg-gray-900 shadow-xl transform transition-transform duration-300 ease-out flex flex-col',
          isVisible ? 'translate-x-0' : 'translate-x-full'
        )}
      >
        {/* Header */}
        <header className="flex items-center justify-between px-6 py-4 border-b border-gray-100 dark:border-gray-800">
          <h1 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
            {mode === 'login' ? _('auth.loginTitle', 'Sign in to Alphane.ai') : _('auth.registerTitle', 'Create your Alphane.ai account')}
          </h1>
          {/* Switch mode */}
          <button
            onClick={toggleMode}
            className="text-sm font-medium text-indigo-600 hover:text-indigo-500 transition-colors"
          >
            {mode === 'login' ? _('auth.needAccount', 'Need an account?') : _('auth.alreadyHaveAccount', 'Already have an account?')}
          </button>
        </header>

        {/* Content */}
        <main className="flex-1 px-6 py-8">
          {mode === 'login' ? <LoginForm lang={lang} onSwitchToRegister={toggleMode} /> : <RegisterForm lang={lang} />}
        </main>

        {/* Footer */}
        <footer className="px-6 py-4 text-center text-xs text-gray-500 dark:text-gray-400 border-t border-gray-100 dark:border-gray-800">
          {_('auth.byContinuing', 'By continuing, you agree to our')}{' '}
          <a href={`/${lang}/terms`} className="underline">{_('auth.terms', 'Terms of Service')}</a>{' '}
          {_('auth.and', 'and')}{' '}
          <a href={`/${lang}/privacy`} className="underline">{_('auth.privacy', 'Privacy Policy')}</a>.
        </footer>
      </div>
    </div>
  );
};

export default AuthOverlay;

/* ------------------------------------------------------------------ */

const inputBase =
  'w-full h-11 px-3 border-2 border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:outline-none focus:border-indigo-500 dark:bg-gray-800 dark:text-gray-100 transition-colors placeholder-gray-400 dark:placeholder-gray-500';

function LoginForm({ lang, onSwitchToRegister }: { lang: string; onSwitchToRegister: () => void }) {
  const { t: _ } = useTranslation(lang, 'translation');
  const { loginUser, sendOtpCode, isLoading, error, clearError } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    otp: '',
    provider: 'password' as 'password' | 'otp',
    rememberMe: false,
  });
  const [otpSent, setOtpSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // OTP倒计时逻辑
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // 发送OTP验证码
  const handleSendOtp = async () => {
    if (!formData.email || countdown > 0) return;

    try {
      clearError();
      setFormErrors({});
      await sendOtpCode({
        email: formData.email,
        type: 'signin',
      });
      setOtpSent(true);
      setCountdown(60); // 60秒倒计时
    } catch (error) {
      console.error('Send OTP failed:', error);
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.email) {
      errors.email = _('auth.errors.emailRequired', 'Email is required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = _('auth.errors.emailInvalid', 'Please enter a valid email address');
    }

    if (formData.provider === 'password' && !formData.password) {
      errors.password = _('auth.errors.passwordRequired', 'Password is required');
    }

    if (formData.provider === 'otp' && !formData.otp) {
      errors.otp = _('auth.errors.verificationCodeRequired', 'Verification code is required');
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (!validateForm()) {
      return;
    }

    try {
      const loginData = {
        provider: formData.provider,
        email: formData.email,
        ...(formData.provider === 'password' 
          ? { password: formData.password }
          : { otp: formData.otp }
        ),
      };

      await loginUser(loginData);
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误信息
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const toggleProvider = () => {
    setFormData(prev => ({ 
      ...prev, 
      provider: prev.provider === 'password' ? 'otp' : 'password',
      password: '',
      otp: ''
    }));
    setOtpSent(false);
    setCountdown(0);
    setFormErrors({});
  };

  return (
    <form className="space-y-5" onSubmit={handleSubmit}>
      {error && (
        <div className="p-3 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-sm">
          {error}
        </div>
      )}

      {/* Provider Toggle */}
      <div className="flex items-center justify-center mb-6">
        <div className="flex rounded-lg border border-gray-200 dark:border-gray-700 p-1">
          <button
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, provider: 'password' }))}
            className={cx(
              'px-4 py-2 text-sm font-medium rounded-md transition-all',
              formData.provider === 'password'
                ? 'bg-indigo-600 text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
            )}
          >
            {_('auth.password', 'Password')}
          </button>
          <button
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, provider: 'otp' }))}
            className={cx(
              'px-4 py-2 text-sm font-medium rounded-md transition-all',
              formData.provider === 'otp'
                ? 'bg-indigo-600 text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
            )}
          >
            {_('auth.smsEmailCode', 'SMS/Email Code')}
          </button>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="email">
          {_('auth.emailAddress', 'Email Address')}
        </label>
        <input 
          id="email" 
          type="email" 
          placeholder={_('auth.emailPlaceholder', '<EMAIL>')} 
          className={cx(
            inputBase,
            formErrors.email && 'border-red-500 dark:border-red-500'
          )} 
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          required 
        />
        {formErrors.email && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.email}</p>
        )}
      </div>

      {formData.provider === 'password' ? (
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="password">
            {_('auth.password', 'Password')}
          </label>
          <input 
            id="password" 
            type="password" 
            placeholder={_('auth.passwordPlaceholder', 'Enter your password')} 
            className={cx(
              inputBase,
              formErrors.password && 'border-red-500 dark:border-red-500'
            )} 
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            required 
          />
          {formErrors.password && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.password}</p>
          )}
        </div>
      ) : (
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="otp">
            {_('auth.verificationCode', 'Verification Code')}
          </label>
          <div className="flex rounded-lg border-2 border-gray-200 dark:border-gray-700 overflow-hidden">
            <input 
              id="otp" 
              type="text" 
              className={cx(
                inputBase, 
                'border-0 rounded-none flex-1',
                formErrors.otp && 'border-red-500 dark:border-red-500'
              )} 
              placeholder={_('auth.otpPlaceholder', '6-digit code')} 
              value={formData.otp}
              onChange={(e) => handleInputChange('otp', e.target.value.replace(/\D/g, '').slice(0, 6))}
              maxLength={6}
              required 
            />
            <button 
              type="button" 
              onClick={handleSendOtp}
              disabled={isLoading || countdown > 0 || !formData.email}
              className="px-4 text-sm font-semibold bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
            >
              {countdown > 0 ? `${countdown}s` : (otpSent ? _('auth.resend', 'Resend') : _('auth.getCode', 'Get Code'))}
            </button>
          </div>
          {formErrors.otp && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.otp}</p>
          )}
        </div>
      )}

      {formData.provider === 'password' && (
        <div className="flex items-center justify-between">
          <label className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
            <input 
              type="checkbox" 
              className="h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500"
              checked={formData.rememberMe}
              onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
            /> 
            {_('auth.rememberMe', 'Remember me')}
          </label>
          <a href="#" className="text-xs text-indigo-600 hover:text-indigo-500 font-medium">
            {_('auth.forgotPassword', 'Forgot password?')}
          </a>
        </div>
      )}

      <button
        type="submit"
        disabled={isLoading}
        className="w-full h-11 bg-indigo-600 hover:bg-indigo-500 disabled:bg-indigo-400 text-white font-semibold rounded-lg shadow-md transition-colors"
      >
        {isLoading ? _('auth.signingIn', 'Signing in...') : _('auth.continue', 'Continue')}
      </button>

      <div className="flex items-center my-3">
        <span className="flex-1 h-px bg-gray-200 dark:bg-gray-800" />
        <span className="px-3 text-xs text-gray-500 dark:text-gray-400">{_('auth.or', 'OR')}</span>
        <span className="flex-1 h-px bg-gray-200 dark:bg-gray-800" />
      </div>

      <button
        type="button"
        className="w-full h-11 bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-200 font-semibold rounded-lg flex items-center justify-center gap-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
      >
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="w-5 h-5" aria-hidden="true">
          <path fill="#EA4335" d="M24 9.5c3.83 0 7.25 1.47 9.87 3.84l7.29-7.29C36.8 2.1 30.73 0 24 0 14.68 0 6.42 4.92 2.18 12.18l8.47 6.59C12.52 12.54 17.71 9.5 24 9.5z" />
          <path fill="#34A853" d="M46.55 24.52c0-1.56-.14-2.69-.44-3.86H24v7.29h12.68c-.25 2.1-1.67 5.27-4.79 7.41l7.36 5.71c4.36-4.07 6.3-10.06 6.3-16.55z" />
          <path fill="#4A90E2" d="M10.47 27.21l-8.7 6.71C4.37 41.65 13.64 48 24 48c6.73 0 12.8-2.2 17.56-6.01l-8.47-6.59c-2.27 1.6-5.24 2.55-9.09 2.55-6.3 0-11.9-4.16-13.89-9.74z" />
          <path fill="#FBBC05" d="M2.18 12.18L10.6 18.8C12.52 12.54 17.71 9.5 24 9.5c3.83 0 7.25 1.47 9.87 3.84l7.29-7.29C36.8 2.1 30.73 0 24 0 14.68 0 6.42 4.92 2.18 12.18z" opacity=".15" />
        </svg>
        {_('auth.continueWithGoogle', 'Continue with Google')}
      </button>

      <p className="text-center text-sm mt-6 text-gray-600 dark:text-gray-400">
        {_('auth.dontHaveAccount', "Don't have an account?")}{' '}
        <button
          type="button"
          onClick={onSwitchToRegister}
          className="text-indigo-600 hover:text-indigo-500 font-semibold"
        >
          {_('auth.signUp', 'Sign up')}
        </button>
      </p>
    </form>
  );
}

function RegisterForm({ lang }: { lang: string }) {
  const { t: _ } = useTranslation(lang, 'translation');
  const { loginUser, sendOtpCode, isLoading, error, clearError } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    otp: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false,
  });
  const [otpSent, setOtpSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.email) {
      errors.email = _('auth.errors.emailRequired', 'Email is required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = _('auth.errors.emailInvalid', 'Please enter a valid email address');
    }

    if (!formData.otp) {
      errors.otp = _('auth.errors.verificationCodeRequired', 'Verification code is required');
    } else if (formData.otp.length !== 6) {
      errors.otp = _('auth.errors.verificationCodeLength', 'Verification code must be 6 digits');
    }

    if (!formData.password) {
      errors.password = _('auth.errors.passwordRequired', 'Password is required');
    } else if (formData.password.length < 8) {
      errors.password = _('auth.errors.passwordMinLength', 'Password must be at least 8 characters');
    }

    if (!formData.confirmPassword) {
      errors.confirmPassword = _('auth.errors.confirmPassword', 'Please confirm your password');
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = _('auth.errors.passwordsNotMatch', 'Passwords do not match');
    }

    if (!formData.agreeTerms) {
      errors.agreeTerms = _('auth.errors.agreeTerms', 'You must agree to the Terms of Service and Privacy Policy');
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSendOtp = async () => {
    if (!formData.email || countdown > 0) return;

    // 验证邮箱格式
    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      setFormErrors(prev => ({ ...prev, email: _('auth.errors.emailInvalid', 'Please enter a valid email address') }));
      return;
    }

    try {
      clearError();
      setFormErrors(prev => ({ ...prev, email: '' }));
      await sendOtpCode({
        email: formData.email,
        type: 'signin',
      });
      setOtpSent(true);
      setCountdown(60); // 60秒倒计时
    } catch (error) {
      console.error('Send OTP failed:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (!validateForm()) {
      return;
    }

    try {
      await loginUser({
        provider: 'otp',
        email: formData.email,
        otp: formData.otp,
      });
    } catch (error) {
      console.error('Registration failed:', error);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除对应字段的错误信息
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form className="space-y-5" onSubmit={handleSubmit}>
      {error && (
        <div className="p-3 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-sm">
          {error}
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="register-email">
          {_('auth.emailAddress', 'Email Address')}
        </label>
        <input 
          id="register-email" 
          type="email" 
          placeholder={_('auth.emailPlaceholder', '<EMAIL>')} 
          className={cx(
            inputBase,
            formErrors.email && 'border-red-500 dark:border-red-500'
          )} 
          value={formData.email}
          onChange={(e) => handleInputChange('email', e.target.value)}
          required 
        />
        {formErrors.email && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.email}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="register-otp">
          {_('auth.verificationCodeOtp', 'Verification Code (OTP)')}
        </label>
        <div className="flex rounded-lg border-2 border-gray-200 dark:border-gray-700 overflow-hidden">
          <input 
            id="register-otp" 
            type="text" 
            className={cx(
              inputBase, 
              'border-0 rounded-none flex-1',
              formErrors.otp && 'border-red-500 dark:border-red-500'
            )} 
            placeholder={_('auth.otpPlaceholder', '6-digit code')} 
            value={formData.otp}
            onChange={(e) => handleInputChange('otp', e.target.value.replace(/\D/g, '').slice(0, 6))}
            maxLength={6}
            required 
          />
          <button 
            type="button" 
            onClick={handleSendOtp}
            disabled={isLoading || countdown > 0 || !formData.email}
            className="px-4 text-sm font-semibold bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            {countdown > 0 ? `${countdown}s` : (otpSent ? _('auth.resend', 'Resend') : _('auth.getCode', 'Get Code'))}
          </button>
        </div>
        {formErrors.otp && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.otp}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="register-password">
          {_('auth.password', 'Password')}
        </label>
        <input 
          id="register-password" 
          type="password" 
          placeholder={_('auth.passwordMinPlaceholder', 'At least 8 characters')} 
          className={cx(
            inputBase,
            formErrors.password && 'border-red-500 dark:border-red-500'
          )} 
          value={formData.password}
          onChange={(e) => handleInputChange('password', e.target.value)}
          required 
        />
        {formErrors.password && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.password}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" htmlFor="register-confirm">
          {_('auth.confirmPassword', 'Confirm Password')}
        </label>
        <input 
          id="register-confirm" 
          type="password" 
          placeholder={_('auth.passwordRepeatPlaceholder', 'Repeat password')} 
          className={cx(
            inputBase,
            formErrors.confirmPassword && 'border-red-500 dark:border-red-500'
          )} 
          value={formData.confirmPassword}
          onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
          required 
        />
        {formErrors.confirmPassword && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.confirmPassword}</p>
        )}
      </div>

      <div>
        <div className="flex items-start gap-2 text-xs text-gray-600 dark:text-gray-400">
          <input 
            type="checkbox" 
            className={cx(
              'mt-1 h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500',
              formErrors.agreeTerms && 'border-red-500'
            )}
            checked={formData.agreeTerms}
            onChange={(e) => handleInputChange('agreeTerms', e.target.checked)}
            required 
          />
          <span>
            {_('auth.iAgree', 'I agree to the')}{' '}
            <a href="#" className="underline text-indigo-600 hover:text-indigo-500">{_('auth.terms', 'Terms of Service')}</a>{' '}
            {_('auth.and', 'and')}{' '}
            <a href="#" className="underline text-indigo-600 hover:text-indigo-500">{_('auth.privacy', 'Privacy Policy')}</a>.
          </span>
        </div>
        {formErrors.agreeTerms && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{formErrors.agreeTerms}</p>
        )}
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="w-full h-11 bg-indigo-600 hover:bg-indigo-500 disabled:bg-indigo-400 text-white font-semibold rounded-lg shadow-md transition-colors"
      >
        {isLoading ? _('auth.creatingAccount', 'Creating Account...') : _('auth.signUp', 'Sign Up')}
      </button>
    </form>
  );
} 