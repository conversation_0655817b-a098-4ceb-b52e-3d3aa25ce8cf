'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Crown, Sparkles, Brain, BarChart3, HeadphonesIcon, Star, Users, Zap, Palette, Filter, Beaker, Cpu } from 'lucide-react';
import SettingItem from './SettingItem';
import ToggleSwitch from './ToggleSwitch';
import type { SettingsComponentProps } from '@/types/settings';

const PremiumSettings: React.FC<SettingsComponentProps> = ({
  settings,
  updateSetting,
  lang,
  user,
  hasUnsavedChanges,
  isPremiumUser
}) => {
  const { t } = useTranslation(lang, 'translation');

  if (!isPremiumUser) {
    return (
      <div className="space-y-6">
        {/* Upgrade Notice */}
        <div className="text-center p-8 bg-romantic-gradient/10 border border-primary/30 rounded-lg">
          <Crown className="w-16 h-16 text-primary mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-foreground mb-2">
            Diamond Pass Required
          </h3>
          <p className="text-foreground/70 mb-6">
            Unlock exclusive premium features with Diamond Pass membership
          </p>

          <button
            onClick={() => {
              // TODO: Navigate to upgrade page
              alert('Redirecting to upgrade page...');
            }}
            className="px-6 py-3 bg-romantic-gradient text-primary-foreground rounded-lg hover:opacity-90 transition-opacity font-medium"
          >
            Upgrade to Diamond Pass
          </button>
        </div>

        {/* Preview Features */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">Premium Features Preview:</h4>
          
          <div className="grid gap-4">
            {[
              { icon: Brain, title: 'Creator Tools', description: 'Advanced character creation and analytics' },
              { icon: BarChart3, title: 'Advanced Analytics', description: 'Detailed insights into character performance' },
              { icon: HeadphonesIcon, title: 'Priority Support', description: 'Faster response times from support team' },
              { icon: Star, title: 'Exclusive Content', description: 'Access premium characters and storylines' },
              { icon: Users, title: 'Whisper Space', description: 'Join exclusive premium member discussions' },
              { icon: Zap, title: 'Unlimited Fast Requests', description: 'No limits on fast AI responses' }
            ].map(({ icon: Icon, title, description }) => (
              <div key={title} className="p-4 bg-card/50 border border-border rounded-lg opacity-60">
                <div className="flex items-center gap-3">
                  <Icon className="w-5 h-5 text-primary" />
                  <div>
                    <p className="font-medium text-foreground">{title}</p>
                    <p className="text-sm text-foreground/60">{description}</p>
                  </div>
                  <div className="ml-auto">
                    <Crown className="w-4 h-4 text-yellow-500" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Premium Status */}
      <div className="p-4 bg-romantic-gradient/10 border border-primary/30 rounded-lg">
        <div className="flex items-center gap-3">
          <Crown className="w-8 h-8 text-primary" />
          <div>
            <h3 className="text-lg font-semibold text-foreground">Diamond Pass Active</h3>
            <p className="text-sm text-foreground/70">
              Welcome to the exclusive Diamond Pass experience!
            </p>
          </div>
        </div>
      </div>

      {/* Creator Tools */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Brain className="w-4 h-4" />
          {t('settings.categories.premium.creatorTools')}
        </h4>

        <SettingItem
          label={t('settings.categories.premium.creatorTools')}
          description={t('settings.categories.premium.creatorToolsDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.creatorToolsEnabled}
            onChange={(checked) => updateSetting('premium.creatorToolsEnabled', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.premium.customPersonalities')}
          description={t('settings.categories.premium.customPersonalitiesDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.customPersonalities}
            onChange={(checked) => updateSetting('premium.customPersonalities', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.premium.aiModelSelection')}
          description={t('settings.categories.premium.aiModelSelectionDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.aiModelSelection}
            onChange={(checked) => updateSetting('premium.aiModelSelection', checked)}
          />
        </SettingItem>
      </div>

      {/* Analytics & Insights */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <BarChart3 className="w-4 h-4" />
          Analytics & Insights
        </h4>

        <SettingItem
          label={t('settings.categories.premium.advancedAnalytics')}
          description={t('settings.categories.premium.advancedAnalyticsDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.advancedAnalyticsEnabled}
            onChange={(checked) => updateSetting('premium.advancedAnalyticsEnabled', checked)}
          />
        </SettingItem>
      </div>

      {/* Enhanced Features */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Sparkles className="w-4 h-4" />
          Enhanced Features
        </h4>

        <SettingItem
          label={t('settings.categories.premium.enhancedMemoryCapacity')}
          description={t('settings.categories.premium.enhancedMemoryCapacityDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.enhancedMemoryCapacity}
            onChange={(checked) => updateSetting('premium.enhancedMemoryCapacity', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.premium.unlimitedFastRequests')}
          description={t('settings.categories.premium.unlimitedFastRequestsDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.unlimitedFastRequests}
            onChange={(checked) => updateSetting('premium.unlimitedFastRequests', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.premium.customUIThemes')}
          description={t('settings.categories.premium.customUIThemesDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.customUIThemes}
            onChange={(checked) => updateSetting('premium.customUIThemes', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.premium.advancedFilters')}
          description={t('settings.categories.premium.advancedFiltersDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.advancedFilters}
            onChange={(checked) => updateSetting('premium.advancedFilters', checked)}
          />
        </SettingItem>
      </div>

      {/* Content & Social */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Users className="w-4 h-4" />
          Exclusive Content & Social
        </h4>

        <SettingItem
          label={t('settings.categories.premium.exclusiveContent')}
          description={t('settings.categories.premium.exclusiveContentDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.exclusiveContentAccess}
            onChange={(checked) => updateSetting('premium.exclusiveContentAccess', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.premium.whisperSpaceAccess')}
          description={t('settings.categories.premium.whisperSpaceAccessDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.whisperSpaceAccess}
            onChange={(checked) => updateSetting('premium.whisperSpaceAccess', checked)}
          />
        </SettingItem>
      </div>

      {/* Support & Beta */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <HeadphonesIcon className="w-4 h-4" />
          Support & Beta Access
        </h4>

        <SettingItem
          label={t('settings.categories.premium.prioritySupport')}
          description={t('settings.categories.premium.prioritySupportDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.prioritySupportEnabled}
            onChange={(checked) => updateSetting('premium.prioritySupportEnabled', checked)}
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.premium.betaFeatureAccess')}
          description={t('settings.categories.premium.betaFeatureAccessDesc')}
          isPremium
        >
          <ToggleSwitch
            checked={settings.premium.betaFeatureAccess}
            onChange={(checked) => updateSetting('premium.betaFeatureAccess', checked)}
          />
        </SettingItem>
      </div>

      {/* Whisper Space Settings */}
      {settings.premium.whisperSpaceAccess && (
        <div className="space-y-4">
          <h4 className="font-medium text-foreground flex items-center gap-2">
            <Crown className="w-4 h-4" />
            {t('settings.categories.premium.whisperSpaceSettings')}
          </h4>

          <div className="p-4 bg-card/50 border border-border rounded-lg">
            <p className="text-sm text-foreground/70 mb-4">
              {t('settings.categories.premium.whisperSpaceSettingsDesc')}
            </p>
            
            <button
              onClick={() => {
                // TODO: Navigate to whisper space settings
                alert('Whisper Space settings coming soon!');
              }}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm"
            >
              {t('settings.categories.premium.configureSettings')}
            </button>
          </div>
        </div>
      )}

      {/* Premium Benefits Summary */}
      <div className="mt-6 p-4 bg-romantic-gradient/10 border border-primary/30 rounded-lg">
        <h5 className="font-medium text-primary flex items-center gap-2 mb-3">
          <Star className="w-4 h-4" />
          Your Premium Benefits
        </h5>

        <div className="grid grid-cols-2 gap-3 text-sm text-foreground/70">
          <div className="flex items-center gap-2">
            <span className={`w-2 h-2 rounded-full ${settings.premium.creatorToolsEnabled ? 'bg-success' : 'bg-muted-foreground'}`}></span>
            Creator Tools
          </div>
          <div className="flex items-center gap-2">
            <span className={`w-2 h-2 rounded-full ${settings.premium.advancedAnalyticsEnabled ? 'bg-success' : 'bg-muted-foreground'}`}></span>
            Advanced Analytics
          </div>
          <div className="flex items-center gap-2">
            <span className={`w-2 h-2 rounded-full ${settings.premium.exclusiveContentAccess ? 'bg-success' : 'bg-muted-foreground'}`}></span>
            Exclusive Content
          </div>
          <div className="flex items-center gap-2">
            <span className={`w-2 h-2 rounded-full ${settings.premium.whisperSpaceAccess ? 'bg-success' : 'bg-muted-foreground'}`}></span>
            Whisper Space
          </div>
          <div className="flex items-center gap-2">
            <span className={`w-2 h-2 rounded-full ${settings.premium.unlimitedFastRequests ? 'bg-success' : 'bg-muted-foreground'}`}></span>
            Unlimited Fast Requests
          </div>
          <div className="flex items-center gap-2">
            <span className={`w-2 h-2 rounded-full ${settings.premium.enhancedMemoryCapacity ? 'bg-green-500' : 'bg-gray-400'}`}></span>
            Enhanced Memory
          </div>
        </div>
      </div>

      {/* Support Contact */}
      <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <h5 className="font-medium text-blue-800 dark:text-blue-200 flex items-center gap-2 mb-2">
          <HeadphonesIcon className="w-4 h-4" />
          Premium Support
        </h5>
        <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
          Need help with your premium features? Our priority support team is here to assist you.
        </p>
        <button
          onClick={() => {
            // TODO: Open support chat or page
            alert('Opening premium support...');
          }}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
        >
          Contact Premium Support
        </button>
      </div>
    </div>
  );
};

export default PremiumSettings; 