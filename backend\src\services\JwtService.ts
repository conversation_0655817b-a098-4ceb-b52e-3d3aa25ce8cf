import jwt, { JwtPayload } from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';

export class JwtService {
  private accessTokenSecret: string;
  private refreshTokenSecret: string;
  private accessTokenExpiry: string;
  private refreshTokenExpiry: string;
  private tokenBlacklist: Set<string>;

  constructor() {
    this.accessTokenSecret = process.env.JWT_ACCESS_SECRET || 'your-access-secret-key';
    this.refreshTokenSecret = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key';
    this.accessTokenExpiry = process.env.JWT_ACCESS_EXPIRY || '15m';
    this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRY || '7d';
    this.tokenBlacklist = new Set();
  }

  /**
   * Generate access token
   */
  public generateAccessToken(userId: string): string {
    return jwt.sign(
      { userId, type: 'access' },
      this.accessTokenSecret,
      { expiresIn: this.accessTokenExpiry }
    );
  }

  /**
   * Generate refresh token
   */
  public generateRefreshToken(userId: string): string {
    return jwt.sign(
      { userId, type: 'refresh', jti: uuidv4() },
      this.refreshTokenSecret,
      { expiresIn: this.refreshTokenExpiry }
    );
  }

  /**
   * Verify access token
   */
  public verifyAccessToken(token: string): JwtPayload | null {
    try {
      // Check if token is blacklisted
      if (this.tokenBlacklist.has(token)) {
        return null;
      }

      return jwt.verify(token, this.accessTokenSecret);
    } catch (error) {
      return null;
    }
  }

  /**
   * Verify refresh token
   */
  public verifyRefreshToken(token: string): JwtPayload | null {
    try {
      // Check if token is blacklisted
      if (this.tokenBlacklist.has(token)) {
        return null;
      }

      return jwt.verify(token, this.refreshTokenSecret);
    } catch (error) {
      return null;
    }
  }

  /**
   * Add token to blacklist
   */
  public async blacklistToken(token: string): Promise<void> {
    this.tokenBlacklist.add(token);
    
    // Optionally, store in Redis for persistence across server restarts
    // await redis.set(`blacklist:${token}`, '1', 'EX', 7 * 24 * 60 * 60); // 7 days
  }

  /**
   * Check if token is blacklisted
   */
  public isTokenBlacklisted(token: string): boolean {
    return this.tokenBlacklist.has(token);
  }

  /**
   * Clean up expired tokens from blacklist
   */
  public cleanupExpiredTokens(): void {
    // In production, this would be handled by Redis TTL
    // For in-memory storage, we can implement periodic cleanup
    console.log('Token blacklist cleanup would happen here');
  }
}