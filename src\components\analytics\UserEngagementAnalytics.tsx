'use client';

import React from 'react';
import { MessageCircle, Clock, Users, Heart, Target, Calendar, TrendingUp, Activity } from 'lucide-react';

interface UserEngagementAnalyticsProps {
  lang: string;
}

const UserEngagementAnalytics: React.FC<UserEngagementAnalyticsProps> = ({ lang }) => {
  // Mock engagement data
  const engagementMetrics = [
    {
      title: 'Daily Active Users',
      value: '8,432',
      change: '+12.3%',
      icon: Users,
      color: 'from-blue-500 to-indigo-600'
    },
    {
      title: 'Avg Session Duration',
      value: '24m 32s',
      change: '+5.7%',
      icon: Clock,
      color: 'from-green-500 to-emerald-600'
    },
    {
      title: 'Messages Sent',
      value: '156,789',
      change: '+18.9%',
      icon: MessageCircle,
      color: 'from-purple-500 to-pink-600'
    },
    {
      title: 'Character Interactions',
      value: '45,231',
      change: '+22.1%',
      icon: Heart,
      color: 'from-red-500 to-pink-600'
    }
  ];

  const topCharacters = [
    { name: '<PERSON>', interactions: 12847, avgRating: 4.9, category: 'CEO' },
    { name: '<PERSON>hade', interactions: 11234, avgRating: 4.8, category: 'Vampire' },
    { name: '<PERSON> Chen', interactions: 9876, avgRating: 4.7, category: 'Romance' },
    { name: 'Marcus Thompson', interactions: 8765, avgRating: 4.6, category: 'Fitness' },
    { name: 'Isabella Rodriguez', interactions: 7654, avgRating: 4.8, category: 'Artist' }
  ];

  const userBehaviorPatterns = [
    { time: '00:00', users: 1200 },
    { time: '04:00', users: 800 },
    { time: '08:00', users: 3400 },
    { time: '12:00', users: 5600 },
    { time: '16:00', users: 4800 },
    { time: '20:00', users: 6200 },
    { time: '23:59', users: 2800 }
  ];

  const engagementFeatures = [
    { feature: 'Character Chat', usage: 89.2, color: 'bg-blue-500' },
    { feature: 'Story Creation', usage: 67.8, color: 'bg-green-500' },
    { feature: 'Memory Building', usage: 54.3, color: 'bg-purple-500' },
    { feature: 'Journey Missions', usage: 78.9, color: 'bg-orange-500' },
    { feature: 'Store Browsing', usage: 45.6, color: 'bg-pink-500' }
  ];

  return (
    <div className="space-y-8">
      {/* Engagement Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {engagementMetrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${metric.color} rounded-lg flex items-center justify-center`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <span className="text-sm font-medium text-green-600 dark:text-green-400">
                  {metric.change}
                </span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">
                {metric.value}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {metric.title}
              </p>
            </div>
          );
        })}
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Top Characters */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
            <Heart className="w-5 h-5 text-red-500" />
            Most Popular Characters
          </h3>
          <div className="space-y-4">
            {topCharacters.map((character, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                      {character.name}
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {character.category} • ⭐ {character.avgRating}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-gray-100 text-sm">
                    {character.interactions.toLocaleString()}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    interactions
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Feature Usage */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
            <Activity className="w-5 h-5 text-blue-500" />
            Feature Usage
          </h3>
          <div className="space-y-4">
            {engagementFeatures.map((feature, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {feature.feature}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {feature.usage}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className={`${feature.color} h-2 rounded-full transition-all duration-300`}
                    style={{ width: `${feature.usage}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* User Activity Heatmap */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
          <Calendar className="w-5 h-5 text-green-500" />
          Daily Activity Pattern
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <span>Peak Hours: 8PM - 10PM</span>
            <span>Low Hours: 4AM - 6AM</span>
          </div>
          <div className="grid grid-cols-7 gap-2">
            {userBehaviorPatterns.map((data, index) => (
              <div key={index} className="text-center">
                <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                  {data.time}
                </div>
                <div
                  className="bg-gradient-to-t from-blue-500 to-indigo-600 rounded-lg mx-auto transition-all duration-300"
                  style={{
                    height: `${(data.users / 6200) * 100}px`,
                    width: '20px',
                    minHeight: '10px'
                  }}
                />
                <div className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                  {data.users.toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Engagement Insights */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-purple-500" />
          Key Insights
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
              Peak Engagement
            </h4>
            <p className="text-sm text-blue-600 dark:text-blue-300">
              Users are most active between 8PM-10PM, with character interactions peaking during this time.
            </p>
          </div>
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
              Character Preferences
            </h4>
            <p className="text-sm text-green-600 dark:text-green-300">
              CEO and Vampire characters show highest engagement rates, suggesting strong preference for power dynamics.
            </p>
          </div>
          <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
            <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-2">
              Feature Adoption
            </h4>
            <p className="text-sm text-purple-600 dark:text-purple-300">
              Character Chat leads usage at 89%, while Store features have room for improvement at 46%.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserEngagementAnalytics;
