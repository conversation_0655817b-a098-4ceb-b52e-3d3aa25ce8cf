import TrophiesClientPage from './TrophiesClientPage';

interface PageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function TrophiesPage({ params }: PageProps) {
  const { lang } = await params;
  return <TrophiesClientPage lang={lang} />;
}

export const metadata = {
  title: 'Trophies & Achievements - Alphane',
  description: 'Showcase your accomplishments and unlock exclusive rewards',
};