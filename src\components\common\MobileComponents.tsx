'use client';

import React from 'react';
import { Flame, Gem, Puzzle, Droplet } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import StaminaDisplay from '@/components/stamina/StaminaDisplay';

// Complex SVG Logo with 3 progress rings around user avatar
export const ComplexLogoWithRings: React.FC<{ user: any }> = ({ user }) => {
  // Mock progress data - in real app this would come from props or context
  const dailyProgress = 20; // 20%
  const weeklyProgress = 45; // 45%
  const monthlyProgress = 70; // 70%

  // Total height is 48px (3rem), with 4px padding top/bottom = 44px usable
  const centerX = 24;
  const centerY = 24;
  const logoSize = 14; // Size of the inner avatar area

  // Ring parameters - adjusted for 48px total height
  const outerRadius = 22;
  const innerRadius = 10;
  const ringThickness = (outerRadius - innerRadius) / 3;

  // Calculate ring radii
  const ring1Radius = outerRadius;
  const ring2Radius = outerRadius - ringThickness;
  const ring3Radius = outerRadius - 2 * ringThickness;

  // Calculate circumferences for progress calculation
  const ring1Circumference = 2 * Math.PI * ring1Radius;
  const ring2Circumference = 2 * Math.PI * ring2Radius;
  const ring3Circumference = 2 * Math.PI * ring3Radius;

  // Calculate stroke dash arrays for progress
  const ring1Progress = (monthlyProgress / 100) * ring1Circumference;
  const ring2Progress = (weeklyProgress / 100) * ring2Circumference;
  const ring3Progress = (dailyProgress / 100) * ring3Circumference;

  return (
    <div className="relative w-12 h-12 flex-shrink-0">
      <svg
        width="48"
        height="48"
        viewBox="0 0 48 48"
        className="absolute inset-0 transform -rotate-90"
      >
        {/* Background rings */}
        <circle
          cx={centerX}
          cy={centerY}
          r={ring1Radius}
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          className="text-gray-200 dark:text-gray-700"
          opacity="0.3"
        />
        <circle
          cx={centerX}
          cy={centerY}
          r={ring2Radius}
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          className="text-gray-200 dark:text-gray-700"
          opacity="0.3"
        />
        <circle
          cx={centerX}
          cy={centerY}
          r={ring3Radius}
          fill="none"
          stroke="currentColor"
          strokeWidth="1.5"
          className="text-gray-200 dark:text-gray-700"
          opacity="0.3"
        />

        {/* Progress rings */}
        {/* Outer ring - Monthly progress (purple) */}
        <circle
          cx={centerX}
          cy={centerY}
          r={ring1Radius}
          fill="none"
          stroke="#8b5cf6"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeDasharray={`${ring1Progress} ${ring1Circumference}`}
          className="transition-all duration-1000 ease-out"
        />

        {/* Middle ring - Weekly progress (blue) */}
        <circle
          cx={centerX}
          cy={centerY}
          r={ring2Radius}
          fill="none"
          stroke="#3b82f6"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeDasharray={`${ring2Progress} ${ring2Circumference}`}
          className="transition-all duration-1000 ease-out"
          style={{ animationDelay: '0.2s' }}
        />

        {/* Inner ring - Daily progress (green) */}
        <circle
          cx={centerX}
          cy={centerY}
          r={ring3Radius}
          fill="none"
          stroke="#10b981"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeDasharray={`${ring3Progress} ${ring3Circumference}`}
          className="transition-all duration-1000 ease-out"
          style={{ animationDelay: '0.4s' }}
        />
      </svg>

      {/* Center logo/avatar */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-7 h-7 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-sm">
          {user?.name ? user.name.charAt(0).toUpperCase() : 'A'}
        </div>
      </div>
    </div>
  );
};

// Horizontal Currency Display for <1024px Headers
export const HorizontalCurrencyDisplay: React.FC<{ user: any; lang?: string }> = ({ user, lang = 'en' }) => {
  const currencies = [
    {
      icon: Flame,
      value: user?.alphane_dust_balance || 1250,
      bgColor: 'bg-orange-500'
    },
    {
      icon: Gem,
      value: user?.endora_crystal_balance || 89,
      bgColor: 'bg-blue-500'
    },
    {
      icon: Puzzle,
      value: user?.serotile_fragment_balance || 156,
      bgColor: 'bg-purple-500'
    },
    {
      icon: Droplet,
      value: user?.oxytol_dew_balance || 42,
      bgColor: 'bg-pink-500'
    }
  ];

  return (
    <div className="flex items-center gap-1 sm:gap-1.5">
      {currencies.map((currency, index) => {
        const Icon = currency.icon;
        return (
          <div key={index} className="flex items-center gap-0.5 sm:gap-1">
            {/* Logo with 1.4x width, same height */}
            <div
              className={`h-8 ${currency.bgColor} rounded-lg flex items-center justify-center`}
              style={{width: '2.8rem'}} // 1.4x of height: 2rem * 1.4 = 2.8rem (32px * 1.4 = 44.8px)
            >
              <Icon className="text-white" size={14}/>
            </div>
            {/* Number on the right - responsive text size */}
            <span className="text-xs font-bold text-gray-800 dark:text-gray-100 leading-none whitespace-nowrap">
              {formatCurrency(currency.value)}
            </span>
          </div>
        );
      })}

      {/* 体力显示 - 添加在货币后面 */}
      <StaminaDisplay user={user} variant="mobile" lang={lang} />
    </div>
  );
};

// Legacy Sidebar-Style Currency Display (kept for compatibility)
export const SidebarStyleCurrencyDisplay: React.FC<{ user: any }> = ({ user }) => {
  const currencies = [
    {
      icon: Flame,
      value: user?.alphane_dust_balance || 1250,
      bgColor: 'bg-orange-500'
    },
    {
      icon: Gem,
      value: user?.endora_crystal_balance || 89,
      bgColor: 'bg-blue-500'
    },
    {
      icon: Puzzle,
      value: user?.serotile_fragment_balance || 156,
      bgColor: 'bg-purple-500'
    },
    {
      icon: Droplet,
      value: user?.oxytol_dew_balance || 42,
      bgColor: 'bg-pink-500'
    }
  ];

  return (
    <div className="flex items-start gap-2.5">
      {currencies.map((currency, index) => {
        const Icon = currency.icon;
        return (
          <div key={index} className="flex flex-col items-center">
            <div
              className={`h-9 ${currency.bgColor} rounded-lg flex items-center justify-center mb-1`}
              style={{width: '2.25rem'}}
            >
              <Icon className="text-white" size={16}/>
            </div>
            <p className="text-xs font-bold text-gray-800 dark:text-gray-100 leading-none">
              {formatCurrency(currency.value)}
            </p>
          </div>
        );
      })}
    </div>
  );
};

// Legacy Mobile Currency Display Component (kept for compatibility)
export const MobileCurrencyDisplay: React.FC<{ user: any }> = ({ user }) => {
  const currencies = [
    {
      icon: Flame,
      value: user?.flame || 1250,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20'
    },
    {
      icon: Gem,
      value: user?.gem || 89,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20'
    },
    {
      icon: Puzzle,
      value: user?.puzzle || 156,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20'
    },
    {
      icon: Droplet,
      value: user?.droplet || 42,
      color: 'text-cyan-500',
      bgColor: 'bg-cyan-50 dark:bg-cyan-900/20'
    }
  ];

  return (
    <div className="flex items-center gap-1">
      {currencies.map((currency, index) => {
        const Icon = currency.icon;
        return (
          <div
            key={index}
            className={`flex items-center gap-1 px-1.5 py-0.5 rounded-md ${currency.bgColor} transition-colors`}
          >
            <Icon size={12} className={currency.color} />
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              {formatCurrency(currency.value)}
            </span>
          </div>
        );
      })}
    </div>
  );
};
