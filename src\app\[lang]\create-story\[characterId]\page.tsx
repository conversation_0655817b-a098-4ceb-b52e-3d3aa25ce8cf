import CreateStoryClientPage from '../CreateStoryClientPage';
import { useTranslation } from '@/app/i18n';

interface CreateStoryPageProps {
  params: Promise<{
    lang: string;
    characterId: string;
  }>;
}

export default async function CreateStoryPage({ params }: CreateStoryPageProps) {
  const { lang, characterId } = await params;

  return <CreateStoryClientPage lang={lang} characterId={characterId} />;
}

export async function generateMetadata({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const { t } = await useTranslation(lang, 'translation');
  
  return {
    title: t('storyCreation.pageTitle'),
    description: t('storyCreation.pageDescription'),
  };
} 