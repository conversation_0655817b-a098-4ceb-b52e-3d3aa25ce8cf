'use client';

import { FC, useState } from 'react';
import Image from 'next/image';
import { Play, Mic, Check, X, User as UserIcon } from 'lucide-react';

interface UserBubbleProps {
  message: {
    text: string;
    voice?: {
      duration: string;
      url?: string;
    };
  };
  user: {
    avatar?: string;
    name?: string;
  } | null;
}

const UserBubble: FC<UserBubbleProps> = ({ message, user }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedText, setEditedText] = useState(message.text);

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedText(e.target.value);
  };
  
  const handleSaveChanges = () => {
    setIsEditing(false);
    // In a real app, this should be handled via a state management solution or a callback prop.
    message.text = editedText; 
  };
  
  const handleCancelChanges = () => {
    setIsEditing(false);
    setEditedText(message.text); // Revert changes
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Enter to save (unless Shift is pressed for a new line)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // Prevent new line
      handleSaveChanges();
    }
    // Escape to cancel
    if (e.key === 'Escape') {
      handleCancelChanges();
    }
  };

  return (
    <div className="flex flex-col items-end w-full">
      <div className="flex items-end gap-1.5 justify-end w-full">
        <div
          className="max-w-xs md:max-w-md rounded-2xl shadow-md bg-blue-500 text-white rounded-br-none relative"
        >
          {/* Voice indicator in top-left corner */}
          {message.voice && (
            <div className="absolute -top-3 -left-3 bg-green-500 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1 shadow-md">
              <Play size={10} className="fill-white" />
              <span>{message.voice.duration}</span>
            </div>
          )}
          {isEditing ? (
            <div className="p-3">
              <textarea
                value={editedText}
                onChange={handleTextChange}
                onKeyDown={handleKeyDown}
                className="bg-transparent text-white placeholder-white/70 focus:outline-none w-full resize-none text-sm"
                rows={editedText.split(/\r\n|\r|\n/).length}
                autoFocus
              />
              <div className="flex items-center justify-start gap-4 mt-2 pt-2 border-t border-white/20">
                 <button title="Voice Input" className="text-white/80 hover:text-white transition-colors">
                    <Mic size={16} />
                 </button>
                 <div className="flex-grow" /> {/* Spacer */}
                 <button title="Save Changes (Enter)" onClick={handleSaveChanges} className="text-white/80 hover:text-white transition-colors">
                    <Check size={18} />
                 </button>
                 <button title="Cancel (Esc)" onClick={handleCancelChanges} className="text-white/80 hover:text-white transition-colors">
                    <X size={18} />
                 </button>
              </div>
            </div>
          ) : (
             <div className="px-4 py-2 cursor-pointer" onDoubleClick={handleDoubleClick}>
                <p className="text-sm whitespace-pre-wrap">{message.text}</p>
             </div>
          )}
        </div>
        {/* User Avatar */}
        <div className="w-10 h-10 flex-shrink-0">
          {user?.avatar ? (
            <Image
              src={user.avatar}
              alt={user?.name || 'user avatar'}
              width={40}
              height={40}
              className="rounded-full"
              unoptimized
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center">
              <UserIcon size={24} className="text-white" />
            </div>
          )}
        </div>
      </div>
      {/* MetaData Row */}
      <div className="flex items-center gap-1.5 mt-1.5 mr-12 text-white">
        <span className="text-xs">A moment ago</span>
        <button title="Play audio" className="hover:opacity-80 transition-opacity">
          <Play size={14} className="fill-white" />
        </button>
      </div>
    </div>
  );
};

export default UserBubble; 