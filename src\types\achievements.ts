// Enhanced Achievement interface with comprehensive data structure
export interface EnhancedAchievement {
  id: string;
  name: string;
  description: string;
  requirement: string;
  tips: string;
  category: 'beginner' | 'interaction' | 'creation' | 'collection' | 'social' | 'special';
  rarity: 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond' | 'legendary';
  status: 'locked' | 'inProgress' | 'completed' | 'claimed';
  progress: {
    current: number;
    total: number;
  };
  points: number;
  icon?: string; // 可选的图标标识符
  rewards: {
    type: 'badge' | 'title' | 'currency' | 'item' | 'privilege' | 'experience';
    name: string;
    amount?: number;
    icon: string;
  }[];
  earnedDate?: string;
  relatedAchievements?: string[];
  difficulty?: 'easy' | 'medium' | 'hard' | 'extreme';
  missable?: boolean;
  estimatedTime?: number; // in minutes
}

export interface AchievementFilters {
  category: string;
  rarity: string;
  status: string;
  sortBy: string;
  searchTerm?: string;
}

export interface AchievementReward {
  type: 'badge' | 'title' | 'currency' | 'item' | 'privilege' | 'experience';
  name: string;
  amount?: number;
  icon: string;
  description?: string;
}

export interface AchievementStats {
  totalAchievements: number;
  completed: number;
  inProgress: number;
  locked: number;
  totalPoints: number;
  completionRate: number;
  rank: number;
  rarityBreakdown: Record<string, number>;
}

export interface RecommendedAchievement extends EnhancedAchievement {
  recommendationReason: string;
  priority: 'high' | 'medium' | 'low';
  estimatedCompletionTime: number;
} 