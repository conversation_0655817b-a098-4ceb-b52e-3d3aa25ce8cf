{"database": "alphane_production", "version": "2.0.0", "description": "Complete PostgreSQL schema for Alphane AI platform with multi-language support, enhanced gamification, store system, and social features", "migration_info": {"previous_version": "1.0.0", "upgrade_date": "2024-01-31", "breaking_changes": false, "new_features": ["Multi-language character and story support", "Enhanced store and subscription system", "Advanced gamification with daily missions", "Social interactions and UGC features", "Memorial events and anniversary system", "Comprehensive analytics and statistics", "Materialized views for performance"]}, "extensions": ["uuid-ossp", "pg_trgm", "btree_gin"], "tables": {"users": {"description": "User accounts and authentication", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "username": {"type": "VARCHAR(50)", "unique": true, "not_null": true}, "email": {"type": "VARCHAR(255)", "unique": true, "not_null": true}, "password_hash": {"type": "VARCHAR(255)", "not_null": true}, "display_name": {"type": "VARCHAR(100)"}, "avatar_url": {"type": "TEXT"}, "bio": {"type": "TEXT"}, "language_preference": {"type": "VARCHAR(10)", "default": "'en'"}, "timezone": {"type": "VARCHAR(50)", "default": "'UTC'"}, "is_creator": {"type": "BOOLEAN", "default": false}, "is_verified": {"type": "BOOLEAN", "default": false}, "is_active": {"type": "BOOLEAN", "default": true}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_users_username", "columns": ["username"]}, {"name": "idx_users_email", "columns": ["email"]}, {"name": "idx_users_created_at", "columns": ["created_at"]}]}, "user_profiles": {"description": "Extended user profile information", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "references": "users(id)"}, "avatar_url": {"type": "TEXT"}, "bio": {"type": "TEXT"}, "location": {"type": "VARCHAR(100)"}, "website": {"type": "VARCHAR(255)"}, "birth_date": {"type": "DATE"}, "gender": {"type": "VARCHAR(20)"}, "interests": {"type": "TEXT[]"}, "social_links": {"type": "JSONB", "default": "'{}'"}, "privacy_settings": {"type": "JSONB", "default": "'{}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "constraints": [{"type": "UNIQUE", "columns": ["user_id"]}]}, "characters": {"description": "AI characters with enhanced multi-language support", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "name": {"type": "VARCHAR(100)", "not_null": true}, "description": {"type": "TEXT"}, "avatar_url": {"type": "TEXT"}, "creator_id": {"type": "UUID", "not_null": true, "references": "users(id)"}, "personality_traits": {"type": "JSONB", "default": "'{}'"}, "background_story": {"type": "TEXT"}, "voice_settings": {"type": "JSONB", "default": "'{}'"}, "is_public": {"type": "BOOLEAN", "default": true}, "is_featured": {"type": "BOOLEAN", "default": false}, "tags": {"type": "TEXT[]", "default": "'{}'"}, "mbti_type": {"type": "VARCHAR(4)", "check": "mbti_type ~ '^[IE][NS][FT][JP]$'"}, "pov": {"type": "VARCHAR(20)", "default": "'neutral'"}, "era": {"type": "VARCHAR(50)"}, "region": {"type": "VARCHAR(100)"}, "abo_ratio": {"type": "JSONB", "default": "'{\"alpha\": 0.33, \"beta\": 0.34, \"omega\": 0.33}'"}, "detailed_personality": {"type": "JSONB", "default": "'{}'"}, "cognitive_model": {"type": "JSONB", "default": "'{}'"}, "appearance_details": {"type": "JSONB", "default": "'{}'"}, "behavioral_patterns": {"type": "JSONB", "default": "'{}'"}, "voice_characteristics": {"type": "JSONB", "default": "'{}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_characters_creator", "columns": ["creator_id"]}, {"name": "idx_characters_public", "columns": ["is_public"]}, {"name": "idx_characters_featured", "columns": ["is_featured"]}, {"name": "idx_characters_mbti", "columns": ["mbti_type"]}, {"name": "idx_characters_era", "columns": ["era"]}, {"name": "idx_characters_region", "columns": ["region"]}, {"name": "idx_characters_personality_gin", "type": "GIN", "columns": ["personality_traits"]}, {"name": "idx_characters_tags_gin", "type": "GIN", "columns": ["tags"]}]}, "character_localizations": {"description": "Multi-language character data (Chinese, Japanese, English)", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "character_id": {"type": "UUID", "not_null": true, "references": "characters(id)"}, "language_code": {"type": "VARCHAR(10)", "not_null": true, "check": "language_code IN ('zh-CN', 'ja-JP', 'en-US')"}, "name": {"type": "JSONB", "not_null": true, "default": "'{}'"}, "background": {"type": "TEXT"}, "personality": {"type": "TEXT"}, "appear_hierarchical_info": {"type": "JSONB", "not_null": true, "default": "'{}'"}, "ext_hierarchical_info": {"type": "JSONB", "not_null": true, "default": "'{}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "constraints": [{"type": "UNIQUE", "columns": ["character_id", "language_code"]}], "indexes": [{"name": "idx_character_localizations_character_lang", "columns": ["character_id", "language_code"]}, {"name": "idx_character_localizations_language", "columns": ["language_code"]}, {"name": "idx_character_localizations_appear_gin", "type": "GIN", "columns": ["appear_hierarchical_info"]}, {"name": "idx_character_localizations_ext_gin", "type": "GIN", "columns": ["ext_hierarchical_info"]}]}, "character_appearance_details": {"description": "Detailed character appearance hierarchy from data_examples", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "character_id": {"type": "UUID", "not_null": true, "references": "characters(id)"}, "physique": {"type": "JSONB", "default": "'{}'"}, "stature": {"type": "JSONB", "default": "'{}'"}, "body_ratio": {"type": "JSONB", "default": "'{}'"}, "face": {"type": "JSONB", "default": "'{}'"}, "hair": {"type": "JSONB", "default": "'{}'"}, "eyes": {"type": "JSONB", "default": "'{}'"}, "base_species": {"type": "JSONB", "default": "'{}'"}, "wardrobe": {"type": "JSONB", "default": "'{}'"}, "garments": {"type": "JSONB", "default": "'{}'"}, "footwear": {"type": "JSONB", "default": "'{}'"}, "adornments": {"type": "JSONB", "default": "'{}'"}, "gear": {"type": "JSONB", "default": "'{}'"}, "handheld_items": {"type": "JSONB", "default": "'{}'"}, "expression_engine": {"type": "JSONB", "default": "'{}'"}, "kinesics": {"type": "JSONB", "default": "'{}'"}, "vocalization": {"type": "JSONB", "default": "'{}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "constraints": [{"type": "UNIQUE", "columns": ["character_id"]}]}, "character_personality_profiles": {"description": "Cognitive models and personality profiles for characters", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "character_id": {"type": "UUID", "not_null": true, "references": "characters(id)"}, "species": {"type": "VARCHAR(50)", "default": "'Human'"}, "vocation": {"type": "TEXT"}, "social_role": {"type": "TEXT"}, "archetypal_story": {"type": "TEXT"}, "core_need": {"type": "TEXT"}, "core_fear": {"type": "TEXT"}, "core_virtue": {"type": "TEXT"}, "openness_score": {"type": "INTEGER", "check": "openness_score >= 1 AND openness_score <= 100"}, "conscientiousness_score": {"type": "INTEGER", "check": "conscientiousness_score >= 1 AND conscientiousness_score <= 100"}, "extraversion_score": {"type": "INTEGER", "check": "extraversion_score >= 1 AND extraversion_score <= 100"}, "agreeableness_score": {"type": "INTEGER", "check": "agreeableness_score >= 1 AND agreeableness_score <= 100"}, "neuroticism_score": {"type": "INTEGER", "check": "neuroticism_score >= 1 AND neuroticism_score <= 100"}, "emotional_baseline": {"type": "VARCHAR(50)"}, "emotional_range": {"type": "TEXT"}, "emotional_triggers": {"type": "TEXT[]"}, "coping_mechanisms": {"type": "TEXT[]"}, "empathy_level": {"type": "VARCHAR(20)"}, "emotional_expression": {"type": "TEXT"}, "personality_details": {"type": "JSONB", "default": "'{}'"}, "cognitive_details": {"type": "JSONB", "default": "'{}'"}, "emotional_details": {"type": "JSONB", "default": "'{}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "constraints": [{"type": "UNIQUE", "columns": ["character_id"]}]}, "stories": {"description": "Story content with enhanced multi-language support", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "title": {"type": "VARCHAR(200)", "not_null": true}, "description": {"type": "TEXT"}, "character_id": {"type": "UUID", "not_null": true, "references": "characters(id)"}, "creator_id": {"type": "UUID", "not_null": true, "references": "users(id)"}, "content": {"type": "TEXT"}, "is_public": {"type": "BOOLEAN", "default": true}, "is_featured": {"type": "BOOLEAN", "default": false}, "tags": {"type": "TEXT[]", "default": "'{}'"}, "difficulty_level": {"type": "VARCHAR(20)", "default": "'beginner'"}, "estimated_duration": {"type": "INTEGER"}, "story_rating": {"type": "VARCHAR(10)", "default": "'G'"}, "scene_count": {"type": "INTEGER", "default": 0}, "narrative_structure": {"type": "VARCHAR(50)", "default": "'linear'"}, "world_setting": {"type": "JSONB", "default": "'{}'"}, "character_relationships": {"type": "JSONB", "default": "'{}'"}, "story_themes": {"type": "TEXT[]"}, "completion_status": {"type": "VARCHAR(20)", "default": "'draft'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "indexes": [{"name": "idx_stories_character", "columns": ["character_id"]}, {"name": "idx_stories_creator", "columns": ["creator_id"]}, {"name": "idx_stories_public", "columns": ["is_public"]}, {"name": "idx_stories_featured", "columns": ["is_featured"]}, {"name": "idx_stories_difficulty", "columns": ["difficulty_level"]}, {"name": "idx_stories_tags_gin", "type": "GIN", "columns": ["tags"]}, {"name": "idx_stories_world_setting_gin", "type": "GIN", "columns": ["world_setting"]}]}, "story_localizations": {"description": "Multi-language story data supporting complex narrative structures", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "story_id": {"type": "UUID", "not_null": true, "references": "stories(id)"}, "language_code": {"type": "VARCHAR(10)", "not_null": true, "check": "language_code IN ('zh-CN', 'ja-JP', 'en-US')"}, "story_name": {"type": "VARCHAR(200)", "not_null": true}, "story_rating": {"type": "VARCHAR(10)", "default": "'G'"}, "story_core_world": {"type": "JSONB", "not_null": true, "default": "'{}'"}, "story_narrative_style": {"type": "JSONB", "not_null": true, "default": "'{}'"}, "story_beats": {"type": "JSONB", "not_null": true, "default": "'[]'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "constraints": [{"type": "UNIQUE", "columns": ["story_id", "language_code"]}], "indexes": [{"name": "idx_story_localizations_story_lang", "columns": ["story_id", "language_code"]}, {"name": "idx_story_localizations_language", "columns": ["language_code"]}, {"name": "idx_story_localizations_core_world_gin", "type": "GIN", "columns": ["story_core_world"]}, {"name": "idx_story_localizations_narrative_gin", "type": "GIN", "columns": ["story_narrative_style"]}, {"name": "idx_story_localizations_beats_gin", "type": "GIN", "columns": ["story_beats"]}]}, "scene_hierarchical_info": {"description": "Four-layer scene information hierarchy from data_examples", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "story_id": {"type": "UUID", "not_null": true, "references": "stories(id)"}, "scene_index": {"type": "INTEGER", "not_null": true}, "language_code": {"type": "VARCHAR(10)", "not_null": true, "check": "language_code IN ('zh-CN', 'ja-JP', 'en-US')"}, "scene_name": {"type": "VARCHAR(200)", "not_null": true}, "scene_description": {"type": "TEXT"}, "first_layer_worldview": {"type": "JSONB", "default": "'{}'"}, "second_layer_scene": {"type": "JSONB", "default": "'{}'"}, "third_layer_antecedent": {"type": "JSONB", "default": "'{}'"}, "fourth_layer_character": {"type": "JSONB", "default": "'{}'"}, "scene_start": {"type": "TEXT"}, "scene_conflict": {"type": "TEXT"}, "scene_turning_point": {"type": "TEXT"}, "scene_character_arc": {"type": "TEXT"}, "scene_relationship_development": {"type": "TEXT"}, "scene_target": {"type": "TEXT"}, "scene_ending": {"type": "TEXT"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "constraints": [{"type": "UNIQUE", "columns": ["story_id", "scene_index", "language_code"]}], "indexes": [{"name": "idx_scene_hierarchical_story_scene", "columns": ["story_id", "scene_index"]}, {"name": "idx_scene_hierarchical_language", "columns": ["language_code"]}, {"name": "idx_scene_hierarchical_worldview_gin", "type": "GIN", "columns": ["first_layer_worldview"]}, {"name": "idx_scene_hierarchical_scene_gin", "type": "GIN", "columns": ["second_layer_scene"]}]}, "story_progression": {"description": "User progress tracking through story scenes", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "references": "users(id)"}, "story_id": {"type": "UUID", "not_null": true, "references": "stories(id)"}, "current_scene_index": {"type": "INTEGER", "default": 1}, "completed_scenes": {"type": "INTEGER[]", "default": "'{}'"}, "total_scenes": {"type": "INTEGER", "not_null": true}, "completion_percentage": {"type": "DECIMAL(5,2)", "default": 0.0}, "story_choices": {"type": "JSONB", "default": "'{}'"}, "relationship_points": {"type": "JSONB", "default": "'{}'"}, "character_affinity": {"type": "JSONB", "default": "'{}'"}, "started_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "last_accessed_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "completed_at": {"type": "TIMESTAMP"}, "total_time_spent": {"type": "INTEGER", "default": 0}, "status": {"type": "VARCHAR(20)", "default": "'in_progress'", "check": "status IN ('not_started', 'in_progress', 'completed', 'paused')"}}, "constraints": [{"type": "UNIQUE", "columns": ["user_id", "story_id"]}], "indexes": [{"name": "idx_story_progression_user_story", "columns": ["user_id", "story_id"]}, {"name": "idx_story_progression_status", "columns": ["status"]}, {"name": "idx_story_progression_completion", "columns": ["completion_percentage"]}, {"name": "idx_story_progression_choices_gin", "type": "GIN", "columns": ["story_choices"]}]}, "user_currencies": {"description": "User virtual currency balances with enhanced tracking", "columns": {"id": {"type": "UUID", "primary_key": true, "default": "gen_random_uuid()"}, "user_id": {"type": "UUID", "not_null": true, "references": "users(id)"}, "coins": {"type": "INTEGER", "default": 0}, "gems": {"type": "INTEGER", "default": 0}, "tokens": {"type": "INTEGER", "default": 0}, "hearts": {"type": "INTEGER", "default": 0}, "star_diamonds": {"type": "INTEGER", "default": 0}, "joy_crystals": {"type": "INTEGER", "default": 0}, "glimmering_dust": {"type": "INTEGER", "default": 0}, "memory_puzzles": {"type": "INTEGER", "default": 0}, "daily_bonus_available": {"type": "BOOLEAN", "default": true}, "next_daily_bonus": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "currency_stats": {"type": "JSONB", "default": "'{}'"}, "created_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}, "updated_at": {"type": "TIMESTAMP", "default": "CURRENT_TIMESTAMP"}}, "constraints": [{"type": "UNIQUE", "columns": ["user_id"]}], "indexes": [{"name": "idx_user_currencies_user", "columns": ["user_id"]}, {"name": "idx_user_currencies_stats_gin", "type": "GIN", "columns": ["currency_stats"]}]}}, "materialized_views": {"character_stats": {"description": "Aggregated character statistics for CharacterCard component", "refresh_strategy": "CONCURRENTLY", "refresh_frequency": "15 minutes", "columns": ["id", "name", "creator_id", "avatar_url", "mbti_type", "era", "region", "chats_count", "unique_chatters", "likes_count", "friends_count", "moments_count", "stories_count", "total_engagement", "avg_rating", "rating_count", "creator_username", "creator_display_name", "creator_avatar", "last_chat_activity", "last_moment_created", "created_at", "updated_at"]}, "moment_stats": {"description": "Aggregated moment statistics for MomentCard component", "refresh_strategy": "CONCURRENTLY", "refresh_frequency": "15 minutes", "columns": ["id", "user_id", "character_id", "content", "moment_type", "visibility", "mood", "tags", "is_featured", "media_urls", "likes_count", "shares_count", "comments_count", "bookmarks_count", "total_engagement", "username", "display_name", "user_avatar", "character_name", "character_avatar", "published_at", "updated_at"]}, "user_dashboard_data": {"description": "Comprehensive user data for dashboard and profile cards", "refresh_strategy": "CONCURRENTLY", "refresh_frequency": "30 minutes", "columns": ["id", "username", "email", "display_name", "avatar_url", "bio", "location", "website", "current_membership_tier", "membership_expires_at", "star_diamonds", "joy_crystals", "glimmering_dust", "memory_puzzles", "daily_bonus_available", "next_daily_bonus", "total_conversations", "characters_created", "stories_completed", "achievements_unlocked", "consecutive_login_days", "total_login_days", "followers_count", "following_count", "moments_count", "avg_moment_engagement", "influence_score", "current_level", "current_xp", "total_xp_earned", "level_xp_required", "last_login_date", "last_activity_date", "user_created_at", "user_updated_at"]}}, "functions": {"update_updated_at_column": {"description": "Trigger function to automatically update updated_at timestamp", "language": "plpgsql", "returns": "TRIGGER"}, "refresh_all_card_views": {"description": "Refreshes all materialized views used by frontend Card components", "language": "plpgsql", "returns": "void"}, "refresh_card_view": {"description": "Refreshes a specific materialized view by name", "language": "plpgsql", "parameters": ["view_name TEXT"], "returns": "void"}, "update_currency_stats": {"description": "Updates user currency statistics for transactions", "language": "plpgsql", "parameters": ["p_user_id UUID", "p_currency_type VARCHAR(50)", "p_amount INTEGER", "p_transaction_type VARCHAR(20)"], "returns": "void"}, "update_story_progression": {"description": "Updates user progress through story scenes", "language": "plpgsql", "parameters": ["p_user_id UUID", "p_story_id UUID", "p_scene_index INTEGER", "p_time_spent INTEGER"], "returns": "void"}}}