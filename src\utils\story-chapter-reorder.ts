/**
 * Utility functions for handling story chapter reordering logic
 * Ensures branch chapters follow main chapters and maintains sequential numbering
 */

import type { StoryChapter } from '@/types/story-creation';

/**
 * Reorders chapters with proper handling of main/branch relationships
 * @param chapters - Current chapters array
 * @param chapterId - ID of the chapter being moved
 * @param newOrder - Target order position
 * @param newParent - Optional new parent for branch chapters
 * @returns Updated chapters array with proper ordering
 */
export function reorderChapters(
  chapters: StoryChapter[],
  chapterId: string,
  newOrder: number,
  newParent?: string
): StoryChapter[] {
  const updatedChapters = [...chapters];
  const chapterIndex = updatedChapters.findIndex(c => c.id === chapterId);

  if (chapterIndex === -1) return chapters;

  const chapter = updatedChapters[chapterIndex];
  const oldOrder = chapter.order;

  // If dragging a main chapter, we need to move all its branches with it
  if (chapter.chapterType === 'main') {
    // Find all branch chapters that belong to this main chapter
    const branchChapters = updatedChapters.filter(c =>
      c.chapterType === 'branch' && c.parentChapter === chapter.id
    );

    // Update the main chapter's order
    updatedChapters[chapterIndex] = { ...chapter, order: newOrder };

    // Update all branch chapters to have the same new order
    branchChapters.forEach(branchChapter => {
      const branchIndex = updatedChapters.findIndex(c => c.id === branchChapter.id);
      if (branchIndex !== -1) {
        updatedChapters[branchIndex] = { ...branchChapter, order: newOrder };
      }
    });

    // Update other main chapters' orders (and their branches)
    updatedChapters.forEach((c, index) => {
      if (index !== chapterIndex && c.chapterType === 'main' && c.id !== chapter.id) {
        let shouldUpdateOrder = false;
        let newOrderForThisChapter = c.order;

        if (oldOrder < newOrder && c.order > oldOrder && c.order <= newOrder) {
          newOrderForThisChapter = c.order - 1;
          shouldUpdateOrder = true;
        } else if (oldOrder > newOrder && c.order >= newOrder && c.order < oldOrder) {
          newOrderForThisChapter = c.order + 1;
          shouldUpdateOrder = true;
        }

        if (shouldUpdateOrder) {
          // Update the main chapter
          updatedChapters[index] = { ...c, order: newOrderForThisChapter };

          // Update all branches of this main chapter
          const thisMainBranches = updatedChapters.filter(branch =>
            branch.chapterType === 'branch' && branch.parentChapter === c.id
          );
          thisMainBranches.forEach(branchChapter => {
            const branchIndex = updatedChapters.findIndex(ch => ch.id === branchChapter.id);
            if (branchIndex !== -1) {
              updatedChapters[branchIndex] = { ...branchChapter, order: newOrderForThisChapter };
            }
          });
        }
      }
    });
  } else if (chapter.chapterType === 'branch') {
    // For branch chapters, only reorder within the same parent
    updatedChapters[chapterIndex] = { ...chapter, order: newOrder };

    // Update other branch chapters with the same parent
    updatedChapters.forEach((c, index) => {
      if (index !== chapterIndex &&
          c.chapterType === 'branch' &&
          c.parentChapter === chapter.parentChapter) {
        if (oldOrder < newOrder && c.order > oldOrder && c.order <= newOrder) {
          updatedChapters[index] = { ...c, order: c.order - 1 };
        } else if (oldOrder > newOrder && c.order >= newOrder && c.order < oldOrder) {
          updatedChapters[index] = { ...c, order: c.order + 1 };
        }
      }
    });
  }

  // Auto-renumber all chapters to ensure sequential ordering
  return renumberChapters(updatedChapters);
}

/**
 * Renumbers all chapters to ensure sequential ordering
 * Main chapters get sequential numbers (1, 2, 3...)
 * Branch chapters inherit their parent's number
 * @param chapters - Chapters array to renumber
 * @returns Renumbered chapters array
 */
export function renumberChapters(chapters: StoryChapter[]): StoryChapter[] {
  const mainChapters = chapters.filter(c => c.chapterType === 'main').sort((a, b) => a.order - b.order);
  const updatedChapters = [...chapters];

  mainChapters.forEach((mainChapter, index) => {
    const newMainOrder = index + 1;
    const mainIndex = updatedChapters.findIndex(c => c.id === mainChapter.id);
    if (mainIndex !== -1) {
      updatedChapters[mainIndex] = { ...mainChapter, order: newMainOrder };
    }

    // Update all branches of this main chapter
    const branchChapters = updatedChapters.filter(c =>
      c.chapterType === 'branch' && c.parentChapter === mainChapter.id
    );
    branchChapters.forEach(branchChapter => {
      const branchIndex = updatedChapters.findIndex(c => c.id === branchChapter.id);
      if (branchIndex !== -1) {
        updatedChapters[branchIndex] = { ...branchChapter, order: newMainOrder };
      }
    });
  });

  return updatedChapters;
}

/**
 * Gets the next available branch letter for a given chapter number
 * @param chapters - Current chapters array
 * @param parentChapterNumber - The parent chapter number
 * @returns Next available branch letter (A, B, C, etc.)
 */
export function getNextBranchLetter(chapters: StoryChapter[], parentChapterNumber: number): string {
  const existingBranches = chapters.filter(
    c => c.chapterType === 'branch' && c.order === parentChapterNumber
  );
  
  const usedLetters = existingBranches
    .map(c => c.branchLetter || '')
    .filter(letter => letter.length > 0)
    .sort();
  
  // Find the next available letter
  for (let i = 0; i < 26; i++) {
    const letter = String.fromCharCode(65 + i); // A, B, C, ...
    if (!usedLetters.includes(letter)) {
      return letter;
    }
  }
  
  return 'A'; // Fallback
}

/**
 * Validates chapter structure and relationships
 * @param chapters - Chapters array to validate
 * @returns Validation result with any issues found
 */
export function validateChapterStructure(chapters: StoryChapter[]): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  
  // Check for orphaned branch chapters
  const branchChapters = chapters.filter(c => c.chapterType === 'branch');
  const mainChapterIds = new Set(chapters.filter(c => c.chapterType === 'main').map(c => c.id));
  
  branchChapters.forEach(branch => {
    if (!branch.parentChapter || !mainChapterIds.has(branch.parentChapter)) {
      issues.push(`Branch chapter "${branch.title}" has invalid or missing parent`);
    }
  });
  
  // Check for duplicate orders within the same type and parent
  const mainChapters = chapters.filter(c => c.chapterType === 'main');
  const mainOrders = mainChapters.map(c => c.order);
  const duplicateMainOrders = mainOrders.filter((order, index) => mainOrders.indexOf(order) !== index);
  
  if (duplicateMainOrders.length > 0) {
    issues.push(`Duplicate main chapter orders found: ${duplicateMainOrders.join(', ')}`);
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
}
