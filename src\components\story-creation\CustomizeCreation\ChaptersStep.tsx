'use client';

import React, { useState, useCallback } from 'react';
import Image from 'next/image';
import { Plus, X, Send, ArrowRight, ArrowLeft, Book, GitBranch, Settings, Heart, MessageCircle, Upload, Check } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryChaptersStepProps } from '@/types/story-creation';
import type { StoryChapter, StoryChoice, ChapterCompletionEffects, WorldSetting } from '@/types/story-creation';
import StoryFlow from './StoryFlow';
import WorldSettingComponent from './WorldSetting';
import { reorderChapters, getNextBranchLetter } from '@/utils/story-chapter-reorder';
import { useStoryImageUpload } from '@/hooks/story-creation/useStoryImageUpload';

const ChaptersStep: React.FC<StoryChaptersStepProps & {
  lang: string;
  selectedChapter: string | null;
  setSelectedChapter: (chapterId: string | null) => void;
}> = ({
  formData,
  setFormData,
  lang,
  onSubmit,
  onStepChange,
  selectedChapter,
  setSelectedChapter
}) => {
  const { t } = useTranslation(lang, 'translation');

  // Use the story image upload hook
  const {
    worldSettingImagePreview,
    chapterImageInputRefs,
    handleChapterImageUpload,
    toggleUseWorldSettingImage,
    triggerChapterImageInput,
    getChapterImagePreview
  } = useStoryImageUpload(formData, setFormData);

  // 生成章节显示名称
  const getChapterDisplayName = useCallback((chapter: StoryChapter) => {
    if (chapter.chapterType === 'main') {
      return `${chapter.order}`;
    } else {
      return `${chapter.order}${chapter.branchLetter || ''}`;
    }
  }, []);

  // 获取下一个主章节编号
  const getNextMainChapterNumber = useCallback(() => {
    const mainChapters = formData.chapters.filter(c => c.chapterType === 'main');
    return mainChapters.length + 1;
  }, [formData.chapters]);

  // 获取可用的分支字母 - 使用工具函数
  const getNextBranchLetterForChapter = useCallback((chapterNumber: number) => {
    return getNextBranchLetter(formData.chapters, chapterNumber);
  }, [formData.chapters]);

  // 添加主章节
  const handleAddMainChapter = useCallback(() => {
    const chapterNumber = getNextMainChapterNumber();
    const newChapter: StoryChapter = {
      id: `chapter-${Date.now()}`,
      title: `Chapter ${chapterNumber}`,
      description: '',
      content: '',
      order: chapterNumber,
      chapterType: 'main',
      backgroundSetting: '',
      completionEffects: {
        bondPointsChange: 0,
        greetingChange: '',
        characterMoodChange: '',
        customEffects: ''
      },
      choices: [],
      nextChapters: []
    };
    setFormData(prev => ({
      ...prev,
      chapters: [...prev.chapters, newChapter]
    }));
    setSelectedChapter(newChapter.id);
  }, [getNextMainChapterNumber, setFormData]);

  // 添加分支章节
  const handleAddBranchChapter = useCallback((parentChapterNumber: number) => {
    const branchLetter = getNextBranchLetterForChapter(parentChapterNumber);
    const parentChapter = formData.chapters.find(
      c => c.chapterType === 'main' && c.order === parentChapterNumber
    );
    
    const newChapter: StoryChapter = {
      id: `chapter-${Date.now()}`,
      title: `Chapter ${parentChapterNumber}${branchLetter}`,
      description: '',
      content: '',
      order: parentChapterNumber,
      chapterType: 'branch',
      parentChapter: parentChapter?.id,
      branchLetter,
      backgroundSetting: '',
      completionEffects: {
        bondPointsChange: 0,
        greetingChange: '',
        characterMoodChange: '',
        customEffects: ''
      },
      choices: [],
      nextChapters: []
    };
    setFormData(prev => ({
      ...prev,
      chapters: [...prev.chapters, newChapter]
    }));
    setSelectedChapter(newChapter.id);
  }, [getNextBranchLetterForChapter, formData.chapters, setFormData]);

  // 删除章节
  const handleRemoveChapter = useCallback((chapterId: string) => {
    setFormData(prev => ({
      ...prev,
      chapters: prev.chapters.filter(chapter => chapter.id !== chapterId)
    }));
    if (selectedChapter === chapterId) {
      setSelectedChapter(null);
    }
  }, [selectedChapter, setFormData]);

  // 更新章节
  const handleChapterUpdate = useCallback((chapterId: string, updates: Partial<StoryChapter>) => {
    setFormData(prev => ({
      ...prev,
      chapters: prev.chapters.map(chapter =>
        chapter.id === chapterId ? { ...chapter, ...updates } : chapter
      )
    }));
  }, [setFormData]);

  // 更新完成效果
  const handleCompletionEffectsUpdate = useCallback((chapterId: string, effects: Partial<ChapterCompletionEffects>) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      handleChapterUpdate(chapterId, {
        completionEffects: { ...chapter.completionEffects, ...effects }
      });
    }
  }, [formData.chapters, handleChapterUpdate]);

  // 章节重新排序 - 使用工具函数
  const handleChapterReorder = useCallback((chapterId: string, newOrder: number, newParent?: string) => {
    setFormData(prev => ({
      ...prev,
      chapters: reorderChapters(prev.chapters, chapterId, newOrder, newParent)
    }));
  }, [setFormData]);

  // 更新世界设定
  const handleWorldSettingChange = useCallback((updates: Partial<WorldSetting>) => {
    setFormData(prev => ({
      ...prev,
      worldSetting: { ...prev.worldSetting, ...updates }
    }));
  }, [setFormData]);

  // 添加选择
  const handleAddChoice = useCallback((chapterId: string) => {
    const newChoice: StoryChoice = {
      id: `choice-${Date.now()}`,
      text: 'Choice Option',
      description: '',
      consequences: '',
      nextChapter: '',
      effectPreview: ''
    };
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      handleChapterUpdate(chapterId, {
        choices: [...(chapter.choices || []), newChoice]
      });
    }
  }, [formData.chapters, handleChapterUpdate]);

  // 删除选择
  const handleRemoveChoice = useCallback((chapterId: string, choiceId: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      handleChapterUpdate(chapterId, {
        choices: chapter.choices?.filter(choice => choice.id !== choiceId) || []
      });
    }
  }, [formData.chapters, handleChapterUpdate]);

  // 更新选择
  const handleChoiceUpdate = useCallback((chapterId: string, choiceId: string, updates: Partial<StoryChoice>) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      handleChapterUpdate(chapterId, {
        choices: chapter.choices?.map(choice =>
          choice.id === choiceId ? { ...choice, ...updates } : choice
        ) || []
      });
    }
  }, [formData.chapters, handleChapterUpdate]);

  // 按章节编号排序
  const sortedChapters = [...formData.chapters].sort((a, b) => {
    if (a.order !== b.order) return a.order - b.order;
    if (a.chapterType === 'main' && b.chapterType === 'branch') return -1;
    if (a.chapterType === 'branch' && b.chapterType === 'main') return 1;
    return (a.branchLetter || '').localeCompare(b.branchLetter || '');
  });

  // 获取可选择的下一章节（只能选择当前章节编号+1的章节）
  const getAvailableNextChapters = useCallback((currentChapter: StoryChapter) => {
    const nextChapterNumber = currentChapter.order + 1;
    
    // 过滤出下一章节编号的所有章节（主章节和分支章节）
    const availableChapters = formData.chapters.filter(chapter => 
      chapter.order === nextChapterNumber
    );
    
    return availableChapters.map(chapter => ({
      id: chapter.id,
      label: `${getChapterDisplayName(chapter)}: ${chapter.title}`,
      value: chapter.id
    }));
  }, [formData.chapters, getChapterDisplayName]);

  const selectedChapterData = formData.chapters.find(c => c.id === selectedChapter);

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2">
          {t('storyCreation.steps.chapters.title')}
        </h2>
      </div>

      {/* World Setting Section */}
      <WorldSettingComponent
        worldSetting={formData.worldSetting}
        onWorldSettingChange={handleWorldSettingChange}
        formData={formData}
        setFormData={setFormData}
        lang={lang}
        className="mb-6"
      />

      <div className="grid grid-cols-1 lg:grid-cols-10 gap-6">
        {/* Left Side: Story Flow (40% width on desktop, full width on mobile) */}
        <div className="lg:col-span-4 space-y-4">
          <StoryFlow
            chapters={formData.chapters}
            selectedChapter={selectedChapter}
            onChapterSelect={setSelectedChapter}
            onAddMainChapter={handleAddMainChapter}
            onAddBranchChapter={handleAddBranchChapter}
            onRemoveChapter={handleRemoveChapter}
            onChapterReorder={handleChapterReorder}
            lang={lang}
            title={t('storyCreation.steps.chapters.storyFlow')}
            icon={<Book className="w-5 h-5" />}
            accentColor="purple"
            showAddButton={true}
            showBranchButtons={true}
            showRemoveButtons={true}
          />
        </div>

        {/* Right Side: Chapter Editing (60% width on desktop, full width on mobile) */}
        <div className="lg:col-span-6 space-y-4">
          {selectedChapterData ? (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 lg:p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4 lg:mb-6">
                <h3 className="text-base lg:text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
                  <Settings className="w-4 h-4 lg:w-5 lg:h-5 text-purple-500" />
                  <span className="truncate">Edit Chapter {getChapterDisplayName(selectedChapterData)}</span>
                </h3>
              </div>

              <div className="space-y-4">
                {/* 章节标题 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.chapters.chapterTitle')}
                  </label>
                  <input
                    type="text"
                    value={selectedChapterData.title}
                    onChange={(e) => handleChapterUpdate(selectedChapterData.id, { title: e.target.value })}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                    placeholder="Enter chapter title"
                  />
                </div>

                {/* 章节描述 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.chapters.chapterDescription')}
                  </label>
                  <textarea
                    value={selectedChapterData.description}
                    onChange={(e) => handleChapterUpdate(selectedChapterData.id, { description: e.target.value })}
                    rows={2}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                    placeholder="Brief description of this chapter"
                  />
                </div>

                {/* 章节内容 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.chapters.chapterContent')}
                  </label>
                  <textarea
                    value={selectedChapterData.content}
                    onChange={(e) => handleChapterUpdate(selectedChapterData.id, { content: e.target.value })}
                    rows={4}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                    placeholder="Chapter content and narrative"
                  />
                </div>

                {/* 背景设定 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.chapters.backgroundSetting')}
                  </label>
                  <textarea
                    value={selectedChapterData.backgroundSetting}
                    onChange={(e) => handleChapterUpdate(selectedChapterData.id, { backgroundSetting: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                    placeholder={t('storyCreation.steps.chapters.backgroundSettingPlaceholder')}
                  />
                </div>

                {/* 背景图片 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('storyCreation.steps.chapters.backgroundImage')}
                  </label>

                  {/* Use World Setting Image Checkbox */}
                  <div className="mb-3">
                    <label className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedChapterData.useWorldSettingImage || false}
                        onChange={(e) => toggleUseWorldSettingImage(selectedChapterData.id, e.target.checked)}
                        className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 dark:focus:ring-purple-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <Check className="w-3 h-3 text-purple-500" />
                      {t('storyCreation.steps.chapters.useWorldSettingImage')}
                    </label>
                  </div>

                  {/* Image Upload Section */}
                  {!selectedChapterData.useWorldSettingImage && (
                    <button
                      type="button"
                      onClick={() => triggerChapterImageInput(selectedChapterData.id)}
                      className="w-full p-4 border-2 border-dashed border-purple-300 dark:border-purple-700 rounded-lg hover:border-purple-400 dark:hover:border-purple-600 transition-all duration-300 bg-white/50 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800 hover:scale-105"
                    >
                      {getChapterImagePreview(selectedChapterData.id) ? (
                        <div className="text-purple-700 dark:text-purple-300">
                          <Image
                            src={getChapterImagePreview(selectedChapterData.id)!}
                            alt="Chapter Background"
                            width={120}
                            height={80}
                            className="mx-auto mb-2 rounded-lg object-cover"
                          />
                          <p className="font-semibold text-sm">{t('storyCreation.steps.chapters.backgroundImageUploaded')}</p>
                          <p className="text-xs">{t('storyCreation.steps.chapters.clickToChangeImage')}</p>
                        </div>
                      ) : (
                        <div className="text-purple-600 dark:text-purple-400">
                          <Upload className="mx-auto mb-2" size={24} />
                          <p className="font-semibold text-sm">{t('storyCreation.steps.chapters.uploadBackgroundImage')}</p>
                          <p className="text-xs">{t('storyCreation.steps.chapters.clickOrDragToUpload')}</p>
                        </div>
                      )}
                    </button>
                  )}

                  {/* World Setting Image Preview */}
                  {selectedChapterData.useWorldSettingImage && worldSettingImagePreview && (
                    <div className="p-4 border-2 border-green-300 dark:border-green-700 rounded-lg bg-green-50/50 dark:bg-green-900/20">
                      <div className="text-green-700 dark:text-green-300 text-center">
                        <Image
                          src={worldSettingImagePreview}
                          alt="World Setting Image"
                          width={120}
                          height={80}
                          className="mx-auto mb-2 rounded-lg object-cover"
                        />
                        <p className="font-semibold text-sm">{t('storyCreation.steps.chapters.usingWorldSettingImage')}</p>
                        <p className="text-xs">{t('storyCreation.steps.chapters.inheritedFromWorldSetting')}</p>
                      </div>
                    </div>
                  )}

                  {/* Hidden file input */}
                  <input
                    ref={(el) => {
                      if (chapterImageInputRefs.current) {
                        chapterImageInputRefs.current[selectedChapterData.id] = el;
                      }
                    }}
                    type="file"
                    accept="image/*"
                    onChange={(e) => e.target.files?.[0] && handleChapterImageUpload(selectedChapterData.id, e.target.files[0])}
                    className="hidden"
                  />
                </div>

                {/* 完成后的变化 */}
                <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
                  <h4 className="font-medium text-purple-800 dark:text-purple-200 mb-3 flex items-center gap-2">
                    <Heart className="w-4 h-4" />
                    {t('storyCreation.steps.chapters.completionEffects')}
                  </h4>
                  
                  <div className="space-y-3">
                                         {/* 好感度积分变化 */}
                     <div>
                       <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                         {t('storyCreation.steps.chapters.bondPointsChange')}
                       </label>
                       <input
                         type="number"
                         min="-100"
                         max="100"
                         value={selectedChapterData.completionEffects.bondPointsChange}
                         onChange={(e) => {
                           const value = parseInt(e.target.value) || 0;
                           const clampedValue = Math.max(-100, Math.min(100, value));
                           handleCompletionEffectsUpdate(selectedChapterData.id, { 
                             bondPointsChange: clampedValue
                           });
                         }}
                         className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                         placeholder={t('storyCreation.steps.chapters.bondPointsPlaceholder')}
                       />
                       <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                         Range: -100 to +100 points
                       </p>
                     </div>

                    {/* 问候语变化 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('storyCreation.steps.chapters.greetingChange')}
                      </label>
                      <textarea
                        value={selectedChapterData.completionEffects.greetingChange || ''}
                        onChange={(e) => handleCompletionEffectsUpdate(selectedChapterData.id, { 
                          greetingChange: e.target.value 
                        })}
                        rows={2}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                        placeholder={t('storyCreation.steps.chapters.greetingChangePlaceholder')}
                      />
                    </div>

                    {/* 角色心情变化 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('storyCreation.steps.chapters.characterMoodChange')}
                      </label>
                      <input
                        type="text"
                        value={selectedChapterData.completionEffects.characterMoodChange || ''}
                        onChange={(e) => handleCompletionEffectsUpdate(selectedChapterData.id, { 
                          characterMoodChange: e.target.value 
                        })}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                        placeholder={t('storyCreation.steps.chapters.characterMoodChangePlaceholder')}
                      />
                    </div>

                    {/* 其他效果 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('storyCreation.steps.chapters.customEffects')}
                      </label>
                      <textarea
                        value={selectedChapterData.completionEffects.customEffects || ''}
                        onChange={(e) => handleCompletionEffectsUpdate(selectedChapterData.id, { 
                          customEffects: e.target.value 
                        })}
                        rows={2}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                        placeholder={t('storyCreation.steps.chapters.customEffectsPlaceholder')}
                      />
                    </div>
                  </div>
                </div>

                {/* 章节选择 */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      <MessageCircle className="inline w-4 h-4 mr-1" />
                      {t('storyCreation.steps.chapters.choices')}
                    </label>
                    <button
                      type="button"
                      onClick={() => handleAddChoice(selectedChapterData.id)}
                      className="flex items-center gap-1 px-3 py-1 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors text-sm"
                    >
                      <Plus size={14} />
                      {t('storyCreation.steps.chapters.addChoice')}
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    {selectedChapterData.choices?.map((choice) => (
                      <div key={choice.id} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Choice</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveChoice(selectedChapterData.id, choice.id)}
                            className="p-1 text-red-500 hover:text-red-700 dark:hover:text-red-300"
                          >
                            <X size={14} />
                          </button>
                        </div>
                        
                        <div className="space-y-3">
                          <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              {t('storyCreation.steps.chapters.choiceText')}
                            </label>
                            <input
                              type="text"
                              value={choice.text}
                              onChange={(e) => handleChoiceUpdate(selectedChapterData.id, choice.id, { text: e.target.value })}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                              placeholder="Enter choice text..."
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              {t('storyCreation.steps.chapters.choiceDescription')}
                            </label>
                            <input
                              type="text"
                              value={choice.description}
                              onChange={(e) => handleChoiceUpdate(selectedChapterData.id, choice.id, { description: e.target.value })}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                              placeholder="Brief description of this choice..."
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              {t('storyCreation.steps.chapters.choiceTriggerCondition')}
                            </label>
                            <textarea
                              value={choice.consequences}
                              onChange={(e) => handleChoiceUpdate(selectedChapterData.id, choice.id, { consequences: e.target.value })}
                              rows={2}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all resize-none"
                              placeholder={t('storyCreation.steps.chapters.choiceTriggerConditionPlaceholder')}
                            />
                          </div>
                                                     <div>
                            <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                              {t('storyCreation.steps.chapters.nextChapter')}
                            </label>
                             <select
                               value={choice.nextChapter}
                               onChange={(e) => handleChoiceUpdate(selectedChapterData.id, choice.id, { nextChapter: e.target.value })}
                               className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                             >
                               <option value="">{t('storyCreation.steps.chapters.selectNextChapter')}</option>
                               {getAvailableNextChapters(selectedChapterData).map(option => (
                                 <option key={option.id} value={option.value}>
                                   {option.label}
                                 </option>
                               ))}
                             </select>
                           </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Chapter Navigation */}
              <div className="flex justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={() => onStepChange?.('objectivesSubjectives')}
                  disabled={formData.chapters.length === 0}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-xl hover:from-purple-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium"
                >
                  {t('storyCreation.storyFlow.navigation.continueToObjectives')}
                  <ArrowRight size={18} />
                </button>

                <button
                  type="button"
                  onClick={() => onStepChange?.('objectivesSubjectives')}
                  disabled={formData.chapters.length === 0}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium"
                >
                  {t('storyCreation.storyFlow.navigation.continueToObjectives')}
                  <ArrowRight size={18} />
                </button>
              </div>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 lg:p-8 border border-gray-200 dark:border-gray-700 text-center">
              <Settings className="w-12 h-12 lg:w-16 lg:h-16 mx-auto mb-3 lg:mb-4 text-gray-400" />
              <p className="text-sm lg:text-base text-gray-500 dark:text-gray-400">
                {t('storyCreation.steps.chapters.selectChapterDescription')}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={() => window.history.back()}
          className="flex items-center gap-2 px-6 py-3 border-2 border-purple-300 dark:border-purple-700 text-purple-700 dark:text-purple-300 rounded-xl hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-all duration-300 font-medium"
        >
          <ArrowLeft size={18} />
          Back To Selecting Characters
        </button>

        <button
          type="button"
          onClick={onSubmit}
          disabled={formData.chapters.length === 0}
          className="flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
        >
          <Send size={20} />
          <span>{t('storyCreation.buttons.createStory')}</span>
        </button>
      </div>

      {/* Helper Text */}
      <div className="text-center">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {formData.chapters.length === 0 
            ? t('storyCreation.validation.atLeastOneChapter')
            : `${formData.chapters.length} ${t('storyCreation.steps.chapters.addMainChapter')} • ${t('storyCreation.steps.chapters.selectChapterDescription')}`
          }
        </p>
      </div>
    </div>
  );
};

export default ChaptersStep; 