'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, Image, Frame, <PERSON><PERSON><PERSON>, Heart, BookOpen, Camera, Wand2, Filter, Search, User, MessageCircle, Users, Map, Brain, Gem, Zap } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface ArtsSectionProps {
  lang: string;
  searchQuery?: string;
  featured?: boolean;
}

interface ArtItem {
  id: string;
  type: 'profile' | 'character' | 'story' | 'scene' | 'memory';
  subType?: 'avatar' | 'chat_bubble' | 'character_series' | 'story_scene' | 'environment' | 'memory_art';
  name: string;
  description: string;
  price: number;
  currency: 'endora' | 'alphane' | 'serotile';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  category: string;
  tags: string[];
  image?: string;
  isNew?: boolean;
  isLimited?: boolean;
  discount?: number;
  series?: string; // For character series
}

const ArtsSection: React.FC<ArtsSectionProps> = ({ lang, searchQuery = '', featured = false }) => {
  const { t } = useTranslation(lang, 'translation');
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [selectedRarity, setSelectedRarity] = useState<string[]>([]);

  const categories = [
    { id: 'all', name: t('store.arts.categories.all'), icon: Palette, description: t('store.arts.categories.allDescription') },
    { id: 'profile', name: t('store.arts.categories.profile'), icon: User, description: t('store.arts.categories.profileDescription') },
    { id: 'character', name: t('store.arts.categories.character'), icon: Heart, description: t('store.arts.categories.characterDescription') },
    { id: 'story', name: t('store.arts.categories.story'), icon: BookOpen, description: t('store.arts.categories.storyDescription') },
    { id: 'memory', name: t('store.arts.categories.memory'), icon: Brain, description: t('store.arts.categories.memoryDescription') }
  ];

  const artItems: ArtItem[] = [
    // Profile Arts - Avatars
    {
      id: '1',
      type: 'profile',
      subType: 'avatar',
      name: 'Elegant Business Avatar',
      description: 'Professional avatar frame with golden accents',
      price: 10,
      currency: 'endora',
      rarity: 'rare',
      category: 'profile',
      tags: ['business', 'professional', 'golden'],
      isNew: true
    },
    {
      id: '2',
      type: 'profile',
      subType: 'avatar',
      name: 'Mystical Aura Frame',
      description: 'Magical avatar frame with floating particles',
      price: 15,
      currency: 'endora',
      rarity: 'epic',
      category: 'profile',
      tags: ['mystical', 'magical', 'particles']
    },
    // Profile Arts - Chat Bubbles
    {
      id: '3',
      type: 'profile',
      subType: 'chat_bubble',
      name: 'Heart Bubble Theme',
      description: 'Romantic chat bubble with heart animations',
      price: 8,
      currency: 'endora',
      rarity: 'common',
      category: 'profile',
      tags: ['romantic', 'hearts', 'animation']
    },
    {
      id: '4',
      type: 'profile',
      subType: 'chat_bubble',
      name: 'Neon Glow Bubbles',
      description: 'Futuristic chat bubbles with neon effects',
      price: 12,
      currency: 'endora',
      rarity: 'rare',
      category: 'profile',
      tags: ['futuristic', 'neon', 'glow']
    },
    // Character Arts - Series
    {
      id: '5',
      type: 'character',
      subType: 'character_series',
      name: 'Vampire Lord Collection',
      description: 'Complete vampire character art series',
      price: 35,
      currency: 'endora',
      rarity: 'legendary',
      category: 'character',
      tags: ['vampire', 'gothic', 'collection'],
      series: 'Dark Romance',
      isNew: true
    },
    {
      id: '6',
      type: 'character',
      subType: 'character_series',
      name: 'CEO Power Series',
      description: 'Business elite character collection',
      price: 30,
      currency: 'endora',
      rarity: 'epic',
      category: 'character',
      tags: ['business', 'power', 'elite'],
      series: 'Corporate Romance'
    },
    // Story Arts - Stories & Scenes
    {
      id: '7',
      type: 'story',
      subType: 'story_scene',
      name: 'Moonlit Castle Romance',
      description: 'Romantic castle scene under starry night',
      price: 18,
      currency: 'endora',
      rarity: 'epic',
      category: 'story',
      tags: ['castle', 'moonlight', 'romance']
    },
    {
      id: '8',
      type: 'story',
      subType: 'environment',
      name: 'Executive Office Suite',
      description: 'Luxury modern office with city skyline',
      price: 20,
      currency: 'endora',
      rarity: 'rare',
      category: 'story',
      tags: ['office', 'modern', 'luxury'],
      discount: 30
    },
    // Memory Arts
    {
      id: '9',
      type: 'memory',
      subType: 'memory_art',
      name: 'Cherry Blossom Memory',
      description: 'Serene spring scene with falling petals',
      price: 15,
      currency: 'serotile',
      rarity: 'rare',
      category: 'memory',
      tags: ['spring', 'nature', 'peaceful']
    },
    {
      id: '10',
      type: 'memory',
      subType: 'memory_art',
      name: 'First Date Memory',
      description: 'Customizable romantic first date scene',
      price: 20,
      currency: 'serotile',
      rarity: 'epic',
      category: 'memory',
      tags: ['romantic', 'date', 'customizable'],
      isNew: true
    },
    {
      id: '11',
      type: 'memory',
      subType: 'memory_art',
      name: 'Anniversary Celebration',
      description: 'Special milestone memory artwork',
      price: 25,
      currency: 'serotile',
      rarity: 'legendary',
      category: 'memory',
      tags: ['anniversary', 'celebration', 'milestone']
    }
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'from-yellow-400 to-orange-500';
      case 'epic': return 'from-purple-400 to-pink-500';
      case 'rare': return 'from-blue-400 to-indigo-500';
      case 'common': return 'from-gray-400 to-gray-500';
      default: return 'from-gray-400 to-gray-500';
    }
  };

  const getRarityBorder = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'border-yellow-400 shadow-yellow-400/20';
      case 'epic': return 'border-purple-400 shadow-purple-400/20';
      case 'rare': return 'border-blue-400 shadow-blue-400/20';
      case 'common': return 'border-gray-400 shadow-gray-400/20';
      default: return 'border-gray-200 dark:border-gray-700';
    }
  };

  const getCurrencyIcon = (currency: string) => {
    switch (currency) {
      case 'endora': return <Gem className="w-4 h-4 text-blue-500" />;
      case 'alphane': return <Sparkles className="w-4 h-4 text-orange-500" />;
      case 'serotile': return <Zap className="w-4 h-4 text-purple-500" />;
      default: return <Gem className="w-4 h-4 text-blue-500" />;
    }
  };

  const getTypeIcon = (type: string, subType?: string) => {
    if (type === 'profile') {
      return subType === 'avatar' ? User : MessageCircle;
    }
    switch (type) {
      case 'character': return Heart;
      case 'story': return BookOpen;
      case 'memory': return Brain;
      default: return Image;
    }
  };

  const getSubTypeLabel = (type: string, subType?: string) => {
    if (type === 'profile') {
      return subType === 'avatar' ? t('store.arts.subTypes.avatarFrame') : t('store.arts.subTypes.chatBubble');
    }
    if (type === 'character') {
      return t('store.arts.subTypes.characterSeries');
    }
    if (type === 'story') {
      return subType === 'story_scene' ? t('store.arts.subTypes.storyScene') : t('store.arts.subTypes.environment');
    }
    if (type === 'memory') {
      return t('store.arts.subTypes.memoryArt');
    }
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const filteredItems = artItems.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesRarity = selectedRarity.length === 0 || selectedRarity.includes(item.rarity);

    return matchesCategory && matchesSearch && matchesRarity;
  });

  const displayItems = featured ? filteredItems.slice(0, 6) : filteredItems;

  return (
    <div className="space-y-6">

      {/* Category Filters */}
      {!featured && (
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  activeCategory === category.id
                    ? 'bg-primary text-primary-foreground shadow-lg'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                <Icon className="w-4 h-4" />
                <div className="text-left">
                  <div className="font-medium">{category.name}</div>
                  {activeCategory === category.id && (
                    <div className="text-xs opacity-75">{category.description}</div>
                  )}
                </div>
              </button>
            );
          })}
        </div>
      )}

      {/* Arts Grid - Mobile optimized */}
      <div className={`grid gap-4 grid-cols-1 xs:grid-cols-2 ${featured ? 'md:grid-cols-2 lg:grid-cols-3' : 'md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'}`}>
        {displayItems.map((item) => {
          const TypeIcon = getTypeIcon(item.type, item.subType);
          
          return (
            <div
              key={item.id}
              className={`relative bg-white dark:bg-gray-800 rounded-xl border-2 p-4 md:p-6 shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl group min-h-[400px] flex flex-col ${getRarityBorder(item.rarity)}`}
            >
              {/* Enhanced Badges - SOTA z-index positioning */}
              <div className="absolute top-3 right-3 flex flex-col gap-1 z-20">
                {item.isNew && (
                  <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg backdrop-blur-sm">
                    {t('store.arts.badges.new')}
                  </div>
                )}
                {item.isLimited && (
                  <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse shadow-lg backdrop-blur-sm">
                    {t('store.arts.badges.limited')}
                  </div>
                )}
                {item.discount && (
                  <div className="bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg backdrop-blur-sm">
                    {t('store.arts.badges.discount', { discount: item.discount })}
                  </div>
                )}
              </div>

              {/* Art Preview - Proper z-index layering */}
              <div className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg mb-4 flex items-center justify-center relative overflow-hidden z-10">
                <div className={`w-16 h-16 bg-gradient-to-br ${getRarityColor(item.rarity)} rounded-full flex items-center justify-center shadow-lg z-10`}>
                  <TypeIcon className="w-8 h-8 text-white" />
                </div>
                {/* Overlay effect - Lower z-index than badges */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300 flex items-center justify-center z-10">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 dark:bg-gray-800/90 rounded-full p-2">
                      <Image className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 flex flex-col space-y-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-bold text-gray-900 dark:text-gray-100 text-sm group-hover:text-primary transition-colors line-clamp-2">
                      {item.name}
                    </h3>
                    <div className={`px-2 py-1 rounded-full text-xs font-bold text-white bg-gradient-to-r ${getRarityColor(item.rarity)} flex-shrink-0`}>
                      {t(`store.arts.rarity.${item.rarity}`)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-xs text-purple-600 dark:text-purple-400 font-medium">
                      {getSubTypeLabel(item.type, item.subType)}
                    </span>
                    {item.series && (
                      <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                        • {item.series}
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-3 mb-4">
                    {item.description}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {item.tags.slice(0, 3).map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Price and Action - Fixed at bottom */}
                <div className="mt-auto space-y-3">
                  <div className="flex items-center justify-center">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                        {item.price}
                      </span>
                      {getCurrencyIcon(item.currency)}
                    </div>
                  </div>
                  <button className="w-full bg-gradient-to-r from-primary to-primary/80 text-white px-4 py-2.5 rounded-lg text-sm font-medium hover:shadow-lg hover:shadow-primary/30 transition-all duration-200 group-hover:scale-105">
                    {t('store.arts.actions.purchase')}
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Art Creation Workshop */}
      {!featured && (
        <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800 rounded-xl p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Wand2 className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              {t('store.arts.workshop.title')}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {t('store.arts.workshop.description')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg font-semibold hover:shadow-lg hover:shadow-purple-500/30 transition-all duration-200 flex items-center justify-center gap-2">
                <Wand2 className="w-5 h-5" />
                {t('store.arts.workshop.createCustomArt')}
              </button>
              <button className="px-6 py-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-semibold hover:shadow-lg transition-all duration-200 flex items-center justify-center gap-2">
                <Frame className="w-5 h-5" />
                {t('store.arts.workshop.viewGallery')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ArtsSection;
