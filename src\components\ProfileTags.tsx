'use client';

import React from 'react';

interface ProfileTagsProps {
  tags: string[];
  maxTags?: number;
  className?: string;
  variant?: 'default' | 'compact';
}

const ProfileTags: React.FC<ProfileTagsProps> = ({
  tags,
  maxTags = 3,
  className = '',
  variant = 'default'
}) => {
  if (!tags || tags.length === 0) return null;

  const displayTags = tags.slice(0, maxTags);
  const remainingCount = tags.length - maxTags;

  const baseClasses = variant === 'compact'
    ? 'px-1.5 py-0.5 text-xs'
    : 'px-2 py-1 text-xs';

  const tagClasses = `${baseClasses} bg-white/20 backdrop-blur-sm rounded-full text-white/90 font-medium transition-all duration-200 hover:bg-white/30 whitespace-nowrap`;
  const remainingClasses = `${baseClasses} text-white/70 bg-white/10 rounded-full whitespace-nowrap`;

  return (
    <div className={`flex flex-wrap gap-1 leading-tight ${className}`} style={{ maxHeight: '3rem', overflow: 'hidden' }}>
      {displayTags.map((tag, index) => (
        <span
          key={index}
          className={tagClasses}
        >
          #{tag}
        </span>
      ))}
      {remainingCount > 0 && (
        <span className={remainingClasses}>
          +{remainingCount}
        </span>
      )}
    </div>
  );
};

export default ProfileTags;
