'use client';

import React from 'react';
import { Trophy, Gift, Star, Crown, Sparkles, Zap } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface ProgressMilestone {
  value: number;
  completed: boolean;
  isRewardClaimed?: boolean;
}

interface CumulativeProgressRewardProps {
  activeTab: 'daily' | 'weekly' | 'monthly';
  completedTasks: number;
  totalTasks: number;
  lang: string;
  onClaimReward?: (milestone: number) => void;
  claimedMilestones?: Set<string>;
}

const CumulativeProgressReward: React.FC<CumulativeProgressRewardProps> = ({
  activeTab,
  completedTasks,
  totalTasks,
  lang,
  onClaimReward,
  claimedMilestones = new Set()
}) => {
  const { t } = useTranslation(lang, 'translation');

  // Define milestones - ORIGINAL VALUES with VISUAL EQUAL SPACING
  const getMilestones = (type: 'daily' | 'weekly' | 'monthly'): number[] => {
    switch (type) {
      case 'daily':
        return [0, 3, 5, 7]; // Original values
      case 'weekly':
        return [0, 5, 9, 12, 14, 15]; // Original values
      case 'monthly':
        return [0, 9, 16, 22, 24, 25]; // Original values
      default:
        return [0];
    }
  };

  // Get reward icon for each milestone level
  const getRewardIcon = (milestoneIndex: number, totalMilestones: number) => {
    const progress = milestoneIndex / (totalMilestones - 1);

    if (progress === 1) return Crown; // Final milestone - Crown
    if (progress >= 0.8) return Trophy; // High milestone - Trophy
    if (progress >= 0.6) return Sparkles; // Medium-high milestone - Sparkles
    if (progress >= 0.4) return Star; // Medium milestone - Star
    if (progress >= 0.2) return Gift; // Low milestone - Gift
    return null; // Starting point
  };

  const milestones = getMilestones(activeTab);
  const maxMilestone = Math.max(...milestones);
  const isBigRewardAvailable = completedTasks >= maxMilestone;

  // 优化：动态计算里程碑间距，避免重叠
  const getOptimizedMilestonePositions = () => {
    const positions = milestones.slice(1).map((milestone, index) => {
      const originalPosition = (milestone / maxMilestone) * 100;
      return {
        milestone,
        originalPosition,
        adjustedPosition: originalPosition,
        index
      };
    });

    // 最小间距阈值（百分比）
    const minSpacing = 8; // 8% minimum spacing

    // 从左到右调整位置，避免重叠
    for (let i = 1; i < positions.length; i++) {
      const current = positions[i];
      const previous = positions[i - 1];
      
      if (current.adjustedPosition - previous.adjustedPosition < minSpacing) {
        current.adjustedPosition = previous.adjustedPosition + minSpacing;
      }
    }

    // 确保最后一个位置不超过100%
    const lastPosition = positions[positions.length - 1];
    if (lastPosition && lastPosition.adjustedPosition > 100) {
      // 从右往左重新分布
      lastPosition.adjustedPosition = 100;
      for (let i = positions.length - 2; i >= 0; i--) {
        const current = positions[i];
        const next = positions[i + 1];
        if (next.adjustedPosition - current.adjustedPosition < minSpacing) {
          current.adjustedPosition = next.adjustedPosition - minSpacing;
        }
      }
    }

    return positions;
  };

  const optimizedPositions = getOptimizedMilestonePositions();

  // 优化：智能标签布局，避免数字重叠（PC端优先保持水平对齐）
  const getLabelLayout = () => {
    const labelPositions = optimizedPositions.map((pos, index) => {
      // 动态计算垂直偏移，基于位置密度和屏幕尺寸
      let verticalOffset = 0;
      
      // 检查与相邻标签的距离
      const leftNeighbor = index > 0 ? optimizedPositions[index - 1] : null;
      const rightNeighbor = index < optimizedPositions.length - 1 ? optimizedPositions[index + 1] : null;
      
      // 如果左右邻居距离太近，在移动端使用交替布局，PC端保持水平
      const leftDistance = leftNeighbor ? pos.adjustedPosition - leftNeighbor.adjustedPosition : 100;
      const rightDistance = rightNeighbor ? rightNeighbor.adjustedPosition - pos.adjustedPosition : 100;
      
      // 在小屏幕（移动端）才使用垂直偏移，PC端（lg及以上）保持水平对齐
      const isMobile = typeof window !== 'undefined' && window.innerWidth < 1024; // lg breakpoint
      
      if ((leftDistance < 12 || rightDistance < 12) && isMobile) {
        verticalOffset = index % 2 === 0 ? 0 : 24;
      }
      
      return {
        ...pos,
        verticalOffset
      };
    });

    return labelPositions;
  };

  const labelLayout = getLabelLayout();

  // Calculate progress for each milestone
  const getProgressMilestones = (): ProgressMilestone[] => {
    return milestones.map(value => ({
      value,
      completed: completedTasks >= value,
      isRewardClaimed: false // This would come from API in real implementation
    }));
  };

  const progressMilestones = getProgressMilestones();

  // Get reward info based on tab and milestone
  const getRewardInfo = (milestone: number, isBigReward: boolean = false) => {
    if (isBigReward) {
      switch (activeTab) {
        case 'daily':
          return {
            icon: Crown,
            title: 'Daily Champion',
            description: 'Complete all daily tasks',
            reward: '500 Star Diamonds + Exclusive Badge',
            color: 'from-yellow-400 to-orange-500'
          };
        case 'weekly':
          return {
            icon: Trophy,
            title: 'Weekly Master',
            description: 'Complete all weekly tasks',
            reward: '1500 Star Diamonds + Premium Avatar',
            color: 'from-blue-400 to-purple-500'
          };
        case 'monthly':
          return {
            icon: Sparkles,
            title: 'Monthly Legend',
            description: 'Complete all monthly tasks',
            reward: '5000 Star Diamonds + Legendary Title',
            color: 'from-purple-400 to-pink-500'
          };
      }
    }

    // Regular milestone rewards
    const baseRewards = {
      daily: { base: 50, multiplier: 25 },
      weekly: { base: 150, multiplier: 75 },
      monthly: { base: 500, multiplier: 250 }
    };

    const reward = baseRewards[activeTab];
    const amount = reward.base + (milestone * reward.multiplier);

    return {
      icon: Gift,
      title: `${milestone} Tasks Milestone`,
      description: `Complete ${milestone} ${activeTab} tasks`,
      reward: `${amount} Star Diamonds`,
      color: 'from-green-400 to-blue-500'
    };
  };

  const bigRewardInfo = getRewardInfo(maxMilestone, true);

  return (
    /* Transparent wrapper container with specified margins */
    <div className="ml-0 mr-8 bg-transparent border-transparent">
      <div className="space-y-0">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-left">
            <div className="w-10 h-10 lg:w-10 lg:h-10 max-lg:w-8 max-lg:h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
              <Trophy className="w-6 h-6 lg:w-6 lg:h-6 max-lg:w-5 max-lg:h-5 text-white" />
            </div>
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 text-left">
              {completedTasks}/{totalTasks} tasks completed
            </p>
          </div>

          {/* Big Reward Indicator */}
          {isBigRewardAvailable && (
            <div className="flex items-center gap-1 bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 px-2 py-1 rounded-full">
              <Crown className="w-3 h-3 lg:w-3 lg:h-3 max-lg:w-[10px] max-lg:h-[10px] text-yellow-600 dark:text-yellow-400" />
              <span className="text-xs font-medium text-yellow-700 dark:text-yellow-300">
                {t('journey.progress.bigReward')}
              </span>
            </div>
          )}
        </div>

      {/* 优化的进度条区域 */}
      <div className="relative">
        {/* 奖励图标区域 - 增加高度避免重叠 */}
        <div className="relative mb-3 h-12 lg:h-12 max-lg:h-8">
          {optimizedPositions.map((positionData) => {
            const { milestone, adjustedPosition, index } = positionData;
            const actualIndex = index + 1; // Since we sliced off the first element
            const isCompleted = completedTasks >= milestone;
            const isBigReward = milestone === maxMilestone;
            const RewardIcon = getRewardIcon(actualIndex, milestones.length);
            const milestoneKey = `${activeTab}-${milestone}`;
            const isRewardClaimed = claimedMilestones.has(milestoneKey);

            return (
              <div
                key={milestone}
                className="absolute top-0 transform -translate-x-1/2 transition-all duration-300"
                style={{ left: `${Math.min(adjustedPosition, 100)}%` }}
              >
                {/* 大型奖励图标 - 响应式缩放 */}
                {isCompleted && !isRewardClaimed ? (
                  <button
                    onClick={() => onClaimReward?.(milestone)}
                    className={`
                      w-12 h-12 lg:w-12 lg:h-12
                      max-lg:w-[32px] max-lg:h-[32px]
                      rounded-full border-2 transition-all duration-300 flex items-center justify-center
                      ${isBigReward
                        ? 'bg-gradient-to-br from-yellow-400 to-orange-500 border-yellow-300 shadow-lg shadow-yellow-400/50 scale-110 hover:scale-115 animate-pulse'
                        : 'bg-gradient-to-br from-green-400 to-blue-500 border-green-300 shadow-md shadow-green-400/30 hover:scale-105'
                      }
                      cursor-pointer hover:shadow-xl z-10
                    `}
                  >
                    {RewardIcon && (
                      <RewardIcon className={`
                        w-6 h-6 lg:w-6 lg:h-6
                        max-lg:w-[16px] max-lg:h-[16px]
                        text-white ${isBigReward ? 'animate-pulse' : ''}
                      `} />
                    )}
                  </button>
                ) : (
                  <div className={`
                    w-12 h-12 lg:w-12 lg:h-12
                    max-lg:w-[32px] max-lg:h-[32px]
                    rounded-full border-2 transition-all duration-300 flex items-center justify-center ${
                    isCompleted && isRewardClaimed
                      ? 'bg-gray-300 dark:bg-gray-600 border-gray-400 dark:border-gray-500 opacity-60'
                      : isCompleted
                      ? isBigReward
                        ? 'bg-gradient-to-br from-yellow-400 to-orange-500 border-yellow-300 shadow-lg shadow-yellow-400/50 scale-110'
                        : 'bg-gradient-to-br from-green-400 to-blue-500 border-green-300 shadow-md shadow-green-400/30'
                      : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600'
                  } z-10`}>
                    {isCompleted && RewardIcon && (
                      <RewardIcon className={`
                        w-6 h-6 lg:w-6 lg:h-6
                        max-lg:w-[16px] max-lg:h-[16px]
                        ${isRewardClaimed ? 'text-gray-500' : 'text-white'}
                        ${isBigReward && !isRewardClaimed ? 'animate-pulse' : ''}
                      `} />
                    )}
                    {!isCompleted && RewardIcon && (
                      <RewardIcon className="
                        w-5 h-5 lg:w-5 lg:h-5
                        max-lg:w-[14px] max-lg:h-[14px]
                        text-gray-400 dark:text-gray-500
                      " />
                    )}
                  </div>
                )}

              </div>
            );
          })}
        </div>

        {/* 优化的进度条 */}
        <div className="relative w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden shadow-inner border border-gray-300/20 dark:border-gray-600/20">
          {/* 渐变进度填充 */}
          <div
            className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 transition-all duration-700 ease-out shadow-sm relative overflow-hidden"
            style={{ 
              width: `${Math.min((completedTasks / maxMilestone) * 100, 100)}%` 
            }}
          >
            {/* 进度条动画效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
          </div>

          {/* 优化的进度标记点 */}
          {optimizedPositions.map((positionData) => {
            const { milestone, adjustedPosition } = positionData;
            const isCompleted = completedTasks >= milestone;
            const isBigReward = milestone === maxMilestone;

            return (
              <div
                key={milestone}
                className="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-300"
                style={{
                  left: `${Math.min(adjustedPosition, 100)}%`,
                }}
              >
                {/* 标记点 - 增大尺寸提高可见性 */}
                <div className={`w-4 h-4 rounded-full border-3 transition-all duration-300 shadow-sm ${
                  isCompleted
                    ? isBigReward
                      ? 'bg-yellow-400 border-yellow-200 shadow-md shadow-yellow-400/50'
                      : 'bg-green-400 border-green-200 shadow-sm shadow-green-400/30'
                    : 'bg-white dark:bg-gray-800 border-gray-400 dark:border-gray-500'
                }`} />
              </div>
            );
          })}
        </div>
      </div>

      {/* 优化的里程碑数字标签 - 智能布局避免重叠 */}
      <div className="relative mt-8 h-12"> {/* 增加容器高度 */}
        {labelLayout.map((labelData) => {
          const { milestone, adjustedPosition, verticalOffset } = labelData;
          const isCompleted = completedTasks >= milestone;
          const isBigReward = milestone === maxMilestone;

          return (
            <div
              key={milestone}
              className="absolute transform -translate-x-1/2 transition-all duration-300"
              style={{ 
                left: `${Math.min(adjustedPosition, 100)}%`,
                top: `${verticalOffset}px` 
              }}
            >
              {/* 优化的数字标签 - 增加背景避免混淆 */}
              <div className={`px-2 py-1 rounded-md text-sm font-semibold transition-all duration-300 ${
                isCompleted
                  ? isBigReward
                    ? 'text-yellow-700 dark:text-yellow-300 bg-yellow-100/80 dark:bg-yellow-900/30 border border-yellow-300/50'
                    : 'text-green-700 dark:text-green-300 bg-green-100/80 dark:bg-green-900/30 border border-green-300/50'
                  : 'text-gray-600 dark:text-gray-400 bg-gray-100/80 dark:bg-gray-800/30 border border-gray-300/50'
              }`}>
                {milestone}
              </div>
            </div>
          );
        })}
      </div>

        {/* Big Reward Section */}
        {isBigRewardAvailable && (
          <div className={`bg-gradient-to-r ${bigRewardInfo.color} rounded-lg p-3 text-white mt-4 shadow-lg`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <bigRewardInfo.icon className="w-5 h-5 lg:w-5 lg:h-5 max-lg:w-3 max-lg:h-3" />
                <div>
                  <h4 className="text-sm font-bold">{bigRewardInfo.title}</h4>
                  <p className="text-xs opacity-90">{bigRewardInfo.reward}</p>
                </div>
              </div>
              <button
                onClick={() => onClaimReward?.(maxMilestone)}
                className="bg-white/20 hover:bg-white/30 px-3 py-1 rounded-full text-xs font-medium transition-colors"
              >
                {t('journey.progress.claim')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CumulativeProgressReward;
