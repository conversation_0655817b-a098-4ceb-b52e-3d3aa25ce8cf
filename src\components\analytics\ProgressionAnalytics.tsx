'use client';

import React from 'react';
import { Target, Trophy, Star, TrendingUp, Users, Calendar } from 'lucide-react';

interface ProgressionAnalyticsProps {
  lang: string;
}

const ProgressionAnalytics: React.FC<ProgressionAnalyticsProps> = ({ lang }) => {
  const progressionMetrics = [
    { title: 'Missions Completed', value: '8,923', change: '+23.1%', icon: Target, color: 'from-blue-500 to-indigo-600' },
    { title: 'Achievements Unlocked', value: '4,567', change: '+18.7%', icon: Trophy, color: 'from-yellow-500 to-orange-600' },
    { title: 'Avg User Level', value: '12.4', change: '+2.3%', icon: Star, color: 'from-purple-500 to-pink-600' },
    { title: 'Completion Rate', value: '67.8%', change: '+5.2%', icon: TrendingUp, color: 'from-green-500 to-emerald-600' }
  ];

  const missionCategories = [
    { category: 'Character Interaction', completed: 3456, total: 4200, percentage: 82.3 },
    { category: 'Story Creation', completed: 2134, total: 3000, percentage: 71.1 },
    { category: 'Store Purchases', completed: 1876, total: 2500, percentage: 75.0 },
    { category: 'Social Features', completed: 1457, total: 2200, percentage: 66.2 }
  ];

  return (
    <div className="space-y-8">
      {/* Progression Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {progressionMetrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${metric.color} rounded-lg flex items-center justify-center`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <span className="text-sm font-medium text-green-600 dark:text-green-400">{metric.change}</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">{metric.value}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">{metric.title}</p>
            </div>
          );
        })}
      </div>

      {/* Mission Categories */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
          <Target className="w-5 h-5 text-blue-500" />
          Mission Completion by Category
        </h3>
        <div className="space-y-6">
          {missionCategories.map((category, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-900 dark:text-gray-100">{category.category}</span>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {category.completed}/{category.total} ({category.percentage}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-blue-500 to-indigo-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${category.percentage}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* User Journey Funnel */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
          <Users className="w-5 h-5 text-purple-500" />
          User Journey Funnel
        </h3>
        <div className="space-y-4">
          {[
            { stage: 'Registration', users: 10000, percentage: 100 },
            { stage: 'First Character Interaction', users: 8500, percentage: 85 },
            { stage: 'First Purchase', users: 3200, percentage: 32 },
            { stage: 'Regular User (7+ days)', users: 4500, percentage: 45 },
            { stage: 'Power User (30+ days)', users: 2100, percentage: 21 }
          ].map((stage, index) => (
            <div key={index} className="flex items-center gap-4">
              <div className="w-32 text-sm font-medium text-gray-900 dark:text-gray-100">
                {stage.stage}
              </div>
              <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-6 relative">
                <div
                  className="bg-gradient-to-r from-purple-500 to-pink-500 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium transition-all duration-300"
                  style={{ width: `${stage.percentage}%` }}
                >
                  {stage.percentage}%
                </div>
              </div>
              <div className="w-20 text-sm text-gray-600 dark:text-gray-400 text-right">
                {stage.users.toLocaleString()}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProgressionAnalytics;
