'use client';

import React, { useState, useRef, useCallback } from 'react';
import Image from 'next/image';
import { X, Check, RotateCcw } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface AvatarCropperProps {
  characterImage: string;
  onCropComplete: (croppedImage: File, cropData: { x: number; y: number; width: number; height: number }) => void;
  onCancel: () => void;
  aspectRatio?: '1:1' | '5:6' | '5:8';
  cropType?: 'avatar' | 'characterImage';
  exportSize?: number; // 导出尺寸，用于头像固定为128*128
  onAspectRatioChange?: (ratio: '1:1' | '5:6' | '5:8') => void; // 新增：比例变化回调
  lang?: string;
}

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

const AvatarCropper: React.FC<AvatarCropperProps> = ({
  characterImage,
  onCropComplete,
  onCancel,
  aspectRatio = '1:1',
  cropType = 'avatar',
  exportSize,
  onAspectRatioChange,
  lang
}) => {
  const { t } = useTranslation();
  const imageRef = useRef<HTMLImageElement>(null);
  const [cropArea, setCropArea] = useState<CropArea>({ x: 50, y: 50, width: 200, height: 200 });
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState<string>('');
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imageLoaded, setImageLoaded] = useState(false);
  const [currentAspectRatio, setCurrentAspectRatio] = useState<'1:1' | '5:6' | '5:8'>(aspectRatio);

  // 获取宽高比
  const getAspectRatio = () => {
    switch (currentAspectRatio) {
      case '5:6': return 5 / 6;
      case '5:8': return 5 / 8;
      default: return 1; // 1:1
    }
  };

  // 处理比例变化
  const handleAspectRatioChange = (newRatio: '1:1' | '5:6' | '5:8') => {
    setCurrentAspectRatio(newRatio);
    onAspectRatioChange?.(newRatio);
    // 重新初始化裁剪区域以适应新比例
    setTimeout(initializeCropArea, 100);
  };

  // 初始化裁剪区域
  const initializeCropArea = useCallback(() => {
    if (!imageRef.current) return;
    
    const img = imageRef.current;
    const containerRect = img.getBoundingClientRect();
    const ratio = getAspectRatio();
    
    // 计算初始裁剪区域大小
    const maxSize = Math.min(containerRect.width, containerRect.height) * 0.6;
    const width = maxSize;
    const height = width / ratio;
    
    // 居中放置
    const x = (containerRect.width - width) / 2;
    const y = (containerRect.height - height) / 2;
    
    setCropArea({ x, y, width, height });
  }, [aspectRatio]);

  // 处理图片加载完成
  const handleImageLoad = () => {
    setImageLoaded(true);
    setTimeout(initializeCropArea, 100);
  };

  // 处理鼠标按下（移动）
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
    setDragStart({
      x: e.clientX - cropArea.x,
      y: e.clientY - cropArea.y
    });
  };

  // 处理调整手柄鼠标按下
  const handleResizeMouseDown = (e: React.MouseEvent, handle: string) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
    setResizeHandle(handle);
    setDragStart({
      x: e.clientX,
      y: e.clientY
    });
  };

  // 处理鼠标移动
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!imageRef.current) return;

    const img = imageRef.current;
    const containerRect = img.getBoundingClientRect();
    const ratio = getAspectRatio();

    if (isDragging) {
      // 移动裁剪区域
      const newX = Math.max(0, Math.min(e.clientX - dragStart.x, containerRect.width - cropArea.width));
      const newY = Math.max(0, Math.min(e.clientY - dragStart.y, containerRect.height - cropArea.height));
      setCropArea(prev => ({ ...prev, x: newX, y: newY }));
    } else if (isResizing) {
      // 调整裁剪区域大小
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;

      let newWidth = cropArea.width;
      let newHeight = cropArea.height;
      let newX = cropArea.x;
      let newY = cropArea.y;

      switch (resizeHandle) {
        case 'se': // 右下角
          newWidth = Math.max(50, cropArea.width + deltaX);
          newHeight = newWidth / ratio;
          break;
        case 'sw': // 左下角
          newWidth = Math.max(50, cropArea.width - deltaX);
          newHeight = newWidth / ratio;
          newX = cropArea.x + cropArea.width - newWidth;
          break;
        case 'ne': // 右上角
          newWidth = Math.max(50, cropArea.width + deltaX);
          newHeight = newWidth / ratio;
          newY = cropArea.y + cropArea.height - newHeight;
          break;
        case 'nw': // 左上角
          newWidth = Math.max(50, cropArea.width - deltaX);
          newHeight = newWidth / ratio;
          newX = cropArea.x + cropArea.width - newWidth;
          newY = cropArea.y + cropArea.height - newHeight;
          break;
      }

      // 确保不超出边界
      if (newX >= 0 && newY >= 0 &&
          newX + newWidth <= containerRect.width &&
          newY + newHeight <= containerRect.height) {
        setCropArea({ x: newX, y: newY, width: newWidth, height: newHeight });
      }
    }
  }, [isDragging, isResizing, dragStart, cropArea, resizeHandle, getAspectRatio]);

  // 处理鼠标释放
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
    setResizeHandle('');
  }, []);

  // 添加全局事件监听器
  React.useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  // 处理滚轮缩放
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (!imageRef.current) return;

    e.preventDefault();
    const img = imageRef.current;
    const containerRect = img.getBoundingClientRect();
    const ratio = getAspectRatio();

    // 计算缩放因子
    const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
    const newWidth = Math.max(50, Math.min(cropArea.width * scaleFactor, containerRect.width));
    const newHeight = newWidth / ratio;

    // 确保新尺寸不超出边界
    if (newHeight <= containerRect.height) {
      // 保持中心点不变
      const centerX = cropArea.x + cropArea.width / 2;
      const centerY = cropArea.y + cropArea.height / 2;
      const newX = Math.max(0, Math.min(centerX - newWidth / 2, containerRect.width - newWidth));
      const newY = Math.max(0, Math.min(centerY - newHeight / 2, containerRect.height - newHeight));

      setCropArea({ x: newX, y: newY, width: newWidth, height: newHeight });
    }
  }, [cropArea, getAspectRatio]);

  // 重置裁剪区域
  const resetCropArea = () => {
    initializeCropArea();
  };

  // 完成裁剪
  const handleCropConfirm = () => {
    if (!imageRef.current) return;

    const img = imageRef.current;
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) return;

    // 获取图片的实际尺寸和显示尺寸的比例
    const displayRect = img.getBoundingClientRect();
    const scaleX = img.naturalWidth / displayRect.width;
    const scaleY = img.naturalHeight / displayRect.height;

    // 计算实际裁剪区域
    const actualCropArea = {
      x: cropArea.x * scaleX,
      y: cropArea.y * scaleY,
      width: cropArea.width * scaleX,
      height: cropArea.height * scaleY
    };

    // 设置canvas尺寸
    if (exportSize && cropType === 'avatar') {
      // 头像固定导出为指定尺寸（如128*128）
      canvas.width = exportSize;
      canvas.height = exportSize;
    } else {
      // 立绘保持原始裁剪尺寸
      canvas.width = actualCropArea.width;
      canvas.height = actualCropArea.height;
    }

    // 创建一个新的图片元素用于裁剪
    const sourceImg = new window.Image();
    sourceImg.crossOrigin = 'anonymous';
    
    sourceImg.onload = () => {
      // 在canvas上绘制裁剪后的图片
      if (exportSize && cropType === 'avatar') {
        // 头像：裁剪并缩放到指定尺寸
        ctx.drawImage(
          sourceImg,
          actualCropArea.x, actualCropArea.y, actualCropArea.width, actualCropArea.height,
          0, 0, exportSize, exportSize
        );
      } else {
        // 立绘：保持原始裁剪尺寸
        ctx.drawImage(
          sourceImg,
          actualCropArea.x, actualCropArea.y, actualCropArea.width, actualCropArea.height,
          0, 0, actualCropArea.width, actualCropArea.height
        );
      }

      // 转换为blob并创建File对象
      canvas.toBlob((blob) => {
        if (blob) {
          const filename = cropType === 'avatar' ? 'avatar.png' : 'character.png';
          const file = new File([blob], filename, { type: 'image/png' });
          onCropComplete(file, {
            x: actualCropArea.x / img.naturalWidth,
            y: actualCropArea.y / img.naturalHeight,
            width: actualCropArea.width / img.naturalWidth,
            height: actualCropArea.height / img.naturalHeight
          });
        }
      }, 'image/png', 0.9);
    };

    sourceImg.src = characterImage;
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-4 w-full h-full max-w-6xl max-h-[95vh] flex flex-col">
        <div className="flex items-center justify-between mb-4 flex-shrink-0">
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
              {cropType === 'avatar' ? t('characterCreation.cropper.avatar.title') : t('characterCreation.cropper.avatar.character')}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('characterCreation.cropper.avatar.description', { 
                type: cropType === 'avatar' ? '头像' : '立绘', 
                ratio: currentAspectRatio 
              })}
            </p>
          </div>
          <button
            onClick={onCancel}
            className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-4 min-h-0">
          {/* 裁剪区域 */}
          <div className="lg:col-span-3 flex items-center justify-center">
            <div
              className="relative max-w-full max-h-full flex items-center justify-center"
              onWheel={handleWheel}
            >
              <Image
                ref={imageRef}
                src={characterImage}
                alt="Character Image"
                width={600}
                height={800}
                className="max-w-full max-h-[60vh] w-auto h-auto rounded-lg object-contain"
                onLoad={handleImageLoad}
                style={{ userSelect: 'none' }}
                draggable={false}
              />
              
              {/* 裁剪选择框 */}
              {imageLoaded && (
                <div
                  className={`absolute border-2 border-blue-500 bg-blue-500/20 cursor-move ${
                    cropType === 'avatar' ? 'rounded-full' : ''
                  }`}
                  style={{
                    left: `${cropArea.x}px`,
                    top: `${cropArea.y}px`,
                    width: `${cropArea.width}px`,
                    height: `${cropArea.height}px`,
                  }}
                  onMouseDown={handleMouseDown}
                >
                  {/* 四个角的调整手柄 */}
                  <div
                    className="absolute -top-1 -left-1 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-nw-resize hover:bg-blue-600 transition-colors"
                    onMouseDown={(e) => handleResizeMouseDown(e, 'nw')}
                  />
                  <div
                    className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-ne-resize hover:bg-blue-600 transition-colors"
                    onMouseDown={(e) => handleResizeMouseDown(e, 'ne')}
                  />
                  <div
                    className="absolute -bottom-1 -left-1 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-sw-resize hover:bg-blue-600 transition-colors"
                    onMouseDown={(e) => handleResizeMouseDown(e, 'sw')}
                  />
                  <div
                    className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 border-2 border-white rounded-full cursor-se-resize hover:bg-blue-600 transition-colors"
                    onMouseDown={(e) => handleResizeMouseDown(e, 'se')}
                  />
                  
                  {/* 中心移动指示 */}
                                      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                      <div className="text-white text-xs bg-blue-500/80 px-2 py-1 rounded">
                        {isResizing ? t('characterCreation.cropper.avatar.resizing') : t('characterCreation.cropper.avatar.dragToMoveText')}
                      </div>
                    </div>
                </div>
              )}
              
              {/* 遮罩层 */}
              {imageLoaded && (
                <div className="absolute inset-0 pointer-events-none">
                  {cropType === 'avatar' ? (
                    // 圆形遮罩 - 使用 radial-gradient 创建圆形透明区域
                    <div
                      className="absolute inset-0"
                      style={{
                        background: `radial-gradient(circle ${cropArea.width / 2}px at ${cropArea.x + cropArea.width / 2}px ${cropArea.y + cropArea.height / 2}px, transparent ${cropArea.width / 2}px, rgba(0,0,0,0.4) ${cropArea.width / 2 + 1}px)`
                      }}
                    />
                  ) : (
                    // 矩形遮罩
                    <div
                      className="absolute inset-0 bg-black/40"
                      style={{
                        clipPath: `polygon(0% 0%, 0% 100%, ${cropArea.x}px 100%, ${cropArea.x}px ${cropArea.y}px, ${cropArea.x + cropArea.width}px ${cropArea.y}px, ${cropArea.x + cropArea.width}px ${cropArea.y + cropArea.height}px, ${cropArea.x}px ${cropArea.y + cropArea.height}px, ${cropArea.x}px 100%, 100% 100%, 100% 0%)`
                      }}
                    />
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 预览和控制 */}
          <div className="space-y-4 overflow-y-auto">
            {/* 比例选择器 - 只在立绘裁剪时显示 */}
            {cropType === 'characterImage' && (
                              <div>
                  <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">
                    {t('characterCreation.cropper.avatar.selectAspectRatio')}
                  </h4>
                  <div className="space-y-2">
                    {[
                      { value: '5:6' as const, label: '5:6', desc: t('characterCreation.cropper.avatar.ratios.5:6') },
                      { value: '5:8' as const, label: '5:8', desc: t('characterCreation.cropper.avatar.ratios.5:8') },
                    ].map((ratio) => (
                    <button
                      key={ratio.value}
                      type="button"
                      onClick={() => handleAspectRatioChange(ratio.value)}
                      className={`w-full text-left p-3 rounded-lg border-2 transition-all ${
                        currentAspectRatio === ratio.value
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
                      }`}
                    >
                      <div className="font-medium text-gray-900 dark:text-white">
                        {ratio.label}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {ratio.desc}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            <div>
              <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">
                {cropType === 'avatar' ? t('characterCreation.cropper.avatar.avatarPreview') : t('characterCreation.cropper.avatar.characterPreview')}
              </h4>
              <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-4">
                {imageLoaded && (() => {
                  // 根据裁剪类型和比例计算预览尺寸
                  const getPreviewSize = () => {
                    if (cropType === 'avatar') {
                      // 头像预览，如果有exportSize则显示对应比例，否则默认128
                      const size = exportSize ? Math.min(exportSize, 128) : 128;
                      return { width: size, height: size };
                    } else {
                      // 立绘预览，根据比例计算
                      const maxWidth = 120;
                      const ratio = getAspectRatio();
                      const width = maxWidth;
                      const height = width / ratio;
                      return { width, height };
                    }
                  };
                  
                  const { width: previewWidth, height: previewHeight } = getPreviewSize();
                  
                  return (
                    <div
                      className={`relative mx-auto bg-white dark:bg-gray-600 overflow-hidden border-2 border-gray-200 dark:border-gray-500 ${
                        cropType === 'avatar' ? 'rounded-full' : 'rounded-xl'
                      }`}
                      style={{ width: `${previewWidth}px`, height: `${previewHeight}px` }}
                    >
                      <div
                        className="absolute"
                        style={{
                          left: `-${cropArea.x * (previewWidth / cropArea.width)}px`,
                          top: `-${cropArea.y * (previewHeight / cropArea.height)}px`,
                          width: `${imageRef.current ? (imageRef.current.getBoundingClientRect().width * (previewWidth / cropArea.width)) : 0}px`,
                          height: `${imageRef.current ? (imageRef.current.getBoundingClientRect().height * (previewHeight / cropArea.height)) : 0}px`,
                        }}
                      >
                        <Image
                          src={characterImage}
                          alt="Preview"
                          width={600}
                          height={800}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                  );
                })()}
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {cropType === 'avatar' ? t('characterCreation.cropper.avatar.avatarInfo') : t('characterCreation.cropper.avatar.characterRatio')}
                </label>
                <div className="text-center space-y-2">
                  <span className="inline-block px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 rounded-full text-sm">
                    比例: {currentAspectRatio}
                  </span>
                  <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                    <p>💡 {t('characterCreation.cropper.avatar.dragToMove')}</p>
                    <p>🔄 {t('characterCreation.cropper.avatar.dragCorners')}</p>
                    <p>🖱️ {t('characterCreation.cropper.avatar.mouseWheel')}</p>
                  </div>
                </div>
              </div>

              <button
                onClick={resetCropArea}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <RotateCcw size={16} />
                {t('characterCreation.cropper.avatar.resetArea')}
              </button>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0">
          <button
            onClick={onCancel}
            className="px-6 py-2 text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            {t('characterCreation.cropper.avatar.cancel')}
          </button>
          <button
            onClick={handleCropConfirm}
            className="px-6 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all shadow-lg flex items-center gap-2"
          >
            <Check size={16} />
            {t('characterCreation.cropper.avatar.confirmCrop')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AvatarCropper; 