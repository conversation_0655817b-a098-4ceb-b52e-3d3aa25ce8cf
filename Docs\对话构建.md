# AI对话构建流程

## 整体流程
```
用户消息 → 获取数据（检索知识库） → 检索记忆 → 6个并行(构建潜意识prompt → 得到潜意识结果)6个不同结果(选择？) → 构建Prompt → 调用LLM → 返回结果
```

## 1. 数据获取
从数据库获取：
- 角色信息 (Characters表)
- 故事线信息 (Stories表)
- 用户信息 (Users表)
- 历史消息 (Chat_Messages表)

## 2. 记忆检索 (RAG)
- 把用户消息转成向量
- 在Memory_Capsules表里搜索相关记忆
- 取最相关的3-5条记忆

## 3. System Prompt构建

### 基本结构
```
角色设定 + 故事背景 + 记忆 + 知识 + 用户信息 + 对话要求
```

### 具体内容
```
你是{角色名}，{角色描述}

## 背景设定
{角色背景设定}
当前故事：{故事标题和描述}

## 重要记忆
{从记忆胶囊检索到的内容}

## 用户信息
用户：{用户名}
{用户简介}

## 要求
- 保持角色性格一致
- 记住之前的对话内容
- 回复自然流畅
```

## 4. History Messages构建

```json
[
  {"role": "system", "content": "上面的System Prompt"},
  {"role": "system", "content": "历史消息总结"}
  {"role": "assistant", "content": "角色开场白"},
  {"role": "user", "content": "历史消息1"},
  {"role": "assistant", "content": "历史回复1"},
  {"role": "user", "content": "当前用户消息"}
]
```

### History管理
- 保留最近20轮对话
- 超过token限制就总结完塞在system之后
- 确保用户-AI消息成对出现

就这么简单，核心就是把角色、故事、记忆、用户信息拼成一个完整的prompt给LLM。