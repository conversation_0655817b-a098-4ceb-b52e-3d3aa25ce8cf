# 技术架构详情

本文档用于记录 aruai.app 项目所选用的各项基础设施和关键技术组件信息。

## 1. 核心技术栈

根据项目初期规划：

*   **前端框架:** React Native (P0 选型)
*   **后端语言与框架:** GO + TS (P0 选型)
*   **数据库:** GCP上的PostgreSQL (P0 选型)
*   **对象存储:** Google Cloud Storage (GCS) (P0 选型)
*   **部署方案:** CI/CD (具体工具 P0 选型)
*   **云服务商:** Google Cloud Platform (GCP) (P0 选型)

## 2. 核心第三方 AI 服务

根据项目初期规划：

*   **大语言模型 (LLM):** (P0 选型)
*   **文本内容审核:** (P0 选型)

## 3. 其他关键组件

根据项目规划，后续阶段可能引入：

*   **文本转语音 (TTS):** (P1 - Phase 2 开始时选型)
*   **语音转文本 (STT):** (P1 - Phase 2 开始时选型)
*   **图像生成 API/模型:** (P1 - Phase 2 开始时选型)
*   **向量数据库:** PGVector / Milvus 等 (P1 - Phase 2 开始时选型)
*   **实时通信方案:** WebSocket / Server-Sent Events (SSE) (P1 - Phase 2 开始时选型)
*   **消息队列和异步任务处理:** (P1 - Phase 2 开始时选型)

## 4. 数据库表设计概要 (功能分类与优先级)

本数据库表设计基于对 API 文档和项目待办事项的详细分析。优先级主要依据 API 文档中各接口的APP端优先级（P0, P1, P2, P3）以及项目中相关功能的开发阶段确定。

**通用约定:**
*   所有表（除非特别说明）将包含 `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP) 和 `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP) 审计时间戳字段。 (注意: PostgreSQL 的 `ON UPDATE CURRENT_TIMESTAMP` 需要通过触发器实现)
*   主键 (PK) 通常为 `_id` (UUID 类型，除非特别说明)。
*   外键 (FK) 会明确指出关联的表和字段，并建议明确 `ON DELETE` 和 `ON UPDATE` 的行为。
*   JSON类型的字段后缀为 `_jsonb`，使用 `JSONB` 类型。
*   文件路径通常指在对象存储 (如 GCS) 中的路径，一般为 VARCHAR(512) 或 TEXT。
*   所有 VARCHAR 类型的字段应根据实际需求设定合理的长度。
*   所有表示状态或类型的字段，应使用 `CHECK` 约束或 PostgreSQL 的 `ENUM` 类型来限制取值范围。

### 4.1 核心用户与认证 (P0-P1)
*   **`Users`** (用户信息表)
    *   `_id` (PK, UUID)
    *   `name` (VARCHAR(50), NOT NULL, 用户昵称)
    *   `uid` (VARCHAR(30), UNIQUE, NOT NULL, 用户自定义唯一ID)
    *   `email` (VARCHAR(255), UNIQUE, Nullable)
    *   `phone` (VARCHAR(30), UNIQUE, Nullable)
    *   `password_hash` (VARCHAR(255), Nullable, 用于密码登录)
    *   `auth_provider` (VARCHAR(20), NOT NULL, CHECK (`auth_provider` IN ('password', 'otp', 'google')), e.g., "password", "otp", "google")
    *   `google_id` (VARCHAR(255), Nullable, UNIQUE, 用于Google登录)
    *   `invitor_id` (FK to `Users._id` ON DELETE SET NULL, Nullable)
    *   `invitation_code` (VARCHAR(30), UNIQUE, Nullable, 用户自己的邀请码)
    *   `is_alphane_pass_member` (Boolean, DEFAULT false, 小月卡)
    *   `alphane_pass_expires_at` (Timestamp, Nullable)
    *   `is_alphane_diamond_member` (Boolean, DEFAULT false, 大月卡)
    *   `alphane_diamond_expires_at` (Timestamp, Nullable)
    *   `next_alphane_pass_reward_at` (Timestamp, Nullable)
    *   `next_alphane_diamond_reward_at` (Timestamp, Nullable)
    *   `sign` (VARCHAR(255), Nullable, 个性签名)
    *   `gender` (VARCHAR(15), CHECK (`gender` IN ('male', 'female', 'other', 'not_specified')), DEFAULT 'not_specified')
    *   `followed_character_count` (Integer, DEFAULT 0, 关注角色数)
    *   `following_user_count` (Integer, DEFAULT 0, 关注用户数)
    *   `follower_user_count` (Integer, DEFAULT 0, 粉丝数)
    *   `created_character_count` (Integer, DEFAULT 0, 创建角色数)
    *   `created_story_count` (Integer, DEFAULT 0, 创建故事线数)
    *   `avatar_image_version` (Integer, DEFAULT 0, 头像版本)
    *   `avatar_image_path` (VARCHAR(512), Nullable)
    *   `current_avatar_frame_id` (FK to `User_Owned_Avatar_Frames._id` ON DELETE SET NULL, Nullable)
    *   `is_verified_creator` (Boolean, DEFAULT false)
    *   `bio` (Text, Nullable, 个人简介/全局人设)
    *   `alphane_balance` (Numeric, DEFAULT 0)
    *   `endora_balance` (Numeric, DEFAULT 0, 心悦晶石/星钻余额)
    *   `serotile_balance` (Numeric, DEFAULT 0, Serotile 忆境拼图碎片余额)
    *   `oxytol_balance` (Numeric, DEFAULT 0, Oxytol 羁绊之露余额)
    *   `current_streak_days` (Integer, DEFAULT 0)
    *   `streak_freeze_cards_balance` (Integer, DEFAULT 0)
    *   `last_interaction_date` (Date, Nullable)
    *   `streak_repair_chances_balance` (Integer, DEFAULT 0)
    *   `daily_fast_req_remaining` (Integer, DEFAULT 0)
    *   `daily_fast_req_total` (Integer, DEFAULT 10) /* 示例默认值 */
    *   `daily_slow_req_remaining` (Integer, DEFAULT 0)
    *   `daily_slow_req_total` (Integer, DEFAULT 50) /* 示例默认值 */
    *   `last_daily_quota_reset_at` (Timestamp, Nullable)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('active', 'suspended', 'deleted', 'pending_verification')), DEFAULT 'pending_verification')
    *   `last_login_at` (Timestamp, Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`OTPs`** (一次性验证码表, P0)
    *   `_id` (PK, BIGSERIAL)
    *   `target_contact` (VARCHAR(255), NOT NULL, 邮箱或手机号)
    *   `contact_type` (VARCHAR(10), NOT NULL, CHECK (`contact_type` IN ('email', 'phone')))
    *   `otp_code` (VARCHAR(255), NOT NULL, 哈希存储)
    *   `type` (VARCHAR(30), NOT NULL, CHECK (`type` IN ('signin', 'recovery', 'change_email_old', 'change_email_new')))
    *   `expires_at` (Timestamp, NOT NULL)
    *   `is_used` (Boolean, DEFAULT false)
    *   `attempts_count` (Integer, DEFAULT 0)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Email_Change_Requests`** (邮箱更换请求记录, P2)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `old_email` (VARCHAR(255), NOT NULL)
    *   `new_email` (VARCHAR(255), NOT NULL)
    *   `old_email_otp_id` (FK to `OTPs._id` ON DELETE SET NULL, NOT NULL)
    *   `new_email_otp_id` (FK to `OTPs._id` ON DELETE SET NULL, Nullable)
    *   `status` (VARCHAR(30), NOT NULL, CHECK (`status` IN ('initiated', 'new_email_pending_verification', 'completed', 'failed', 'expired')))
    *   `expires_at` (Timestamp, NOT NULL)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Refresh_Tokens`** (JWT刷新令牌, P0)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `token_hash` (VARCHAR(255), UNIQUE, NOT NULL)
    *   `expires_at` (Timestamp, NOT NULL)
    *   `issued_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `is_revoked` (Boolean, DEFAULT false)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

### 4.2 用户账户与社交 (P1-P2)
*   **`User_Transactions`** (用户交易记录表, P1-P2)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `amount` (Numeric, NOT NULL, 正为收入，负为支出)
    *   `currency_type` (VARCHAR(20), NOT NULL, CHECK (`currency_type` IN ('alphane_dust', 'endora_crystal', 'serotile_fragment', 'oxytol_dew', 'battle_pass_xp')))
    *   `transaction_type` (VARCHAR(50), NOT NULL, e.g., "daily_login_reward", "gacha_cost", "image_generation_cost", "subscription_purchase", "task_reward", "streak_milestone_reward")
    *   `description` (VARCHAR(255), Nullable, 交易描述)
    *   `related_entity_type` (VARCHAR(50), Nullable, e.g., "image_generation_task", "subscription_plan", "gacha_event", "task", "achievement")
    *   `related_entity_id` (VARCHAR(255), Nullable)
    *   `timestamp` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP, 交易时间)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Follows`** (用户互相关注表, P2)
    *   `_id` (PK, UUID)
    *   `follower_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `following_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   UNIQUE (`follower_user_id`, `following_user_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`App_Subscription_Plans`** (应用内订阅计划定义表, P1)
    *   `plan_id_pk` (VARCHAR(50), PK, e.g., "alphane_pass_monthly", "alphane_diamond_yearly")
    *   `name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `price` (Numeric, NOT NULL)
    *   `currency` (VARCHAR(10), NOT NULL)
    *   `billing_interval` (VARCHAR(20), NOT NULL, CHECK (`billing_interval` IN ('month', 'year', 'week')))
    *   `features_jsonb` (JSONB, Nullable, 描述计划包含的特性)
    *   `is_active` (Boolean, DEFAULT true)
    *   `display_order` (Integer, DEFAULT 0)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_App_Subscriptions`** (用户应用内订阅记录表, P1)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `plan_id` (FK to `App_Subscription_Plans.plan_id_pk` ON DELETE RESTRICT, NOT NULL)
    *   `payment_transaction_id` (FK to `Payment_Transactions._id` ON DELETE SET NULL, Nullable)
    *   `start_date` (Timestamp, NOT NULL)
    *   `end_date` (Timestamp, NOT NULL)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('active', 'expired', 'cancelled', 'payment_failed', 'pending')))
    *   `auto_renew` (Boolean, DEFAULT true)
    *   `platform` (VARCHAR(20), Nullable, CHECK (`platform` IN ('apple', 'google', 'stripe', 'other'))) /* 记录购买平台 */
    *   `platform_subscription_id` (VARCHAR(255), Nullable) /* 平台订阅ID */
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Blocks`** (用户屏蔽列表, P2)
    *   `_id` (PK, UUID)
    *   `blocker_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `blocked_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   UNIQUE (`blocker_user_id`, `blocked_user_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Badges`** (徽章定义表, P1)
    *   `badge_id_pk` (VARCHAR(50), PK, e.g., "first_greet_badge", "streak_30_days")
    *   `name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `icon_url` (VARCHAR(512), Nullable)
    *   `rarity` (VARCHAR(20), CHECK (`rarity` IN ('common', 'rare', 'epic', 'legendary', 'mythic')))
    *   `is_active` (Boolean, DEFAULT true)
    *   `category` (VARCHAR(50), Nullable, e.g., "streak", "creation", "event")
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Owned_Badges`** (用户拥有徽章表, P1) /* 替代 User_Achievements 中的 badge_reward_id，更直接 */
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `badge_id` (FK to `Badges.badge_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `owned_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `source_description` (VARCHAR(255), Nullable, e.g., "Achievement: First Chat", "Event: Anniversary")
    *   UNIQUE (`user_id`, `badge_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Displayed_Badges`** (用户展示徽章表, P1)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `user_owned_badge_id` (FK to `User_Owned_Badges._id` ON DELETE CASCADE, NOT NULL)
    *   `display_order` (Integer, NOT NULL, DEFAULT 0)
    *   UNIQUE (`user_id`, `display_order`)
    *   UNIQUE (`user_id`, `user_owned_badge_id`) /* 一个徽章只能展示一次 */
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Avatar_Frames`** (头像框定义表, P1)
    *   `frame_id_pk` (VARCHAR(50), PK, e.g., "frame_default", "frame_vip_gold")
    *   `name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `image_url` (VARCHAR(512), NOT NULL)
    *   `category` (VARCHAR(50), Nullable, e.g., "event", "vip", "achievement", "gacha", "system_default")
    *   `rarity` (VARCHAR(20), CHECK (`rarity` IN ('N', 'R', 'SR', 'SSR', 'UR')))
    *   `source_description` (VARCHAR(255), Nullable, 获取途径描述)
    *   `unlock_condition_text` (Text, Nullable, 解锁条件文字描述)
    *   `price_alphane_dust` (Integer, Nullable)
    *   `price_endora_crystal` (Integer, Nullable)
    *   `is_time_limited` (Boolean, DEFAULT false)
    *   `available_until` (Timestamp, Nullable)
    *   `tags_jsonb` (JSONB, Nullable)
    *   `is_active` (Boolean, DEFAULT true)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Owned_Avatar_Frames`** (用户拥有头像框表, P1)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `frame_id` (FK to `Avatar_Frames.frame_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `owned_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `source_description` (VARCHAR(255), Nullable, e.g., "Purchased", "Reward: VIP Level 3")
    *   UNIQUE (`user_id`, `frame_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Achievements`** (成就定义表, P1)
    *   `achievement_id_pk` (VARCHAR(50), PK, e.g., "complete_first_chat", "create_3_characters")
    *   `name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `unlock_criteria_description` (Text, Nullable, 解锁条件文字描述)
    *   `unlock_logic_key` (VARCHAR(100), Nullable, UNIQUE, 用于程序判断的解锁逻辑标识)
    *   `badge_reward_id` (FK to `Badges.badge_id_pk` ON DELETE SET NULL, Nullable, 完成此成就授予的徽章)
    *   `avatar_frame_reward_id` (FK to `Avatar_Frames.frame_id_pk` ON DELETE SET NULL, Nullable)
    *   `alphane_dust_reward` (Integer, DEFAULT 0)
    *   `endora_crystal_reward` (Integer, DEFAULT 0)
    *   `other_rewards_jsonb` (JSONB, Nullable, 其他类型奖励，如特定物品ID和数量)
    *   `is_repeatable` (Boolean, DEFAULT false)
    *   `is_active` (Boolean, DEFAULT true)
    *   `category` (VARCHAR(50), Nullable, e.g., "chat", "creation", "social")
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Achievements`** (用户成就达成记录表, P1)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `achievement_id` (FK to `Achievements.achievement_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `unlocked_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `is_reward_claimed` (Boolean, DEFAULT false)
    *   `claimed_at` (Timestamp, Nullable)
    *   `progress_jsonb` (JSONB, Nullable, 对于有进度的成就，记录当前进度)
    *   UNIQUE (`user_id`, `achievement_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Streak_Milestone_Rewards`** (连续互动里程碑奖励定义表, P0)
    *   `milestone_days_pk` (Integer, PK, 里程碑天数, e.g., 3, 7, 15, 30)
    *   `description` (VARCHAR(255), Nullable)
    *   `rewards_jsonb` (JSONB, NOT NULL, 奖励列表, e.g., `[{"type": "alphane_dust", "amount": 100}, {"type": "item", "item_id": "streak_badge_30d"}]`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Claimed_Streak_Rewards`** (用户已领取的连续互动里程碑奖励记录表, P0)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `milestone_days` (FK to `Streak_Milestone_Rewards.milestone_days_pk` ON DELETE CASCADE, NOT NULL)
    *   `claimed_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   UNIQUE (`user_id`, `milestone_days`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Battle_Pass_Seasons`** (战令赛季信息表, P1)
    *   `season_id_pk` (VARCHAR(50), PK, e.g., "S202505")
    *   `season_name` (VARCHAR(100), NOT NULL)
    *   `start_time_utc` (Timestamp, NOT NULL)
    *   `end_time_utc` (Timestamp, NOT NULL)
    *   `max_level` (Integer, NOT NULL)
    *   `xp_per_level_jsonb` (JSONB, Nullable, 定义每级所需经验的曲线, e.g. `{"1": 100, "2": 120}` or a formula key)
    *   `premium_track_store_product_sku` (FK to `Store_Products.product_sku_pk` ON DELETE SET NULL, Nullable, 解锁付费轨道的商品ID)
    *   `is_active_season` (Boolean, DEFAULT false)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Battle_Pass_Tracks`** (战令轨道与等级奖励表, P1)
    *   `_id` (PK, UUID)
    *   `season_id` (FK to `Battle_Pass_Seasons.season_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `level_number` (Integer, NOT NULL)
    *   `track_type` (VARCHAR(10), NOT NULL, CHECK (`track_type` IN ('free', 'premium')))
    *   `reward_sequence_in_level` (Integer, DEFAULT 1, 同一等级同一轨道的第几个奖励)
    *   `reward_item_type` (VARCHAR(50), NOT NULL, e.g., "alphane_dust", "endora_crystal", "badge_id", "avatar_frame_id", "battle_pass_coin")
    *   `reward_item_id_or_value` (VARCHAR(255), NOT NULL, 奖励的具体ID或数量)
    *   `reward_item_name_display` (VARCHAR(100), Nullable)
    *   `reward_item_icon_url` (VARCHAR(512), Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Battle_Pass_Progress`** (用户战令进度表, P1)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `season_id` (FK to `Battle_Pass_Seasons.season_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `current_level` (Integer, DEFAULT 0)
    *   `current_xp` (Integer, DEFAULT 0)
    *   `is_premium_track_unlocked` (Boolean, DEFAULT false)
    *   `claimed_rewards_jsonb` (JSONB, Nullable, 记录已领取的等级奖励, e.g., `{"free_1": true, "paid_1": true}`)
    *   `battle_pass_coin_balance` (Integer, DEFAULT 0) /* 战令币余额 */
    *   UNIQUE (`user_id`, `season_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Tasks`** (任务定义表, P0-P1)
    *   `task_id_pk` (VARCHAR(50), PK, e.g., "daily_chat_3_chars", "weekly_create_story")
    *   `title` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `task_type` (VARCHAR(20), NOT NULL, CHECK (`task_type` IN ('daily', 'weekly', 'monthly', 'event', 'achievement_linked', 'beginner')))
    *   `reset_frequency_cron` (VARCHAR(50), Nullable, 对于周期性任务的重置CRON表达式)
    *   `target_action_key` (VARCHAR(100), Nullable, 程序判断任务完成的关键行为标识)
    *   `target_count` (Integer, DEFAULT 1, 完成任务所需次数)
    *   `rewards_jsonb` (JSONB, Nullable, 奖励列表, e.g., `[{"type": "alphane_dust", "amount": 20}, {"type": "battle_pass_xp", "amount": 10}]`)
    *   `is_auto_claim_reward` (Boolean, DEFAULT false, 完成后是否自动发放奖励)
    *   `is_active` (Boolean, DEFAULT true)
    *   `unlock_condition_jsonb` (JSONB, Nullable, 任务解锁条件，如用户等级)
    *   `display_order` (Integer, DEFAULT 0)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Task_Progress`** (用户任务进度与状态表, P0-P1)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `task_id` (FK to `Tasks.task_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `task_instance_key` (VARCHAR(100), NOT NULL, DEFAULT 'default', 若任务可重复但有不同实例, e.g. daily task for "2025-05-17")
    *   `current_progress_count` (Integer, DEFAULT 0)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('active', 'completed', 'reward_claimed', 'expired', 'locked')))
    *   `last_progress_at` (Timestamp, Nullable)
    *   `expires_at` (Timestamp, Nullable, 针对有时限的任务)
    *   UNIQUE (`user_id`, `task_id`, `task_instance_key`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Invitations`** (用户邀请关系表, P1)
    *   `_id` (PK, UUID)
    *   `invitor_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `invited_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL, UNIQUE) /* 被邀请者只能有一个邀请人 */
    *   `invited_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('pending_activation', 'activated', 'reward_eligible')), DEFAULT 'pending_activation')
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Invitation_Rewards`** (用户邀请奖励记录表, P1)
    *   `_id` (PK, UUID)
    *   `invitation_id` (FK to `User_Invitations._id` ON DELETE CASCADE, NOT NULL)
    *   `reward_type` (VARCHAR(50), NOT NULL, e.g., "alphane_dust_for_invitor", "pass_trial_for_invited")
    *   `reward_details_jsonb` (JSONB, NOT NULL, e.g., `{"currency": "alphane_dust", "amount": 50}` or `{"pass_type": "alphane_pass", "trial_days": 7}`)
    *   `granted_to_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL) /* 奖励发放给邀请者还是被邀请者 */
    *   `granted_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

### 4.3 AI 角色 (Character) (P0-P1)
*   **`Characters`** (AI角色信息表)
    *   `_id` (PK, UUID, `character_id`)
    *   `creator_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `name` (VARCHAR(100), NOT NULL, 角色名)
    *   `description` (Text, Nullable, 角色描述)
    *   `setting` (Text, Nullable, 角色世界观、背景设定)
    *   `greeting_message` (Text, Nullable, 角色问候语)
    *   `initial_memories_text` (Text, Nullable, 初始记忆文本)
    *   `custom_prompt_prefix` (Text, Nullable, 自定义Prompt前缀)
    *   `gender` (VARCHAR(15), CHECK (`gender` IN ('male', 'female', 'other', 'not_specified')), DEFAULT 'not_specified')
    *   `visibility` (VARCHAR(15), NOT NULL, CHECK (`visibility` IN ('public', 'unlisted', 'private')), DEFAULT 'public')
    *   `avatar_image_path` (VARCHAR(512), Nullable, 角色头像文件路径)
    *   `profile_image_path` (VARCHAR(512), Nullable, 角色立绘文件路径)
    *   `image_version` (Integer, DEFAULT 0, 用于头像/立绘缓存刷新)
    *   `background_audio_url` (VARCHAR(512), Nullable, 背景音乐/音效URL)
    *   `follower_count` (Integer, DEFAULT 0, 粉丝数)
    *   `chat_count` (Integer, DEFAULT 0, 聊天次数/热度)
    *   `like_count` (Integer, DEFAULT 0) /* 新增，如果角色本身可以被点赞 */
    *   `comment_count` (Integer, DEFAULT 0)
    *   `status` (VARCHAR(30), NOT NULL, CHECK (`status` IN ('active', 'draft', 'archived', 'reported_pending_review', 'rejected')), DEFAULT 'draft')
    *   `version` (Integer, DEFAULT 1)
    *   `is_official` (Boolean, DEFAULT false)
    *   `is_featured` (Boolean, DEFAULT false)
    *   `last_updated_by_user_id` (FK to `Users._id` ON DELETE SET NULL, Nullable)
    *   `published_at` (Timestamp, Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Character_Opening_Message_Templates`** (角色开场白模板表, P1)
    *   `_id` (PK, UUID)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `template_id_custom` (VARCHAR(50), NOT NULL, 用户或系统生成的唯一标识, e.g., "greeting_1", "scenario_A_opener")
    *   `content` (Text, NOT NULL)
    *   `priority` (Integer, DEFAULT 0, Nullable)
    *   `conditions_jsonb` (JSONB, Nullable, 存储触发条件, e.g., `{"user_relationship": "known"}`)
    *   UNIQUE (`character_id`, `template_id_custom`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Character_Personality_Tags`** (角色性格标签表, P1)
    *   `_id` (PK, UUID)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `tag_name` (VARCHAR(50), NOT NULL)
    *   `weight` (Numeric, NOT NULL, CHECK (`weight` >= 0 AND `weight` <= 1))
    *   UNIQUE (`character_id`, `tag_name`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Character_Voice_Mappings`** (角色语音配置表, P1-P2)
    *   `_id` (PK, UUID)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `voice_id` (FK to `Voices.voice_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `priority_order` (Integer, DEFAULT 0)
    *   UNIQUE (`character_id`, `voice_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Character_Knowledge_Files`** (角色知识库文件表, P1-P2)
    *   `_id` (PK, UUID)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `file_name` (VARCHAR(255), NOT NULL)
    *   `file_type` (VARCHAR(20), NOT NULL, e.g., "txt", "pdf", "md", "docx")
    *   `file_path` (VARCHAR(512), NOT NULL, GCS路径)
    *   `file_size_kb` (Integer, Nullable)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('active', 'processing', 'error', 'pending_deletion')), DEFAULT 'active')
    *   `vector_db_status` (VARCHAR(20), CHECK (`vector_db_status` IN ('pending', 'indexed', 'error', 'stale')), DEFAULT 'pending')
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Character_Sample_Dialogues`** (角色对话示例表, P1-P2)
    *   `_id` (PK, UUID)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `dialogue_order` (Integer, NOT NULL, DEFAULT 0)
    *   `role` (VARCHAR(10), NOT NULL, CHECK (`role` IN ('user', 'ai')))
    *   `text_content` (Text, NOT NULL)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Character_Follows`** (用户关注角色表, P1-P2)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   UNIQUE (`user_id`, `character_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Character_Reports`** (角色举报记录表, P2)
    *   `_id` (PK, UUID)
    *   `reporter_user_id` (FK to `Users._id` ON DELETE SET NULL, Nullable) /* 举报者删除账号，举报依然有效 */
    *   `reported_character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `reason_type` (VARCHAR(50), NOT NULL, e.g., "spam", "inappropriate_content", "impersonation")
    *   `reason_details` (Text, Nullable)
    *   `status` (VARCHAR(30), NOT NULL, CHECK (`status` IN ('pending_review', 'action_taken', 'dismissed')), DEFAULT 'pending_review')
    *   `admin_notes` (Text, Nullable)
    *   `reviewed_by_admin_id` (FK to `Users._id` ON DELETE SET NULL, Nullable) /* 假设管理员也是用户 */
    *   `reviewed_at` (Timestamp, Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Character_Templates`** (角色模板定义表, P1)
    *   `template_id_pk` (VARCHAR(50), PK, e.g., "official_knight_tpl", "community_elf_tpl")
    *   `name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `creator_user_id` (FK to `Users._id` ON DELETE SET NULL, Nullable, 如果是用户贡献的模板)
    *   `creator_name_display` (VARCHAR(50), Nullable, 创建者显示名称, e.g., "官方团队", "UserX")
    *   `tags_jsonb` (JSONB, Nullable, 模板标签)
    *   `usage_count` (Integer, DEFAULT 0)
    *   `preview_image_url` (VARCHAR(512), Nullable)
    *   `base_character_profile_jsonb` (JSONB, NOT NULL, 包含模板预设的角色核心字段)
    *   `is_public` (Boolean, DEFAULT true)
    *   `is_official` (Boolean, DEFAULT false)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('active', 'pending_review', 'rejected')), DEFAULT 'pending_review')
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Saved_Character_Templates`** (用户基于角色创建的模板记录, P2)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL, 模板创建者)
    *   `source_character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL, 源角色)
    *   `template_id` (FK to `Character_Templates.template_id_pk` ON DELETE CASCADE, NOT NULL, 创建成功后关联的模板ID)
    *   `custom_template_name` (VARCHAR(100), Nullable)
    *   `custom_template_description` (Text, Nullable)
    *   `is_made_public` (Boolean, DEFAULT false, 用户是否选择将此保存的模板公开)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Character_Bonds`** (用户与角色羁绊进度表, P1)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `current_bond_level` (Integer, DEFAULT 0)
    *   `current_bond_exp` (Integer, DEFAULT 0)
    *   `last_gifted_at` (Timestamp, Nullable)
    *   `last_interacted_at` (Timestamp, Nullable, 用于计算羁绊衰减或活跃度)
    *   UNIQUE (`user_id`, `character_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Bond_Levels_Definitions`** (羁绊等级定义表, P1)
    *   `level_number_pk` (Integer, PK, 羁绊等级)
    *   `level_name` (VARCHAR(50), NOT NULL, 等级名称, e.g., "初识", "信赖", "亲密无间")
    *   `exp_required_to_reach` (Integer, NOT NULL, 达到此等级所需的总经验)
    *   `description` (Text, Nullable, 等级描述)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Bond_Level_Rewards_Definitions`** (羁绊等级奖励定义表, P1)
    *   `reward_id_pk` (VARCHAR(50), PK)
    *   `bond_level_required` (FK to `Bond_Levels_Definitions.level_number_pk` ON DELETE CASCADE, NOT NULL)
    *   `reward_name` (VARCHAR(100), NOT NULL)
    *   `reward_description` (Text, Nullable)
    *   `reward_type` (VARCHAR(50), NOT NULL, e.g., "voice_unlock", "story_unlock", "item_grant", "avatar_frame_id", "alphane_dust")
    *   `reward_details_jsonb` (JSONB, NOT NULL, 包含奖励的具体内容)
    *   `is_active_reward` (Boolean, DEFAULT true)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Claimed_Bond_Rewards`** (用户已领取的羁绊奖励记录表, P1)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `bond_reward_id` (FK to `Bond_Level_Rewards_Definitions.reward_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `claimed_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   UNIQUE (`user_id`, `character_id`, `bond_reward_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Gifts_Definitions`** (礼物定义表, P1)
    *   `gift_id_pk` (VARCHAR(50), PK)
    *   `name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `icon_url` (VARCHAR(512), Nullable)
    *   `bond_exp_increase` (Integer, NOT NULL, DEFAULT 0)
    *   `cost_alphane_dust` (Integer, Nullable)
    *   `cost_endora_crystal` (Integer, Nullable)
    *   `is_purchasable` (Boolean, DEFAULT true)
    *   `availability_logic_jsonb` (JSONB, Nullable, 如特定活动期间可用)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Character_Gift_Log`** (用户向角色赠送礼物记录表, P1)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `gift_id` (FK to `Gifts_Definitions.gift_id_pk` ON DELETE RESTRICT, NOT NULL)
    *   `quantity_gifted` (Integer, DEFAULT 1)
    *   `gifted_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `bond_exp_gained_from_gift` (Integer, NOT NULL)
    *   `triggered_special_dialogue_id` (VARCHAR(100), Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

### 4.4 故事线 (Story) (P0-P2)
*   **`Stories`** (故事线信息表)
    *   `_id` (PK, UUID, `story_id`)
    *   `creator_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `associated_character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `title` (VARCHAR(150), NOT NULL, 故事线标题)
    *   `description` (Text, Nullable, 故事线描述)
    *   `opening_message` (Text, Nullable, 故事线开场白)
    *   `cover_image_path` (VARCHAR(512), Nullable, 封面图片路径)
    *   `cover_image_version` (Integer, DEFAULT 0)
    *   `cover_image_width` (Integer, Nullable)
    *   `cover_image_height` (Integer, Nullable)
    *   `background_music_url` (VARCHAR(512), Nullable)
    *   `specific_setting_text` (Text, Nullable, 故事线特定附加设定)
    *   `is_private` (Boolean, DEFAULT false)
    *   `chat_count` (Integer, DEFAULT 0, 热度/聊天次数)
    *   `like_count` (Integer, DEFAULT 0, 点赞数)
    *   `comment_count` (Integer, DEFAULT 0)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('active', 'draft', 'archived', 'reported_pending_review')), DEFAULT 'draft')
    *   `published_at` (Timestamp, Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Story_User_Personas`** (故事线特定用户人设表, P2-P3)
    *   `_id` (PK, UUID)
    *   `story_id` (FK to `Stories._id` ON DELETE CASCADE, NOT NULL)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `persona_name` (VARCHAR(100), Nullable, 人设名称)
    *   `persona_bio` (Text, Nullable, 人设描述/简介)
    *   UNIQUE (`story_id`, `user_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Story_Likes`** (故事线点赞表, P2)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `story_id` (FK to `Stories._id` ON DELETE CASCADE, NOT NULL)
    *   UNIQUE (`user_id`, `story_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Story_Reports`** (故事线举报记录表, P2)
    *   `_id` (PK, UUID)
    *   `reporter_user_id` (FK to `Users._id` ON DELETE SET NULL, Nullable)
    *   `reported_story_id` (FK to `Stories._id` ON DELETE CASCADE, NOT NULL)
    *   `reason_type` (VARCHAR(50), NOT NULL)
    *   `reason_details` (Text, Nullable)
    *   `status` (VARCHAR(30), NOT NULL, CHECK (`status` IN ('pending_review', 'action_taken', 'dismissed')), DEFAULT 'pending_review')
    *   `admin_notes` (Text, Nullable)
    *   `reviewed_by_admin_id` (FK to `Users._id` ON DELETE SET NULL, Nullable)
    *   `reviewed_at` (Timestamp, Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

### 4.5 聊天与会话 (Chat & Session) (P0-P1)
*   **`Chat_Sessions`** (聊天会话元数据表, P0)
    *   `_id` (PK, UUID, `session_id`)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `story_id` (FK to `Stories._id` ON DELETE CASCADE, NOT NULL)
    *   `character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `session_title` (VARCHAR(150), Nullable, 用户可自定义或系统生成)
    *   `last_message_at` (Timestamp, Nullable)
    *   `last_message_snippet` (VARCHAR(200), Nullable) /* 新增，用于列表展示 */
    *   `is_pinned_by_user` (Boolean, DEFAULT false)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('active', 'archived', 'ended')), DEFAULT 'active')
    *   `unread_message_count_for_user` (Integer, DEFAULT 0) /* 新增 */
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Chat_Messages`** (聊天消息记录表, P0)
    *   `_id` (PK, UUID, `message_id`)
    *   `session_id` (FK to `Chat_Sessions._id` ON DELETE CASCADE, NOT NULL)
    *   `round_id` (UUID, NOT NULL, 标记一轮对话)
    *   `sender_role` (VARCHAR(10), NOT NULL, CHECK (`sender_role` IN ('user', 'ai')))
    *   `sender_id` (UUID, NOT NULL, 若user则Users._id, 若ai则Characters._id)
    *   `text_content` (Text, Nullable)
    *   `message_type` (VARCHAR(20), NOT NULL, DEFAULT 'text', CHECK (`message_type` IN ('text', 'image_url', 'audio_url', 'system')))
    *   `media_url` (VARCHAR(512), Nullable)
    *   `ai_model_used_id` (FK to `AI_Chat_Models.model_id_pk` ON DELETE SET NULL, Nullable)
    *   `ai_generation_params_jsonb` (JSONB, Nullable) /* 替换 ai_length_param_used, 存储更完整参数 */
    *   `ai_run_id` (VARCHAR(100), Nullable)
    *   `is_edited` (Boolean, DEFAULT false)
    *   `edited_at` (Timestamp, Nullable)
    *   `is_deleted_by_user` (Boolean, DEFAULT false) /* 软删除标记 */
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`AI_Chat_Models`** (聊天AI模型定义表, P1-P2)
    *   `model_id_pk` (VARCHAR(100), PK, API `model`字段)
    *   `display_name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `provider_name` (VARCHAR(50), Nullable, e.g., "OpenAI", "Anthropic", "Internal")
    *   `input_price_per_1k_tokens` (Numeric, Nullable)
    *   `output_price_per_1k_tokens` (Numeric, Nullable)
    *   `price_currency` (VARCHAR(10), Nullable)
    *   `ui_color_hex` (VARCHAR(7), Nullable)
    *   `tags_jsonb` (JSONB, Nullable)
    *   `is_alphane_plus_required` (Boolean, DEFAULT false)
    *   `is_free_to_use` (Boolean, DEFAULT false)
    *   `capabilities_jsonb` (JSONB, Nullable, e.g., context_window, creativity_score)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('online', 'offline', 'maintenance', 'beta')), DEFAULT 'online')
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

### 4.6 多模态内容 (Image, Voice, Animation) (P2-P3)
*   **`Image_Generation_Tasks`** (图片生成任务表, P2)
    *   `request_id_pk` (UUID, PK, 任务ID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `model_id` (FK to `AI_Image_Models.model_id_pk` ON DELETE RESTRICT, NOT NULL)
    *   `width` (Integer, NOT NULL)
    *   `height` (Integer, NOT NULL)
    *   `positive_prompt` (Text, Nullable)
    *   `negative_prompt` (Text, Nullable)
    *   `gender_hint` (VARCHAR(15), Nullable, CHECK (`gender_hint` IN ('male', 'female', 'other', 'not_specified')))
    *   `styles_config_jsonb` (JSONB, Nullable)
    *   `reference_image_path` (VARCHAR(512), Nullable)
    *   `seed` (BigInt, Nullable)
    *   `steps` (Integer, Nullable)
    *   `cfg_scale` (Numeric, Nullable)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('pending', 'processing', 'completed', 'failed')), DEFAULT 'pending')
    *   `progress_percentage` (Integer, Nullable, CHECK (`progress_percentage` >= 0 AND `progress_percentage` <= 100))
    *   `estimated_time_seconds` (Integer, Nullable)
    *   `error_code` (VARCHAR(50), Nullable)
    *   `error_message` (Text, Nullable)
    *   `result_image_id` (FK to `Images.image_id_pk` ON DELETE SET NULL, Nullable, 任务成功后关联)
    *   `cost_endora_crystal` (Integer, Nullable) /* 记录消耗 */
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Images`** (图片元数据表, P2)
    *   `image_id_pk` (UUID, PK)
    *   `uploader_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL, 如果是用户上传或生成)
    *   `source_generation_task_id` (FK to `Image_Generation_Tasks.request_id_pk` ON DELETE SET NULL, Nullable)
    *   `image_url_original` (VARCHAR(512), NOT NULL)
    *   `thumbnail_url` (VARCHAR(512), Nullable)
    *   `image_width` (Integer, NOT NULL)
    *   `image_height` (Integer, NOT NULL)
    *   `mime_type` (VARCHAR(50), NOT NULL)
    *   `file_size_bytes` (BigInt, Nullable)
    *   `generation_params_jsonb` (JSONB, Nullable, 记录生成参数回顾)
    *   `is_public` (Boolean, DEFAULT true)
    *   `like_count` (Integer, DEFAULT 0)
    *   `view_count` (Integer, DEFAULT 0)
    *   `comment_count` (Integer, DEFAULT 0)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('active', 'pending_review', 'rejected', 'deleted')), DEFAULT 'active')
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Favorite_Images`** (用户收藏图片表, P2)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `image_id` (FK to `Images.image_id_pk` ON DELETE CASCADE, NOT NULL)
    *   UNIQUE (`user_id`, `image_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`AI_Image_Models`** (图片AI模型定义表, P2)
    *   `model_id_pk` (VARCHAR(100), PK)
    *   `display_name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `tags_jsonb` (JSONB, Nullable)
    *   `preview_image_url` (VARCHAR(512), Nullable)
    *   `model_type` (VARCHAR(30), NOT NULL, CHECK (`model_type` IN ('text-to-image', 'image-to-image')))
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('online', 'offline', 'maintenance', 'beta')), DEFAULT 'online')
    *   `provider_name` (VARCHAR(50), Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`AI_Image_Styles`** (图片风格预设表, P2)
    *   `style_id_pk` (VARCHAR(50), PK)
    *   `display_name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `tags_jsonb` (JSONB, Nullable)
    *   `preview_image_url` (VARCHAR(512), Nullable)
    *   `is_active` (Boolean, DEFAULT true)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`AI_Image_Style_Model_Compatibilities`** (图片风格与模型兼容性表, P2)
    *   `style_id` (FK to `AI_Image_Styles.style_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `model_id` (FK to `AI_Image_Models.model_id_pk` ON DELETE CASCADE, NOT NULL)
    *   PRIMARY KEY (`style_id`, `model_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`AI_Image_Presets`** (图片组合预设表, P2)
    *   `preset_id_pk` (VARCHAR(50), PK)
    *   `display_name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `preview_image_url` (VARCHAR(512), Nullable)
    *   `settings_jsonb` (JSONB, NOT NULL, 包含模型、提示词、风格等)
    *   `is_active` (Boolean, DEFAULT true)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Voices`** (语音样本表, P2)
    *   `voice_id_pk` (UUID, PK)
    *   `uploader_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `voice_name` (VARCHAR(100), NOT NULL)
    *   `language_code` (VARCHAR(10), Nullable) /* 新增 */
    *   `detected_gender` (VARCHAR(15), Nullable, CHECK (`detected_gender` IN ('male', 'female', 'other', 'unknown')))
    *   `status` (VARCHAR(30), NOT NULL, CHECK (`status` IN ('pending_review', 'active', 'rejected', 'processing_clone', 'failed_clone')), DEFAULT 'pending_review')
    *   `duration_seconds` (Numeric, Nullable)
    *   `file_format_original` (VARCHAR(10), Nullable)
    *   `audio_file_path` (VARCHAR(512), NOT NULL, GCS路径)
    *   `preview_audio_url` (VARCHAR(512), Nullable)
    *   `like_count` (Integer, DEFAULT 0)
    *   `usage_count` (Integer, DEFAULT 0)
    *   `comment_count` (Integer, DEFAULT 0)
    *   `is_cloneable` (Boolean, DEFAULT false)
    *   `cloned_from_voice_id` (FK to `Voices.voice_id_pk` ON DELETE SET NULL, Nullable, 如果是克隆的)
    *   `is_public` (Boolean, DEFAULT true) /* 新增 */
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`TTS_Cache`** (文本转语音缓存表, P2)
    *   `_id` (PK, BIGSERIAL)
    *   `text_hash` (VARCHAR(64), UNIQUE, NOT NULL, hash of text, voice_config, lang, speed, pitch)
    *   `text_to_synthesize` (Text, NOT NULL) /* 新增，存储原始文本 */
    *   `voice_config_jsonb` (JSONB, NOT NULL, e.g., {"voice_ids": ["id1"], "character_id": null})
    *   `language_code` (VARCHAR(10), NOT NULL)
    *   `speed_param` (Numeric, DEFAULT 1.0)
    *   `pitch_param` (Numeric, DEFAULT 1.0)
    *   `cached_audio_file_path` (VARCHAR(512), NOT NULL, GCS路径)
    *   `file_format` (VARCHAR(10), NOT NULL, e.g., "mp3", "opus")
    *   `duration_seconds` (Numeric, Nullable) /* 新增 */
    *   `last_accessed_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Animation_Tasks`** (图片动画任务表, P3)
    *   `task_id_pk` (UUID, PK)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `source_story_id` (FK to `Stories._id` ON DELETE CASCADE, NOT NULL)
    *   `source_image_path` (VARCHAR(512), NOT NULL)
    *   `animation_style_id` (VARCHAR(50), Nullable)
    *   `intensity_param` (Numeric, Nullable)
    *   `status` (VARCHAR(20), NOT NULL, CHECK (`status` IN ('pending', 'processing', 'completed', 'failed')), DEFAULT 'pending')
    *   `progress_percentage` (Integer, Nullable, CHECK (`progress_percentage` >= 0 AND `progress_percentage` <= 100))
    *   `result_animation_url` (VARCHAR(512), Nullable, e.g., gif, mp4)
    *   `result_thumbnail_url` (VARCHAR(512), Nullable)
    *   `error_message` (Text, Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

### 4.7 内容创作与管理 (Drafts, Memories) (P1-P2)
*   **`Drafts`** (创作草稿表, P1)
    *   `draft_id_pk` (UUID, PK)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `draft_type` (VARCHAR(15), NOT NULL, CHECK (`draft_type` IN ('character', 'story')))
    *   `linked_published_character_id` (FK to `Characters._id` ON DELETE SET NULL, Nullable)
    *   `linked_published_story_id` (FK to `Stories._id` ON DELETE SET NULL, Nullable)
    *   `last_step_completed` (Integer, Nullable)
    *   `preview_name` (VARCHAR(150), Nullable)
    *   `preview_image_url` (VARCHAR(512), Nullable)
    *   `content_jsonb` (JSONB, NOT NULL, 存储完整的草稿数据结构)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Draft_Associated_Files`** (草稿关联文件表, P1)
    *   `_id` (PK, UUID)
    *   `draft_id` (FK to `Drafts.draft_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `file_purpose` (VARCHAR(50), NOT NULL, e.g., "character_memory", "character_avatar", "story_cover_image")
    *   `file_name_original` (VARCHAR(255), NOT NULL)
    *   `file_path_storage` (VARCHAR(512), NOT NULL, GCS路径)
    *   `file_mime_type` (VARCHAR(100), Nullable)
    *   `file_size_bytes` (BigInt, Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Memory_Capsules`** (记忆胶囊表, P1)
    *   `memory_id_pk` (UUID, PK)
    *   `creator_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `capsule_name` (VARCHAR(150), Nullable)
    *   `source_session_id` (FK to `Chat_Sessions._id` ON DELETE CASCADE, NOT NULL)
    *   `source_character_id` (FK to `Characters._id` ON DELETE CASCADE, NOT NULL)
    *   `source_story_id` (FK to `Stories._id` ON DELETE CASCADE, NOT NULL)
    *   `shareable_url` (VARCHAR(255), Nullable, UNIQUE)
    *   `snapshot_image_url` (VARCHAR(512), Nullable, 聊天截图)
    *   `call_to_action_text` (VARCHAR(255), Nullable)
    *   `excerpt_text_1` (VARCHAR(200), Nullable) /* 新增 */
    *   `excerpt_role_1` (VARCHAR(10), Nullable, CHECK (`excerpt_role_1` IN ('user', 'ai'))) /* 新增 */
    *   `excerpt_text_2` (VARCHAR(200), Nullable) /* 新增 */
    *   `excerpt_role_2` (VARCHAR(10), Nullable, CHECK (`excerpt_role_2` IN ('user', 'ai'))) /* 新增 */
    *   `message_count_in_capsule` (Integer, DEFAULT 0) /* 新增 */
    *   `like_count` (Integer, DEFAULT 0)
    *   `comment_count` (Integer, DEFAULT 0)
    *   `is_public` (Boolean, DEFAULT false)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Memory_Capsule_Messages_Selection`** (记忆胶囊选定消息表, P1)
    *   `_id` (PK, UUID)
    *   `memory_id` (FK to `Memory_Capsules.memory_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `message_id` (FK to `Chat_Messages._id` ON DELETE CASCADE, NOT NULL)
    *   `order_in_capsule` (Integer, NOT NULL)
    *   UNIQUE (`memory_id`, `message_id`)
    *   UNIQUE (`memory_id`, `order_in_capsule`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Memory_Capsule_Likes`** (记忆胶囊点赞表, P2)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `memory_id` (FK to `Memory_Capsules.memory_id_pk` ON DELETE CASCADE, NOT NULL)
    *   UNIQUE (`user_id`, `memory_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

### 4.8 社区互动 (Comments, Notifications, Tags) (P1-P3)
*   **`Content_Comments`** (统一内容评论表, P2)
    *   `_id` (PK, UUID, `comment_id`)
    *   `commenter_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `target_content_type` (VARCHAR(30), NOT NULL, CHECK (`target_content_type` IN ('character', 'story', 'memory_capsule', 'image', 'voice')))
    *   `target_content_id` (UUID, NOT NULL, 相应实体的ID) /* 假设其他实体ID也是UUID */
    *   `parent_comment_id` (FK to `Content_Comments._id` ON DELETE CASCADE, Nullable)
    *   `root_comment_id` (FK to `Content_Comments._id` ON DELETE CASCADE, Nullable)
    *   `text_content` (Text, NOT NULL)
    *   `like_count` (Integer, DEFAULT 0)
    *   `reply_count` (Integer, DEFAULT 0)
    *   `is_pinned_by_owner` (Boolean, DEFAULT false)
    *   `status` (VARCHAR(30), NOT NULL, CHECK (`status` IN ('active', 'hidden_by_user', 'removed_by_admin', 'reported')), DEFAULT 'active')
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Content_Comment_Likes`** (评论点赞表, P2)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `comment_id` (FK to `Content_Comments._id` ON DELETE CASCADE, NOT NULL)
    *   UNIQUE (`user_id`, `comment_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Notifications`** (应用内通知表, P2)
    *   `notification_id_pk` (UUID, PK)
    *   `recipient_user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `sender_user_id` (FK to `Users._id` ON DELETE SET NULL, Nullable for system notifications)
    *   `sender_user_name_cache` (VARCHAR(50), Nullable) /* 冗余 */
    *   `sender_avatar_url_cache` (VARCHAR(512), Nullable) /* 冗余 */
    *   `notification_type_key` (VARCHAR(50), NOT NULL, e.g., "new_comment_reply", "new_follower_user", "memory_liked", "system_announcement")
    *   `title_text` (VARCHAR(150), Nullable)
    *   `body_text` (Text, NOT NULL)
    *   `is_read_by_recipient` (Boolean, DEFAULT false)
    *   `read_at` (Timestamp, Nullable) /* 新增 */
    *   `related_target_type` (VARCHAR(30), Nullable)
    *   `related_target_id` (VARCHAR(255), Nullable) /* 保持VARCHAR以适应不同类型ID */
    *   `related_target_summary` (VARCHAR(255), Nullable) /* 新增 */
    *   `deep_link_url` (VARCHAR(512), Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Tags`** (全局标签库, P1)
    *   `tag_id_pk` (BIGSERIAL, PK) /* 改为自增ID */
    *   `tag_name` (VARCHAR(50), NOT NULL)
    *   `language_code` (VARCHAR(10), NOT NULL, DEFAULT 'en', e.g., "en", "zh-CN")
    *   `applies_to_content_type` (VARCHAR(20), NOT NULL, CHECK (`applies_to_content_type` IN ('character', 'story', 'voice', 'image', 'common')))
    *   `usage_count` (Integer, DEFAULT 0)
    *   `is_system_defined` (Boolean, DEFAULT true)
    *   `is_featured_on_mainpage` (Boolean, DEFAULT false)
    *   UNIQUE (`tag_name`, `language_code`, `applies_to_content_type`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Content_Tag_Mappings`** (内容与标签的通用多对多映射表, P1)
    *   `_id` (PK, UUID)
    *   `tag_id` (FK to `Tags.tag_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `content_type` (VARCHAR(20), NOT NULL, CHECK (`content_type` IN ('character', 'story', 'voice', 'image')))
    *   `content_id` (UUID, NOT NULL, 相应实体的ID) /* 假设其他实体ID也是UUID */
    *   PRIMARY KEY (`tag_id`, `content_type`, `content_id`)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

### 4.9 商业化与运营 (P2-P3)
*   **`Redeem_Codes`** (兑换码表, P3)
    *   `code_pk` (VARCHAR(50), PK, 兑换码本身, UNIQUE)
    *   `reward_type_key` (VARCHAR(50), NOT NULL, e.g., "alphane_currency", "subscription_days", "specific_item", "feature_unlock")
    *   `reward_details_jsonb` (JSONB, NOT NULL) /* 替代多个reward字段，更灵活 */
    *   `reward_description_text` (VARCHAR(255), Nullable)
    *   `max_uses_total` (Integer, Nullable, -1 for unlimited)
    *   `current_uses_count` (Integer, DEFAULT 0)
    *   `max_uses_per_user` (Integer, DEFAULT 1, Nullable)
    *   `valid_from_timestamp` (Timestamp, Nullable)
    *   `expires_at_timestamp` (Timestamp, Nullable)
    *   `is_active_code` (Boolean, DEFAULT true)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`User_Redeem_History`** (用户兑换记录表, P3)
    *   `_id` (PK, UUID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `redeem_code_used` (FK to `Redeem_Codes.code_pk` ON DELETE RESTRICT, NOT NULL)
    *   `redeemed_at_timestamp` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `reward_details_applied_jsonb` (JSONB, NOT NULL, 记录实际发放的奖励)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Payment_Gateways`** (支付渠道配置表, P2)
    *   `gateway_id_pk` (VARCHAR(50), PK, e.g., "stripe_card", "apple_iap", "google_play_billing")
    *   `display_name` (VARCHAR(100), NOT NULL)
    *   `is_enabled` (Boolean, DEFAULT true)
    *   `config_jsonb` (JSONB, Nullable, 存储API密钥等配置)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Store_Products`** (应用商店可购买商品定义表, P1)
    *   `product_sku_pk` (VARCHAR(100), PK, e.g., "100_alphane_coins", "alphane_pass_monthly_sub")
    *   `product_type` (VARCHAR(20), NOT NULL, CHECK (`product_type` IN ('consumable', 'subscription')))
    *   `display_name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `price_tier_or_amount` (VARCHAR(50), Nullable, 根据平台)
    *   `currency_code` (VARCHAR(10), Nullable)
    *   `grants_reward_type` (VARCHAR(50), Nullable, e.g., "alphane_dust", "endora_crystal", "app_subscription_plan")
    *   `grants_reward_value` (VARCHAR(100), Nullable, e.g., 100 or "alphane_pass_monthly")
    *   `platform_product_id_apple` (VARCHAR(255), Nullable, Apple App Store Product ID)
    *   `platform_product_id_google` (VARCHAR(255), Nullable, Google Play Product ID)
    *   `is_active_for_sale` (Boolean, DEFAULT true)
    *   `display_order` (Integer, DEFAULT 0)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Payment_Transactions`** (支付交易记录表, P1)
    *   `_id` (PK, UUID, 我方内部交易ID)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `store_product_sku` (FK to `Store_Products.product_sku_pk` ON DELETE RESTRICT, Nullable)
    *   `quantity_purchased` (Integer, DEFAULT 1)
    *   `total_amount_charged` (Numeric, NOT NULL)
    *   `currency_charged` (VARCHAR(10), NOT NULL)
    *   `payment_gateway_used_id` (FK to `Payment_Gateways.gateway_id_pk` ON DELETE RESTRICT, Nullable)
    *   `gateway_transaction_id` (VARCHAR(255), Nullable, 支付平台返回的交易ID)
    *   `gateway_payment_intent_id` (VARCHAR(255), Nullable, e.g. Stripe Payment Intent)
    *   `client_secret_for_sdk` (Text, Nullable, e.g. Stripe client_secret)
    *   `checkout_url_redirect` (VARCHAR(1024), Nullable)
    *   `status` (VARCHAR(30), NOT NULL, CHECK (`status` IN ('pending_checkout', 'pending_confirmation', 'succeeded', 'failed', 'refunded', 'disputed')), DEFAULT 'pending_checkout')
    *   `raw_checkout_response_jsonb` (JSONB, Nullable, 结账接口返回给客户端的数据)
    *   `raw_notification_payload_jsonb` (JSONB, Nullable, 支付平台回调的原始数据)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Gacha_Pools`** (抽卡池配置表, P3)
    *   `pool_id_pk` (VARCHAR(50), PK)
    *   `pool_name` (VARCHAR(100), NOT NULL)
    *   `description` (Text, Nullable)
    *   `start_time_utc` (Timestamp, Nullable)
    *   `end_time_utc` (Timestamp, Nullable)
    *   `cost_alphane_single_roll` (Integer, Nullable)
    *   `cost_endora_single_roll` (Integer, Nullable) /* 新增 */
    *   `cost_ticket_item_id_single_roll` (VARCHAR(50), Nullable) /* 假设物品ID是VARCHAR */
    *   `is_active_pool` (Boolean, DEFAULT true)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Gacha_Pool_Items`** (抽卡池物品及其概率表, P3)
    *   `_id` (PK, UUID)
    *   `pool_id` (FK to `Gacha_Pools.pool_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `item_reward_type` (VARCHAR(50), NOT NULL, CHECK (`item_reward_type` IN ('character_image', 'generic_item', 'currency_alphane_dust', 'currency_endora_crystal', 'avatar_frame')))
    *   `item_reference_id` (VARCHAR(255), NOT NULL, 指向具体物品的ID)
    *   `item_display_name` (VARCHAR(100), Nullable)
    *   `item_rarity_tier` (VARCHAR(10), NOT NULL, CHECK (`item_rarity_tier` IN ('N', 'R', 'SR', 'SSR', 'UR')))
    *   `item_preview_image_url` (VARCHAR(512), Nullable)
    *   `draw_weight` (Integer, NOT NULL, CHECK (`draw_weight` > 0))
    *   `is_guaranteed_drop_after_x_rolls` (Integer, Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Gacha_Events`** (用户抽卡事件记录表, P3)
    *   `gacha_event_id_pk` (UUID, PK)
    *   `user_id` (FK to `Users._id` ON DELETE CASCADE, NOT NULL)
    *   `pool_id` (FK to `Gacha_Pools.pool_id_pk` ON DELETE RESTRICT, NOT NULL)
    *   `context_character_id` (FK to `Characters._id` ON DELETE SET NULL, Nullable)
    *   `roll_type` (VARCHAR(20), NOT NULL, CHECK (`roll_type` IN ('single', 'multi_10', 'reroll_single')))
    *   `cost_type_paid` (VARCHAR(30), NOT NULL, CHECK (`cost_type_paid` IN ('alphane_dust', 'endora_crystal', 'free_ticket', 'reroll_ticket')))
    *   `cost_amount_paid` (Numeric, NOT NULL)
    *   `rerolled_from_gacha_event_id` (FK to `Gacha_Events.gacha_event_id_pk` ON DELETE SET NULL, Nullable)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
*   **`Gacha_Event_Results`** (单次抽卡事件获得物品明细表, P3)
    *   `_id` (PK, UUID)
    *   `gacha_event_id` (FK to `Gacha_Events.gacha_event_id_pk` ON DELETE CASCADE, NOT NULL)
    *   `gacha_pool_item_id_won` (FK to `Gacha_Pool_Items._id` ON DELETE RESTRICT, NOT NULL) /* 关联到卡池中的具体奖品项 */
    *   `sequence_in_multi_roll` (Integer, DEFAULT 1, 如果是多连抽中的第几个)
    *   `is_newly_acquired_for_user` (Boolean, DEFAULT false)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

### 4.10 服务状态 (Uptime) (P3 / 运维)
*   **`AI_Model_Status_Checks`** (AI模型状态检查记录表, 运维)
    *   `_id` (PK, BIGSERIAL)
    *   `model_identifier` (VARCHAR(100), NOT NULL) /* 通用模型标识符 */
    *   `model_type` (VARCHAR(20), NOT NULL, CHECK (`model_type` IN ('chat', 'image', 'tts', 'stt', 'moderation')))
    *   `status_name` (VARCHAR(50), NOT NULL, e.g., "online", "offline", "maintenance", "degraded")
    *   `last_checked_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `response_time_ms` (Integer, Nullable)
    *   `details_jsonb` (JSONB, Nullable, 存储额外检查细节或错误信息)
    *   `created_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)
    *   `updated_at` (Timestamp, NOT NULL, DEFAULT CURRENT_TIMESTAMP)

*(以上设计基于 API 文档进行了详细的字段和表结构推断与优化。实际实现时，还需要考虑索引策略、具体数据类型长度的最终确认、更细致的约束条件、视图、存储过程以及数据库用户权限等。PostgreSQL 中 `updated_at` 字段的自动更新通常需要通过触发器实现。本文件将随着项目进展持续更新。)*

---

*(本文件将随着项目进展持续更新。)*