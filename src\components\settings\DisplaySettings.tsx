'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { useTheme } from 'next-themes';
import { useRouter, usePathname } from 'next/navigation';
import { Globe, Palette, Type, Image, Sparkles, Filter, MapPin } from 'lucide-react';
import SettingItem from './SettingItem';
import ToggleSwitch from './ToggleSwitch';
import <PERSON>Field from './SelectField';

interface DisplaySettingsProps {
  settings: any;
  updateSetting: (path: string, value: any) => void;
  lang: string;
  user: any;
  hasUnsavedChanges: boolean;
  isPremiumUser: boolean;
}

const DisplaySettings: React.FC<DisplaySettingsProps> = ({
  settings,
  updateSetting,
  lang,
  user,
  hasUnsavedChanges,
  isPremiumUser
}) => {
  const { t } = useTranslation(lang, 'translation');
  const { setTheme } = useTheme();
  const router = useRouter();
  const pathname = usePathname();

  const languageOptions = [
    { value: 'en', label: t('settings.categories.display.languageOptions.en') },
    { value: 'zh', label: t('settings.categories.display.languageOptions.zh') },
    { value: 'ja', label: t('settings.categories.display.languageOptions.ja') }
  ];

  const themeOptions = [
    { value: 'system', label: t('settings.categories.display.themeOptions.auto') },
    { value: 'light', label: t('settings.categories.display.themeOptions.light') },
    { value: 'dark', label: t('settings.categories.display.themeOptions.dark') }
  ];

  const fontSizeOptions = [
    { value: 'small', label: t('settings.categories.display.fontSizeOptions.small') },
    { value: 'medium', label: t('settings.categories.display.fontSizeOptions.medium') },
    { value: 'large', label: t('settings.categories.display.fontSizeOptions.large') },
    { value: 'extra_large', label: t('settings.categories.display.fontSizeOptions.extra_large') }
  ];

  const animationOptions = [
    { value: 'none', label: t('settings.categories.display.animationLevelOptions.none') },
    { value: 'reduced', label: t('settings.categories.display.animationLevelOptions.reduced') },
    { value: 'standard', label: t('settings.categories.display.animationLevelOptions.standard') },
    { value: 'rich', label: t('settings.categories.display.animationLevelOptions.rich') }
  ];

  const handleLanguageChange = (newLang: string | number) => {
    updateSetting('display.language', newLang);
    
    // Navigate to the new language URL
    const currentPath = pathname;
    const newPath = currentPath.replace(/^\/[^\/]+/, `/${newLang}`);
    router.push(newPath);
  };

  const handleThemeChange = (newTheme: string | number) => {
    updateSetting('display.theme', newTheme);
    setTheme(newTheme as string);
  };

  return (
    <div className="space-y-6">
      {/* Language & Localization */}
      <div className="space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Globe className="w-4 h-4" />
          Language & Localization
        </h4>

        <SettingItem
          label={t('settings.categories.display.language')}
          description={t('settings.categories.display.languageDesc')}
        >
          <SelectField
            value={settings.display.language}
            onChange={handleLanguageChange}
            options={languageOptions}
            className="w-40"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.display.regionalization')}
          description={t('settings.categories.display.regionalizationDesc')}
        >
          <ToggleSwitch
            checked={settings.display.regionalization}
            onChange={(checked) => updateSetting('display.regionalization', checked)}
          />
        </SettingItem>
      </div>

      {/* Theme & Appearance */}
      <div className="border-t border-border pt-6 space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Palette className="w-4 h-4" />
          Theme & Appearance
        </h4>

        <SettingItem
          label={t('settings.categories.display.theme')}
          description={t('settings.categories.display.themeDesc')}
        >
          <SelectField
            value={settings.display.theme}
            onChange={handleThemeChange}
            options={themeOptions}
            className="w-40"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.display.fontSize')}
          description={t('settings.categories.display.fontSizeDesc')}
        >
          <SelectField
            value={settings.display.fontSize}
            onChange={(value) => updateSetting('display.fontSize', value)}
            options={fontSizeOptions}
            className="w-40"
          />
        </SettingItem>

        <SettingItem
          label={t('settings.categories.display.animationLevel')}
          description={t('settings.categories.display.animationLevelDesc')}
        >
          <SelectField
            value={settings.display.animationLevel}
            onChange={(value) => updateSetting('display.animationLevel', value)}
            options={animationOptions}
            className="w-40"
          />
        </SettingItem>
      </div>

      {/* Chat Customization */}
      <div className="border-t border-border/50 pt-6 space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Image className="w-4 h-4" />
          Chat Customization
        </h4>

        <SettingItem
          label={t('settings.categories.display.chatBackground')}
          description={t('settings.categories.display.chatBackgroundDesc')}
          showArrow
          onClick={() => {
            // TODO: Implement chat background customization
            alert('Chat background customization coming soon!');
          }}
        >
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded border-2 border-border bg-gradient-to-br from-primary/20 to-primary-pink/20"></div>
            <span className="text-sm text-foreground/70">Default</span>
          </div>
        </SettingItem>
      </div>

      {/* Content Controls */}
      <div className="border-t border-border/50 pt-6 space-y-4">
        <h4 className="font-medium text-foreground flex items-center gap-2">
          <Filter className="w-4 h-4" />
          Content Controls
        </h4>

        <SettingItem
          label={t('settings.categories.display.contentFilter')}
          description={t('settings.categories.display.contentFilterDesc')}
        >
          <ToggleSwitch
            checked={settings.display.contentFilter}
            onChange={(checked) => updateSetting('display.contentFilter', checked)}
          />
        </SettingItem>

        {/* Advanced Display Settings (Premium Feature Showcase) */}
        {isPremiumUser && (
          <div className="mt-6 p-4 bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
            <h5 className="font-medium text-yellow-800 dark:text-yellow-200 flex items-center gap-2 mb-3">
              <Sparkles className="w-4 h-4" />
              Premium Display Features
            </h5>
            
            <div className="space-y-3">
              <SettingItem
                label="Custom UI Themes"
                description="Access to exclusive interface themes and color schemes"
                isPremium
                showArrow
                onClick={() => alert('Custom themes coming soon!')}
                className="!py-2"
              />
              
              <SettingItem
                label="Advanced Font Options"
                description="Additional font families and typography controls"
                isPremium
                showArrow
                onClick={() => alert('Advanced fonts coming soon!')}
                className="!py-2"
              />
            </div>
          </div>
        )}
      </div>

      {/* Preview Section */}
      <div className="border-t border-border/50 pt-6">
        <h4 className="font-medium text-foreground mb-4">Preview</h4>
        <div className="p-4 bg-card border border-border rounded-lg">
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-primary"></div>
              <div className="flex-1">
                <p className={`font-medium text-foreground ${
                  settings.display.fontSize === 'small' ? 'text-xs' :
                  settings.display.fontSize === 'large' ? 'text-base' :
                  settings.display.fontSize === 'extra_large' ? 'text-lg' :
                  'text-sm'
                }`}>
                  AI Character
                </p>
                <p className={`text-foreground/60 ${
                  settings.display.fontSize === 'small' ? 'text-xs' :
                  settings.display.fontSize === 'large' ? 'text-sm' :
                  settings.display.fontSize === 'extra_large' ? 'text-base' :
                  'text-xs'
                }`}>
                  Online
                </p>
              </div>
            </div>
            <div className="bg-primary/10 rounded-lg p-3">
              <p className={`text-foreground ${
                settings.display.fontSize === 'small' ? 'text-xs' :
                settings.display.fontSize === 'large' ? 'text-base' :
                settings.display.fontSize === 'extra_large' ? 'text-lg' :
                'text-sm'
              }`}>
                Hello! This is how messages will appear with your current settings.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DisplaySettings; 