'use client';

import React from 'react';
import { 
  Trophy, 
  Star, 
  Lock, 
  Clock, 
  CheckCircle, 
  Gift,
  MessageCircle,
  Palette,
  Heart,
  Users,
  Sparkles,
  Target,
  Zap,
  Crown,
  Flame,
  Book,
  Camera,
  Settings,
  Award,
  Diamond,
  Shield,
  Compass
} from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

import { EnhancedAchievement } from '@/types/achievements';

interface TrophyCardProps {
  achievement: EnhancedAchievement;
  lang: string;
  onClick: (achievement: EnhancedAchievement) => void;
  onClaimReward?: (achievementId: string) => void;
}

const TrophyCard: React.FC<TrophyCardProps> = ({
  achievement,
  lang,
  onClick,
  onClaimReward
}) => {
  const { t } = useTranslation(lang, 'translation');

  // 获取成就类别对应的图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'beginner': return Target;
      case 'interaction': return MessageCircle;
      case 'creation': return Palette;
      case 'collection': return Heart;
      case 'social': return Users;
      case 'special': return Crown;
      default: return Trophy;
    }
  };

  // 获取具体的成就图标
  const getSpecificIcon = (iconName?: string) => {
    if (!iconName) return null;
    
    switch (iconName) {
      case 'target': return Target;
      case 'message-circle': return MessageCircle;
      case 'heart': return Heart;
      case 'palette': return Palette;
      case 'users': return Users;
      case 'flame': return Flame;
      case 'compass': return Compass;
      case 'book': return Book;
      case 'star': return Star;
      case 'crown': return Crown;
      case 'trophy': return Trophy;
      case 'camera': return Camera;
      case 'sparkles': return Sparkles;
      case 'zap': return Zap;
      case 'diamond': return Diamond;
      case 'shield': return Shield;
      default: return null;
    }
  };

  // 获取稀有度对应的特殊效果图标
  const getRarityIcon = (rarity: string) => {
    switch (rarity) {
      case 'bronze': return Award;
      case 'silver': return Star;
      case 'gold': return Trophy;
      case 'platinum': return Diamond;
      case 'diamond': return Sparkles;
      case 'legendary': return Crown;
      default: return Award;
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'bronze': return 'text-amber-600 bg-amber-50 border-amber-200 dark:bg-amber-950 dark:border-amber-800';
      case 'silver': return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900 dark:border-gray-700';
      case 'gold': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800';
      case 'platinum': return 'text-cyan-600 bg-cyan-50 border-cyan-200 dark:bg-cyan-950 dark:border-cyan-800';
      case 'diamond': return 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800';
      case 'legendary': return 'text-purple-600 bg-purple-50 border-purple-200 dark:bg-purple-950 dark:border-purple-800';
      default: return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900 dark:border-gray-700';
    }
  };

  const getRarityGradient = (rarity: string) => {
    switch (rarity) {
      case 'bronze': return 'from-amber-400 via-amber-500 to-amber-600';
      case 'silver': return 'from-gray-400 via-gray-500 to-gray-600';
      case 'gold': return 'from-yellow-400 via-yellow-500 to-yellow-600';
      case 'platinum': return 'from-cyan-400 via-cyan-500 to-cyan-600';
      case 'diamond': return 'from-blue-400 via-blue-500 to-blue-600';
      case 'legendary': return 'from-purple-400 via-purple-500 to-purple-600';
      default: return 'from-gray-400 via-gray-500 to-gray-600';
    }
  };

  const getStatusIcon = () => {
    switch (achievement.status) {
      case 'locked':
        return <Lock className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />;
      case 'inProgress':
        return <Clock className="w-3 h-3 sm:w-4 sm:h-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />;
      case 'claimed':
        return <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-500 fill-current" />;
      default:
        return <Trophy className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />;
    }
  };

  const isInteractive = achievement.status !== 'locked';
  const canClaim = achievement.status === 'completed' && onClaimReward;

  const progressPercentage = (achievement.progress.current / achievement.progress.total) * 100;
  
  // 获取主要显示的图标 - 优先使用具体图标，回退到类别图标
  const SpecificIcon = getSpecificIcon(achievement.icon);
  const CategoryIcon = getCategoryIcon(achievement.category);
  const MainIcon = SpecificIcon || CategoryIcon;
  
  const RarityIcon = getRarityIcon(achievement.rarity);

  return (
    <div 
      className={`
        relative group bg-card border rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl theme-transition
        ${isInteractive ? 'cursor-pointer hover:scale-[1.02] hover:-translate-y-1' : 'opacity-60'}
        ${getRarityColor(achievement.rarity)}
      `}
      onClick={() => isInteractive && onClick(achievement)}
    >
      {/* 稀有度装饰边框 */}
      {achievement.rarity !== 'bronze' && (
        <div className={`absolute inset-0 bg-gradient-to-r ${getRarityGradient(achievement.rarity)} opacity-10 pointer-events-none`} />
      )}

      {/* 传奇成就特殊效果 */}
      {achievement.rarity === 'legendary' && (
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-purple-500/20 opacity-50 animate-pulse pointer-events-none" />
      )}

      <div className="relative p-4 sm:p-5">
        {/* 状态标识和稀有度标签 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-1.5">
            {getStatusIcon()}
            {achievement.rarity !== 'bronze' && (
              <RarityIcon className={`w-3 h-3 sm:w-4 sm:h-4 ${achievement.rarity === 'legendary' ? 'text-purple-500' : 'text-current'}`} />
            )}
          </div>
          <span className={`
            inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
            ${getRarityColor(achievement.rarity)}
          `}>
            {t(`trophies.rarity.${achievement.rarity}`)}
          </span>
        </div>

        {/* 主要成就图标区域 */}
        <div className="flex flex-col items-center text-center mb-4">
          <div className={`
            w-16 h-16 sm:w-20 sm:h-20 rounded-2xl flex items-center justify-center mb-3 relative overflow-hidden
            ${achievement.status === 'locked' ? 'bg-gray-100 dark:bg-gray-800' : `bg-gradient-to-br ${getRarityGradient(achievement.rarity)}`}
          `}>
            {/* 背景图案 */}
            {achievement.status !== 'locked' && (
              <div className="absolute inset-0 bg-white/20 dark:bg-black/20" />
            )}
            
            {/* 主图标 */}
            <MainIcon className={`
              w-8 h-8 sm:w-10 sm:h-10 relative z-10
              ${achievement.status === 'locked' ? 'text-gray-400' : 'text-white'}
            `} />
            
            {/* 完成状态装饰 */}
            {achievement.status === 'completed' && (
              <div className="absolute top-1 right-1">
                <CheckCircle className="w-4 h-4 text-green-400 bg-white rounded-full" />
              </div>
            )}
            
            {/* 已领取装饰 */}
            {achievement.status === 'claimed' && (
              <div className="absolute top-1 right-1">
                <Star className="w-4 h-4 text-yellow-400 bg-white rounded-full fill-current" />
              </div>
            )}
          </div>

          {/* 简化的成就名称 */}
          <h3 className={`font-bold text-sm sm:text-base leading-tight text-center ${
            achievement.status === 'locked' ? 'text-gray-500' : 'text-foreground'
          }`}>
            {achievement.name}
          </h3>
        </div>

        {/* 进度区域 - 简化显示 */}
        {achievement.status !== 'locked' && achievement.progress.total > 1 && (
          <div className="mb-3">
            {/* 进度条 */}
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-1">
              <div 
                className={`h-2 rounded-full transition-all duration-500 bg-gradient-to-r ${getRarityGradient(achievement.rarity)}`}
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
            {/* 进度数字 */}
            <div className="flex justify-center">
              <span className="text-xs font-medium text-muted-foreground">
                {achievement.progress.current}/{achievement.progress.total}
              </span>
            </div>
          </div>
        )}

        {/* 底部信息 */}
        <div className="flex items-center justify-between">
          {/* 积分显示 */}
          <div className="flex items-center gap-1">
            <Star className="w-3 h-3 text-yellow-500" />
            <span className="text-xs font-medium text-muted-foreground">
              {achievement.points} {t('trophies.common.points')}
            </span>
          </div>

          {/* 奖励预览 - 图标化 */}
          {achievement.rewards.length > 0 && achievement.status !== 'locked' && (
            <div className="flex items-center gap-1">
              <Gift className="w-3 h-3 text-purple-500" />
              <span className="text-xs text-muted-foreground">×{achievement.rewards.length}</span>
            </div>
          )}

          {/* 领取按钮 */}
          {canClaim && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onClaimReward(achievement.id);
              }}
              className="px-3 py-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg text-xs font-medium hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-sm"
            >
              {t('trophies.card.claimReward')}
            </button>
          )}
        </div>

        {/* 难度指示器 */}
        {achievement.difficulty && achievement.status !== 'locked' && (
          <div className="mt-2 flex justify-center">
            <div className="flex gap-0.5">
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className={`w-1 h-1 rounded-full ${
                    i < (achievement.difficulty === 'easy' ? 1 : achievement.difficulty === 'medium' ? 2 : achievement.difficulty === 'hard' ? 3 : 4)
                      ? 'bg-orange-400' 
                      : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 悬停效果 */}
      {isInteractive && (
        <div className="absolute inset-0 bg-gradient-to-t from-primary/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity rounded-xl pointer-events-none" />
      )}

      {/* 传奇成就光芒效果 */}
      {achievement.rarity === 'legendary' && achievement.status !== 'locked' && (
        <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-xl blur opacity-20 group-hover:opacity-40 transition-opacity pointer-events-none" />
      )}
    </div>
  );
};

export default TrophyCard; 