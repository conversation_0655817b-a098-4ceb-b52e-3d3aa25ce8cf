'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { CheckCheck } from 'lucide-react';
import NotificationItem, { NotificationData } from './NotificationItem';
import NotificationEmptyState from './NotificationEmptyState';
import type { NotificationTab } from './NotificationTabs';

interface NotificationListProps {
  lang: string;
  notifications: NotificationData[];
  activeTab: NotificationTab;
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
  onDelete?: (id: string) => void;
}

const NotificationList: React.FC<NotificationListProps> = ({
  lang,
  notifications,
  activeTab,
  onMarkAsRead,
  onMarkAllAsRead,
  onDelete
}) => {
  const { t } = useTranslation(lang, 'translation');
  
  const unreadCount = notifications.filter(n => n.isUnread).length;

  if (notifications.length === 0) {
    return <NotificationEmptyState lang={lang} activeTab={activeTab} />;
  }

  return (
    <div className="space-y-4">
      {/* Mark all as read button */}
      {unreadCount > 0 && onMarkAllAsRead && (
        <div className="flex justify-between items-center bg-card border border-border rounded-lg p-4 theme-transition">
          <div className="text-card-foreground">
            {t('notifications.unreadCount', { count: unreadCount })}{' '}
            {t('notifications.inThisCategory')}
          </div>
          <button
            onClick={onMarkAllAsRead}
            className="flex items-center text-primary hover:text-primary/80 font-medium text-sm transition-colors"
          >
            <CheckCheck size={16} className="mr-1" />
            {t('notifications.markAllAsRead')}
          </button>
        </div>
      )}

      {/* Notification items */}
      <div className="space-y-4">
        {notifications.map((notification) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onMarkAsRead={onMarkAsRead}
            onDelete={onDelete}
          />
        ))}
      </div>
    </div>
  );
};

export default NotificationList; 