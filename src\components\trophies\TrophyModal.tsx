'use client';

import React from 'react';
import { X, Trophy, Star, Gift, Calendar, Lightbulb, ArrowRight } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

import { EnhancedAchievement } from '@/types/achievements';

interface TrophyModalProps {
  achievement: EnhancedAchievement | null;
  onClose: () => void;
  onClaimReward?: (achievementId: string) => void;
  lang: string;
}

const TrophyModal: React.FC<TrophyModalProps> = ({
  achievement,
  onClose,
  onClaimReward,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');

  if (!achievement) return null;

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'bronze': return 'text-amber-600 bg-amber-50 border-amber-200 dark:bg-amber-950 dark:border-amber-800';
      case 'silver': return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900 dark:border-gray-700';
      case 'gold': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800';
      case 'platinum': return 'text-cyan-600 bg-cyan-50 border-cyan-200 dark:bg-cyan-950 dark:border-cyan-800';
      case 'diamond': return 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800';
      case 'legendary': return 'text-purple-600 bg-purple-50 border-purple-200 dark:bg-purple-950 dark:border-purple-800';
      default: return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900 dark:border-gray-700';
    }
  };

  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'badge': return '🎖️';
      case 'title': return '👑';
      case 'currency': return '💰';
      case 'item': return '🎁';
      case 'privilege': return '⭐';
      case 'experience': return '✨';
      default: return '🎁';
    }
  };

  const canClaim = achievement.status === 'completed' && onClaimReward;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[70] flex items-center justify-center p-4">
      <div className="bg-card border rounded-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto theme-transition">
        {/* Header */}
        <div className="sticky top-0 bg-card border-b p-6 rounded-t-2xl">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-foreground">
              {t('trophies.modal.achievementDetails')}
            </h2>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-lg bg-secondary hover:bg-secondary/80 flex items-center justify-center transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Achievement Header */}
          <div className="flex items-start gap-6">
            <div className={`w-20 h-20 rounded-xl flex items-center justify-center ${getRarityColor(achievement.rarity)}`}>
              <Trophy className="w-10 h-10" />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="text-2xl font-bold text-foreground">{achievement.name}</h3>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRarityColor(achievement.rarity)}`}>
                  {t(`trophies.rarity.${achievement.rarity}`)}
                </span>
              </div>
              
              <p className="text-muted-foreground mb-4">{achievement.description}</p>
              
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span>{achievement.points} {t('trophies.card.points')}</span>
                </div>
                
                {achievement.earnedDate && (
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span>{t('trophies.modal.earnedDate')} {new Date(achievement.earnedDate).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Progress */}
          {achievement.progress.total > 1 && (
            <div className="bg-secondary rounded-xl p-4">
              <h4 className="font-semibold text-foreground mb-3">{t('trophies.modal.progress')}</h4>
              <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                <span>{achievement.progress.current}/{achievement.progress.total}</span>
                <span>{Math.round((achievement.progress.current / achievement.progress.total) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  className="h-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-500"
                  style={{ width: `${(achievement.progress.current / achievement.progress.total) * 100}%` }}
                />
              </div>
            </div>
          )}

          {/* Requirements */}
          <div>
            <h4 className="font-semibold text-foreground mb-3">{t('trophies.modal.requirements')}</h4>
            <div className="bg-secondary rounded-lg p-4">
              <p className="text-muted-foreground">{achievement.requirement}</p>
            </div>
          </div>

          {/* Tips */}
          {achievement.tips && (
            <div>
              <h4 className="font-semibold text-foreground mb-3 flex items-center gap-2">
                <Lightbulb className="w-4 h-4" />
                {t('trophies.modal.tips')}
              </h4>
              <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <p className="text-blue-800 dark:text-blue-200">{achievement.tips}</p>
              </div>
            </div>
          )}

          {/* Rewards */}
          {achievement.rewards.length > 0 && (
            <div>
              <h4 className="font-semibold text-foreground mb-3">{t('trophies.modal.rewards')}</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {achievement.rewards.map((reward, index) => (
                  <div key={index} className="bg-secondary rounded-lg p-3 flex items-center gap-3">
                    <span className="text-2xl">{getRewardIcon(reward.type)}</span>
                    <div className="flex-1">
                      <p className="font-medium text-foreground">
                        {reward.amount && `${reward.amount} `}{reward.name}
                      </p>
                      <p className="text-sm text-muted-foreground capitalize">{reward.type}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Tips */}
          {achievement.tips && (
            <div className="bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
              <div className="flex items-start gap-3">
                <Lightbulb className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">
                    {t('trophies.modal.tips')}
                  </h4>
                  <p className="text-blue-800 dark:text-blue-200 text-sm">{achievement.tips}</p>
                </div>
              </div>
            </div>
          )}

          {/* Action Button */}
          {achievement.status === 'completed' && onClaimReward && (
            <div className="flex justify-center pt-4">
              <button
                onClick={() => onClaimReward(achievement.id)}
                className="px-6 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors flex items-center gap-2"
              >
                <Gift className="w-5 h-5" />
                {t('trophies.card.claimReward')}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TrophyModal; 