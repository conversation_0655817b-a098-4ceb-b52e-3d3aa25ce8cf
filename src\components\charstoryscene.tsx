'use client';

import React, { useState } from 'react';
import { MapP<PERSON>, Clock, Cloud, Lightbulb, Heart, Brain, Target, Users, ChevronDown, ChevronUp, MessageCircle, FileText, Trash2 } from 'lucide-react';

// Chapter interface - matches the one from AdvancedStep
export interface Chapter {
  id: number;
  // Scene Layer
  title: string;
  environment: string;
  timeElements: {
    season: string;
    timeOfDay: string;
    duration: string;
    specialDate: string;
  };
  spatialElements: {
    location: string;
    atmosphere: string;
    keyObjects: string;
  };
  environmentalElements: {
    weather: string;
    lighting: string;
    sounds: string;
    scents: string;
    temperature: string;
  };
  
  // Background Context
  macroHistory: string;
  characterPast: string;
  immediateTrigger: string;
  
  // Character Psychology
  mentalModel: {
    coreValues: string;
    thinkingMode: string;
    decisionLogic: string;
  };
  emotionalBaseline: {
    displayedEmotion: string;
    hiddenEmotion: string;
    emotionalIntensity: number;
    emotionalStability: number;
  };
  memorySystem: {
    triggeredMemories: string;
    emotionalMemories: string;
    knowledgePriority: string;
  };
  
  // Interaction Dynamics
  dialogueStrategy: {
    initiative: number;
    listeningRatio: number;
    questioningStyle: string;
    responseSpeed: string;
  };
  relationshipDynamics: {
    initialGoodwill: number;
    trustLevel: number;
    intimacyLevel: string;
    powerRelation: string;
  };
  goalOrientation: {
    sceneGoal: string;
    displayedIntent: string;
    hiddenIntent: string;
    successCriteria: string;
  };
  
  // Legacy fields for backward compatibility
  freshness: number;
  satisfaction: number;
  emotion: number;
  dependency: number;
}

// Component props interface
export interface CharStorySceneProps {
  chapter: Chapter;
  chapters: Chapter[];
  updateChapter: (id: number, field: string, value: string | number, subField?: string) => void;
  removeChapter?: (id: number) => void;
  showRemoveButton?: boolean;
  className?: string;
}

const CharStoryScene: React.FC<CharStorySceneProps> = ({ 
  chapter, 
  chapters, 
  updateChapter, 
  removeChapter, 
  showRemoveButton = true,
  className = ""
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    scene: true,
    antecedent: false,
    character: false,
    interaction: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  // Predefined options based on the framework
  const timeOptions = {
    season: ['Spring', 'Summer', 'Autumn', 'Winter', 'Rainy Season', 'Eternal Night', 'Other'],
    timeOfDay: ['Dawn', 'Noon', 'Dusk (Golden Hour)', 'Midnight', 'Blue Hour', 'Other'],
    duration: ['Brief', 'Extended', 'Uncertain', 'Approximately 10 minutes', 'Other'],
    weather: ['Sunny', 'Cloudy', 'Light Rain', 'Thunderstorm', 'Heavy Snow', 'Foggy', 'Other'],
    lighting: ['Bright Natural Light', 'Dim Candlelight', 'Flickering Neon Light', 'Soft Lamp Light', 'Cold Moonlight', 'Other'],
    atmosphere: ['Peaceful', 'Oppressive', 'Nostalgic', 'Romantic', 'Dangerous', 'Mysterious', 'Other'],
    thinkingMode: ['Logically Rigorous', 'Divergent & Jumping', 'Pessimistic', 'Optimistic', 'Dialectical', 'Other'],
    emotionDisplay: ['Calm', 'Joyful', 'Melancholic', 'Anxious', 'Exhausted', 'Excited', 'Other'],
    emotionHidden: ['Suppressed Anger', 'Deep-seated Sadness', 'Latent Fear', 'Unspoken Love', 'Hidden Jealousy', 'Other'],
    questioningStyle: ['Prefers Open-ended Questions', 'Prefers Closed Questions', 'Likes Counter-questions', 'Tends to Guide with Questions', 'Other'],
    responseSpeed: ['Quick Response', 'Thoughtful Response', 'Hesitant Response', 'Other'],
    intimacyLevel: ['Close Friend', 'Confidant', 'Casual Friend', 'Newly Met Stranger', 'Other'],
    powerRelation: ['Equal', 'Dependent on User', 'Protective of User', 'Teacher-Student', 'Other']
  };

  return (
    <div className={`bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700 shadow-lg ${className}`}>
      {/* Chapter Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Brain size={20} className="text-blue-600 dark:text-blue-400" />
          <div>
            <input
              type="text"
              value={chapter.title || `Chapter ${chapter.id}`}
              onChange={(e) => updateChapter(chapter.id, 'title', e.target.value)}
              className="text-xl font-bold text-blue-800 dark:text-blue-200 bg-transparent border-none outline-none focus:bg-white dark:focus:bg-gray-800 rounded px-2 py-1"
              placeholder={`Chapter ${chapter.id}`}
            />
          </div>
        </div>
        {showRemoveButton && removeChapter && chapters.length > 1 && (
          <button
            type="button"
            onClick={() => removeChapter(chapter.id)}
            className="text-red-500 hover:text-red-700 dark:hover:text-red-400 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-all"
          >
            <Trash2 size={18} />
          </button>
        )}
      </div>

      {/* Scene Layer */}
      <div className="mb-6">
        <button
          type="button"
          onClick={() => toggleSection('scene')}
          className="w-full flex items-center justify-between p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-all"
        >
          <div className="flex items-center gap-2">
            <MapPin size={18} className="text-blue-600 dark:text-blue-400" />
            <span className="font-semibold text-blue-800 dark:text-blue-200">Scene Setting</span>
          </div>
          {expandedSections.scene ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </button>
        
        {expandedSections.scene && (
          <div className="mt-4 space-y-4 pl-4 border-l-2 border-blue-200 dark:border-blue-700">
            {/* Environment Description */}
            <div>
              <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                Environment Overview
              </label>
              <textarea
                value={chapter.environment}
                onChange={(e) => updateChapter(chapter.id, 'environment', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all resize-none"
                placeholder="Describe the overall environmental setting for this chapter..."
                maxLength={500}
              />
            </div>

            {/* Time Elements */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  <Clock size={16} className="inline mr-1" />
                  Season
                </label>
                <select
                  value={chapter.timeElements.season}
                  onChange={(e) => updateChapter(chapter.id, 'timeElements', e.target.value, 'season')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100"
                >
                  <option value="">Select season...</option>
                  {timeOptions.season.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Time of Day
                </label>
                <select
                  value={chapter.timeElements.timeOfDay}
                  onChange={(e) => updateChapter(chapter.id, 'timeElements', e.target.value, 'timeOfDay')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100"
                >
                  <option value="">Select time...</option>
                  {timeOptions.timeOfDay.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Duration
                </label>
                <select
                  value={chapter.timeElements.duration}
                  onChange={(e) => updateChapter(chapter.id, 'timeElements', e.target.value, 'duration')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100"
                >
                  <option value="">Select duration...</option>
                  {timeOptions.duration.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Special Date
                </label>
                <input
                  type="text"
                  value={chapter.timeElements.specialDate}
                  onChange={(e) => updateChapter(chapter.id, 'timeElements', e.target.value, 'specialDate')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                  placeholder="e.g., Birthday, Anniversary, Holiday..."
                />
              </div>
            </div>

            {/* Spatial Elements */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Location
                </label>
                <input
                  type="text"
                  value={chapter.spatialElements.location}
                  onChange={(e) => updateChapter(chapter.id, 'spatialElements', e.target.value, 'location')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                  placeholder="e.g., Cozy coffee shop, Empty rooftop..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Atmosphere
                </label>
                <select
                  value={chapter.spatialElements.atmosphere}
                  onChange={(e) => updateChapter(chapter.id, 'spatialElements', e.target.value, 'atmosphere')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100"
                >
                  <option value="">Select atmosphere...</option>
                  {timeOptions.atmosphere.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Key Objects
                </label>
                <input
                  type="text"
                  value={chapter.spatialElements.keyObjects}
                  onChange={(e) => updateChapter(chapter.id, 'spatialElements', e.target.value, 'keyObjects')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                  placeholder="e.g., Unfinished cup of coffee, Crack in the wall, Flickering neon sign..."
                />
              </div>
            </div>

            {/* Environmental Elements */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  <Cloud size={16} className="inline mr-1" />
                  Weather
                </label>
                <select
                  value={chapter.environmentalElements.weather}
                  onChange={(e) => updateChapter(chapter.id, 'environmentalElements', e.target.value, 'weather')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100"
                >
                  <option value="">Select weather...</option>
                  {timeOptions.weather.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Lighting
                </label>
                <select
                  value={chapter.environmentalElements.lighting}
                  onChange={(e) => updateChapter(chapter.id, 'environmentalElements', e.target.value, 'lighting')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100"
                >
                  <option value="">Select lighting...</option>
                  {timeOptions.lighting.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Sounds
                </label>
                <input
                  type="text"
                  value={chapter.environmentalElements.sounds}
                  onChange={(e) => updateChapter(chapter.id, 'environmentalElements', e.target.value, 'sounds')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                  placeholder="e.g., Soft background music, Rain drops..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Scents
                </label>
                <input
                  type="text"
                  value={chapter.environmentalElements.scents}
                  onChange={(e) => updateChapter(chapter.id, 'environmentalElements', e.target.value, 'scents')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                  placeholder="e.g., Coffee aroma, Old book smell, Petrichor..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                  Temperature
                </label>
                <input
                  type="text"
                  value={chapter.environmentalElements.temperature}
                  onChange={(e) => updateChapter(chapter.id, 'environmentalElements', e.target.value, 'temperature')}
                  className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                  placeholder="e.g., Warm and comfortable, Cold..."
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Background Context Layer */}
      <div className="mb-6">
        <button
          type="button"
          onClick={() => toggleSection('antecedent')}
          className="w-full flex items-center justify-between p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-all"
        >
          <div className="flex items-center gap-2">
            <FileText size={18} className="text-purple-600 dark:text-purple-400" />
            <span className="font-semibold text-purple-800 dark:text-purple-200">Background Context</span>
          </div>
          {expandedSections.antecedent ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </button>

        {expandedSections.antecedent && (
          <div className="mt-4 space-y-4 pl-4 border-l-2 border-purple-200 dark:border-purple-700">
            <div>
              <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                Macro History
              </label>
              <textarea
                value={chapter.macroHistory}
                onChange={(e) => updateChapter(chapter.id, 'macroHistory', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
                placeholder="e.g., Just experienced a revolution, In the midst of technological singularity, A key invention never existed..."
                maxLength={300}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                Character's Past
              </label>
              <textarea
                value={chapter.characterPast}
                onChange={(e) => updateChapter(chapter.id, 'characterPast', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
                placeholder="e.g., Noble descendant, Betrayed by lover, War survivor, Just completed a difficult task..."
                maxLength={300}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                Immediate Trigger
              </label>
              <textarea
                value={chapter.immediateTrigger}
                onChange={(e) => updateChapter(chapter.id, 'immediateTrigger', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
                placeholder="e.g., Just argued with someone, Received anonymous letter, Suddenly wants to see user, To solve a mystery..."
                maxLength={200}
              />
            </div>
          </div>
        )}
      </div>

      {/* Character Psychology Layer */}
      <div className="mb-6">
        <button
          type="button"
          onClick={() => toggleSection('character')}
          className="w-full flex items-center justify-between p-3 bg-green-100 dark:bg-green-900/30 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-all"
        >
          <div className="flex items-center gap-2">
            <Brain size={18} className="text-green-600 dark:text-green-400" />
            <span className="font-semibold text-green-800 dark:text-green-200">Character Psychology</span>
          </div>
          {expandedSections.character ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </button>

        {expandedSections.character && (
          <div className="mt-4 space-y-6 pl-4 border-l-2 border-green-200 dark:border-green-700">
            {/* Mental Model */}
            <div className="space-y-4">
              <h6 className="font-semibold text-green-700 dark:text-green-300 flex items-center gap-2">
                <Lightbulb size={16} />
                Mental Model
              </h6>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Core Values
                  </label>
                  <input
                    type="text"
                    value={chapter.mentalModel.coreValues}
                    onChange={(e) => updateChapter(chapter.id, 'mentalModel', e.target.value, 'coreValues')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all"
                    placeholder="e.g., Life above all, Pursuit of truth, Honor above everything..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Thinking Mode
                  </label>
                  <select
                    value={chapter.mentalModel.thinkingMode}
                    onChange={(e) => updateChapter(chapter.id, 'mentalModel', e.target.value, 'thinkingMode')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100"
                  >
                    <option value="">Select thinking mode...</option>
                    {timeOptions.thinkingMode.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Decision Logic
                  </label>
                  <input
                    type="text"
                    value={chapter.mentalModel.decisionLogic}
                    onChange={(e) => updateChapter(chapter.id, 'mentalModel', e.target.value, 'decisionLogic')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all"
                    placeholder="e.g., Based on data analysis, Based on intuition, Based on moral principles, Risk-averse..."
                  />
                </div>
              </div>
            </div>

            {/* Emotional Baseline */}
            <div className="space-y-4">
              <h6 className="font-semibold text-green-700 dark:text-green-300 flex items-center gap-2">
                <Heart size={16} />
                Emotional Baseline
              </h6>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Displayed Emotion
                  </label>
                  <select
                    value={chapter.emotionalBaseline.displayedEmotion}
                    onChange={(e) => updateChapter(chapter.id, 'emotionalBaseline', e.target.value, 'displayedEmotion')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100"
                  >
                    <option value="">Select displayed emotion...</option>
                    {timeOptions.emotionDisplay.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Hidden Emotion
                  </label>
                  <select
                    value={chapter.emotionalBaseline.hiddenEmotion}
                    onChange={(e) => updateChapter(chapter.id, 'emotionalBaseline', e.target.value, 'hiddenEmotion')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100"
                  >
                    <option value="">Select hidden emotion...</option>
                    {timeOptions.emotionHidden.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Emotional Intensity: {chapter.emotionalBaseline.emotionalIntensity}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={chapter.emotionalBaseline.emotionalIntensity}
                    onChange={(e) => updateChapter(chapter.id, 'emotionalBaseline', parseInt(e.target.value), 'emotionalIntensity')}
                    className="w-full h-2 bg-green-200 rounded-lg appearance-none cursor-pointer dark:bg-green-700"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Emotional Stability: {chapter.emotionalBaseline.emotionalStability}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={chapter.emotionalBaseline.emotionalStability}
                    onChange={(e) => updateChapter(chapter.id, 'emotionalBaseline', parseInt(e.target.value), 'emotionalStability')}
                    className="w-full h-2 bg-green-200 rounded-lg appearance-none cursor-pointer dark:bg-green-700"
                  />
                </div>
              </div>
            </div>

            {/* Memory System */}
            <div className="space-y-4">
              <h6 className="font-semibold text-green-700 dark:text-green-300">
                Memory System
              </h6>

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Triggered Memories
                  </label>
                  <input
                    type="text"
                    value={chapter.memorySystem.triggeredMemories}
                    onChange={(e) => updateChapter(chapter.id, 'memorySystem', e.target.value, 'triggeredMemories')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all"
                    placeholder="e.g., Current scene triggers memories of a past event..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Emotional Memories
                  </label>
                  <input
                    type="text"
                    value={chapter.memorySystem.emotionalMemories}
                    onChange={(e) => updateChapter(chapter.id, 'memorySystem', e.target.value, 'emotionalMemories')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all"
                    placeholder="e.g., When sad, recalls past failures..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                    Knowledge Priority
                  </label>
                  <input
                    type="text"
                    value={chapter.memorySystem.knowledgePriority}
                    onChange={(e) => updateChapter(chapter.id, 'memorySystem', e.target.value, 'knowledgePriority')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all"
                    placeholder="e.g., Prioritizes knowledge related to current topic..."
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Interaction Dynamics Layer */}
      <div className="mb-6">
        <button
          type="button"
          onClick={() => toggleSection('interaction')}
          className="w-full flex items-center justify-between p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-all"
        >
          <div className="flex items-center gap-2">
            <Users size={18} className="text-orange-600 dark:text-orange-400" />
            <span className="font-semibold text-orange-800 dark:text-orange-200">Interaction Dynamics</span>
          </div>
          {expandedSections.interaction ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </button>

        {expandedSections.interaction && (
          <div className="mt-4 space-y-6 pl-4 border-l-2 border-orange-200 dark:border-orange-700">
            {/* Dialogue Strategy */}
            <div className="space-y-4">
              <h6 className="font-semibold text-orange-700 dark:text-orange-300 flex items-center gap-2">
                <MessageCircle size={16} />
                Dialogue Strategy
              </h6>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Initiative: {chapter.dialogueStrategy.initiative}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={chapter.dialogueStrategy.initiative}
                    onChange={(e) => updateChapter(chapter.id, 'dialogueStrategy', parseInt(e.target.value), 'initiative')}
                    className="w-full h-2 bg-orange-200 rounded-lg appearance-none cursor-pointer dark:bg-orange-700"
                  />
                  <div className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                    0% = Waits for user questions | 100% = Actively starts topics
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Listening Ratio: {chapter.dialogueStrategy.listeningRatio}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={chapter.dialogueStrategy.listeningRatio}
                    onChange={(e) => updateChapter(chapter.id, 'dialogueStrategy', parseInt(e.target.value), 'listeningRatio')}
                    className="w-full h-2 bg-orange-200 rounded-lg appearance-none cursor-pointer dark:bg-orange-700"
                  />
                  <div className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                    0% = Mostly expressing | 100% = Mostly listening
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Questioning Style
                  </label>
                  <select
                    value={chapter.dialogueStrategy.questioningStyle}
                    onChange={(e) => updateChapter(chapter.id, 'dialogueStrategy', e.target.value, 'questioningStyle')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-orange-300 dark:border-orange-700 rounded-lg text-gray-900 dark:text-gray-100"
                  >
                    <option value="">Select questioning style...</option>
                    {timeOptions.questioningStyle.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Response Speed
                  </label>
                  <select
                    value={chapter.dialogueStrategy.responseSpeed}
                    onChange={(e) => updateChapter(chapter.id, 'dialogueStrategy', e.target.value, 'responseSpeed')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-orange-300 dark:border-orange-700 rounded-lg text-gray-900 dark:text-gray-100"
                  >
                    <option value="">Select response speed...</option>
                    {timeOptions.responseSpeed.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Relationship Dynamics */}
            <div className="space-y-4">
              <h6 className="font-semibold text-orange-700 dark:text-orange-300 flex items-center gap-2">
                <Heart size={16} />
                Relationship Dynamics
              </h6>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Initial Goodwill: {chapter.relationshipDynamics.initialGoodwill}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={chapter.relationshipDynamics.initialGoodwill}
                    onChange={(e) => updateChapter(chapter.id, 'relationshipDynamics', parseInt(e.target.value), 'initialGoodwill')}
                    className="w-full h-2 bg-orange-200 rounded-lg appearance-none cursor-pointer dark:bg-orange-700"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Trust Level: {chapter.relationshipDynamics.trustLevel}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={chapter.relationshipDynamics.trustLevel}
                    onChange={(e) => updateChapter(chapter.id, 'relationshipDynamics', parseInt(e.target.value), 'trustLevel')}
                    className="w-full h-2 bg-orange-200 rounded-lg appearance-none cursor-pointer dark:bg-orange-700"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Intimacy Level
                  </label>
                  <select
                    value={chapter.relationshipDynamics.intimacyLevel}
                    onChange={(e) => updateChapter(chapter.id, 'relationshipDynamics', e.target.value, 'intimacyLevel')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-orange-300 dark:border-orange-700 rounded-lg text-gray-900 dark:text-gray-100"
                  >
                    <option value="">Select intimacy level...</option>
                    {timeOptions.intimacyLevel.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Power Relation
                  </label>
                  <select
                    value={chapter.relationshipDynamics.powerRelation}
                    onChange={(e) => updateChapter(chapter.id, 'relationshipDynamics', e.target.value, 'powerRelation')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-orange-300 dark:border-orange-700 rounded-lg text-gray-900 dark:text-gray-100"
                  >
                    <option value="">Select power relation...</option>
                    {timeOptions.powerRelation.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Goal Orientation */}
            <div className="space-y-4">
              <h6 className="font-semibold text-orange-700 dark:text-orange-300 flex items-center gap-2">
                <Target size={16} />
                Goal Orientation
              </h6>

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Scene Goal
                  </label>
                  <input
                    type="text"
                    value={chapter.goalOrientation.sceneGoal}
                    onChange={(e) => updateChapter(chapter.id, 'goalOrientation', e.target.value, 'sceneGoal')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-orange-300 dark:border-orange-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 transition-all"
                    placeholder="e.g., Seek comfort, Share a secret, Solve a puzzle..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Displayed Intent
                  </label>
                  <input
                    type="text"
                    value={chapter.goalOrientation.displayedIntent}
                    onChange={(e) => updateChapter(chapter.id, 'goalOrientation', e.target.value, 'displayedIntent')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-orange-300 dark:border-orange-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 transition-all"
                    placeholder="e.g., I need your help, I just want to chat..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Hidden Intent
                  </label>
                  <input
                    type="text"
                    value={chapter.goalOrientation.hiddenIntent}
                    onChange={(e) => updateChapter(chapter.id, 'goalOrientation', e.target.value, 'hiddenIntent')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-orange-300 dark:border-orange-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 transition-all"
                    placeholder="e.g., Probe user information through chat, Seek emotional comfort..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-2">
                    Success Criteria
                  </label>
                  <input
                    type="text"
                    value={chapter.goalOrientation.successCriteria}
                    onChange={(e) => updateChapter(chapter.id, 'goalOrientation', e.target.value, 'successCriteria')}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-orange-300 dark:border-orange-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 transition-all"
                    placeholder="e.g., User mood improves, Puzzle is solved, Relationship deepens..."
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CharStoryScene;
