'use client';

import React, { useState } from 'react';
import { Bell, X, <PERSON>, Trophy, ShoppingBag, Target, Wallet, Gift, Star, Heart, Zap, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { useNotifications, Notification } from '@/contexts/NotificationContext';

interface NotificationCenterProps {
  className?: string;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ className = '' }) => {
  const { notifications, unreadCount, markAsRead, markAllAsRead, removeNotification, clearAll } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'achievements' | 'purchases' | 'milestones'>('all');

  const getNotificationIcon = (notification: Notification) => {
    switch (notification.type) {
      case 'achievement':
        return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 'purchase':
        return <ShoppingBag className="w-5 h-5 text-green-500" />;
      case 'milestone':
        return <Target className="w-5 h-5 text-blue-500" />;
      case 'reward':
        return <Gift className="w-5 h-5 text-purple-500" />;
      case 'celebration':
        return <Heart className="w-5 h-5 text-pink-500" />;
      case 'mission':
        return <Zap className="w-5 h-5 text-indigo-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />;
      default:
        return <Bell className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'store':
        return <ShoppingBag className="w-3 h-3" />;
      case 'journey':
        return <Target className="w-3 h-3" />;
      case 'trophies':
        return <Trophy className="w-3 h-3" />;
      case 'wallet':
        return <Wallet className="w-3 h-3" />;
      default:
        return <Bell className="w-3 h-3" />;
    }
  };

  const getNotificationColor = (notification: Notification) => {
    if (!notification.read) {
      switch (notification.type) {
        case 'achievement':
          return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
        case 'purchase':
          return 'border-l-green-500 bg-green-50 dark:bg-green-900/20';
        case 'milestone':
          return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/20';
        case 'reward':
          return 'border-l-purple-500 bg-purple-50 dark:bg-purple-900/20';
        case 'celebration':
          return 'border-l-pink-500 bg-pink-50 dark:bg-pink-900/20';
        case 'mission':
          return 'border-l-indigo-500 bg-indigo-50 dark:bg-indigo-900/20';
        default:
          return 'border-l-gray-500 bg-gray-50 dark:bg-gray-900/20';
      }
    }
    return 'border-l-gray-300 bg-white dark:bg-gray-800';
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !notification.read;
      case 'achievements':
        return notification.type === 'achievement';
      case 'purchases':
        return notification.type === 'purchase';
      case 'milestones':
        return notification.type === 'milestone' || notification.type === 'celebration';
      default:
        return true;
    }
  });

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    if (notification.action) {
      notification.action.onClick();
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
      >
        <Bell className="w-6 h-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification Panel */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-96 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Notifications
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Filter Tabs */}
            <div className="flex gap-2 text-sm">
              {[
                { key: 'all', label: 'All' },
                { key: 'unread', label: 'Unread' },
                { key: 'achievements', label: 'Achievements' },
                { key: 'purchases', label: 'Purchases' },
                { key: 'milestones', label: 'Milestones' }
              ].map(tab => (
                <button
                  key={tab.key}
                  onClick={() => setFilter(tab.key as any)}
                  className={`px-3 py-1 rounded-full transition-colors ${
                    filter === tab.key
                      ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Actions */}
            {notifications.length > 0 && (
              <div className="flex gap-2 mt-3">
                <button
                  onClick={markAllAsRead}
                  className="text-xs text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-200"
                >
                  Mark all read
                </button>
                <button
                  onClick={clearAll}
                  className="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
                >
                  Clear all
                </button>
              </div>
            )}
          </div>

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                <Bell className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>No notifications</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredNotifications.map(notification => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`p-4 border-l-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors ${getNotificationColor(notification)}`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-0.5">
                        {getNotificationIcon(notification)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
                            {notification.title}
                          </h4>
                          <div className="flex items-center gap-2">
                            {notification.metadata?.source && (
                              <div className="flex items-center gap-1 text-gray-400">
                                {getSourceIcon(notification.metadata.source)}
                              </div>
                            )}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                removeNotification(notification.id);
                              }}
                              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500 dark:text-gray-500">
                            {notification.timestamp.toLocaleTimeString()}
                          </span>
                          {notification.action && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                notification.action!.onClick();
                              }}
                              className="text-xs text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-200 font-medium"
                            >
                              {notification.action.label}
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;
