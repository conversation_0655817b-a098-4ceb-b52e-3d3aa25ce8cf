import { generateAllSampleMoments } from '@/lib/mock-data';
import { notFound } from 'next/navigation';
import MomentClientPage from './MomentClientPage';

interface MomentPageProps {
  params: Promise<{
    lang: string;
    momentID: string;
  }>;
}

export default async function MomentPage({ params }: MomentPageProps) {
  const { momentID, lang } = await params;

  const allMoments = generateAllSampleMoments(20);
  const momentData = allMoments.find(m => m.moment.id === momentID);

  if (!momentData) {
    notFound();
  }

  const initialMessages = [
    {
      id: 1,
      text: `This is the story of "${momentData.moment.title}". It's a tale of adventure and mystery.`,
      side: 'left' as const,
      voice: { duration: '0:18', url: '/audio/story1.mp3' }
    },
    {
      id: 2,
      text: "Wow, sounds amazing! Tell me more.",
      side: 'right' as const
    },
    {
      id: 3,
      text: "It begins with a map, old and brittle, pointing to a place that doesn't exist on any modern chart.",
      side: 'left' as const,
      voice: { duration: '0:22', url: '/audio/story2.mp3' }
    },
    {
      id: 4,
      text: "Are you brave enough to see where it leads?",
      side: 'left' as const
    },
    {
      id: 5,
      text: "I'm definitely intrigued! Let's explore this mystery together.",
      side: 'right' as const,
      voice: { duration: '0:10', url: '/audio/user1.mp3' }
    }
  ];

  return <MomentClientPage momentData={momentData} initialMessages={initialMessages} lang={lang} />;
}

export async function generateStaticParams() {
    const allMoments = generateAllSampleMoments(20);
    return allMoments.map(moment => ({
        momentID: moment.moment.id,
    }));
}