# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=alphane_development
DB_USER=postgres
DB_PASSWORD=password
# Alternative: Use connection string
# DATABASE_URL=postgresql://username:password@localhost:5432/alphane_development

# JWT Configuration
JWT_ACCESS_SECRET=your-super-secret-access-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d

# Server Configuration
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# Email Configuration (for OTP)
EMAIL_SERVICE=sendgrid
EMAIL_API_KEY=your-sendgrid-api-key
EMAIL_FROM=<EMAIL>

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379

# AI Service Configuration
AI_SERVICE_URL=https://api.openai.com/v1
AI_API_KEY=your-openai-api-key

# External Services
STORAGE_SERVICE=s3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_BUCKET_NAME=alphane-storage
AWS_REGION=us-east-1