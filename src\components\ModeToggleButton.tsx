'use client';

import React from 'react';
import { User, Palette } from 'lucide-react';
import { useCreatorMode } from '@/contexts/CreatorModeContext';

interface ModeToggleButtonProps {
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

const ModeToggleButton: React.FC<ModeToggleButtonProps> = ({ 
  size = 'md', 
  showLabel = false,
  className = ''
}) => {
  const { mode, toggleMode, isCreatorMode } = useCreatorMode();

  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base'
  };

  const iconSize = {
    sm: 14,
    md: 16,
    lg: 20
  };

  return (
    <button
      onClick={toggleMode}
      className={`
        flex items-center gap-2 rounded-full transition-all duration-200
        ${isCreatorMode 
          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600' 
          : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700'
        }
        ${sizeClasses[size]}
        ${showLabel ? 'px-3 py-1.5' : 'p-1.5'}
        ${className}
      `}
      title={isCreatorMode ? 'Switch to User Mode' : 'Switch to Creator Mode'}
    >
      {isCreatorMode ? (
        <Palette size={iconSize[size]} />
      ) : (
        <User size={iconSize[size]} />
      )}
      
      {showLabel && (
        <span className="font-medium whitespace-nowrap">
          {isCreatorMode ? 'Creator' : 'User'}
        </span>
      )}
    </button>
  );
};

export default ModeToggleButton;
