import { MissionReward, MissionStatus } from '@/components/journey/JourneyMissionsRow';

export interface MockMissionData {
  id: string;
  category: 'interaction' | 'creation' | 'social' | 'memory' | 'store';
  title: string;
  description: string;
  progress: {
    current: number;
    total: number;
  };
  rewards: MissionReward[];
  status: MissionStatus;
  type: 'daily' | 'weekly' | 'monthly';
}

// Mock mission data with realistic completion and claim states
const createMockMissions = (): Record<'daily' | 'weekly' | 'monthly', MockMissionData[]> => {
  return {
    daily: [
      {
        id: 'daily-1',
        category: 'interaction',
        title: 'Morning Conversation',
        description: 'Have a meaningful conversation with any AI character',
        progress: { current: 1, total: 1 },
        rewards: [
          { currency: 'glimmering_dust', amount: 50 },
          { currency: 'joy_crystals', amount: 10 },
          { currency: 'trophy_points', amount: 100 }
        ],
        status: { completed: true, rewardClaimed: true },
        type: 'daily'
      },
      {
        id: 'daily-2',
        category: 'memory',
        title: 'Memory Keeper',
        description: 'Save a new memory to your memory capsule',
        progress: { current: 1, total: 1 },
        rewards: [
          { currency: 'memory_puzzles', amount: 2 },
          { currency: 'star_diamonds', amount: 25 },
          { currency: 'trophy_points', amount: 150 }
        ],
        status: { completed: true, rewardClaimed: false },
        type: 'daily'
      },
      {
        id: 'daily-3',
        category: 'creation',
        title: 'Character Explorer',
        description: 'Interact with a new character for the first time and discover their unique personality, backstory, and special abilities through meaningful conversations',
        progress: { current: 0, total: 1 },
        rewards: [
          { currency: 'glimmering_dust', amount: 75 },
          { currency: 'trophy_points', amount: 200 }
        ],
        status: { completed: false, rewardClaimed: false },
        type: 'daily'
      },
      {
        id: 'daily-4',
        category: 'store',
        title: 'Store Visitor',
        description: 'Visit the store and browse available items',
        progress: { current: 1, total: 1 },
        rewards: [
          { currency: 'star_diamonds', amount: 15 },
          { currency: 'bond_dew', amount: 5 },
          { currency: 'trophy_points', amount: 80 }
        ],
        status: { completed: true, rewardClaimed: true },
        type: 'daily'
      },
      {
        id: 'daily-5',
        category: 'social',
        title: 'Bond Builder',
        description: 'Increase intimacy level with any character',
        progress: { current: 0, total: 1 },
        rewards: [
          { currency: 'bond_dew', amount: 10 },
          { currency: 'trophy_points', amount: 120 }
        ],
        status: { completed: false, rewardClaimed: false },
        type: 'daily'
      },
      {
        id: 'daily-6',
        category: 'creation',
        title: 'Creative Spark',
        description: 'Create or customize character content',
        progress: { current: 0.7, total: 1 },
        rewards: [
          { currency: 'memory_puzzles', amount: 1 },
          { currency: 'glimmering_dust', amount: 40 },
          { currency: 'trophy_points', amount: 180 }
        ],
        status: { completed: false, rewardClaimed: false },
        type: 'daily'
      },
      {
        id: 'daily-7',
        category: 'social',
        title: 'Social Butterfly',
        description: 'Interact with multiple characters in one session to build relationships, share experiences, and create memorable moments that strengthen your bonds with the community',
        progress: { current: 2.3, total: 3 },
        rewards: [
          { currency: 'joy_crystals', amount: 15 },
          { currency: 'trophy_points', amount: 90 }
        ],
        status: { completed: false, rewardClaimed: false },
        type: 'daily'
      },
      {
        id: 'daily-8',
        category: 'memory',
        title: 'Daily Collector',
        description: 'Collect daily login rewards',
        progress: { current: 1, total: 1 },
        rewards: [
          { currency: 'star_diamonds', amount: 20 },
          { currency: 'trophy_points', amount: 50 }
        ],
        status: { completed: true, rewardClaimed: true },
        type: 'daily'
      },
      {
        id: 'daily-9',
        category: 'interaction',
        title: 'Adventure Seeker',
        description: 'Complete any story or adventure mode',
        progress: { current: 0, total: 1 },
        rewards: [
          { currency: 'glimmering_dust', amount: 100 },
          { currency: 'memory_puzzles', amount: 1 },
          { currency: 'trophy_points', amount: 200 }
        ],
        status: { completed: false, rewardClaimed: false },
        type: 'daily'
      }
    ],
    weekly: [
      {
        id: 'weekly-1',
        category: 'interaction',
        title: 'Deep Connection',
        description: 'Reach intimacy level 5 with any character',
        progress: { current: 3, total: 5 },
        rewards: [
          { currency: 'bond_dew', amount: 25 },
          { currency: 'star_diamonds', amount: 100 },
          { currency: 'trophy_points', amount: 350 }
        ],
        status: { completed: false, rewardClaimed: false },
        type: 'weekly'
      },
      {
        id: 'weekly-2',
        category: 'creation',
        title: 'Creative Soul',
        description: 'Create 5 custom memories or scenes',
        progress: { current: 5, total: 5 },
        rewards: [
          { currency: 'memory_puzzles', amount: 10 },
          { currency: 'joy_crystals', amount: 50 }
        ],
        status: { completed: true, rewardClaimed: false },
        type: 'weekly'
      },
      // Add more weekly missions...
      ...Array.from({ length: 16 }, (_, i) => ({
        id: `weekly-${i + 3}`,
        category: ['memory', 'social', 'store', 'interaction', 'creation'][i % 5] as any,
        title: `Weekly Challenge ${i + 3}`,
        description: `Complete weekly objective ${i + 3}`,
        progress: { current: Math.floor(Math.random() * 3), total: Math.floor(Math.random() * 3) + 1 },
        rewards: [
          { currency: ['glimmering_dust', 'joy_crystals', 'memory_puzzles', 'bond_dew', 'star_diamonds'][Math.floor(Math.random() * 5)] as any, amount: 50 + (i * 10) }
        ],
        status: { 
          completed: Math.random() > 0.7, 
          rewardClaimed: Math.random() > 0.5 
        },
        type: 'weekly' as const
      }))
    ],
    monthly: [
      {
        id: 'monthly-1',
        category: 'interaction',
        title: 'Legendary Companion',
        description: 'Maintain a 30-day interaction streak',
        progress: { current: 15, total: 30 },
        rewards: [
          { currency: 'star_diamonds', amount: 500 },
          { currency: 'bond_dew', amount: 100 }
        ],
        status: { completed: false, rewardClaimed: false },
        type: 'monthly'
      },
      {
        id: 'monthly-2',
        category: 'memory',
        title: 'Memory Master',
        description: 'Create 50 memories this month',
        progress: { current: 50, total: 50 },
        rewards: [
          { currency: 'memory_puzzles', amount: 25 },
          { currency: 'joy_crystals', amount: 200 }
        ],
        status: { completed: true, rewardClaimed: true },
        type: 'monthly'
      },
      // Add more monthly missions...
      ...Array.from({ length: 28 }, (_, i) => ({
        id: `monthly-${i + 3}`,
        category: ['creation', 'social', 'store', 'interaction', 'memory'][i % 5] as any,
        title: `Monthly Quest ${i + 3}`,
        description: `Complete monthly objective ${i + 3}`,
        progress: { current: Math.floor(Math.random() * 10), total: Math.floor(Math.random() * 10) + 5 },
        rewards: [
          { currency: ['glimmering_dust', 'joy_crystals', 'memory_puzzles', 'bond_dew', 'star_diamonds'][Math.floor(Math.random() * 5)] as any, amount: 100 + (i * 20) },
          { currency: ['glimmering_dust', 'joy_crystals', 'memory_puzzles', 'bond_dew', 'star_diamonds'][Math.floor(Math.random() * 5)] as any, amount: 50 + (i * 10) }
        ],
        status: { 
          completed: Math.random() > 0.8, 
          rewardClaimed: Math.random() > 0.6 
        },
        type: 'monthly' as const
      }))
    ]
  };
};

// Service functions
export class MockMissionService {
  private static missions = createMockMissions();

  static getMissions(type: 'daily' | 'weekly' | 'monthly'): MockMissionData[] {
    const missions = this.missions[type];
    
    // Sort missions: incomplete first, then completed unclaimed, then completed claimed at bottom
    return missions.sort((a, b) => {
      // Completed and claimed go to bottom
      if (a.status.completed && a.status.rewardClaimed && !(b.status.completed && b.status.rewardClaimed)) {
        return 1;
      }
      if (b.status.completed && b.status.rewardClaimed && !(a.status.completed && a.status.rewardClaimed)) {
        return -1;
      }
      
      // Completed but unclaimed come before completed and claimed
      if (a.status.completed && !a.status.rewardClaimed && (!b.status.completed || b.status.rewardClaimed)) {
        return -1;
      }
      if (b.status.completed && !b.status.rewardClaimed && (!a.status.completed || a.status.rewardClaimed)) {
        return 1;
      }
      
      return 0;
    });
  }

  static claimReward(missionId: string): boolean {
    for (const type of ['daily', 'weekly', 'monthly'] as const) {
      const mission = this.missions[type].find(m => m.id === missionId);
      if (mission && mission.status.completed && !mission.status.rewardClaimed) {
        mission.status.rewardClaimed = true;
        return true;
      }
    }
    return false;
  }

  static updateProgress(missionId: string, progress: { current: number; total: number }): boolean {
    for (const type of ['daily', 'weekly', 'monthly'] as const) {
      const mission = this.missions[type].find(m => m.id === missionId);
      if (mission) {
        mission.progress = progress;
        mission.status.completed = progress.current >= progress.total;
        return true;
      }
    }
    return false;
  }
}

export default MockMissionService;
