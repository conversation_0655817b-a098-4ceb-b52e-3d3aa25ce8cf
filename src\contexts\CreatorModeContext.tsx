'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export type UserMode = 'user' | 'creator';

interface CreatorModeContextType {
  mode: UserMode;
  toggleMode: () => void;
  isCreatorMode: boolean;
}

const CreatorModeContext = createContext<CreatorModeContextType | undefined>(undefined);

export const useCreatorMode = () => {
  const context = useContext(CreatorModeContext);
  if (context === undefined) {
    throw new Error('useCreatorMode must be used within a CreatorModeProvider');
  }
  return context;
};

interface CreatorModeProviderProps {
  children: React.ReactNode;
}

export const CreatorModeProvider: React.FC<CreatorModeProviderProps> = ({ children }) => {
  const [mode, setMode] = useState<UserMode>('user');

  // Load mode from localStorage on mount
  useEffect(() => {
    const savedMode = localStorage.getItem('creatorMode') as UserMode;
    if (savedMode && (savedMode === 'user' || savedMode === 'creator')) {
      setMode(savedMode);
    }
  }, []);

  // Save mode to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('creatorMode', mode);
  }, [mode]);

  const toggleMode = () => {
    setMode(prevMode => prevMode === 'user' ? 'creator' : 'user');
  };

  const isCreatorMode = mode === 'creator';

  const value: CreatorModeContextType = {
    mode,
    toggleMode,
    isCreatorMode,
  };

  return (
    <CreatorModeContext.Provider value={value}>
      {children}
    </CreatorModeContext.Provider>
  );
};
