-- =====================================================
-- Store, Gamification, and Social Features Migration
-- Version: 2.0.0 (Part 2)
-- Description: Store system, enhanced gamification, and social features
-- =====================================================

-- =====================================================
-- PHASE 4: STORE AND COMMERCE SYSTEM
-- =====================================================

-- Create store_products table
CREATE TABLE IF NOT EXISTS store_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sku VARCHAR(100) UNIQUE NOT NULL,
    
    -- Product Basic Information
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN (
        'featured', 'memberships', 'welcome', 'arts', 'memorial', 
        'currency', 'mindfuel', 'character', 'cosmetic', 'subscription'
    )),
    subcategory VARCHAR(50),
    
    -- Product Type and Classification
    product_type VARCHAR(30) NOT NULL CHECK (product_type IN (
        'consumable', 'subscription', 'character', 'cosmetic', 'currency_pack',
        'starter_pack', 'character_bundle', 'premium_subscription', 'memorial_item'
    )),
    
    -- Pricing Structure
    price_data JSONB NOT NULL DEFAULT '{}',
    rewards JSONB DEFAULT '[]',
    
    -- Availability and Limits
    is_available BOOLEAN DEFAULT true,
    purchase_limit INTEGER,
    per_user_limit INTEGER,
    stock_quantity INTEGER,
    
    -- Special Offer Configuration
    is_featured BOOLEAN DEFAULT false,
    is_first_time_only BOOLEAN DEFAULT false,
    is_limited_time BOOLEAN DEFAULT false,
    valid_from TIMESTAMP,
    valid_until TIMESTAMP,
    
    -- Tags and Metadata
    tags TEXT[] DEFAULT '{}',
    special_tag VARCHAR(50) CHECK (special_tag IN (
        'quarterly_special', 'monthly_special', 'weekly_special', 
        'ip_collab', 'new_feature', 'bestseller', 'limited_edition'
    )),
    ip_collab_name VARCHAR(100),
    
    -- Media and Display
    image_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    media_urls JSONB DEFAULT '[]',
    display_order INTEGER DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    
    -- Constraints
    CONSTRAINT valid_price_data CHECK (price_data ? 'base_price' AND price_data ? 'currency'),
    CONSTRAINT valid_dates CHECK (valid_from IS NULL OR valid_until IS NULL OR valid_from < valid_until)
);

-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plan_code VARCHAR(50) UNIQUE NOT NULL,
    
    -- Plan Information
    name VARCHAR(100) NOT NULL,
    description TEXT,
    tier VARCHAR(20) NOT NULL CHECK (tier IN ('standard', 'pass', 'diamond', 'metaverse')),
    
    -- Pricing
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(10) DEFAULT 'USD',
    billing_period VARCHAR(20) NOT NULL CHECK (billing_period IN ('monthly', 'yearly', 'lifetime', 'forever')),
    
    -- Features and Benefits
    features JSONB NOT NULL DEFAULT '[]',
    benefits JSONB NOT NULL DEFAULT '{}',
    daily_interaction_quota JSONB DEFAULT '{}',
    stamina_config JSONB DEFAULT '{}',
    
    -- Status and Availability
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Extend user_currencies table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_currencies' AND column_name = 'star_diamonds') THEN
        ALTER TABLE user_currencies ADD COLUMN star_diamonds INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_currencies' AND column_name = 'joy_crystals') THEN
        ALTER TABLE user_currencies ADD COLUMN joy_crystals INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_currencies' AND column_name = 'glimmering_dust') THEN
        ALTER TABLE user_currencies ADD COLUMN glimmering_dust INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_currencies' AND column_name = 'memory_puzzles') THEN
        ALTER TABLE user_currencies ADD COLUMN memory_puzzles INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_currencies' AND column_name = 'daily_bonus_available') THEN
        ALTER TABLE user_currencies ADD COLUMN daily_bonus_available BOOLEAN DEFAULT true;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_currencies' AND column_name = 'next_daily_bonus') THEN
        ALTER TABLE user_currencies ADD COLUMN next_daily_bonus TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_currencies' AND column_name = 'currency_stats') THEN
        ALTER TABLE user_currencies ADD COLUMN currency_stats JSONB DEFAULT '{}';
    END IF;
END $$;

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_plan_id UUID NOT NULL REFERENCES subscription_plans(id),
    
    -- Subscription Status
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN (
        'active', 'cancelled', 'expired', 'paused', 'pending'
    )),
    
    -- Timing
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    auto_renew BOOLEAN DEFAULT true,
    
    -- Payment Information
    payment_method VARCHAR(50),
    last_payment_date TIMESTAMP,
    next_payment_date TIMESTAMP,
    payment_amount DECIMAL(10,2),
    payment_currency VARCHAR(10),
    
    -- Usage Tracking
    usage_stats JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, subscription_plan_id, started_at)
);

-- Create purchase_history table
CREATE TABLE IF NOT EXISTS purchase_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES store_products(id),
    subscription_plan_id UUID REFERENCES subscription_plans(id),
    
    -- Transaction Details
    transaction_type VARCHAR(30) NOT NULL CHECK (transaction_type IN (
        'product_purchase', 'subscription_purchase', 'currency_purchase', 
        'gift_claim', 'reward_grant', 'refund'
    )),
    
    -- Financial Information
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    payment_method VARCHAR(50),
    payment_provider VARCHAR(50),
    external_transaction_id VARCHAR(200),
    
    -- Purchase Details
    quantity INTEGER DEFAULT 1,
    rewards_granted JSONB DEFAULT '[]',
    
    -- Status and Processing
    status VARCHAR(20) NOT NULL DEFAULT 'completed' CHECK (status IN (
        'pending', 'completed', 'failed', 'cancelled', 'refunded'
    )),
    
    -- Metadata
    purchase_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create promotional_offers table
CREATE TABLE IF NOT EXISTS promotional_offers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Offer Information
    name VARCHAR(200) NOT NULL,
    description TEXT,
    offer_type VARCHAR(30) NOT NULL CHECK (offer_type IN (
        'welcome_gift', 'first_time_purchase', 'seasonal', 'loyalty', 
        'referral', 'comeback', 'milestone'
    )),
    
    -- Targeting
    target_audience JSONB DEFAULT '{}',
    rewards JSONB NOT NULL DEFAULT '[]',
    conditions JSONB DEFAULT '{}',
    
    -- Timing and Availability
    is_active BOOLEAN DEFAULT true,
    starts_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ends_at TIMESTAMP,
    
    -- Usage Tracking
    total_claims INTEGER DEFAULT 0,
    total_budget_used DECIMAL(10,2) DEFAULT 0.00,
    max_budget DECIMAL(10,2),
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id)
);

-- Create user_promotional_claims table
CREATE TABLE IF NOT EXISTS user_promotional_claims (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    promotional_offer_id UUID NOT NULL REFERENCES promotional_offers(id) ON DELETE CASCADE,
    
    -- Claim Details
    rewards_received JSONB NOT NULL DEFAULT '[]',
    claim_value DECIMAL(10,2) DEFAULT 0.00,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'claimed' CHECK (status IN (
        'claimed', 'pending', 'expired', 'revoked'
    )),
    
    -- Metadata
    claim_metadata JSONB DEFAULT '{}',
    claimed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, promotional_offer_id)
);

-- =====================================================
-- PHASE 5: ENHANCED GAMIFICATION SYSTEM
-- =====================================================

-- Extend user_achievements table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'rarity') THEN
        ALTER TABLE user_achievements ADD COLUMN rarity VARCHAR(20) DEFAULT 'bronze' CHECK (rarity IN ('bronze', 'silver', 'gold', 'platinum', 'diamond'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'category') THEN
        ALTER TABLE user_achievements ADD COLUMN category VARCHAR(50);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'progress_current') THEN
        ALTER TABLE user_achievements ADD COLUMN progress_current INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'progress_total') THEN
        ALTER TABLE user_achievements ADD COLUMN progress_total INTEGER DEFAULT 1;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'progress_percentage') THEN
        ALTER TABLE user_achievements ADD COLUMN progress_percentage DECIMAL(5,2) DEFAULT 0.00;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'unlock_conditions') THEN
        ALTER TABLE user_achievements ADD COLUMN unlock_conditions JSONB DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'rewards') THEN
        ALTER TABLE user_achievements ADD COLUMN rewards JSONB DEFAULT '[]';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'is_hidden') THEN
        ALTER TABLE user_achievements ADD COLUMN is_hidden BOOLEAN DEFAULT false;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'unlock_order') THEN
        ALTER TABLE user_achievements ADD COLUMN unlock_order INTEGER;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_achievements' AND column_name = 'achievement_metadata') THEN
        ALTER TABLE user_achievements ADD COLUMN achievement_metadata JSONB DEFAULT '{}';
    END IF;
END $$;

-- Extend user_journey table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_journey' AND column_name = 'journey_type') THEN
        ALTER TABLE user_journey ADD COLUMN journey_type VARCHAR(20) DEFAULT 'main' CHECK (journey_type IN ('main', 'side', 'seasonal', 'special'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_journey' AND column_name = 'track_type') THEN
        ALTER TABLE user_journey ADD COLUMN track_type VARCHAR(20) DEFAULT 'free' CHECK (track_type IN ('free', 'pass', 'diamond'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_journey' AND column_name = 'current_level') THEN
        ALTER TABLE user_journey ADD COLUMN current_level INTEGER DEFAULT 1;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_journey' AND column_name = 'current_xp') THEN
        ALTER TABLE user_journey ADD COLUMN current_xp INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_journey' AND column_name = 'level_xp_required') THEN
        ALTER TABLE user_journey ADD COLUMN level_xp_required INTEGER DEFAULT 100;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_journey' AND column_name = 'total_xp_earned') THEN
        ALTER TABLE user_journey ADD COLUMN total_xp_earned INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_journey' AND column_name = 'milestones_unlocked') THEN
        ALTER TABLE user_journey ADD COLUMN milestones_unlocked INTEGER[] DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_journey' AND column_name = 'rewards_claimed') THEN
        ALTER TABLE user_journey ADD COLUMN rewards_claimed JSONB DEFAULT '[]';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_journey' AND column_name = 'journey_metadata') THEN
        ALTER TABLE user_journey ADD COLUMN journey_metadata JSONB DEFAULT '{}';
    END IF;
END $$;

-- Continue with remaining tables in next migration file...
