// Character creation related type definitions

// Function selection types
export type SelectedFunction = 'flash' | 'customize' | 'import';

// Creation step types
export type Step = 'basics' | 'personality' | 'advanced';

// Next action after creation
export type NextAction = 'chat' | 'story' | null;

// Character form data interface
export interface CharacterFormData {
  // Basic information
  name: string;
  nameOrigin: string; // Name origin or meaning
  description: string; // 角色对外展示的详情（公开描述）
  personality: string; // 角色性格描述
  appearance: string; // 外观描述
  setting: string; // 世界观设定
  settings: string; // 角色设定（BasicStep新增）

  // Image resources
  characterImage: File | null; // Character portrait/illustration
  avatar: File | null; // Avatar (cropped from portrait)
  avatarCrop: {
    x: number;
    y: number;
    width: number;
    height: number;
  } | null; // Avatar crop information

  // Dialogue settings
  greetingMessage: string;
  personalityTags: string[]; // 性格标签数组（用于Additional Tags）

  // Category information
  gender: string;
  pov: string; // Point of view selection
  faction: 'anime' | 'realistic' | 'fantasy' | 'scifi';

  // Publishing settings
  visibility: 'public' | 'unlisted' | 'private';

  // Advanced settings (corresponding to API parameters)
  backgroundStory: string;
  interactionStyleTags: string; // 交互风格标签
  voiceIds: string; // 预留用于后续语音拓展
  facialExpressions: string; // 面部表情描述（新参数）
  bodyLanguage: string; // 肢体语言描述（新参数）
  relationshipWithPlayer: string; // 角色和玩家的关系（新参数）
  initialMemoriesText: string;
  customPromptPrefix: string;

  // Personality subsections
  personalityTraits: string; // Character traits and characteristics
  personalityMind: string; // Mental patterns and thinking style
  personalityEmotion: string; // Emotional patterns and responses

  // Knowledge and skills
  goodAt: string[]; // Things the character is good at
  badAt: string[]; // Things the character is bad at

  // World book files (deprecated - keeping for backward compatibility)
  knowledgeFiles: File[];
}

// Storyboard chapter interface
export interface StoryboardChapter {
  id: number;
  title: string;
  content: string;
  greetingAfter: string;
  updateGreeting: boolean;
}

// Flash generation result interface
export interface FlashGenerationResult {
  name: string;
  personality: string;
  appearance: string;
  behavior: string;
  knowledge: string;
  greetings: string;
  storyboard: string;
}

// Image aspect ratio types
export type AvatarAspectRatio = '1:1' | '5:6' | '5:8';
export type CharacterImageAspectRatio = '5:6' | '5:8';

// Crop stage types
export type CropStage = 'character' | 'avatar';

// Step configuration interface
export interface StepConfig {
  id: Step;
  label: string;
  icon: any; // Lucide icon component
  description: string;
}

// Imported character data interface
export interface ImportedCharacterData {
  name: string;
  description: string;
  appearance: string;
  setting: string;
  characterImage: File | null;
  avatar: File | null;
  avatarCrop: any;
  greetingMessage: string;
  personality: string[];
  gender: string;
  faction: 'anime' | 'realistic' | 'fantasy' | 'scifi';
  visibility: 'public' | 'unlisted' | 'private';
  backgroundStory: string;
  interactionStyleTags: string;
  voiceIds: string;
  initialMemoriesText: string;
  customPromptPrefix: string;
  knowledgeFiles: File[];
}

// Component props interfaces
export interface CreateCharacterClientPageProps {
  lang: string;
}

export interface FunctionSelectorProps {
  selectedFunction: SelectedFunction;
  onFunctionSelect: (func: SelectedFunction) => void;
  lang?: string;
}

export interface FlashCreationProps {
  oneClickPrompt: string;
  setOneClickPrompt: (prompt: string) => void;
  isGenerating: boolean;
  flashResult: FlashGenerationResult | null;
  regenerateCount: number;
  onGenerate: () => void;
  onAccept: () => void;
  onRegenerate: () => void;
}

export interface CustomizeCreationProps {
  currentStep: Step;
  formData: CharacterFormData;
  setFormData: (data: CharacterFormData | ((prev: CharacterFormData) => CharacterFormData)) => void;
  onStepChange: (step: Step) => void;
  onSubmit: () => Promise<string>;
  isStepComplete: (step: Step) => boolean;
  steps: readonly StepConfig[];
  lang?: string;
}

export interface ImportCreationProps {
  importFile: File | null;
  setImportFile: (file: File | null) => void;
  importedCharacters: ImportedCharacterData[];
  setImportedCharacters: (characters: ImportedCharacterData[]) => void;
  onImportFiles: (files: FileList) => void;
  onSelectCharacter: (character: ImportedCharacterData) => void;
  scriptFile: File | null;
  onScriptFileUpload: (file: File) => void;
  lang?: string;
}

export interface ImageUploadProps {
  characterImagePreview: string;
  avatarPreview: string;
  originalImagePreview: string;
  showRatioSelection: boolean;
  showAvatarCrop: boolean;
  showCharacterImageCrop: boolean;
  avatarAspectRatio: AvatarAspectRatio;
  characterImageAspectRatio: CharacterImageAspectRatio;
  cropStage: CropStage;
  onCharacterImageUpload: (file: File) => void;
  onRatioSelectionConfirm: () => void;
  onAvatarCropComplete: (croppedImage: File, cropData: any) => void;
  onCharacterImageCropComplete: (croppedImage: File, cropData: any) => void;
  onCancelCrop: () => void;
  setCharacterImageAspectRatio: (ratio: CharacterImageAspectRatio) => void;
  setShowRatioSelection: (show: boolean) => void;
}
