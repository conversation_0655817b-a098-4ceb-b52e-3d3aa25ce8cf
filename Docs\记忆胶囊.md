### **核心理念**

记忆胶囊的核心是 **“RAG (Retrieval-Augmented Generation)”** 架构。当用户与AI对话时，我们不仅仅依赖于LLM的通用知识和短期上下文，还会：

1. **检索 (Retrieval)**: 智能地从“记忆胶囊”这个外部知识库（也就是您的向量数据库）中，找出与当前对话最相关的记忆片段。  
2. **增强 (Augmented)**: 将这些检索到的相关记忆，作为额外的上下文（Context）注入到发送给大语言模型（LLM）的提示（Prompt）中。  
3. **生成 (Generation)**: LLM基于增强后的Prompt，生成更具个性化、更能体现“记住”了用户的回复。

---

### **第一步：数据库与向量化建设 (Data Layer)**
GCP上的PostgreSQL可以直接使用pgvector插件

**1\. 启用 pgvector 插件**
pgvector 是一个针对PostgreSQL的开源扩展，增加了向量相似度搜索的功能。
* **操作**: 在您的GCP PostgreSQL实例上，执行以下SQL命令来启用扩展：  
  SQL  
  CREATE EXTENSION vector;
* **优势**: 无缝集成，您可以像操作普通列一样操作向量数据，并利用PostgreSQL成熟的事务、备份和权限管理功能。

**2\. 优化数据库表设计**
基于您的 ArchitectureDesign.md 和 TodoList.md 文档，我们对 Memory\_Capsules 表进行优化，增加向量存储列。

* **Memory\_Capsules 表 (记忆胶囊表)**  
  SQL  
  CREATE TABLE Memory\_Capsules (  
      memory\_id\_pk UUID PRIMARY KEY DEFAULT gen\_random\_uuid(),  
      creator\_user\_id UUID NOT NULL REFERENCES Users(\_id) ON DELETE CASCADE,  
      source\_character\_id UUID NOT NULL REFERENCES Characters(\_id) ON DELETE CASCADE,  
      source\_story\_id UUID NOT NULL REFERENCES Stories(\_id) ON DELETE CASCADE,  
      source\_session\_id UUID NOT NULL REFERENCES Chat\_Sessions(\_id) ON DELETE CASCADE,

      capsule\_name VARCHAR(150), \-- 用户自定义的记忆名称，例如 “关于我生日的约定”

      \-- 核心内容  
      dialogue\_context TEXT NOT NULL, \-- 存储的对话原文或上下文摘要  
      summary TEXT,                  \-- 【新增】由LLM生成的对话总结，用于展示  
      embedding VECTOR(1024),         \-- 【核心】存储向量。qwen3-0.6b-emb输出1024维向量

      \-- 元数据  
      tags JSONB,                    \-- 【新增】用户或AI为记忆打的标签，如 '爱好', '工作', '重要'  
      importance\_score NUMERIC(5, 2) DEFAULT 0.5, \-- 【新增】记忆重要性评分 (0.0 \- 1.0)，可由用户或AI评定  
      created\_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT\_TIMESTAMP,  
      updated\_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT\_TIMESTAMP  
  );

  \-- 建立索引以加速向量搜索  
  CREATE INDEX ON Memory\_Capsules USING HNSW (embedding vector\_l2\_ops);   
  \-- 或者使用IVFFlat，适合极大规模数据  
  \-- CREATE INDEX ON Memory\_Capsules USING ivfflat (embedding vector\_l2\_ops) WITH (lists \= 100);


* Memory_Capsule 表关于不同用户之间的信息隔离问题：
  1.分区表
    -- 按用户ID进行分区
    CREATE TABLE Memory_Capsules (
        -- 字段定义
    ) PARTITION BY HASH (creator_user_id);

    -- 创建分区
    CREATE TABLE Memory_Capsules_p0 PARTITION OF Memory_Capsules
        FOR VALUES WITH (modulus 4, remainder 0);
    -- ... 更多分区
  2.分库分表
    ...

* Memory_Capsule_Messages_Selection 表
  dialogue_context,已经具备保存原始对话的功能；映射到原始会话的功能 可以做在Memory_Capsule表中删除这个多余的表，也可以做在这里并保留这个表。
* Memory_Capsule_Likes 表
  用于社交功能，可以直接把对话精选加进记忆胶囊，并把这段对话分享到社区，此表用于储存社交信息


**数据表设计说明：**

**Memory_Capsules 表（记忆胶囊主表）**
这是记忆胶囊功能的核心数据表，用于存储用户与AI对话中的重要记忆片段。表结构包含以下几个关键部分：

- **主键与关联关系**：使用UUID作为主键，通过外键关联用户、角色、故事和聊天会话，确保记忆片段能够准确追溯到具体的对话场景
- **记忆内容存储**：capsule_name作为用户自定义的记忆片段名称便于查找, dialogue_context字段存储原始对话内容，summary字段存储AI生成的对话摘要，tags字段使用JSONB格式存储灵活的标签信息，emotion_marker记录情感标记, importance_score评估记忆重要性
- **向量化支持**：embedding字段存储1024维向量数据，这是实现智能检索的核心，通过pgvector插件支持高效的相似度搜索
- **索引优化**：使用HNSW索引加速向量搜索，支持大规模数据的快速检索

---

### **第二步：后端实现 (Golang)**

您的后端服务需要承担向量生成、存储和检索的关键任务。

**1\. 向量生成服务**

* **模型集成**: qwen3-0.6b-emb 模型需要一个服务来调用。您可以选择：  
  * **云服务**: 如果有云厂商（如阿里云、Hugging Face等）提供了该模型的API服务，直接通过HTTP调用。  
  * **自托管**: 使用Ollama、vLLM等工具在您自己的GCP服务器上部署该模型，并提供一个内部API接口。对于您的Go后端来说，这就像是调用另一个微服务。  
* **Go实现**: 在您的Go后端创建一个EmbeddingService，它包含一个函数：  
  Go  
  // embedding\_service.go  
  func (s \*EmbeddingService) CreateEmbedding(text string) (\[\]float32, error) {  
      // 1\. 调用 qwen3-0.6b-emb 模型服务 (通过HTTP请求)  
      // 2\. 将模型返回的向量数据解析为 \[\]float32  
      // 3\. 返回向量或错误  
  }

**2\. API接口设计**

基于您的APIdocs.md，我们需要新增和修改以下接口：

* **POST /memory/create (创建记忆片段)**  
  * **流程**:  
    1. 接收前端传来的session\_id, message\_ids, character\_id, story\_id, name等信息。  
    2. 从Chat\_Messages表中获取对应的消息内容，拼接成dialogue\_context。  
    3. **【新增】** 调用大语言模型（如Gemini）对dialogue\_context进行总结，生成summary。(tags标签,emotion_marker情感标注,importance_score重要性数值 可以用户自己标 也可以我们后端AI自动标)  
    4. **【新增】** 调用EmbeddingService，将dialogue\_context（或summary，或二者结合）转换为向量embedding。  
    5. 将所有信息存入Memory\_Capsules表。  

* **GET /memory/me (获取我的记忆片段列表)**  
  * 保持原有功能，用于展示用户的记忆胶囊列表。  

* **【新增】 POST /memory/search (智能检索记忆，用于后端测试和/compeletion/chat接口使用)**  
  * **请求 (Body \- JSON):**  
    JSON  
    {  
      "query": "我生日那天的事情",  
      "character\_id": "character\_uuid", // 可选，用于在特定角色记忆中搜索  
      "top\_k": 5 // 返回最相关的 K 条记忆  
    }

  * **流程**:  
    1. 接收用户的搜索查询query。  
    2. 调用EmbeddingService将query转换为查询向量query\_embedding。  
    3. 执行SQL查询，使用pgvector的向量相似度函数（如 vector\_l2\_ops (L2距离), vector\_ip\_ops (内积), vector\_cosine\_ops (余弦相似度)）来查找最相似的记忆。  
       SQL  
       \-- 使用余弦相似度进行搜索  
       SELECT memory\_id\_pk, summary, dialogue\_context, 1 \- (embedding \<=\> :query\_embedding) AS similarity  
       FROM Memory\_Capsules  
       WHERE creator\_user\_id \= :user\_id   
       \-- AND character\_id \= :character\_id \-- 可选  
       ORDER BY embedding \<=\> :query\_embedding  
       LIMIT :top\_k;

       **注意**: \<=\> 是 pgvector 提供的L2距离操作符。对于归一化向量，L2距离和余弦相似度是相关的。  
    4. 返回查询结果给前端。

---

### **第三步：前端实现 (React/Next.js)**

根据您的UI设计文档，前端需要实现以下交互：

**1\. 记忆添加**

* 在聊天界面，提供“存入记忆胶囊”的选项。（具体UI实现待定）  
* 允许用户选择多条相关消息，并可以为这个记忆片段命名（capsule\_name）。  
* 点击确认后，调用后端的POST /memory/create接口。

**2\. 记忆管理**

* 创建一个新的页面 /memory-capsules。  
* 页面顶部有一个搜索框，用于智能检索记忆。输入内容后，调用后端的POST /memory/search接口，并高亮显示最相关的结果。  
* 调用GET /memory/me接口获取数据，展示所有记忆胶囊内容。  
* 待定：页面主体以卡片形式（附加：与记忆抽卡AI画图结合 高光精华对话记录可以抽卡AI画图记录当下）展示所有记忆片段。
* 每个卡片上应显示记忆名称、摘要、关联角色、创建时间，并提供编辑和删除按钮。

---

### **第四步：整合到AI对话流**

这是实现“AI记住你”的核心步骤。

* **修改聊天接口 (/completion/chat) 的后端逻辑**:  
  1. 当接收到用户的消息query时，**首先**调用EmbeddingService生成query\_embedding。  
  2. 使用此query\_embedding在Memory\_Capsules表中进行一次快速的向量检索（例如，取top\_k=5的最相关记忆）。  
  3. 将检索到的记忆的summary或dialogue\_context拼接成一段额外的背景信息。  
  4. **构建新的Prompt**:  
     在对话构建.md中详细解释。 
  5. 将这个增强后的Prompt发送给大语言模型（Gemini 2.5 Flash/Pro）。  
  6. AI生成的回复，就会因为有了这些额外的记忆上下文，而显得更加个性化和贴心。