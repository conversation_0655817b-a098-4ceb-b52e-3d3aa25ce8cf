'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON>, Spark<PERSON>, Zap, Gift, Plus } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface MindSupplyItem {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  quantity: number;
  mindFuelRestore: number;
  bgGradient: string;
  borderColor: string;
  glowColor: string;
}

interface QuickUseItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentMindFuel: number;
  maxMindFuel: number;
  lang: string;
  onUseItem: (item: MindSupplyItem, quantity: number) => void;
  onGoToStore: () => void;
}

const QuickUseItemModal: React.FC<QuickUseItemModalProps> = ({
  isOpen,
  onClose,
  currentMindFuel,
  maxMindFuel,
  lang,
  onUseItem,
  onGoToStore
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [selectedItem, setSelectedItem] = useState<MindSupplyItem | null>(null);
  const [useQuantity, setUseQuantity] = useState(1);

  // 模拟库存道具数据
  const inventoryItems: MindSupplyItem[] = [
    {
      id: 'mind_supply_small',
      name: t('mindFuel.items.types.mindSupplySmall'),
      description: t('mindFuel.items.types.restoreDescription', { amount: 1 }),
      icon: Heart,
      rarity: 'common',
      quantity: 12,
      mindFuelRestore: 1,
      bgGradient: 'from-pink-400 to-red-400',
      borderColor: 'border-pink-300',
      glowColor: 'shadow-pink-500/50'
    },
    {
      id: 'mind_supply_standard',
      name: t('mindFuel.items.types.mindSupplyStandard'),
      description: t('mindFuel.items.types.restoreDescription', { amount: 3 }),
      icon: Heart,
      rarity: 'rare',
      quantity: 5,
      mindFuelRestore: 3,
      bgGradient: 'from-blue-400 to-purple-400',
      borderColor: 'border-blue-300',
      glowColor: 'shadow-blue-500/50'
    },
    {
      id: 'mind_supply_premium',
      name: t('mindFuel.items.types.mindSupplyPremium'),
      description: t('mindFuel.items.types.restoreDescription', { amount: 5 }),
      icon: Heart,
      rarity: 'epic',
      quantity: 2,
      mindFuelRestore: 5,
      bgGradient: 'from-purple-400 to-indigo-400',
      borderColor: 'border-purple-300',
      glowColor: 'shadow-purple-500/50'
    },
    {
      id: 'mind_supply_perfect',
      name: t('mindFuel.items.types.mindSupplyPerfect'),
      description: t('mindFuel.items.types.fullRestoreDescription'),
      icon: Sparkles,
      rarity: 'legendary',
      quantity: 1,
      mindFuelRestore: 999,
      bgGradient: 'from-yellow-400 via-orange-400 to-red-400',
      borderColor: 'border-yellow-300',
      glowColor: 'shadow-yellow-500/50'
    }
  ];

  const getRarityConfig = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return {
          label: t('mindFuel.items.rarity.common'),
          textColor: 'text-gray-600 dark:text-gray-400',
          bgColor: 'bg-gray-100 dark:bg-gray-700'
        };
      case 'rare':
        return {
          label: t('mindFuel.items.rarity.rare'),
          textColor: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-100 dark:bg-blue-900/30'
        };
      case 'epic':
        return {
          label: t('mindFuel.items.rarity.epic'),
          textColor: 'text-purple-600 dark:text-purple-400',
          bgColor: 'bg-purple-100 dark:bg-purple-900/30'
        };
      case 'legendary':
        return {
          label: t('mindFuel.items.rarity.legendary'),
          textColor: 'text-yellow-600 dark:text-yellow-400',
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/30'
        };
      default:
        return {
          label: t('mindFuel.items.rarity.common'),
          textColor: 'text-gray-600',
          bgColor: 'bg-gray-100'
        };
    }
  };

  const getMaxUsableQuantity = (item: MindSupplyItem) => {
    if (item.mindFuelRestore === 999) return 1; // 完全恢复只能用1个
    const mindFuelNeeded = maxMindFuel - currentMindFuel;
    const maxByMindFuel = Math.ceil(mindFuelNeeded / item.mindFuelRestore);
    return Math.min(item.quantity, maxByMindFuel);
  };

  const handleUseItem = (item: MindSupplyItem) => {
    if (item.quantity <= 0) return;
    setSelectedItem(item);
    setUseQuantity(1);
  };

  const confirmUseItem = () => {
    if (!selectedItem) return;
    onUseItem(selectedItem, useQuantity);
    setSelectedItem(null);
    setUseQuantity(1);
    onClose();
  };

  const availableItems = inventoryItems.filter(item => item.quantity > 0);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[70] flex items-center justify-center">
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={onClose} />
      <div className="relative bg-white dark:bg-gray-800 rounded-3xl p-6 mx-4 max-w-md w-full max-h-[80vh] overflow-y-auto shadow-2xl">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            {t('mindFuel.items.title')}
          </h3>
          <button
            onClick={onClose}
            className="p-2 bg-gray-100 dark:bg-gray-700 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 当前状态 */}
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-2xl p-4 mb-6">
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">{t('mindFuel.currentMindFuel')}</p>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {currentMindFuel} / {maxMindFuel}
            </p>
          </div>
        </div>

        {/* 道具列表 */}
        {availableItems.length > 0 ? (
          <div className="space-y-3 mb-6">
            {availableItems.map((item) => {
              const IconComponent = item.icon;
              const rarityConfig = getRarityConfig(item.rarity);
              const maxUsable = getMaxUsableQuantity(item);
              const canUse = maxUsable > 0 && currentMindFuel < maxMindFuel;

              return (
                <div
                  key={item.id}
                  className={`border-2 ${item.borderColor} rounded-2xl p-4 transition-all duration-300 ${
                    canUse ? 'hover:scale-105 cursor-pointer' : 'opacity-50 cursor-not-allowed'
                  }`}
                  onClick={() => canUse && handleUseItem(item)}
                >
                  <div className="flex items-center gap-4">
                    {/* 道具图标 */}
                    <div className={`w-12 h-12 bg-gradient-to-br ${item.bgGradient} rounded-xl flex items-center justify-center ${item.glowColor} shadow-lg`}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>

                    {/* 道具信息 */}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-bold text-gray-900 dark:text-gray-100">
                          {item.name}
                        </h4>
                        <span className={`text-xs px-2 py-1 rounded-full ${rarityConfig.bgColor} ${rarityConfig.textColor}`}>
                          {rarityConfig.label}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {item.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {t('mindFuel.items.inventory.owned', { count: item.quantity })}
                        </span>
                        {canUse && (
                          <span className="text-xs text-green-600 dark:text-green-400">
                            {t('mindFuel.items.inventory.canUse', { count: maxUsable })}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* 使用按钮 */}
                    <div className="text-center">
                      {canUse ? (
                        <button className={`px-4 py-2 bg-gradient-to-r ${item.bgGradient} text-white text-sm font-bold rounded-lg hover:scale-105 transition-transform`}>
                          {t('mindFuel.items.use')}
                        </button>
                      ) : (
                        <div className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-400 text-sm font-bold rounded-lg">
                          {currentMindFuel >= maxMindFuel ? t('mindFuel.items.alreadyFull') : t('mindFuel.items.cannotUse')}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8 mb-6">
            <Heart className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400 mb-2">{t('mindFuel.items.noItems')}</p>
            <p className="text-sm text-gray-400 dark:text-gray-500">
              {t('mindFuel.items.goToBuy')}
            </p>
          </div>
        )}

        {/* 去商店按钮 */}
        <button
          onClick={onGoToStore}
          className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 rounded-xl font-medium hover:scale-105 transition-transform flex items-center justify-center gap-2"
        >
          <Gift className="w-5 h-5" />
          {t('mindFuel.items.goToBuy')}
        </button>

        {/* 使用确认弹窗 */}
        {selectedItem && (
          <div className="fixed inset-0 z-60 flex items-center justify-center">
            <div className="absolute inset-0 bg-black/50" onClick={() => setSelectedItem(null)} />
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl">
              <div className="text-center">
                {/* 道具图标 */}
                <div className={`w-16 h-16 bg-gradient-to-br ${selectedItem.bgGradient} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                  <selectedItem.icon className="w-8 h-8 text-white" />
                </div>

                <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  {t('mindFuel.items.confirmUse', { itemName: selectedItem.name })}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {selectedItem.description}
                </p>

                {/* 数量选择 */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {t('mindFuel.items.quantity')}
                  </label>
                  <div className="flex items-center justify-center gap-3">
                    <button
                      onClick={() => setUseQuantity(Math.max(1, useQuantity - 1))}
                      className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center"
                    >
                      -
                    </button>
                    <span className="text-xl font-bold text-gray-900 dark:text-gray-100 min-w-[3rem] text-center">
                      {useQuantity}
                    </span>
                    <button
                      onClick={() => setUseQuantity(Math.min(getMaxUsableQuantity(selectedItem), useQuantity + 1))}
                      className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center"
                    >
                      +
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    最多可用 {getMaxUsableQuantity(selectedItem)} 个
                  </p>
                </div>

                {/* 效果预览 */}
                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-3 mb-6">
                  <p className="text-sm text-green-700 dark:text-green-400">
                    思维燃料: {currentMindFuel} → {Math.min(maxMindFuel, currentMindFuel + (selectedItem.mindFuelRestore === 999 ? maxMindFuel : selectedItem.mindFuelRestore * useQuantity))}
                  </p>
                </div>

                {/* 按钮 */}
                <div className="flex gap-3">
                  <button
                    onClick={() => setSelectedItem(null)}
                    className="flex-1 py-3 px-4 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium"
                  >
                    {t('mindFuel.items.cancel')}
                  </button>
                  <button
                    onClick={confirmUseItem}
                    className={`flex-1 py-3 px-4 bg-gradient-to-r ${selectedItem.bgGradient} text-white rounded-xl font-medium hover:scale-105 transition-transform`}
                  >
                    {t('mindFuel.items.confirm')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuickUseItemModal;
