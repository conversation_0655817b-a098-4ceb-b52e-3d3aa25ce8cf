'use client';

import React, { useState } from 'react';
import { Target, Clock, MapPin, Cloud, Sun, Volume2, Thermometer, ArrowLeft, ArrowRight, ChevronDown, ChevronUp, History, User, Zap, Send } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryFormData, StoryStep, StoryChapter } from '@/types/story-creation';
import StoryFlow from './StoryFlow';

interface ObjectivesStepProps {
  formData: StoryFormData;
  setFormData: React.Dispatch<React.SetStateAction<StoryFormData>>;
  lang: string;
  onStepChange?: (step: StoryStep) => void;
  onSubmit?: () => void;
  selectedChapter: string | null;
  setSelectedChapter: (chapterId: string | null) => void;
}

const ObjectivesStep: React.FC<ObjectivesStepProps> = ({
  formData,
  setFormData,
  lang,
  onStepChange,
  onSubmit,
  selectedChapter,
  setSelectedChapter
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    scene: true,
    antecedent: false
  });

  const selectedChapterData = formData.chapters.find(c => c.id === selectedChapter);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  const updateChapter = (chapterId: string, updates: Partial<StoryChapter>) => {
    setFormData(prev => ({
      ...prev,
      chapters: prev.chapters.map(chapter =>
        chapter.id === chapterId ? { ...chapter, ...updates } : chapter
      )
    }));
  };

  const updateTimeElements = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      updateChapter(chapterId, {
        timeElements: {
          season: '',
          timeOfDay: '',
          duration: '',
          specialDate: '',
          ...chapter.timeElements,
          [field]: value
        }
      });
    }
  };

  const updateSpatialElements = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      updateChapter(chapterId, {
        spatialElements: {
          location: '',
          atmosphere: '',
          keyObjects: '',
          ...chapter.spatialElements,
          [field]: value
        }
      });
    }
  };

  const updateEnvironmentalElements = (chapterId: string, field: string, value: string) => {
    const chapter = formData.chapters.find(c => c.id === chapterId);
    if (chapter) {
      updateChapter(chapterId, {
        environmentalElements: {
          weather: '',
          lighting: '',
          sounds: '',
          scents: '',
          temperature: '',
          ...chapter.environmentalElements,
          [field]: value
        }
      });
    }
  };

  const getChapterDisplayName = (chapter: StoryChapter) => {
    if (chapter.chapterType === 'main') {
      return `${chapter.order}`;
    } else {
      return `${chapter.order}${chapter.branchLetter || ''}`;
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-blue-800 dark:text-blue-200 mb-2 flex items-center justify-center gap-2">
          <Target className="w-8 h-8" />
          {t('storyCreation.steps.objectives.title')} - {t('storyCreation.steps.objectives.description')}
        </h2>
        <p className="text-blue-600 dark:text-blue-300">
          {t('storyCreation.steps.objectives.selectChapterDescription')}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-10 gap-6">
        {/* Left Side: Story Flow (40% width on desktop, full width on mobile) */}
        <div className="lg:col-span-4 space-y-4">
          <StoryFlow
            chapters={formData.chapters}
            selectedChapter={selectedChapter}
            onChapterSelect={setSelectedChapter}
            lang={lang}
            title={t('storyCreation.storyFlow.title')}
            icon={<Target className="w-5 h-5" />}
            accentColor="blue"
            showAddButton={false}
            showBranchButtons={false}
            showRemoveButtons={false}
          />
        </div>

        {/* Right Side: Objectives Editor (60% width on desktop, full width on mobile) */}
        <div className="lg:col-span-6 space-y-4">
          {selectedChapterData ? (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-500" />
                  {t('storyCreation.steps.chapters.chapterTitle')} {getChapterDisplayName(selectedChapterData)} - {t('storyCreation.steps.objectives.title')}
                </h3>
              </div>

              <div className="space-y-6">
                {/* Scene Layer */}
                <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
                  <button
                    onClick={() => toggleSection('scene')}
                    className="w-full flex items-center justify-between text-left mb-4"
                  >
                    <h4 className="text-xl font-bold text-blue-800 dark:text-blue-200 flex items-center gap-2">
                      <MapPin className="w-6 h-6" />
                      {t('storyCreation.steps.objectives.scene.title')}
                    </h4>
                    {expandedSections.scene ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                  </button>
                  
                  {expandedSections.scene && (
                    <div className="space-y-4">
                      {/* Environment Overview */}
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                          {t('storyCreation.steps.objectives.scene.environmentalElements')}
                        </label>
                        <textarea
                          value={selectedChapterData.environment || ''}
                          onChange={(e) => updateChapter(selectedChapterData.id, { environment: e.target.value })}
                          rows={3}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all resize-none"
                          placeholder={t('storyCreation.steps.objectives.scene.atmospherePlaceholder')}
                          maxLength={500}
                        />
                      </div>

                      {/* Time Elements */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            <Clock className="inline w-4 h-4 mr-1" />
                            {t('storyCreation.steps.objectives.scene.season')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.timeElements?.season || ''}
                            onChange={(e) => updateTimeElements(selectedChapterData.id, 'season', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder={`${t('storyCreation.steps.objectives.scene.seasons.spring')}, ${t('storyCreation.steps.objectives.scene.seasons.winter')}...`}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            <Sun className="inline w-4 h-4 mr-1" />
                            {t('storyCreation.steps.objectives.scene.timeOfDay')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.timeElements?.timeOfDay || ''}
                            onChange={(e) => updateTimeElements(selectedChapterData.id, 'timeOfDay', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder={`${t('storyCreation.steps.objectives.scene.timesOfDay.morning')}, ${t('storyCreation.steps.objectives.scene.timesOfDay.evening')}...`}
                          />
                        </div>
                      </div>

                      {/* Spatial Elements */}
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            {t('storyCreation.steps.objectives.scene.location')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.spatialElements?.location || ''}
                            onChange={(e) => updateSpatialElements(selectedChapterData.id, 'location', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder={t('storyCreation.steps.objectives.scene.locationPlaceholder')}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            {t('storyCreation.steps.objectives.scene.atmosphere')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.spatialElements?.atmosphere || ''}
                            onChange={(e) => updateSpatialElements(selectedChapterData.id, 'atmosphere', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder={t('storyCreation.steps.objectives.scene.atmospherePlaceholder')}
                          />
                        </div>
                      </div>

                      {/* Environmental Elements */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            <Cloud className="inline w-4 h-4 mr-1" />
                            {t('storyCreation.steps.objectives.scene.weather')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.environmentalElements?.weather || ''}
                            onChange={(e) => updateEnvironmentalElements(selectedChapterData.id, 'weather', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder={`${t('storyCreation.steps.objectives.scene.weather.sunny')}, ${t('storyCreation.steps.objectives.scene.weather.rainy')}...`}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            <Volume2 className="inline w-4 h-4 mr-1" />
                            {t('storyCreation.steps.objectives.scene.environmentalElements')}
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.environmentalElements?.sounds || ''}
                            onChange={(e) => updateEnvironmentalElements(selectedChapterData.id, 'sounds', e.target.value)}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder="例如：鸟儿chirping, 城市noise..."
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Antecedent Layer */}
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
                  <button
                    onClick={() => toggleSection('antecedent')}
                    className="w-full flex items-center justify-between text-left mb-4"
                  >
                    <h4 className="text-xl font-bold text-green-800 dark:text-green-200 flex items-center gap-2">
                      <History className="w-6 h-6" />
                      {t('storyCreation.steps.objectives.antecedent.title')}
                    </h4>
                    {expandedSections.antecedent ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                  </button>

                  {expandedSections.antecedent && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                          <History className="inline w-4 h-4 mr-1" />
                          {t('storyCreation.steps.objectives.antecedent.macroHistory')}
                        </label>
                        <textarea
                          value={selectedChapterData.macroHistory || ''}
                          onChange={(e) => updateChapter(selectedChapterData.id, { macroHistory: e.target.value })}
                          rows={3}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all resize-none"
                          placeholder={t('storyCreation.steps.objectives.antecedent.macroHistoryPlaceholder')}
                          maxLength={500}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                          <User className="inline w-4 h-4 mr-1" />
                          {t('storyCreation.steps.objectives.antecedent.characterPast')}
                        </label>
                        <textarea
                          value={selectedChapterData.characterPast || ''}
                          onChange={(e) => updateChapter(selectedChapterData.id, { characterPast: e.target.value })}
                          rows={3}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all resize-none"
                          placeholder={t('storyCreation.steps.objectives.antecedent.characterPastPlaceholder')}
                          maxLength={500}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                          <Zap className="inline w-4 h-4 mr-1" />
                          {t('storyCreation.steps.objectives.antecedent.immediateTrigger')}
                        </label>
                        <textarea
                          value={selectedChapterData.immediateTrigger || ''}
                          onChange={(e) => updateChapter(selectedChapterData.id, { immediateTrigger: e.target.value })}
                          rows={2}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all resize-none"
                          placeholder={t('storyCreation.steps.objectives.antecedent.immediateTriggerPlaceholder')}
                          maxLength={300}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Chapter Navigation */}
              <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={() => onStepChange?.('storyFlow')}
                  className="flex items-center gap-2 px-6 py-3 border-2 border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-300 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 font-medium"
                >
                  <ArrowLeft size={18} />
                  {t('storyCreation.storyFlow.navigation.previous')}
                </button>

                <button
                  type="button"
                  onClick={() => onStepChange?.('objectivesSubjectives')}
                  className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-xl hover:from-blue-600 hover:to-cyan-600 transition-all duration-300 font-medium shadow-lg hover:shadow-xl"
                >
                  {t('storyCreation.storyFlow.navigation.continueToObjectives')}
                  <ArrowRight size={18} />
                </button>
              </div>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 lg:p-8 border border-gray-200 dark:border-gray-700 text-center">
              <Target className="w-12 h-12 lg:w-16 lg:h-16 mx-auto mb-3 lg:mb-4 text-gray-400" />
              <p className="text-sm lg:text-base text-gray-500 dark:text-gray-400">
                {t('storyCreation.steps.objectives.selectChapterDescription')}
              </p>
            </div>
          )}

          {/* Unified Navigation */}
          <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => window.history.back()}
              className="flex items-center gap-2 px-6 py-3 border-2 border-blue-300 dark:border-blue-700 text-blue-700 dark:text-blue-300 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 font-medium"
            >
              <ArrowLeft size={18} />
              {t('storyCreation.storyFlow.navigation.backToWorldSetting')}
            </button>

            <button
              type="button"
              onClick={onSubmit}
              disabled={formData.chapters.length === 0}
              className="flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
            >
              <Send size={20} />
              {t('storyCreation.buttons.createStory')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ObjectivesStep;
