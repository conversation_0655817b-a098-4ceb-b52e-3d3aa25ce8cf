# Customize模式每个UI框的字段详细对比分析

## 🎯 **重要发现**

根据最新前端代码分析，发现前端customize模式中的字段使用仍存在以下问题：

### 🚨 **字段用途不匹配的问题**

1. **`voiceIds` 字段误用**
   - **前端用途**: "Facial Expressions" (面部表情)
   - **后端API**: `voice_ids` (语音ID)
   - **问题**: 完全不匹配的用途
   - **当前状态**: 前端代码已添加`facialExpressions`字段，但仍然在UI中使用`voiceIds`

2. **`setting` 字段用途混乱**
   - **前端用途**: "Relationship with Player" (与玩家的关系)
   - **后端API**: `setting` (角色的世界观、背景等设定)
   - **问题**: 用途不匹配
   - **当前状态**: 前端代码已添加`relationshipWithPlayer`字段，但仍然在UI中使用`setting`

3. **`interactionStyleTags`字段复合存储**
   - **前端用途**: 存储体态语言和标签
   - **问题**: 使用字符串拼接存储多种数据 `"BODY_LANG:{bodyLanguage}|TAGS:{tags}"`
   - **当前状态**: 前端已添加`bodyLanguage`字段，但UI仍通过拆分`interactionStyleTags`字段获取数据

## 📋 **每个UI框的详细对比**

### **BasicsStep 中的UI框**

| UI框名称 | 前端字段 | 后端API参数 | 状态 | 说明 |
|---------|---------|------------|------|------|
| Character Name | `name` | `name` | ✅ 完全匹配 | 角色名称 |
| Name Origin | `nameOrigin` | **❌ 缺失** | ❌ 需要后端添加 | 名字的起源/含义 |
| Gender | `gender` | `gender` | ✅ 完全匹配 | 性别选择 |
| POV | `pov` | **❌ 缺失** | ❌ 需要后端添加 | 视角选择 |
| Character Images | `characterImage` | `image` | ✅ 匹配 | 角色立绘 |
| Character Images | `avatar` | `avatar` | ✅ 完全匹配 | 头像文件 |
| Appearance Description | `appearance` | `appearance` | ✅ 已添加 | 外观描述 |
| Personality Traits | `personalityTraits` | **❌ 缺失** | ❌ 需要后端添加 | 角色特质描述 |
| Personality Mind | `personalityMind` | **❌ 缺失** | ❌ 需要后端添加 | 思维方式描述 |
| Personality Emotion | `personalityEmotion` | **❌ 缺失** | ❌ 需要后端添加 | 情感模式描述 |
| Character Details | `description` | `description` | ✅ 完全匹配 | 角色对外展示的详情 |
| Character Settings | `settings` | `settings` | ✅ 已添加 | 角色设定 |
| Additional Tags | `personalityTags` (array) | `personality_tags` (JSON) | ✅ 需转换 | 性格标签 |

> **注意**: 前端代码已添加了`personality`字段，但在BasicsStep中分为三个子字段`personalityTraits`、`personalityMind`和`personalityEmotion`，需要在提交时合并或分别提交。

### **PersonalityStep 中的UI框**

| UI框名称 | 前端字段 | 后端API参数 | 状态 | UI实际情况 | 说明 |
|---------|---------|------------|------|----------|------|
| Default Greeting Message | `greetingMessage` | `greeting_message` | ✅ 匹配 | 正确使用 | 问候语 |
| Default Greeting Message | `greetingMessage` | `opening_message_templates` | ✅ 需转换 | 正确使用 | 开场白模板 |
| Speech Style | `customPromptPrefix` | `custom_prompt_prefix` | ✅ 完全匹配 | 正确使用 | 说话风格 |
| **Facial Expressions** | `facialExpressions` | `facial_expressions` | ✅ 已添加 | **仍使用`voiceIds`** | 面部表情描述 |
| **Body Language** | `bodyLanguage` | `body_language` | ✅ 已添加 | **从`interactionStyleTags`中提取** | 肢体语言描述 |
| Knowledge Base | `initialMemoriesText` | `initial_memories_text` | ✅ 完全匹配 | 正确使用 | 知识库内容 |
| Import Knowledge Files | `knowledgeFiles` | `knowledge_files` | ✅ 完全匹配 | 正确使用 | 知识文件 |
| Good At | `goodAt` | **❌ 缺失** | ❌ 需要后端添加 | 正确使用数组 | 角色擅长的事情 |
| Bad At | `badAt` | **❌ 缺失** | ❌ 需要后端添加 | 正确使用数组 | 角色不擅长的事情 |
| **Relationship with Player** | `relationshipWithPlayer` | `relationship_with_player` | ✅ 已添加 | **仍使用`setting`** | 与玩家的关系 |
| Important Past Experiences | `backgroundStory` | `background_story` | ✅ 完全匹配 | 正确使用 | 背景故事 |
| Visibility | `visibility` | `visibility` | ✅ 完全匹配 | 正确使用 | 可见性设置 |

> **注意**: 
> 1. UI中仍然使用`voiceIds`字段保存面部表情数据，而不是使用已添加的`facialExpressions`字段
> 2. UI中仍然使用`setting`字段保存与玩家的关系数据，而不是使用已添加的`relationshipWithPlayer`字段
> 3. 体态语言存储在`interactionStyleTags`字段的特殊格式中："BODY_LANG:{bodyLanguage}|TAGS:{tags}"

### **AdvancedStep 中的UI框**
主要是故事板功能，大部分是前端特有，不对应后端API。

## 🔧 **最新状态分析与修正方案**

### 1. **前端已添加的字段**
✅ 前端FormData中已添加以下字段，但部分尚未在UI中正确使用：
```
appearance              - 外观描述 (已在UI中正确使用)
personality             - 角色性格描述 (未直接使用，分为3个子字段)
personalityTraits       - 角色特质描述 
personalityMind         - 思维方式描述
personalityEmotion      - 情感模式描述
settings                - 角色设定 (已在UI中正确使用)
facialExpressions       - 面部表情描述 (定义了但UI仍使用voiceIds)
bodyLanguage            - 肢体语言描述 (定义了但UI从interactionStyleTags中提取)
relationshipWithPlayer  - 与玩家的关系 (定义了但UI仍使用setting)
goodAt                  - 擅长的事情 (数组)
badAt                   - 不擅长的事情 (数组)
pov                     - 视角选择
nameOrigin              - 名字的起源/含义
```

### 2. **API调用中已添加的字段**
在提交表单时，前端已在API调用中添加以下新参数：
```
personality             - 已添加到API调用
appearance              - 已添加到API调用
settings                - 已添加到API调用
facial_expressions      - 已添加到API调用
body_language           - 已添加到API调用
relationship_with_player- 已添加到API调用
```

### 3. **需要后端添加的新参数**

```
personality_traits      | string | 否 | 角色特质描述
personality_mind        | string | 否 | 思维方式描述
personality_emotion     | string | 否 | 情感模式描述
name_origin             | string | 否 | 名字的起源/含义
pov                     | string | 否 | 视角选择
good_at                 | array  | 否 | 擅长的事情
bad_at                  | array  | 否 | 不擅长的事情
```

**注意**: `voiceIds` 字段保留用于后续语音拓展功能，目前前端应停止将其用于面部表情描述。

### 4. **字段映射修正建议**

| 前端UI框 | 当前使用字段 | 应该使用的字段 | 对应后端参数 |
|---------|-------------|--------------|------------|
| Facial Expressions | `voiceIds` | `facialExpressions` | `facial_expressions` |
| Body Language | 从`interactionStyleTags`提取 | `bodyLanguage` | `body_language` |
| Relationship with Player | `setting` | `relationshipWithPlayer` | `relationship_with_player` |
| World Setting | 无专用字段 | 新增`worldSetting` | `setting` |
| Voice Settings | 无，被错用 | `voiceIds` | `voice_ids` |

### 5. **字段转换实现**

```javascript
// 提交前需处理的字段合并逻辑
// 1. 合并性格三个子字段
const personality = [
  formData.personalityTraits.trim(),
  formData.personalityMind.trim(),
  formData.personalityEmotion.trim()
].filter(Boolean).join('\n\n');
submitData.append('personality', personality);

// 2. 性格标签转换
const personalityTags = JSON.stringify(
  formData.personalityTags.map(trait => ({ tag_name: trait, weight: 0.8 }))
);
submitData.append('personality_tags', personalityTags);

// 3. 开场白模板转换  
const openingTemplates = JSON.stringify([{
  template_id: 'default',
  content: formData.greetingMessage,
  priority: 1,
  conditions: null
}]);
submitData.append('opening_message_templates', openingTemplates);

// 4. 将擅长/不擅长事项转为JSON格式
if (formData.goodAt.length > 0) {
  submitData.append('good_at', JSON.stringify(formData.goodAt));
}
if (formData.badAt.length > 0) {
  submitData.append('bad_at', JSON.stringify(formData.badAt));
}

// 5. 确保使用正确的字段映射
submitData.append('facial_expressions', formData.facialExpressions || formData.voiceIds); // 临时兼容
submitData.append('relationship_with_player', formData.relationshipWithPlayer || formData.setting); // 临时兼容
```

## 📝 **当前状态总结与下一步建议**

### 🟢 **已完成的前端工作**

1. ✅ **新增了正确数据字段**：
   - 已在FormData中添加了所有需要的字段，包括`appearance`, `personality`, `settings`等
   - 已在API提交过程中添加所有新字段的映射

2. ✅ **准备了合适的数据结构**：
   - 为性格细分添加了三个子字段`personalityTraits`, `personalityMind`, `personalityEmotion`
   - 为擅长/不擅长添加了数组字段`goodAt`, `badAt`

3. ✅ **优化了提交逻辑**：
   - 添加了字段转换逻辑，确保数据符合API需求
   - 保持了向后兼容，避免因API更新导致功能中断

### 🟠 **存在的问题**

1. **UI组件与数据字段不匹配**：
   - Facial Expressions UI仍使用`voiceIds`而非`facialExpressions`
   - Body Language UI仍从`interactionStyleTags`中提取而非直接使用`bodyLanguage`
   - Relationship with Player UI仍使用`setting`而非`relationshipWithPlayer`

2. **复合数据存储**：
   - 仍在使用字符串拼接存储多种数据：`"BODY_LANG:{bodyLanguage}|TAGS:{tags}"`

### 🔵 **后端需求**

1. **需要添加的API参数**:
   - `name_origin`: 名字的起源/含义
   - `pov`: 视角选择
   - `personality_traits`, `personality_mind`, `personality_emotion`: 性格的三个子方面
   - `good_at`, `bad_at`: 擅长和不擅长的事情(数组)
