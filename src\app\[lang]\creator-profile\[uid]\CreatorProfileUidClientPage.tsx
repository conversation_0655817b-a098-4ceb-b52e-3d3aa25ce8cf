'use client';

import { useState } from 'react';
import Link from 'next/link';
import CharacterCard from '@/components/CharacterCard';
import ProfileModeToggle from '@/components/ProfileModeToggle';
import OtherUserModeToggle from '@/components/OtherUserModeToggle';
import HeroSection from '@/components/HeroSection';
import TabNavigation from '@/components/TabNavigation';
import { ManagedCharacter } from '@/lib/mock-data';
import { User } from '@/lib/api';
import { ExternalLink, UserPlus, TrendingUp, DollarSign, Users, Heart, Edit3, Play, Calendar, Crown, Link as LinkIcon } from 'lucide-react';
import Image from 'next/image';
import { useAuthContext } from '@/components/AuthProvider';
import { useTranslation } from '@/app/i18n/client';

// Icon wrapper to fix type compatibility
const iconWrapper = (IconComponent: any) => ({ className, size }: { className?: string; size?: number }) => (
  <IconComponent className={className} size={size} />
);

interface CreatorStory {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  likes: number;
  reads: number;
  chapters: number;
  status: string;
  createdAt: string;
}

interface DashboardData {
  totalEarnings: number;
  monthlyEarnings: number;
  totalFollowers: number;
  totalLikes: number;
  totalCharacters: number;
  totalStories: number;
  topCharacters: any[];
  topStories: CreatorStory[];
  monthlyStats: any[];
}

interface CreatorProfileUidClientPageProps {
  lang: string;
  uid: string;
  userData: User | null;
  createdCharacters: ManagedCharacter[];
  createdStories: CreatorStory[];
  dashboardData: DashboardData;
}

const TABS = ['characters', 'stories', 'dashboard'] as const;

const CreatorProfileUidClientPage: React.FC<CreatorProfileUidClientPageProps> = ({ 
  lang, 
  uid,
  userData,
  createdCharacters, 
  createdStories, 
  dashboardData 
}) => {
  const [activeTab, setActiveTab] = useState<typeof TABS[number]>('characters');
  const { user: currentUser, isAuthenticated } = useAuthContext();
  const { t } = useTranslation(lang, 'translation');
  
  // Check if this is the current user's own profile
  const isOwnProfile = isAuthenticated && currentUser?.uid === uid;
  
  // Use userData if available, otherwise use currentUser for own profile
  const displayUser = userData || (isOwnProfile ? currentUser : null);

  if (!displayUser) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{t('creatorProfile.errors.creatorNotFound')}</h2>
          <p className="text-gray-600 dark:text-gray-400">{t('creatorProfile.errors.creatorNotFoundDesc')}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Top profile cover - Now using optimized HeroSection */}
      <HeroSection
        backgroundImage="https://picsum.photos/1200/800?blur=3"
        title={displayUser.name || t('creatorProfile.anonymous')}
        subtitle={t('creatorProfile.header.creatorBadge')}
        description={displayUser.bio || t('creatorProfile.description.default')}
        avatar={displayUser.avatar || "https://i.pravatar.cc/160?u=creator"}
        avatarSize="lg"
        variant="profile"
        height="lg"
        stats={[
          { label: t('creatorProfile.header.followers'), value: displayUser.subscriber_count || dashboardData.totalFollowers, icon: iconWrapper(Users) },
          { label: t('creatorProfile.header.following'), value: displayUser.follow_count || 0, icon: iconWrapper(Heart) },
          { label: t('creatorProfile.header.characters'), value: displayUser.character_count || dashboardData.totalCharacters, icon: iconWrapper(Play) },
          { label: t('creatorProfile.header.stories'), value: displayUser.story_count || dashboardData.totalStories, icon: iconWrapper(Calendar) },
          { label: t('creatorProfile.header.earnings'), value: isOwnProfile ? `$${dashboardData.totalEarnings.toLocaleString()}` : 'N/A', icon: iconWrapper(DollarSign) },
          { label: t('creatorProfile.header.links'), value: '7', icon: iconWrapper(LinkIcon) }
        ]}
      >
        <div className="flex items-center gap-2">
          {isOwnProfile ? (
            <ProfileModeToggle lang={lang} currentPage="creator-profile" uid={uid} size="md" className="flex-shrink-0" />
          ) : (
            <OtherUserModeToggle lang={lang} currentPage="creator-profile" uid={uid} size="md" className="flex-shrink-0" />
          )}
        </div>
      </HeroSection>

      {/* Action buttons */}
      {!isOwnProfile && isAuthenticated && (
        <section className="bg-background border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="flex gap-3">
            <button className="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
              <UserPlus size={16} />
              {t('creatorProfile.actions.follow')}
            </button>
            <button className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              {t('creatorProfile.actions.message')}
            </button>
          </div>
        </section>
      )}

      {/* Tabs */}
      <TabNavigation
        tabs={[
          { id: 'characters', label: t('creatorProfile.tabs.characters'), icon: iconWrapper(Users) },
          { id: 'stories', label: t('creatorProfile.tabs.stories'), icon: iconWrapper(Play) },
          { id: 'dashboard', label: t('creatorProfile.tabs.dashboard'), icon: iconWrapper(TrendingUp) }
        ]}
        activeTab={activeTab}
        onTabChange={(tabId) => setActiveTab(tabId as typeof TABS[number])}
        variant="underline"
        sticky={true}
        stickyTop="top-12 lg:top-16"
        className="bg-background border-b border-border"
      />

      {/* Content */}
      <div className="bg-background p-1.5 md:p-2">
        {activeTab === 'characters' && (
          <div className="columns-2 md:columns-3 lg:columns-3 xl:columns-4 2xl:columns-5 3xl:columns-5 gap-1.5 space-y-1.5">
            {createdCharacters.map((character, idx) => (
              <div key={`char-${idx}`} className="break-inside-avoid">
                <CharacterCard
                  lang={lang}
                  character={character}
                  stats={{
                    likes: Math.floor(Math.random() * 1000) + 100,
                    friends: character.followers,
                    shares: Math.floor(Math.random() * 100) + 10,
                  }}
                  aspectRatio={0.75}
                />
              </div>
            ))}
          </div>
        )}

        {activeTab === 'stories' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {createdStories.map((story) => (
              <div key={story.id} className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="aspect-[3/4] relative">
                  <Image
                    src={story.coverImage}
                    alt={story.title}
                    fill
                    className="object-cover"
                    unoptimized
                  />
                  <div className="absolute top-2 right-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      story.status === 'published' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    }`}>
                      {story.status === 'published' ? t('creatorProfile.stories.published') : t('creatorProfile.stories.draft')}
                    </span>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">{story.title}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{story.description}</p>
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-3">
                      <span className="flex items-center gap-1">
                        <Heart size={12} />
                        {story.likes}
                      </span>
                      <span>{story.reads} {t('creatorProfile.stories.reads')}</span>
                    </div>
                    <span>{story.chapters} {t('creatorProfile.stories.chapters')}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'dashboard' && isOwnProfile && (
          <div className="space-y-6">
            {/* Stats Overview */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <DollarSign size={20} className="text-green-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('creatorProfile.dashboard.totalEarnings')}</span>
                </div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">${dashboardData.totalEarnings}</p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <Users size={20} className="text-blue-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('creatorProfile.dashboard.followers')}</span>
                </div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{dashboardData.totalFollowers.toLocaleString()}</p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <Heart size={20} className="text-red-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('creatorProfile.dashboard.totalLikes')}</span>
                </div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{dashboardData.totalLikes.toLocaleString()}</p>
              </div>
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp size={20} className="text-purple-600" />
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('creatorProfile.dashboard.thisMonth')}</span>
                </div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">${dashboardData.monthlyEarnings}</p>
              </div>
            </div>

            {/* Top Characters */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">{t('creatorProfile.dashboard.topCharacters')}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {dashboardData.topCharacters.map((character, idx) => (
                  <div key={idx} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Image
                      src={character.avatar}
                      alt={character.name}
                      width={40}
                      height={40}
                      className="rounded-full object-cover"
                      unoptimized
                    />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 dark:text-white truncate">{character.name}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{character.followers} {t('creatorProfile.dashboard.followers').toLowerCase()}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'dashboard' && !isOwnProfile && (
          <div className="text-center py-12">
            <p className="text-gray-600 dark:text-gray-400">{t('creatorProfile.dashboard.onlyVisibleToCreator')}</p>
          </div>
        )}
      </div>
    </>
  );
};

export default CreatorProfileUidClientPage;
