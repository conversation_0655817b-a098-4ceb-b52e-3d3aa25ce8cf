'use client';

import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';
import type { CharacterFormData, Step, NextAction } from '@/types/character-creation';

export const useCharacterForm = (lang: string) => {
  const router = useRouter();
  
  const [formData, setFormData] = useState<CharacterFormData>({
    // Basic information
    name: '',
    nameOrigin: '', // Name origin or meaning
    description: '', // 角色对外展示的详情
    personality: '', // 角色性格描述
    appearance: '',
    setting: '', // 世界观设定
    settings: '', // 角色设定

    // Image resources
    characterImage: null,
    avatar: null,
    avatarCrop: null,

    // Dialogue settings
    greetingMessage: '',
    personalityTags: [], // 性格标签数组

    // Category information
    gender: '',
    pov: '', // Point of view selection
    faction: 'anime',

    // Publishing settings
    visibility: 'public',

    // Advanced settings
    backgroundStory: '',
    interactionStyleTags: '',
    voiceIds: '', // 预留用于后续语音拓展
    facialExpressions: '', // 面部表情描述
    bodyLanguage: '', // 肢体语言描述
    relationshipWithPlayer: '', // 角色和玩家的关系
    initialMemoriesText: '',
    customPromptPrefix: '',

    // Personality subsections
    personalityTraits: '', // Character traits and characteristics
    personalityMind: '', // Mental patterns and thinking style
    personalityEmotion: '', // Emotional patterns and responses

    // Knowledge and skills
    goodAt: [], // Things the character is good at
    badAt: [], // Things the character is bad at

    // World book files (deprecated)
    knowledgeFiles: [],
  });

  const [nextAction, setNextAction] = useState<NextAction>(null);

  // Check if a step is complete
  const isStepComplete = useCallback((step: Step): boolean => {
    switch (step) {
      case 'basics':
        // Character name, gender, appearance, and personality are required
        return !!(formData.name.trim() && formData.gender.trim() &&
                 formData.appearance.trim() && formData.description.trim());
      case 'personality':
        // At least one behavior field should be filled
        return !!(formData.greetingMessage.trim() || formData.customPromptPrefix.trim() || formData.voiceIds.trim());
      case 'advanced':
        // This step is optional - always complete
        return true;
      default:
        return false;
    }
  }, [formData]);

  // Handle knowledge file upload
  const handleKnowledgeFileUpload = useCallback((files: FileList) => {
    const validFiles: File[] = [];
    const supportedTypes = ['text/plain', 'text/markdown', 'application/json', '.md', '.txt', '.json'];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const extension = file.name.toLowerCase().split('.').pop();
      
      if (file.type === 'text/plain' || 
          file.type === 'text/markdown' || 
          file.type === 'application/json' ||
          extension === 'md' || 
          extension === 'txt' || 
          extension === 'json') {
        validFiles.push(file);
      }
    }
    
    if (validFiles.length > 0) {
      setFormData(prev => ({ 
        ...prev, 
        knowledgeFiles: [...prev.knowledgeFiles, ...validFiles] 
      }));
      toast.success(`${validFiles.length} knowledge file(s) uploaded successfully`);
    } else {
      toast.error('Please upload supported file formats: .txt, .md, .json');
    }
  }, []);

  // Remove knowledge file
  const removeKnowledgeFile = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      knowledgeFiles: prev.knowledgeFiles.filter((_, i) => i !== index)
    }));
    toast.success('Knowledge file removed');
  }, []);

  // 修改handleSubmit函数，使其返回一个Promise<string>，包含创建的角色ID
  // Handle form submission
  const handleSubmit = useCallback(async (e?: React.FormEvent): Promise<string> => {
    if (e) e.preventDefault();
    
    try {
      const submitData = new FormData();
      
      // Required parameters
      submitData.append('name', formData.name);
      submitData.append('description', formData.description);
      submitData.append('setting', formData.setting || `${formData.name} lives in a world full of stories...`);
      submitData.append('greeting_message', formData.greetingMessage);
      submitData.append('gender', formData.gender);
      
      // Opening message templates (required)
      const openingTemplates = JSON.stringify([{
        template_id: 'default',
        content: formData.greetingMessage,
        priority: 1,
        conditions: null
      }]);
      submitData.append('opening_message_templates', openingTemplates);
      
      // Personality tags (required)
      const personalityTags = formData.personalityTags.length > 0
        ? JSON.stringify(formData.personalityTags.map(trait => ({ tag_name: trait, weight: 0.8 })))
        : JSON.stringify([{ tag_name: 'Friendly', weight: 0.8 }]);
      submitData.append('personality_tags', personalityTags);

      // 合并性格三个子字段为personality字段
      const personality = [
        formData.personalityTraits.trim(),
        formData.personalityMind.trim(),
        formData.personalityEmotion.trim()
      ].filter(Boolean).join('\n\n');
      if (personality) {
        submitData.append('personality', personality);
      }
      
      // Content tags (required) - 映射前端的交互风格标签到后端的内容标签
      submitData.append('content_tags', formData.interactionStyleTags || 'Daily,Warm');

      // Interaction style tags (optional) - 保持原有的交互风格标签
      if (formData.interactionStyleTags) {
        submitData.append('interaction_style_tags', formData.interactionStyleTags);
      }
      
      // Image files (required)
      if (formData.avatar) {
        submitData.append('avatar', formData.avatar);
      }
      
      // Character illustration (required)
      if (formData.characterImage) {
        submitData.append('image', formData.characterImage);
      }
      
      // Avatar crop information (optional)
      if (formData.avatarCrop) {
        submitData.append('avatar_crop_info', JSON.stringify(formData.avatarCrop));
      }
      
      // Knowledge files (optional)
      if (formData.knowledgeFiles.length > 0) {
        formData.knowledgeFiles.forEach((file, index) => {
          submitData.append('knowledge_files', file);
        });
      }
      
      // Optional parameters
      if (formData.visibility) {
        submitData.append('visibility', formData.visibility);
      }
      
      // Advanced settings (optional)
      if (formData.backgroundStory) {
        submitData.append('background_story', formData.backgroundStory);
      }
      if (formData.interactionStyleTags) {
        submitData.append('interaction_style_tags', formData.interactionStyleTags);
      }
      if (formData.voiceIds) {
        submitData.append('voice_ids', formData.voiceIds);
      }
      if (formData.initialMemoriesText) {
        submitData.append('initial_memories_text', formData.initialMemoriesText);
      }
      if (formData.customPromptPrefix) {
        submitData.append('custom_prompt_prefix', formData.customPromptPrefix);
      }

      // 前端新增字段 - 需要后端支持
      if (formData.appearance) {
        submitData.append('appearance', formData.appearance);
      }
      if (formData.faction) {
        submitData.append('faction', formData.faction);
      }
      if (formData.settings) {
        submitData.append('settings', formData.settings);
      }
      
      // 使用正确的字段映射，不再使用错误的字段
      if (formData.facialExpressions) {
        submitData.append('facial_expressions', formData.facialExpressions);
      }
      if (formData.bodyLanguage) {
        submitData.append('body_language', formData.bodyLanguage);
      }
      if (formData.relationshipWithPlayer) {
        submitData.append('relationship_with_player', formData.relationshipWithPlayer);
      }
      
      // 添加新的字段
      if (formData.nameOrigin) {
        submitData.append('name_origin', formData.nameOrigin);
      }
      if (formData.pov) {
        submitData.append('pov', formData.pov);
      }
      
      // 擅长和不擅长的事情
      if (formData.goodAt.length > 0) {
        submitData.append('good_at', JSON.stringify(formData.goodAt));
      }
      if (formData.badAt.length > 0) {
        submitData.append('bad_at', JSON.stringify(formData.badAt));
      }

      // Sample conversations (optional)
      const sampleConversations = JSON.stringify([
        { role: 'user', text: 'Hello' },
        { role: 'ai', text: formData.greetingMessage }
      ]);
      submitData.append('sample_conversations', sampleConversations);
      
      console.log('Submitting character data to /character/create:', {
        name: formData.name,
        description: formData.description, // 角色对外展示的详情
        personality: personality || null, // 合并后的角色性格描述
        setting: formData.setting,
        greeting_message: formData.greetingMessage,
        gender: formData.gender,
        personality_tags: personalityTags,
        content_tags: formData.interactionStyleTags,
        visibility: formData.visibility,
        has_avatar: !!formData.avatar,
        knowledge_files_count: formData.knowledgeFiles.length,
        knowledge_files_names: formData.knowledgeFiles.map(f => f.name),
        background_story: formData.backgroundStory || null,
        interaction_style_tags: formData.interactionStyleTags || null,
        initial_memories_text: formData.initialMemoriesText || null,
        custom_prompt_prefix: formData.customPromptPrefix || null,
        // 修正后的字段映射
        appearance: formData.appearance || null,
        faction: formData.faction || null,
        settings: formData.settings || null,
        facial_expressions: formData.facialExpressions || null, // 不再使用voiceIds
        body_language: formData.bodyLanguage || null, // 不再从interactionStyleTags中提取
        relationship_with_player: formData.relationshipWithPlayer || null, // 不再使用setting
        // 新增字段
        name_origin: formData.nameOrigin || null,
        pov: formData.pov || null,
        good_at: formData.goodAt.length > 0 ? formData.goodAt : null,
        bad_at: formData.badAt.length > 0 ? formData.badAt : null,
        // 性格子字段
        personality_traits: formData.personalityTraits || null,
        personality_mind: formData.personalityMind || null,
        personality_emotion: formData.personalityEmotion || null
      });
      
      // TODO: Call actual API
      // const response = await fetch('/api/character/create', {
      //   method: 'POST',
      //   body: submitData
      // });
      // const result = await response.json();
      
      // Temporary simulation of success - redirect based on nextAction
      const characterId = 'new-character-id'; // Mock character ID
      
      if (nextAction === 'chat') {
        // Start chatting directly
        router.push(`/${lang}/chats/${characterId}`);
      } else if (nextAction === 'story') {
        // Go to story creation page with character ID
        router.push(`/${lang}/create-story/${characterId}`);
      } else {
        // Default redirect to character detail page
        router.push(`/${lang}/character/${characterId}`);
      }
      
      toast.success('Character created successfully!');

      // 返回角色ID
      return characterId;
      
    } catch (error) {
      console.error('Failed to create character:', error);
      toast.error('Failed to create character, please try again');
      return ''; // 失败时返回空字符串
    }
  }, [formData, router, lang, nextAction]);

  return {
    formData,
    setFormData,
    nextAction,
    setNextAction,
    isStepComplete,
    handleKnowledgeFileUpload,
    removeKnowledgeFile,
    handleSubmit,
  };
};
