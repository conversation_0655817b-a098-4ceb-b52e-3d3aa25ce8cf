import { Suspense } from 'react';
import AuthGuard from '@/components/AuthGuard';
import CreatorProfileRedirectPage from './CreatorProfileRedirectPage';
import MainAppLayout from '@/components/MainAppLayout';
import { characters, getManagedCharacters } from '@/lib/mock-data';

// Mock story data
const generateCreatorStories = () => {
  const storyTitles = [
    "The Enchanted Forest Adventure",
    "Cyberpunk City Chronicles", 
    "Medieval Kingdom Quest",
    "Space Station Mystery",
    "Magical Academy Tales",
    "Post-Apocalyptic Survival",
    "Victorian Era Romance",
    "Underwater Kingdom Saga"
  ];

  return storyTitles.map((title, index) => ({
    id: `story-${index}`,
    title,
    description: `An immersive story experience featuring ${characters[index % characters.length].name} in a captivating narrative.`,
    coverImage: `https://picsum.photos/seed/${title}/400/600`,
    character: characters[index % characters.length],
    stats: {
      plays: Math.floor(Math.random() * 5000) + 100,
      likes: Math.floor(Math.random() * 1000) + 50,
      shares: Math.floor(Math.random() * 200) + 10,
    },
    createdAt: `${Math.floor(Math.random() * 30) + 1} days ago`,
    status: 'published' as const,
    earnings: Math.floor(Math.random() * 500) + 50,
  }));
};

// Mock dashboard data
const generateDashboardData = () => {
  return {
    totalEarnings: 2847.50,
    monthlyEarnings: 456.30,
    totalFollowers: 1247,
    totalLikes: 8934,
    totalCharacters: getManagedCharacters().length,
    totalStories: 8,
    topCharacters: getManagedCharacters().slice(0, 3).map(char => ({
      ...char,
      earnings: Math.floor(Math.random() * 300) + 100,
      interactions: Math.floor(Math.random() * 1000) + 200,
    })),
    topStories: generateCreatorStories().slice(0, 3),
    monthlyStats: [
      { month: 'Jan', earnings: 234, interactions: 1200 },
      { month: 'Feb', earnings: 345, interactions: 1450 },
      { month: 'Mar', earnings: 456, interactions: 1680 },
      { month: 'Apr', earnings: 567, interactions: 1920 },
      { month: 'May', earnings: 432, interactions: 1750 },
      { month: 'Jun', earnings: 456, interactions: 1890 },
    ]
  };
};

export default async function CreatorProfilePage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;

  return (
    <AuthGuard requireAuth={true}>
      <Suspense fallback={
        <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
          <div className="w-8 h-8 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      }>
        <MainAppLayout lang={lang}>
          <CreatorProfileRedirectPage lang={lang} />
        </MainAppLayout>
      </Suspense>
    </AuthGuard>
  );
}
