import { EnhancedAchievement } from '@/types/achievements';

export const generateAchievementsData = (): EnhancedAchievement[] => {
  const achievementTemplates: EnhancedAchievement[] = [
    // ACHIEVERS - Chat Depth & Quality Achievements
    {
      id: 'first-steps',
      name: 'First Steps',
      description: 'Complete your first conversation with an AI character',
      requirement: 'Send 10 messages in a single conversation',
      tips: 'Just start chatting with any character to unlock this achievement',
      category: 'beginner',
      rarity: 'bronze',
      status: 'completed',
      progress: { current: 10, total: 10 },
      points: 10,
      difficulty: 'easy',
      missable: false,
      estimatedTime: 15,
      earnedDate: '2024-01-15',
      icon: 'target',
      rewards: [
        { type: 'badge', name: 'Newcomer Badge', icon: 'badge' },
        { type: 'currency', name: 'Alphane Dust', amount: 50, icon: 'alphane' }
      ]
    },
    {
      id: 'conversationalist',
      name: 'Conversationalist',
      description: 'Master of engaging conversations',
      requirement: 'Complete 100 total conversations',
      tips: 'Keep chatting with different characters to build relationships',
      category: 'interaction',
      rarity: 'silver',
      status: 'inProgress',
      progress: { current: 67, total: 100 },
      points: 25,
      difficulty: 'medium',
      missable: false,
      estimatedTime: 480,
      icon: 'message-circle',
      rewards: [
        { type: 'title', name: 'Social Butterfly', icon: 'title' },
        { type: 'currency', name: 'Heart Crystal', amount: 100, icon: 'endora' }
      ]
    },
    {
      id: 'memory-keeper',
      name: 'Memory Keeper',
      description: 'Guardian of precious moments',
      requirement: 'Create 50 memory capsules',
      tips: 'Save special moments during conversations to create lasting memories',
      category: 'collection',
      rarity: 'gold',
      status: 'inProgress',
      progress: { current: 23, total: 50 },
      points: 50,
      difficulty: 'medium',
      missable: false,
      estimatedTime: 300,
      icon: 'heart',
      rewards: [
        { type: 'privilege', name: 'Memory Vault Access', icon: 'privilege' },
        { type: 'currency', name: 'Memory Fragment', amount: 10, icon: 'serotile' }
      ]
    },
    {
      id: 'creator',
      name: 'Character Creator',
      description: 'Create your first AI character',
      requirement: 'Successfully create and publish a character',
      tips: 'Use the character creation tools to bring your imagination to life',
      category: 'creation',
      rarity: 'bronze',
      status: 'completed',
      progress: { current: 1, total: 1 },
      points: 15,
      difficulty: 'easy',
      missable: false,
      estimatedTime: 60,
      earnedDate: '2024-01-20',
      icon: 'palette',
      rewards: [
        { type: 'badge', name: 'Creator Badge', icon: 'badge' },
        { type: 'experience', name: 'Creator XP', amount: 200, icon: 'experience' }
      ]
    },
    {
      id: 'socialite',
      name: 'Community Explorer',
      description: 'Engage with the community through likes and comments',
      requirement: 'Give 20 likes and comments on community posts',
      tips: 'Explore the community section and interact with other users\' content',
      category: 'social',
      rarity: 'silver',
      status: 'inProgress',
      progress: { current: 8, total: 20 },
      points: 30,
      difficulty: 'easy',
      missable: false,
      estimatedTime: 120,
      icon: 'users',
      rewards: [
        { type: 'title', name: 'Community Explorer', icon: 'title' }
      ]
    },
    {
      id: 'streak-master',
      name: 'Streak Master',
      description: 'Maintain a long conversation streak',
      requirement: 'Chat for 30 consecutive days',
      tips: 'Keep your daily chat streak alive by talking to characters every day',
      category: 'interaction',
      rarity: 'platinum',
      status: 'inProgress',
      progress: { current: 12, total: 30 },
      points: 100,
      difficulty: 'hard',
      missable: true,
      estimatedTime: 1800, // 30 days × 60min
      icon: 'flame',
      rewards: [
        { type: 'badge', name: 'Streak Master Badge', icon: 'badge' },
        { type: 'privilege', name: 'Streak Freeze Card', icon: 'privilege' },
        { type: 'currency', name: 'Bond Dew', amount: 50, icon: 'oxytol' }
      ]
    },
    {
      id: 'explorer',
      name: 'Character Explorer',
      description: 'Discover and interact with many different characters',
      requirement: 'Chat with 25 different characters',
      tips: 'Explore the character gallery and try chatting with various personalities',
      category: 'interaction',
      rarity: 'gold',
      status: 'locked',
      progress: { current: 5, total: 25 },
      points: 75,
      difficulty: 'medium',
      missable: false,
      estimatedTime: 600,
      icon: 'compass',
      rewards: [
        { type: 'title', name: 'Explorer', icon: 'title' },
        { type: 'currency', name: 'Alphane Dust', amount: 300, icon: 'alphane' }
      ]
    },
    {
      id: 'storyteller',
      name: 'Master Storyteller',
      description: 'Create engaging stories with AI characters',
      requirement: 'Create 5 published stories',
      tips: 'Use the story creation tools to craft compelling narratives',
      category: 'creation',
      rarity: 'diamond',
      status: 'locked',
      progress: { current: 0, total: 5 },
      points: 200,
      difficulty: 'hard',
      missable: false,
      estimatedTime: 1200,
      icon: 'book',
      rewards: [
        { type: 'badge', name: 'Master Storyteller', icon: 'badge' },
        { type: 'privilege', name: 'Premium Story Templates', icon: 'privilege' }
      ]
    },
    {
      id: 'perfectionist',
      name: 'Perfectionist',
      description: 'Achieve high ratings on your created content',
      requirement: 'Get 100 total likes on your characters and stories',
      tips: 'Focus on quality when creating characters and stories',
      category: 'creation',
      rarity: 'diamond',
      status: 'locked',
      progress: { current: 12, total: 100 },
      points: 250,
      difficulty: 'extreme',
      missable: false,
      estimatedTime: 2400,
      icon: 'star',
      rewards: [
        { type: 'title', name: 'Perfectionist', icon: 'title' },
        { type: 'privilege', name: 'Featured Creator Status', icon: 'privilege' }
      ]
    },
    {
      id: 'legendary',
      name: 'Legendary Creator',
      description: 'Reach the pinnacle of creativity and community engagement',
      requirement: 'Accumulate 10,000 total interaction points',
      tips: 'This is the ultimate achievement - keep creating, chatting, and engaging!',
      category: 'special',
      rarity: 'legendary',
      status: 'locked',
      progress: { current: 847, total: 10000 },
      points: 1000,
      difficulty: 'extreme',
      missable: false,
      estimatedTime: 6000,
      icon: 'crown',
      rewards: [
        { type: 'badge', name: 'Legendary Creator', icon: 'badge' },
        { type: 'title', name: 'Legend', icon: 'title' },
        { type: 'privilege', name: 'Legendary Status', icon: 'privilege' },
        { type: 'currency', name: 'All Currencies Bundle', amount: 1000, icon: 'bundle' }
      ]
    },

    // SOCIAL - Community & Sharing Achievements
    {
      id: 'community-helper',
      name: 'Community Helper',
      description: 'Help newcomers find their way in the community',
      requirement: 'Provide helpful guidance to 10 new users',
      tips: 'Answer questions in the community forum and help new users get started',
      category: 'social',
      rarity: 'silver',
      status: 'inProgress',
      progress: { current: 3, total: 10 },
      points: 25,
      difficulty: 'easy',
      missable: false,
      estimatedTime: 120,
      icon: 'users',
      rewards: [
        { type: 'badge', name: 'Helper Badge', icon: 'badge' },
        { type: 'currency', name: 'Community Points', amount: 100, icon: 'social' }
      ]
    },
    {
      id: 'social-butterfly',
      name: 'Social Butterfly',
      description: 'Build connections across the community',
      requirement: 'Make 50 friends and receive 100 likes on your posts',
      tips: 'Engage with other users, share interesting content, and be active in discussions',
      category: 'social',
      rarity: 'gold',
      status: 'inProgress',
      progress: { current: 24, total: 50 },
      points: 75,
      difficulty: 'medium',
      missable: false,
      estimatedTime: 480,
      icon: 'heart',
      rewards: [
        { type: 'badge', name: 'Social Butterfly', icon: 'badge' },
        { type: 'title', name: 'Community Star', icon: 'title' },
        { type: 'currency', name: 'Social Currency', amount: 200, icon: 'social' }
      ]
    },
    {
      id: 'content-curator',
      name: 'Content Curator',
      description: 'Share amazing content with the community',
      requirement: 'Share 25 characters or stories that receive positive feedback',
      tips: 'Find and share high-quality content that others will appreciate',
      category: 'social',
      rarity: 'gold',
      status: 'locked',
      progress: { current: 0, total: 25 },
      points: 100,
      difficulty: 'medium',
      missable: false,
      estimatedTime: 360,
      icon: 'share',
      rewards: [
        { type: 'badge', name: 'Curator Badge', icon: 'badge' },
        { type: 'privilege', name: 'Featured Content Access', icon: 'privilege' }
      ]
    },

    // RANKING - Competition & Elite Achievements
    {
      id: 'top-performer',
      name: 'Top Performer',
      description: 'Reach the top 10% of all users in monthly rankings',
      requirement: 'Achieve top 10% ranking in any monthly leaderboard',
      tips: 'Consistently engage with content and maintain high activity levels',
      category: 'special',
      rarity: 'platinum',
      status: 'inProgress',
      progress: { current: 15, total: 100 },
      points: 150,
      difficulty: 'hard',
      missable: true,
      estimatedTime: 720,
      icon: 'trophy',
      rewards: [
        { type: 'badge', name: 'Elite Performer', icon: 'badge' },
        { type: 'title', name: 'Top Tier', icon: 'title' },
        { type: 'currency', name: 'Elite Points', amount: 500, icon: 'elite' }
      ]
    },
    {
      id: 'champion',
      name: 'Champion',
      description: 'Dominate the global leaderboards',
      requirement: 'Reach #1 position in global rankings',
      tips: 'The ultimate competitive achievement - requires consistent excellence across all areas',
      category: 'special',
      rarity: 'legendary',
      status: 'locked',
      progress: { current: 0, total: 1 },
      points: 500,
      difficulty: 'extreme',
      missable: true,
      estimatedTime: 2400,
      icon: 'crown',
      rewards: [
        { type: 'badge', name: 'Global Champion', icon: 'badge' },
        { type: 'title', name: 'Champion', icon: 'title' },
        { type: 'privilege', name: 'Champion Status', icon: 'privilege' },
        { type: 'currency', name: 'Champion Rewards', amount: 1000, icon: 'champion' }
      ]
    }
  ];

  return achievementTemplates;
};

export const getAchievementsByCategory = (achievements: EnhancedAchievement[], category: string) => {
  if (category === 'all') return achievements;
  return achievements.filter(achievement => achievement.category === category);
};

export const getAchievementsByRarity = (achievements: EnhancedAchievement[], rarity: string) => {
  if (rarity === 'all') return achievements;
  return achievements.filter(achievement => achievement.rarity === rarity);
};

export const getAchievementsByStatus = (achievements: EnhancedAchievement[], status: string) => {
  if (status === 'all') return achievements;
  return achievements.filter(achievement => achievement.status === status);
};

export const calculateCompletionRate = (achievements: EnhancedAchievement[]) => {
  const completed = achievements.filter(a => a.status === 'completed' || a.status === 'claimed').length;
  return Math.round((completed / achievements.length) * 100);
};

export const getTotalPoints = (achievements: EnhancedAchievement[]) => {
  return achievements
    .filter(a => a.status === 'completed' || a.status === 'claimed')
    .reduce((sum, a) => sum + a.points, 0);
}; 