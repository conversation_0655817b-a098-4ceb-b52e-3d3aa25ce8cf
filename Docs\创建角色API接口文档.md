# 创建角色API接口文档

## 📋 **接口概述**

**接口地址**: `POST /api/character/create`  
**请求格式**: `multipart/form-data`  
**说明**: 创建新角色的API接口，支持文本字段和文件上传

---

## 📝 **请求参数**

### **基础信息字段**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `name` | string | ✅ | 角色名称 | "小雪" |
| `name_origin` | string | ❌ | 名字的起源/含义 | "来自冬天的第一场雪" |
| `description` | string | ✅ | 角色对外展示的详情描述 | "一个温柔善良的女孩..." |
| `character_settings` | string | ❌ | 角色详细设定 | "高中三年级学生，喜欢阅读..." |
| `gender` | string | ✅ | 角色性别 | "female" / "male" / "other" |
| `pov` | string | ❌ | 视角选择 | "female_pov" / "male_pov" |
| `faction` | string | ❌ | 角色类型/派系 | "anime" / "realistic" / "fantasy" / "scifi" |
| `visibility` | string | ❌ | 可见性设置 | "public" / "unlisted" / "private" |

### **外观与性格字段**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `appearance` | string | ❌ | 角色外观描述，可以I2T识别 | "长发飘逸，眼神温和..." |
| `personality` | string | ❌ | 合并后的完整性格描述 | "特质：温和善良\n\n思维：理性分析\n\n情感：细腻敏感" |
| `personality_traits` | string | ❌ | 角色特质描述 | "温和、善良、有耐心" |
| `personality_mind` | string | ❌ | 思维方式描述 | "喜欢理性分析问题" |
| `personality_emotion` | string | ❌ | 情感模式描述 | "情感细腻，容易共情" |
| `tags` | JSON string | ✅ | tag标签 用于搜推和对话构造 | `["friendly,mysterious"]` |

### **行为与交互字段**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `greeting_message` | string | ✅ | 默认问候语 | "你好！很高兴见到你～" |
| `opening_message_templates` | JSON string | ✅ | 暂时预留 后期开放用户写多种开场白预设 本版本前端暂时不提交 | `[{"template_id": "default", "content": "...", "priority": 1}]` |
| `speech_style` | string | ❌ | 说话风格/自定义提示前缀 | "说话温柔，语气亲切" |
| `facial_expressions` | string | ❌ | 面部表情描述 | "微笑时眼睛会弯成月牙" |
| `body_language` | string | ❌ | 肢体语言描述 | "走路轻盈，手势优雅" |

### **背景与设定字段**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `knowledge_base_text` | string | ❌ | 知识内容 | " " |
| `relationship_with_player` | string | ❌ | 与玩家的关系设定 | "青梅竹马的好朋友" |
| `world_settings` | string | ❌ | 世界观/背景设定 | "生活在现代都市的学生" |
| `important_past_experiences` | string | ❌ | 重要过往经历（与background_story相同） | "从小在书香门第长大..." |

### **技能与特长字段**

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `good_at` | JSON string | ❌ | 擅长的事情 | `["阅读", "写作", "钢琴"]` |
| `bad_at` | JSON string | ❌ | 不擅长的事情 | `["运动", "数学", "料理"]` |

### **文件上传字段**

| 参数名 | 类型 | 必填 | 说明 | 备注 |
|--------|------|------|------|------|
| `avatar` | File | ✅ | 头像文件 | 支持jpg, png格式 |
| `image` | File | ✅ | 角色立绘文件 | 支持jpg, png格式 |
| `knowledge_files` | File[] | ❌ | 知识文件 | 支持txt, md, json格式，可多个 |

**返回示例 (成功):**
```json
    {
        "code": 201, // HTTP 201 Created
        "message": "Character created successfully.",
        "data": {
            "character_id": "string", // 新创建的角色ID
            "version": 1,             // 初始版本号
            "status": "active"        // 角色状态，例如 "active" "pending" "banned"
        },
        "detail": null
    }
    ```
    -   **成功状态码:**
        *   `201 Created`: 角色创建成功。
    -   **错误状态码:**
        *   `400 Bad Request`: 请求参数无效或缺失 (例如 `name` 等必选项为空, 格式错误, 上传文件类型不支持等)。
        *   `401 Unauthorized`: 用户未登录或 Token 无效/过期。
        *   `413 Payload Too Large`: 上传的图片或知识库文件过大。
        *   `422 Unprocessable Entity`: 语义错误，例如标签格式不正确，或者参数过长。