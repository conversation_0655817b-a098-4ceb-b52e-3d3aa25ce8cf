'use client';

import React, { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';


interface User {
  stamina_current?: number;
  stamina_max?: number;
  stamina_recovery_time?: number; // 下次回复时间的时间戳
  membership_tier?: 'standard' | 'pass' | 'diamond' | 'metaverse';
}

interface SidebarMindFuelDisplayProps {
  user: User | null;
  lang: string;
}

const SidebarMindFuelDisplay: React.FC<SidebarMindFuelDisplayProps> = ({ user, lang }) => {
  const router = useRouter();
  const { t } = useTranslation(lang, 'translation');
  const [isClient, setIsClient] = useState(false);

  // Ensure this only runs on client to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 根据会员等级获取思维燃料配置
  const getMindFuelConfig = (membershipTier: string = 'standard') => {
    switch (membershipTier) {
      case 'pass':
        return {
          maxMindFuel: 20,
          recoverySpeed: 2, // 2倍回复速度
          recoveryInterval: 30 * 60 * 1000, // 30分钟回复1点
        };
      case 'diamond':
        return {
          maxMindFuel: 50,
          recoverySpeed: 5, // 5倍回复速度
          recoveryInterval: 12 * 60 * 1000, // 12分钟回复1点
        };
      case 'metaverse':
        return {
          maxMindFuel: Infinity,
          recoverySpeed: Infinity,
          recoveryInterval: 0,
        };
      default: // standard
        return {
          maxMindFuel: 10,
          recoverySpeed: 1,
          recoveryInterval: 60 * 60 * 1000, // 60分钟回复1点
        };
    }
  };

  const config = getMindFuelConfig(user?.membership_tier);
  const currentMindFuel = user?.stamina_current || 8; // 默认值
  const maxMindFuel = config.maxMindFuel === Infinity ? '∞' : config.maxMindFuel;
  const recoveryTime = user?.stamina_recovery_time || Date.now() + 45 * 60 * 1000; // 默认45分钟后回复

  // 计算回复时间显示
  const getRecoveryTimeText = () => {
    // Return placeholder during SSR to avoid hydration mismatch
    if (!isClient) return '45m';

    if (config.maxMindFuel === Infinity) return t('mindFuel.sidebar.unlimited');
    if (currentMindFuel >= config.maxMindFuel) return t('mindFuel.sidebar.full');

    const now = Date.now();
    const timeLeft = recoveryTime - now;

    if (timeLeft <= 0) return t('mindFuel.sidebar.aboutToRecover');

    const minutes = Math.floor(timeLeft / (60 * 1000));
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  // 计算思维燃料百分比用于进度条
  const mindFuelPercentage = config.maxMindFuel === Infinity ? 100 : (currentMindFuel / config.maxMindFuel) * 100;

  const handleClick = () => {
    router.push(`/${lang}/mind-fuel`);
  };

  return (
    <div
      className="group cursor-pointer transition-all duration-300 hover:opacity-80"
      onClick={handleClick}
    >
      <div className="flex items-center gap-3">
        {/* 大图标 - 与货币组件保持一致 */}
        <div className="h-11 bg-gradient-to-br from-red-500 via-red-600 to-pink-600 rounded-lg flex items-center justify-center shadow-lg" style={{width: '3.625rem'}}>
          <Heart className="text-white fill-current" size={20}/>
        </div>

        {/* 右侧内容区域 */}
        <div className="flex-1 min-w-0">
          {/* 标题和数值 - 与INTERACTION STREAK样式一致 */}
          <div className="flex items-center justify-between mb-1">
            <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">
              {t('mindFuel.sidebar.title')}
            </h3>
            <span className="text-lg font-bold text-gray-800 dark:text-gray-100">
              {currentMindFuel}/{maxMindFuel}
            </span>
          </div>

          {/* 进度条 */}
          <div className="relative w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-1">
            <div
              className="bg-gradient-to-r from-red-400 via-red-500 to-pink-500 h-2 rounded-full transition-all duration-700 ease-out"
              style={{ width: `${mindFuelPercentage}%` }}
            />
          </div>

          {/* 下次恢复标签和时间 */}
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span className="font-medium">
              {t('mindFuel.sidebar.nextRecovery')}
            </span>
            <span>
              {getRecoveryTimeText()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SidebarMindFuelDisplay;
