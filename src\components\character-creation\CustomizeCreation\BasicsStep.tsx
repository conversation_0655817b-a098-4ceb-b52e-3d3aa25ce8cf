'use client';

import React, { useState } from 'react';
import { User, <PERSON>, <PERSON><PERSON>, <PERSON>h, Plus, Heart, Lightbulb, Zap } from 'lucide-react';
import type { CharacterFormData } from '@/types/character-creation';
import ImageUploadSection from '../ImageUpload/ImageUploadSection';
import ImageCropModals from '../ImageUpload/ImageCropModals';
import { useImageUpload } from '@/hooks/character-creation/useImageUpload';
import CharacterGenreCreator from '../../CharacterGenreCreator';
import { useTranslation } from '@/app/i18n/client';

interface BasicsStepProps {
  formData: CharacterFormData;
  setFormData: (data: CharacterFormData | ((prev: CharacterFormData) => CharacterFormData)) => void;
  lang?: string;
}

const BasicsStep: React.FC<BasicsStepProps> = ({
  formData,
  setFormData,
  lang = 'en'
}) => {
  const { t } = useTranslation(lang, 'translation');
  const imageUpload = useImageUpload(formData, setFormData);



  // Parse additional tags from personalityTags field
  const additionalTags = formData.personalityTags || [];

  // State for tag input
  const [tagInput, setTagInput] = useState('');

  const handleAddTag = () => {
    const tag = tagInput.trim();
    if (tag && !additionalTags.includes(tag)) {
      const newTags = [...additionalTags, tag];
      setFormData(prev => ({
        ...prev,
        personalityTags: newTags
      }));
      setTagInput('');
    }
  };

  const handleTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const removeTag = (indexToRemove: number) => {
    const newTags = additionalTags.filter((_, index) => index !== indexToRemove);
    setFormData(prev => ({
      ...prev,
      personalityTags: newTags
    }));
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-purple-800 dark:text-purple-200 mb-2">{t('characterCreation.basics.title')}</h3>
        <p className="text-gray-600 dark:text-gray-400">{t('characterCreation.basics.subtitle')}</p>
      </div>

      {/* Character Name */}
      <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200/50 dark:border-purple-700/50 shadow-sm">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-4 flex items-center gap-2">
          <User size={20} />
          {t('characterCreation.basics.characterName')}
        </h4>
        <div className="space-y-4">
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-xl text-foreground placeholder-gray-400 dark:placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-lg"
            placeholder={t('characterCreation.basics.namePlaceholder')}
            maxLength={50}
          />
          <input
            type="text"
            value={formData.nameOrigin || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, nameOrigin: e.target.value }))}
            className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-xl text-foreground placeholder-gray-400 dark:placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
            placeholder={t('characterCreation.basics.nameOriginPlaceholder')}
            maxLength={100}
          />
        </div>
      </div>

      {/* Gender & POV Selection */}
      <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200/50 dark:border-purple-700/50 shadow-sm">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-4 flex items-center gap-2">
          <Users size={20} />
          {t('characterCreation.basics.genderPov')}
        </h4>
        <CharacterGenreCreator
          gender={formData.gender}
          pov={formData.pov || ''}
          onGenderChange={(gender) => setFormData(prev => ({ ...prev, gender }))}
          onPovChange={(pov) => setFormData(prev => ({ ...prev, pov }))}
          lang={lang}
        />
      </div>

      {/* Character Images */}
      <ImageUploadSection {...imageUpload} lang={lang} />

      {/* Appearance Description */}
      <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200/50 dark:border-purple-700/50 shadow-sm">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-4 flex items-center gap-2">
          <Palette size={20} />
          {t('characterCreation.basics.appearance')}
        </h4>
        <textarea
          value={formData.appearance}
          onChange={(e) => setFormData(prev => ({ ...prev, appearance: e.target.value }))}
          rows={4}
          className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-xl text-foreground placeholder-gray-400 dark:placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none"
          placeholder={t('characterCreation.basics.appearancePlaceholder')}
          maxLength={500}
          style={{ userSelect: 'text' }}
        />
        <div className="flex justify-end items-center mt-3">
          <span className={`character-counter ${formData.appearance.length > 100 ? '' : 'over-limit'}`}>
            {formData.appearance.length}/500
          </span>
        </div>
      </div>

      {/* Personality */}
      <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200/50 dark:border-purple-700/50 shadow-sm">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-4 flex items-center gap-2">
          <Heart size={20} />
          {t('characterCreation.basics.personality')}
        </h4>

        <div className="space-y-4">
          {/* Traits */}
          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2 flex items-center gap-1">
              <Zap size={16} />
              {t('characterCreation.basics.traits')}
            </label>
            <textarea
              value={formData.personalityTraits}
              onChange={(e) => setFormData(prev => ({ ...prev, personalityTraits: e.target.value }))}
              rows={3}
              className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-xl text-foreground placeholder-gray-400 dark:placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none"
              placeholder={t('characterCreation.basics.traitsPlaceholder')}
              maxLength={500}
            />
            <div className="flex justify-end items-center mt-2">
              <span className={`character-counter ${formData.personalityTraits.length > 100 ? '' : 'over-limit'}`}>
                {formData.personalityTraits.length}/500
              </span>
            </div>
          </div>

          {/* Mind */}
          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2 flex items-center gap-1">
              <Lightbulb size={16} />
              {t('characterCreation.basics.mind')}
            </label>
            <textarea
              value={formData.personalityMind}
              onChange={(e) => setFormData(prev => ({ ...prev, personalityMind: e.target.value }))}
              rows={3}
              className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-xl text-foreground placeholder-gray-400 dark:placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none"
              placeholder={t('characterCreation.basics.mindPlaceholder')}
              maxLength={500}
            />
            <div className="flex justify-end items-center mt-2">
              <span className={`character-counter ${formData.personalityMind.length > 100 ? '' : 'over-limit'}`}>
                {formData.personalityMind.length}/500
              </span>
            </div>
          </div>

          {/* Emotion */}
          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2 flex items-center gap-1">
              <Heart size={16} />
              {t('characterCreation.basics.emotion')}
            </label>
            <textarea
              value={formData.personalityEmotion}
              onChange={(e) => setFormData(prev => ({ ...prev, personalityEmotion: e.target.value }))}
              rows={3}
              className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-xl text-foreground placeholder-gray-400 dark:placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none"
              placeholder={t('characterCreation.basics.emotionPlaceholder')}
              maxLength={500}
            />
            <div className="flex justify-end items-center mt-2">
              <span className={`character-counter ${formData.personalityEmotion.length > 100 ? '' : 'over-limit'}`}>
                {formData.personalityEmotion.length}/500
              </span>
            </div>
          </div>
        </div>
      </div>



      {/* Character Settings */}
      <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200/50 dark:border-purple-700/50 shadow-sm">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-4 flex items-center gap-2">
          <Hash size={20} />
          {t('characterCreation.basics.characterSettings')}
        </h4>
        <textarea
          value={formData.settings}
          onChange={(e) => setFormData(prev => ({ ...prev, settings: e.target.value }))}
          rows={4}
          className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-xl text-foreground placeholder-gray-400 dark:placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 resize-none"
          placeholder={t('characterCreation.basics.characterSettingsPlaceholder')}
          maxLength={1000}
          style={{ userSelect: 'text' }}
        />
        <div className="flex justify-end items-center mt-3">
          <span className={`character-counter ${formData.settings.length > 200 ? '' : 'over-limit'}`}>
            {formData.settings.length}/1000
          </span>
        </div>
      </div>

      {/* Additional Tags */}
      <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200/50 dark:border-purple-700/50 shadow-sm">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-4 flex items-center gap-2">
          <Hash size={20} />
          {t('characterCreation.basics.additionalTags')}
        </h4>
        <div className="space-y-4">
          <div className="flex gap-3">
            <input
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyDown={handleTagKeyDown}
              placeholder={t('characterCreation.basics.tagPlaceholder')}
              className="flex-1 px-4 py-3 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-xl text-foreground placeholder-gray-400 dark:placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
            />
            <button
              type="button"
              onClick={handleAddTag}
              disabled={!tagInput.trim() || additionalTags.includes(tagInput.trim())}
              className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium flex items-center gap-2 shadow-md hover:shadow-lg"
            >
              <Plus size={16} />
              {t('characterCreation.basics.addTag')}
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {additionalTags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-2 bg-gradient-to-r from-pink-100 to-rose-100 dark:from-pink-900/50 dark:to-rose-900/50 text-pink-700 dark:text-pink-300 rounded-full text-sm font-medium border border-pink-200 dark:border-pink-700 shadow-sm"
              >
                #{tag}
                <button
                  type="button"
                  onClick={() => removeTag(index)}
                  className="ml-2 w-4 h-4 flex items-center justify-center text-pink-500 hover:text-pink-700 dark:hover:text-pink-200 hover:bg-pink-200 dark:hover:bg-pink-800 rounded-full transition-all duration-200"
                >
                  ×
                </button>
              </span>
            ))}
          </div>

        </div>
      </div>

      {/* Image Crop Modals */}
      <ImageCropModals
        showRatioSelection={false} // 不再使用独立的比例选择器
        characterImageAspectRatio={imageUpload.characterImageAspectRatio}
        setCharacterImageAspectRatio={imageUpload.setCharacterImageAspectRatio}
        onRatioSelectionConfirm={imageUpload.handleRatioSelectionConfirm}
        setShowRatioSelection={imageUpload.setShowRatioSelection}
        showAvatarCrop={imageUpload.showAvatarCrop}
        originalImagePreview={imageUpload.originalImagePreview}
        onAvatarCropComplete={imageUpload.handleAvatarCropComplete}
        onCancelCrop={imageUpload.handleCancelCrop}
        showCharacterImageCrop={imageUpload.showCharacterImageCrop}
        onCharacterImageCropComplete={imageUpload.handleCharacterImageCropComplete}
        lang={lang}
      />
    </div>
  );
};

export default BasicsStep;
