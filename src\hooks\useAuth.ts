import { useState, useCallback } from 'react';
import { 
  login, 
  sendOtp, 
  verifyOtp,
  saveAuthToken,
  LoginRequest,
  OtpRequest 
} from '@/lib/auth-api';
import { useAuthContext } from '@/components/AuthProvider';
import toast from 'react-hot-toast';

interface UseAuthState {
  isLoading: boolean;
  error: string | null;
}

interface UseAuthActions {
  loginUser: (data: LoginRequest) => Promise<void>;
  sendOtpCode: (email: string) => Promise<void>;
  verifyOtpCode: (data: OtpRequest) => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

export function useAuth(): UseAuthState & UseAuthActions {
  const { login: contextLogin, logout: contextLogout } = useAuthContext();
  const [state, setState] = useState<UseAuthState>({
    isLoading: false,
    error: null,
  });

  const setLoading = (isLoading: boolean) => {
    setState(prev => ({ ...prev, isLoading }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({ ...prev, error }));
  };

  // Get captcha token (simplified implementation, actual project needs Google Recaptcha integration)
  const getCaptchaToken = async (): Promise<string> => {
    // TODO: Actual project needs Google Recaptcha v2 integration
    // This returns a placeholder token
    return 'demo-captcha-token';
  };

  const loginUser = useCallback(async (data: LoginRequest) => {
    try {
      setLoading(true);
      setError(null);

      const response = await login(data);

      if (response.success && response.data) {
        saveAuthToken(response.data.token, response.data.refreshToken);
        contextLogin(response.data.user);
        
        // Show success message
        toast.success('Login successful!');
      } else {
        const errorMessage = response.message || 'Login failed';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Login failed, please try again';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [contextLogin]);

  const sendOtpCode = useCallback(async (email: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await sendOtp(email);

      if (response.success) {
        toast.success('Verification code sent, please check your email');
      } else {
        const errorMessage = response.message || 'Failed to send verification code';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Send OTP error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to send verification code, please try again';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const verifyOtpCode = useCallback(async (data: OtpRequest) => {
    try {
      setLoading(true);
      setError(null);

      const response = await verifyOtp(data);

      if (response.success && response.data) {
        saveAuthToken(response.data.token, response.data.refreshToken);
        contextLogin(response.data.user);
        toast.success('Login successful!');
      } else {
        const errorMessage = response.message || 'Invalid verification code';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Verify OTP error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Verification failed, please try again';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [contextLogin]);

  const logout = useCallback(() => {
    contextLogout();
    setError(null);
    toast.success('Logged out successfully');
  }, [contextLogout]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    ...state,
    loginUser,
    sendOtpCode,
    verifyOtpCode,
    logout,
    clearError,
  };
} 