export interface User {
  id: string;
  username: string;
  email: string;
  password_hash: string;
  display_name?: string;
  avatar_url?: string;
  bio?: string;
  language_preference: string;
  timezone: string;
  is_creator: boolean;
  is_verified: boolean;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  profile?: UserProfile;
}

export interface UserProfile {
  id?: string;
  user_id: string;
  avatar_url?: string;
  bio?: string;
  location?: string;
  website?: string;
  birth_date?: Date;
  gender?: string;
  interests?: string[];
  social_links?: Record<string, string>;
  privacy_settings?: Record<string, unknown>;
  created_at?: Date;
  updated_at?: Date;
}

export interface UserCurrency {
  id?: string;
  user_id: string;
  coins: number;
  gems: number;
  tokens: number;
  hearts: number;
  star_diamonds: number;
  joy_crystals: number;
  glimmering_dust: number;
  memory_puzzles: number;
  daily_bonus_available: boolean;
  next_daily_bonus: Date;
  currency_stats?: Record<string, number>;
  created_at?: Date;
  updated_at?: Date;
}