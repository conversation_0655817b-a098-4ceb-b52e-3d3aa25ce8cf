'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, ExternalLink, Eye, Edit, Plus, ToggleLeft, ToggleRight, BarChart3 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/components/AuthProvider';
import { User } from '@/lib/api';

interface LinkItem {
  id: string;
  title: string;
  url: string;
  description: string;
  icon: string;
  clicks: number;
  isActive: boolean;
}

interface LinktreeClientPageProps {
  lang: string;
  uid: string;
  userData: User | null;
  linksData: LinkItem[];
}

const LinktreeClientPage: React.FC<LinktreeClientPageProps> = ({
  lang,
  uid,
  userData,
  linksData
}) => {
  const router = useRouter();
  const { user: currentUser, isAuthenticated } = useAuthContext();
  const [links, setLinks] = useState<LinkItem[]>(linksData);
  
  // Check if this is the current user's own linktree
  const isOwnLinktree = isAuthenticated && currentUser?.uid === uid;
  
  // Use userData if available, otherwise use currentUser for own linktree
  const displayUser = userData || (isOwnLinktree ? currentUser : null);

  if (!displayUser) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">User Not Found</h2>
          <p className="text-gray-600 dark:text-gray-400">The user you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  const handleToggleLink = (linkId: string) => {
    if (!isOwnLinktree) return;
    
    setLinks(prev => prev.map(link => 
      link.id === linkId ? { ...link, isActive: !link.isActive } : link
    ));
  };

  const handleLinkClick = (linkId: string, url: string) => {
    // Track click if it's not the owner viewing their own links
    if (!isOwnLinktree) {
      setLinks(prev => prev.map(link => 
        link.id === linkId ? { ...link, clicks: link.clicks + 1 } : link
      ));
    }
    
    // Open link in new tab
    window.open(url, '_blank');
  };

  const activeLinks = links.filter(link => link.isActive);
  const totalClicks = links.reduce((sum, link) => sum + link.clicks, 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 shadow-sm px-6 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
            >
              <ArrowLeft size={20} />
            </button>
            {isOwnLinktree && (
              <div className="flex items-center gap-2">
                <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors">
                  <BarChart3 size={20} />
                </button>
                <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors">
                  <Edit size={20} />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Profile Section */}
        <div className="bg-white dark:bg-gray-800 px-6 py-8 text-center">
          <Image
            src={displayUser.avatar || "https://i.pravatar.cc/160?u=linktree"}
            alt={displayUser.name || 'User'}
            width={80}
            height={80}
            className="w-20 h-20 rounded-full mx-auto mb-4 object-cover border-4 border-white shadow-lg"
            unoptimized
          />
          <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            {displayUser.name || 'Unknown User'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
            {displayUser.bio || displayUser.sign || 'Welcome to my link tree!'}
          </p>
          
          {isOwnLinktree && (
            <div className="flex justify-center gap-6 text-sm text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <div className="font-semibold text-gray-900 dark:text-white">{activeLinks.length}</div>
                <div>Active Links</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900 dark:text-white">{totalClicks.toLocaleString()}</div>
                <div>Total Clicks</div>
              </div>
            </div>
          )}
        </div>

        {/* Links Section */}
        <div className="px-6 py-4 space-y-3">
          {isOwnLinktree && (
            <button className="w-full p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-indigo-400 dark:hover:border-indigo-500 transition-colors flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400">
              <Plus size={20} />
              Add New Link
            </button>
          )}

          {activeLinks.length === 0 && !isOwnLinktree ? (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No links available yet.</p>
            </div>
          ) : (
            <>
              {(isOwnLinktree ? links : activeLinks).map((link) => (
                <div
                  key={link.id}
                  className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all ${
                    !link.isActive && isOwnLinktree ? 'opacity-50' : ''
                  }`}
                >
                  {isOwnLinktree && (
                    <div className="absolute top-2 right-2 flex items-center gap-2">
                      <button
                        onClick={() => handleToggleLink(link.id)}
                        className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                      >
                        {link.isActive ? (
                          <ToggleRight size={20} className="text-green-500" />
                        ) : (
                          <ToggleLeft size={20} className="text-gray-400" />
                        )}
                      </button>
                    </div>
                  )}
                  
                  <button
                    onClick={() => handleLinkClick(link.id, link.url)}
                    className="w-full p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    disabled={!link.isActive}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{link.icon}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                            {link.title}
                          </h3>
                          <ExternalLink size={14} className="text-gray-400 flex-shrink-0" />
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                          {link.description}
                        </p>
                        {isOwnLinktree && (
                          <div className="flex items-center gap-2 mt-1 text-xs text-gray-500 dark:text-gray-400">
                            <Eye size={12} />
                            {link.clicks.toLocaleString()} clicks
                          </div>
                        )}
                      </div>
                    </div>
                  </button>
                </div>
              ))}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-8 text-center">
          <Link
            href={`/${lang}`}
            className="text-sm text-gray-500 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
          >
            Create your own link tree on Alphane.ai
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LinktreeClientPage;
