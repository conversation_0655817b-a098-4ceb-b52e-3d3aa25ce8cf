'use client';

import React, { useState } from 'react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';
import { useAuthContext } from '@/components/AuthProvider';
import { useSettings } from '@/hooks/useSettings';
import { 
  Shield, 
  MessageCircle, 
  Bell, 
  Lock, 
  Palette, 
  Gamepad2, 
  Database, 
  Crown,
  Settings,
  Save,
  RotateCcw,
  Download
} from 'lucide-react';

// Import setting components
import AIInteractionSettings from '@/components/settings/AIInteractionSettings';
import NotificationSettings from '@/components/settings/NotificationSettings';
import DisplaySettings from '@/components/settings/DisplaySettings';
import GamificationSettings from '@/components/settings/GamificationSettings';
import DataManagementSettings from '@/components/settings/DataManagementSettings';
import PremiumSettings from '@/components/settings/PremiumSettings';

// Create merged Security & Privacy component
import SecurityPrivacySettings from '@/components/settings/SecurityPrivacySettings';



interface SettingsClientPageProps {
  lang: string;
}

interface SettingsCategory {
  id: string;
  titleKey: string;
  descriptionKey: string;
  icon: React.ReactNode;
  isPremium?: boolean;
  component: React.ComponentType<any>;
}

const SettingsClientPage: React.FC<SettingsClientPageProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const { user } = useAuthContext();
  const { state, updateSetting, saveSettings, resetSettings, exportData } = useSettings();
  
  const [activeCategory, setActiveCategory] = useState('securityPrivacy');
  const [showConfirmReset, setShowConfirmReset] = useState(false);

  // Check if user has premium access
  const hasPresimUser = user?.alphane_pass_details?.is_active && 
    (user?.alphane_pass_details as any)?.plan === 'diamond_pass';

  // Debug mode check (for development)
  const isDebugMode = typeof window !== 'undefined' && 
    (window.location.search.includes('debug=true') || 
     localStorage.getItem('settings_debug') === 'true');

  // Use debug mode or actual premium status
  const isPremiumForSettings = hasPresimUser || isDebugMode;

  const categories: SettingsCategory[] = [
    {
      id: 'securityPrivacy',
      titleKey: 'Security & Privacy',
      descriptionKey: 'Account security and privacy settings',
      icon: <Shield className="w-5 h-5" />,
      component: SecurityPrivacySettings
    },
    {
      id: 'interaction',
      titleKey: 'Interaction',
      descriptionKey: 'AI interaction preferences',
      icon: <MessageCircle className="w-5 h-5" />,
      component: AIInteractionSettings
    },
    {
      id: 'notifications',
      titleKey: 'Notifications',
      descriptionKey: 'Notification preferences',
      icon: <Bell className="w-5 h-5" />,
      component: NotificationSettings
    },
    {
      id: 'appearance',
      titleKey: 'Appearance',
      descriptionKey: 'Display and theme settings',
      icon: <Palette className="w-5 h-5" />,
      component: DisplaySettings
    },
    {
      id: 'gamification',
      titleKey: 'Gamification',
      descriptionKey: 'Achievement and progress settings',
      icon: <Gamepad2 className="w-5 h-5" />,
      component: GamificationSettings
    },
    {
      id: 'dataBackup',
      titleKey: 'Data Backup',
      descriptionKey: 'Data export and backup options',
      icon: <Database className="w-5 h-5" />,
      component: DataManagementSettings
    },
    {
      id: 'premium',
      titleKey: 'Premium',
      descriptionKey: 'Premium features and settings',
      icon: <Crown className="w-5 h-5" />,
      isPremium: true,
      component: PremiumSettings
    }
  ];

  const activeComponentData = categories.find(cat => cat.id === activeCategory);
  const ActiveComponent = activeComponentData?.component || SecurityPrivacySettings;

  const handleSaveSettings = async () => {
    await saveSettings();
  };

  const handleResetSettings = () => {
    if (showConfirmReset) {
      resetSettings();
      setShowConfirmReset(false);
    } else {
      setShowConfirmReset(true);
    }
  };

  const handleExportData = () => {
    exportData({
      includeChats: true,
      includeCharacters: true,
      includeMemories: true,
      includeAchievements: true,
      includeSettings: true,
      format: 'json',
      dateRange: 'all'
    });
  };

  if (state.loading) {
    return (
      <MainAppLayout lang={lang}>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center space-y-4">
            <Settings className="w-12 h-12 animate-spin text-primary mx-auto" />
            <p className="text-foreground/70">Loading settings...</p>
          </div>
        </div>
      </MainAppLayout>
    );
  }

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-background text-foreground theme-transition">
        {/* Horizontal Sticky Tabs */}
        <div className="sticky top-12 lg:top-16 z-10 bg-background border-b border-border">
          <div className="max-w-7xl mx-auto px-4">
            {/* Mobile Layout: Icons Only (<1024px) */}
            <div className="lg:hidden">
              <nav className="flex justify-between items-center py-2 gap-1">
                {categories.map((category, index) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-300 ease-out relative group touch-manipulation ${
                      activeCategory === category.id
                        ? 'bg-gradient-to-br from-primary to-primary-pink text-primary-foreground shadow-md shadow-primary/20 scale-105'
                        : 'text-foreground hover:bg-accent hover:text-accent-foreground hover:scale-105 active:scale-95'
                    } ${category.isPremium && !isPremiumForSettings ? 'opacity-50' : ''}`}
                    disabled={category.isPremium && !isPremiumForSettings}
                    title={category.titleKey} // Tooltip for accessibility
                    style={{
                      // Ensure even distribution with proper gaps
                      flex: '1 1 0%',
                      maxWidth: `calc((100% - ${(categories.length - 1) * 4}px) / ${categories.length})`,
                    }}
                  >
                    <div className="relative flex flex-col items-center">
                      {React.cloneElement(category.icon, {
                        className: `w-5 h-5 transition-transform duration-200 ${
                          activeCategory === category.id ? 'scale-110' : 'group-hover:scale-105'
                        }`
                      })}
                      {category.isPremium && !isPremiumForSettings && (
                        <Crown className="w-2.5 h-2.5 text-yellow-500 absolute -top-0.5 -right-0.5 drop-shadow-sm" />
                      )}
                      {/* Active indicator - simplified */}
                      {activeCategory === category.id && (
                        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-0.5 bg-primary-foreground rounded-full" />
                      )}
                      {/* Ripple effect on tap */}
                      <div className="absolute inset-0 rounded-lg overflow-hidden">
                        <div className="absolute inset-0 bg-white/20 rounded-lg scale-0 group-active:scale-100 transition-transform duration-150 ease-out" />
                      </div>
                    </div>
                  </button>
                ))}
              </nav>
            </div>

            {/* Desktop Layout: Icons + Text (>=1024px) */}
            <div className="hidden lg:flex overflow-x-auto scrollbar-hide">
              <nav className="flex gap-2 py-3 px-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`flex items-center gap-3 px-5 py-2.5 rounded-xl whitespace-nowrap transition-all duration-300 ease-out group relative overflow-hidden ${
                      activeCategory === category.id
                        ? 'bg-gradient-to-r from-primary to-primary-pink text-primary-foreground shadow-lg shadow-primary/20'
                        : 'text-foreground hover:bg-accent hover:text-accent-foreground hover:shadow-md'
                    } ${category.isPremium && !isPremiumForSettings ? 'opacity-50' : ''}`}
                    disabled={category.isPremium && !isPremiumForSettings}
                  >
                    {/* Background gradient animation */}
                    {activeCategory === category.id && (
                      <div className="absolute inset-0 bg-gradient-to-r from-primary-pink to-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    )}

                    <div className="relative z-10 flex items-center gap-3">
                      {React.cloneElement(category.icon, {
                        className: `w-5 h-5 transition-transform duration-200 ${
                          activeCategory === category.id ? 'scale-110' : 'group-hover:scale-105'
                        }`
                      })}
                      <span className="font-medium text-sm tracking-wide">
                        {category.titleKey}
                      </span>
                      {category.isPremium && !isPremiumForSettings && (
                        <Crown className="w-3.5 h-3.5 text-yellow-500 drop-shadow-sm" />
                      )}
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </div>

        {/* Debug Mode Indicator */}
        {isDebugMode && (
          <div className="max-w-7xl mx-auto px-4 pt-4">
            <div className="p-3 bg-muted border border-border rounded-lg inline-block">
              <p className="text-sm text-muted-foreground">
                🐛 Debug Mode: Premium features enabled for testing
              </p>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="bg-card rounded-lg border border-border">
            {/* Settings Content */}
            <div className="p-6">
              <ActiveComponent
                settings={state.settings}
                updateSetting={updateSetting}
                lang={lang}
                user={user}
                hasUnsavedChanges={state.hasUnsavedChanges}
                isPremiumUser={!!isPremiumForSettings}
              />
            </div>

            {/* Action Buttons */}
            <div className="p-6 border-t border-border bg-muted/30">
              <div className="flex flex-wrap gap-4 justify-between">
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={handleSaveSettings}
                    disabled={state.saving || !state.hasUnsavedChanges}
                    className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <Save className="w-4 h-4" />
                    {state.saving ? t('settings.saving') : t('settings.actions.save')}
                  </button>

                  <button
                    onClick={handleResetSettings}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                      showConfirmReset
                        ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
                        : 'bg-secondary text-secondary-foreground hover:bg-secondary/90'
                    }`}
                  >
                    <RotateCcw className="w-4 h-4" />
                    {showConfirmReset ? 'Confirm Reset' : t('settings.actions.reset')}
                  </button>

                  {showConfirmReset && (
                    <button
                      onClick={() => setShowConfirmReset(false)}
                      className="px-4 py-2 text-muted-foreground hover:text-foreground transition-colors"
                    >
                      Cancel
                    </button>
                  )}
                </div>

                <button
                  onClick={handleExportData}
                  className="flex items-center gap-2 px-4 py-2 bg-secondary text-secondary-foreground rounded-lg hover:bg-secondary/80 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  {t('settings.actions.export')}
                </button>
              </div>

              {state.hasUnsavedChanges && (
                <div className="mt-4 p-3 bg-muted border border-border rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    {t('settings.unsavedChanges')}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
};

export default SettingsClientPage; 