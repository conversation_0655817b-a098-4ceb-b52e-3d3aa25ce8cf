'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

// New Token Display Component
const TokenDisplay = ({ icon, value, colorClass }: { icon: string; value: string; colorClass: string }) => (
    <div className="flex items-center gap-1">
        {/* Circular icon holder */}
        <div className="glass w-9 h-9 rounded-full flex items-center justify-center">
            <span className={`${colorClass} text-lg`}>{icon}</span>
        </div>
        {/* Value, with responsive text size */}
        <span className="text-xs sm:text-sm font-bold text-slate-700 dark:text-slate-200 pr-1">{value}</span>
    </div>
);

// User Avatar Component
const UserAvatar = () => (
    <div className="flex items-center gap-2 ml-2">
        <div className="relative">
            <div className="w-9 h-9 rounded-full overflow-hidden border-2 border-white/50 shadow-lg hover:border-white/80 transition-all duration-300 cursor-pointer">
                <Image
                    src="https://i.pravatar.cc/40?u=user"
                    alt="User Avatar"
                    width={36}
                    height={36}
                    className="w-full h-full object-cover"
                    unoptimized
                />
            </div>
            {/* Online status indicator */}
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
        </div>
    </div>
);

export default function CharacterPageHeader() {
  const router = useRouter();

  return (
    <header className="glass-strong sticky top-0 z-10 shadow-lg h-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 h-full flex items-center justify-between">
        {/* Left side */}
        <div className="flex items-center gap-2">
          {/* Back button with updated styling */}
          <button 
            onClick={() => router.back()} 
            className="glass p-2 rounded-full hover:bg-white/40 dark:hover:bg-slate-700/40 transition-colors"
          >
            <ArrowLeft className="text-slate-700 dark:text-slate-200" />
          </button>
        </div>

        {/* Right side - Tokens and User Avatar */}
        <div className="flex items-center gap-1 sm:gap-2">
            <TokenDisplay icon="🔥" value="1.2k" colorClass="text-orange-500" />
            <TokenDisplay icon="💎" value="89" colorClass="text-blue-500" />
            <TokenDisplay icon="🧩" value="23" colorClass="text-purple-500" />
            <TokenDisplay icon="💧" value="156" colorClass="text-pink-500" />
            <UserAvatar />
        </div>
      </div>
    </header>
  );
} 