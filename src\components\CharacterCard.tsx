'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { <PERSON>, Users, Share2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Palette, QrCode } from 'lucide-react';
import Link from 'next/link';
import { Character } from '@/lib/mock-data';

interface CharacterCardProps {
  lang: string;
  character: Character;
  stats: {
    likes: number;
    friends: number;
    shares: number;
  };
  aspectRatio: number;
}

const CharacterCard: React.FC<CharacterCardProps> = ({ lang, character, stats, aspectRatio }) => {
  const [bgImageSrc, setBgImageSrc] = useState(character.character_bg_image);
  const [avatarSrc, setAvatarSrc] = useState(character.character_avatar);

  // Mock character descriptions and tags based on character names
  const getCharacterData = (characterName: string) => {
    const characterData: { [key: string]: { description: string; tags: string[]; creator: string } } = {
      'Seraphina': {
        description: 'A mysterious sorceress with ancient knowledge and a caring heart, always ready to guide you through magical adventures.',
        tags: ['#Magic', '#Wisdom', '#Adventure', '#Mystical', '#Caring', '#Ancient'],
        creator: 'MysticM<PERSON>'
      },
      '<PERSON>ele<PERSON>': {
        description: 'A skilled warrior with a strong sense of justice and honor, protecting those who cannot protect themselves.',
        tags: ['#Warrior', '#Justice', '#Honor', '#Protection', '#Brave', '#Noble'],
        creator: 'EpicTales'
      },
      'Lyra': {
        description: 'A playful mage who loves adventure and making new friends, bringing joy and wonder to every encounter.',
        tags: ['#Playful', '#Magic', '#Adventure', '#Friendship', '#Joy', '#Wonder', '#Energetic'],
        creator: 'FunCreator'
      },
      'Riven': {
        description: 'A stoic guardian with unwavering loyalty and determination, standing firm against any challenge.',
        tags: ['#Guardian', '#Loyalty', '#Determination', '#Stoic', '#Strong'],
        creator: 'GuardianMaster'
      },
      'Elara': {
        description: 'A gentle healer with deep wisdom and compassion, offering comfort and guidance in times of need.',
        tags: ['#Healer', '#Wisdom', '#Compassion', '#Gentle', '#Guidance', '#Comfort'],
        creator: 'HealingHeart'
      },
      'Zane': {
        description: 'A cyberpunk hacker with a rebellious spirit and tech expertise, navigating the digital frontier with style.',
        tags: ['#Cyberpunk', '#Hacker', '#Tech', '#Rebellious', '#Digital', '#Style', '#Future'],
        creator: 'CyberCraft'
      },
      'Aiko': {
        description: 'A cheerful and vibrant character with an infectious smile, bringing joy and positivity to every interaction.',
        tags: ['#Cheerful', '#Vibrant', '#Positive', '#Joy', '#Friendly', '#Energetic'],
        creator: 'Sophia'
      },
      'Nova': {
        description: 'A cosmic entity with ethereal beauty and starweaving abilities, connecting the mysteries of the universe.',
        tags: ['#Cosmic', '#Ethereal', '#Stars', '#Universe', '#Mystery', '#Beautiful'],
        creator: 'CosmicDreamer'
      },
      'Kai': {
        description: 'An adventurous storm rider who commands the winds and seeks thrills in the highest peaks and wildest storms.',
        tags: ['#Adventure', '#Storm', '#Wind', '#Thrill', '#Mountain', '#Wild'],
        creator: 'StormChaser'
      },
      'Aria': {
        description: 'A musical soul with a melodious heart, weaving emotions and stories through enchanting songs and melodies.',
        tags: ['#Music', '#Melody', '#Song', '#Emotion', '#Enchanting', '#Artistic'],
        creator: 'MelodyMaker'
      },
      'Phoenix': {
        description: 'A fiery spirit with a passionate flame heart, rising from challenges stronger and more determined than before.',
        tags: ['#Fire', '#Passion', '#Phoenix', '#Strong', '#Determined', '#Rebirth'],
        creator: 'FlameForge'
      },
      'Sage': {
        description: 'A wise keeper of ancient knowledge and secrets, offering profound insights and guidance to those who seek truth.',
        tags: ['#Wisdom', '#Ancient', '#Knowledge', '#Guidance', '#Truth', '#Sage'],
        creator: 'WisdomSeeker'
      },
      'Luna': {
        description: 'A mysterious moon whisperer who dances with shadows and moonlight, revealing secrets hidden in the night.',
        tags: ['#Moon', '#Mystery', '#Shadow', '#Night', '#Whisper', '#Dance'],
        creator: 'MoonDancer'
      },
      'Atlas': {
        description: 'A strong-willed guardian with iron determination, standing as an unshakeable fortress against any adversity.',
        tags: ['#Strong', '#Guardian', '#Iron', '#Fortress', '#Determination', '#Unshakeable'],
        creator: 'IronForge'
      },
      'Iris': {
        description: 'A dreamy artist who weaves reality and imagination, creating beautiful worlds where dreams come to life.',
        tags: ['#Dream', '#Artist', '#Imagination', '#Beautiful', '#Creative', '#Weaver'],
        creator: 'DreamCatcher'
      },
      'Orion': {
        description: 'A noble star guardian who protects the cosmic realm, watching over travelers in the vast expanse of space.',
        tags: ['#Noble', '#Star', '#Guardian', '#Cosmic', '#Space', '#Protector'],
        creator: 'StarGuardian'
      },
      'Vera': {
        description: 'An elegant shadow dancer who moves with grace through darkness, mastering the art of stealth and mystery.',
        tags: ['#Elegant', '#Shadow', '#Dance', '#Grace', '#Stealth', '#Mystery'],
        creator: 'ShadowMaster'
      },
      'Echo': {
        description: 'An ethereal void walker who traverses between dimensions, bringing whispers from otherworldly realms.',
        tags: ['#Ethereal', '#Void', '#Dimension', '#Otherworldly', '#Whisper', '#Walker'],
        creator: 'VoidWalker'
      }
    };
    return characterData[characterName] || {
      description: 'An intriguing character with a unique personality and fascinating stories to share.',
      tags: ['#Unique', '#Personality', '#Stories', '#Intriguing'],
      creator: 'DefaultCreator'
    };
  };

  return (
    <Link href={`/${lang}/chats/${character.id}`} className="block group w-full">
      <div className="relative overflow-hidden rounded-lg shadow-sm group-hover:shadow-md transition-shadow duration-200">
        {/* Placeholder to maintain aspect ratio */}
        <div style={{ paddingBottom: `${(1 / aspectRatio) * 100}%` }} />

        {/* Background */}
        {bgImageSrc && (
          <Image
            src={bgImageSrc}
            alt="bg"
            fill
            className="absolute top-0 left-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
            unoptimized
            onError={() => setBgImageSrc('https://picsum.photos/500/800')}
          />
        )}

        {/* Gradient overlay covering bottom 60% */}
        <div className="absolute bottom-0 left-0 right-0 h-[60%] bg-gradient-to-t from-black/80 via-black/40 to-transparent" />

        {/* Content overlay */}
        <div className="absolute inset-0 p-4 flex flex-col justify-end">
          {/* Tags */}
          <div className="flex flex-wrap gap-1 mb-2">
            {getCharacterData(character.name).tags.slice(0, 6).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-0.5 bg-white/20 backdrop-blur-sm rounded-full text-xs text-white/90 font-medium"
              >
                {tag}
              </span>
            ))}
          </div>

          {/* Character description (complete text) */}
          <p className="font-medium text-xs text-white mb-3">
            {getCharacterData(character.name).description}
          </p>

          {/* Character info and stats */}
          <div className="flex items-center gap-3">
            <Image
              src={avatarSrc}
              alt={character.name}
              width={40}
              height={40}
              className="w-10 h-10 rounded-full border-2 border-white/60 flex-shrink-0 object-cover"
              unoptimized
              onError={() => setAvatarSrc('https://picsum.photos/400/400')}
            />
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="font-bold text-sm text-white truncate">{character.name}</p>
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // TODO: Add share functionality
                    console.log('Share character:', character.name);
                  }}
                  className="hover:text-purple-300 transition-colors cursor-pointer flex-shrink-0"
                >
                  <QrCode size={16} className="text-white/90" />
                </button>
              </div>
              <div className="flex items-center gap-3 mt-1 text-white/90 text-xs">
                <span className="flex items-center gap-0.5"><Users size={14} />{stats.friends}</span>
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.location.href = `/${lang}/creator-profile/${getCharacterData(character.name).creator.toLowerCase()}`;
                  }}
                  className="flex items-center gap-0.5 hover:text-purple-300 transition-colors cursor-pointer"
                >
                  <Palette size={12} />
                  {getCharacterData(character.name).creator}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CharacterCard; 