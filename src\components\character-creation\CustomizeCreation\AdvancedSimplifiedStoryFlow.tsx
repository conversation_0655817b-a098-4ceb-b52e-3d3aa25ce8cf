'use client';

import React, { useState } from 'react';
import { 
  Globe, Clock, MapPin, Users, Crown, Zap, Heart, 
  BookOpen, ChevronDown, ChevronUp, Plus, Trash2,
  Sun, Cloud, Volume2, History, User, Brain, 
  Lightbulb, MessageCircle, Target, Eye
} from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { CharacterFormData } from '@/types/character-creation';

interface AdvancedSimplifiedStoryFlowProps {
  formData: CharacterFormData;
  setFormData: (data: CharacterFormData | ((prev: CharacterFormData) => CharacterFormData)) => void;
  lang?: string;
}

// World Setting Interface (simplified from story-creation)
interface WorldSetting {
  storyName: string;
  worldOverview: string;
  storyBackground: string;
  historicalEra: string;
  geographicEnvironment: string;
  mainRaces: string;
  coreConflict: string;
  physicsRules: string;
  supernaturalElements: string;
  socialForm: string;
  politicalStructure: string;
  economicFoundation: string;
  techLevel: string;
  timeBackground: string;
}

// Enhanced Chapter Interface (based on charstoryscene.tsx)
interface EnhancedChapter {
  id: number;
  title: string;
  description: string;
  
  // Scene Layer
  environment: string;
  timeElements: {
    season: string;
    timeOfDay: string;
    duration: string;
    specialDate: string;
  };
  spatialElements: {
    location: string;
    atmosphere: string;
    keyObjects: string;
  };
  environmentalElements: {
    weather: string;
    lighting: string;
    sounds: string;
    scents: string;
    temperature: string;
  };
  
  // Antecedent Layer
  macroHistory: string;
  characterPast: string;
  immediateTrigger: string;
  
  // Character Psychology
  mentalModel: {
    coreValues: string;
    thinkingMode: string;
    decisionLogic: string;
  };
  emotionalBaseline: {
    displayedEmotion: string;
    hiddenEmotion: string;
    emotionalIntensity: number;
    emotionalStability: number;
  };
  memorySystem: {
    triggeredMemories: string;
    emotionalMemories: string;
    knowledgePriority: string;
  };
  
  // Interaction Dynamics
  dialogueStrategy: {
    initiative: number;
    listeningRatio: number;
    questioningStyle: string;
    responseSpeed: string;
  };
  relationshipDynamics: {
    initialGoodwill: number;
    trustLevel: number;
    intimacyLevel: string;
    powerRelation: string;
  };
  goalOrientation: {
    sceneGoal: string;
    displayedIntent: string;
    hiddenIntent: string;
    successCriteria: string;
  };
}

const AdvancedSimplifiedStoryFlow: React.FC<AdvancedSimplifiedStoryFlowProps> = ({
  formData,
  setFormData,
  lang = 'en'
}) => {
  const { t } = useTranslation(lang, 'translation');
  
  // State management
  const [worldSetting, setWorldSetting] = useState<WorldSetting>({
    storyName: '',
    worldOverview: '',
    storyBackground: '',
    historicalEra: '',
    geographicEnvironment: '',
    mainRaces: '',
    coreConflict: '',
    physicsRules: '',
    supernaturalElements: '',
    socialForm: '',
    politicalStructure: '',
    economicFoundation: '',
    techLevel: '',
    timeBackground: ''
  });
  
  const [chapters, setChapters] = useState<EnhancedChapter[]>([]);
  const [selectedChapter, setSelectedChapter] = useState<number | null>(null);
  const [nextChapterId, setNextChapterId] = useState(1);
  
  // Collapsible sections state
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    worldSetting: true,
    chapters: true,
    scene: false,
    antecedent: false,
    psychology: false,
    interaction: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  // World Setting handlers
  const handleWorldSettingChange = (field: keyof WorldSetting, value: string) => {
    setWorldSetting(prev => ({ ...prev, [field]: value }));
  };

  // Chapter management
  const addChapter = () => {
    if (chapters.length < 15) {
      const newChapter: EnhancedChapter = {
        id: nextChapterId,
        title: `Chapter ${nextChapterId}`,
        description: '',
        environment: '',
        timeElements: { season: '', timeOfDay: '', duration: '', specialDate: '' },
        spatialElements: { location: '', atmosphere: '', keyObjects: '' },
        environmentalElements: { weather: '', lighting: '', sounds: '', scents: '', temperature: '' },
        macroHistory: '',
        characterPast: '',
        immediateTrigger: '',
        mentalModel: { coreValues: '', thinkingMode: '', decisionLogic: '' },
        emotionalBaseline: { displayedEmotion: '', hiddenEmotion: '', emotionalIntensity: 50, emotionalStability: 50 },
        memorySystem: { triggeredMemories: '', emotionalMemories: '', knowledgePriority: '' },
        dialogueStrategy: { initiative: 50, listeningRatio: 50, questioningStyle: '', responseSpeed: '' },
        relationshipDynamics: { initialGoodwill: 50, trustLevel: 50, intimacyLevel: '', powerRelation: '' },
        goalOrientation: { sceneGoal: '', displayedIntent: '', hiddenIntent: '', successCriteria: '' }
      };
      setChapters([...chapters, newChapter]);
      setNextChapterId(nextChapterId + 1);
      setSelectedChapter(newChapter.id);
    }
  };

  const removeChapter = (id: number) => {
    setChapters(chapters.filter(chapter => chapter.id !== id));
    if (selectedChapter === id) {
      setSelectedChapter(chapters.length > 1 ? chapters[0].id : null);
    }
  };

  const updateChapter = (id: number, updates: Partial<EnhancedChapter>) => {
    setChapters(chapters.map(chapter => 
      chapter.id === id ? { ...chapter, ...updates } : chapter
    ));
  };

  const selectedChapterData = chapters.find(c => c.id === selectedChapter);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-2xl font-bold text-purple-800 dark:text-purple-200 mb-2">
          Advanced Story Flow
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Create detailed story chapters with world setting, scenes, and character dynamics
        </p>
      </div>

      {/* World Setting Section */}
      <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700">
        <button
          onClick={() => toggleSection('worldSetting')}
          className="w-full flex items-center justify-between text-left mb-4"
        >
          <h4 className="text-xl font-bold text-purple-800 dark:text-purple-200 flex items-center gap-2">
            <Globe className="w-6 h-6" />
            World Setting
          </h4>
          {expandedSections.worldSetting ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
        </button>

        {expandedSections.worldSetting && (
          <div className="space-y-6">
            {/* Cosmic Laws */}
            <div>
              <h5 className="text-md font-medium text-purple-700 dark:text-purple-300 mb-4 flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Cosmic Laws
              </h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-purple-600 dark:text-purple-400 mb-2">
                    Physics Rules
                  </label>
                  <select
                    value={worldSetting.physicsRules}
                    onChange={(e) => handleWorldSettingChange('physicsRules', e.target.value)}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-purple-300 dark:border-purple-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                  >
                    <option value="">Select physics rules...</option>
                    <option value="realistic">Realistic</option>
                    <option value="soft-scifi">Soft Sci-Fi</option>
                    <option value="high-fantasy">High Fantasy</option>
                    <option value="cosmic-horror">Cosmic Horror</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-purple-600 dark:text-purple-400 mb-2">
                    Tech Level
                  </label>
                  <select
                    value={worldSetting.techLevel}
                    onChange={(e) => handleWorldSettingChange('techLevel', e.target.value)}
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-purple-300 dark:border-purple-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                  >
                    <option value="">Select tech level...</option>
                    <option value="industrial-revolution">Industrial Revolution</option>
                    <option value="information-age">Information Age</option>
                    <option value="cyberpunk-near-future">Cyberpunk Near Future</option>
                    <option value="interstellar-civilization">Interstellar Civilization</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Core Settings */}
            <div>
              <h5 className="text-md font-medium text-purple-700 dark:text-purple-300 mb-4 flex items-center gap-2">
                <Crown className="w-4 h-4" />
                Core Settings
              </h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-purple-600 dark:text-purple-400 mb-2">
                    Time Background
                  </label>
                  <input
                    type="text"
                    value={worldSetting.timeBackground}
                    onChange={(e) => handleWorldSettingChange('timeBackground', e.target.value)}
                    placeholder="e.g., Victorian Era, 2045 AD..."
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-purple-300 dark:border-purple-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-purple-600 dark:text-purple-400 mb-2">
                    Geographic Environment
                  </label>
                  <input
                    type="text"
                    value={worldSetting.geographicEnvironment}
                    onChange={(e) => handleWorldSettingChange('geographicEnvironment', e.target.value)}
                    placeholder="e.g., Urban metropolis, Rural countryside..."
                    className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-purple-300 dark:border-purple-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Chapters Section */}
      <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
        <button
          onClick={() => toggleSection('chapters')}
          className="w-full flex items-center justify-between text-left mb-4"
        >
          <h4 className="text-xl font-bold text-blue-800 dark:text-blue-200 flex items-center gap-2">
            <BookOpen className="w-6 h-6" />
            Story Chapters ({chapters.length}/15)
          </h4>
          {expandedSections.chapters ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
        </button>

        {expandedSections.chapters && (
          <div className="space-y-4">
            {/* Chapter List */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {chapters.map((chapter) => (
                <div
                  key={chapter.id}
                  onClick={() => setSelectedChapter(chapter.id)}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    selectedChapter === chapter.id
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                      : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-semibold text-blue-800 dark:text-blue-200">
                      {chapter.title}
                    </h5>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeChapter(chapter.id);
                      }}
                      className="text-red-500 hover:text-red-700 dark:hover:text-red-400 p-1 rounded"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                    {chapter.description || 'No description'}
                  </p>
                </div>
              ))}

              {/* Add Chapter Button */}
              {chapters.length < 15 && (
                <button
                  onClick={addChapter}
                  className="p-4 border-2 border-dashed border-blue-300 dark:border-blue-700 rounded-lg text-blue-600 dark:text-blue-400 hover:border-blue-400 dark:hover:border-blue-600 transition-all flex items-center justify-center gap-2"
                >
                  <Plus size={18} />
                  Add Chapter
                </button>
              )}
            </div>

            {/* Chapter Editor */}
            {selectedChapterData && (
              <div className="mt-6 space-y-6">
                {/* Chapter Basics */}
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                  <h5 className="font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    Chapter {selectedChapterData.id} - Basics
                  </h5>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Chapter Title
                      </label>
                      <input
                        type="text"
                        value={selectedChapterData.title}
                        onChange={(e) => updateChapter(selectedChapterData.id, { title: e.target.value })}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                        placeholder="Enter chapter title..."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Chapter Description
                      </label>
                      <textarea
                        value={selectedChapterData.description}
                        onChange={(e) => updateChapter(selectedChapterData.id, { description: e.target.value })}
                        rows={3}
                        className="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all resize-none"
                        placeholder="Describe what happens in this chapter..."
                        maxLength={500}
                      />
                    </div>
                  </div>
                </div>

                {/* Objectives - Scene Layer */}
                <div className="bg-gradient-to-br from-blue-50 to-sky-50 dark:from-blue-900/20 dark:to-sky-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
                  <button
                    onClick={() => toggleSection('scene')}
                    className="w-full flex items-center justify-between text-left mb-4"
                  >
                    <h4 className="text-xl font-bold text-blue-800 dark:text-blue-200 flex items-center gap-2">
                      <MapPin className="w-6 h-6" />
                      Objectives - Scene Layer
                    </h4>
                    {expandedSections.scene ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                  </button>

                  {expandedSections.scene && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                          Environment Overview
                        </label>
                        <textarea
                          value={selectedChapterData.environment}
                          onChange={(e) => updateChapter(selectedChapterData.id, { environment: e.target.value })}
                          rows={3}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all resize-none"
                          placeholder="Describe the overall environmental setting for this chapter..."
                          maxLength={500}
                        />
                      </div>

                      {/* Time Elements */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            <Clock className="inline w-4 h-4 mr-1" />
                            Season
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.timeElements.season}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              timeElements: { ...selectedChapterData.timeElements, season: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder="e.g., Spring, Winter..."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            <Sun className="inline w-4 h-4 mr-1" />
                            Time of Day
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.timeElements.timeOfDay}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              timeElements: { ...selectedChapterData.timeElements, timeOfDay: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder="e.g., Morning, Sunset..."
                          />
                        </div>
                      </div>

                      {/* Spatial Elements */}
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            Location
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.spatialElements.location}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              spatialElements: { ...selectedChapterData.spatialElements, location: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder="e.g., Cozy café, Mountain peak..."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            Atmosphere
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.spatialElements.atmosphere}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              spatialElements: { ...selectedChapterData.spatialElements, atmosphere: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder="e.g., Peaceful, Tense, Romantic..."
                          />
                        </div>
                      </div>

                      {/* Environmental Elements */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            <Cloud className="inline w-4 h-4 mr-1" />
                            Weather
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.environmentalElements.weather}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              environmentalElements: { ...selectedChapterData.environmentalElements, weather: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder="e.g., Sunny, Rainy..."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                            <Volume2 className="inline w-4 h-4 mr-1" />
                            Sounds
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.environmentalElements.sounds}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              environmentalElements: { ...selectedChapterData.environmentalElements, sounds: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-blue-300 dark:border-blue-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all"
                            placeholder="e.g., Birds chirping, City noise..."
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Objectives - Antecedent Layer */}
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
                  <button
                    onClick={() => toggleSection('antecedent')}
                    className="w-full flex items-center justify-between text-left mb-4"
                  >
                    <h4 className="text-xl font-bold text-green-800 dark:text-green-200 flex items-center gap-2">
                      <History className="w-6 h-6" />
                      Objectives - Antecedent Layer
                    </h4>
                    {expandedSections.antecedent ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                  </button>

                  {expandedSections.antecedent && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                          <History className="inline w-4 h-4 mr-1" />
                          Macro History
                        </label>
                        <textarea
                          value={selectedChapterData.macroHistory}
                          onChange={(e) => updateChapter(selectedChapterData.id, { macroHistory: e.target.value })}
                          rows={3}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all resize-none"
                          placeholder="Broader historical context, world events, or background story..."
                          maxLength={500}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                          <User className="inline w-4 h-4 mr-1" />
                          Character Past
                        </label>
                        <textarea
                          value={selectedChapterData.characterPast}
                          onChange={(e) => updateChapter(selectedChapterData.id, { characterPast: e.target.value })}
                          rows={3}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all resize-none"
                          placeholder="Character's relevant past experiences, memories, or background..."
                          maxLength={500}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                          <Zap className="inline w-4 h-4 mr-1" />
                          Immediate Trigger
                        </label>
                        <textarea
                          value={selectedChapterData.immediateTrigger}
                          onChange={(e) => updateChapter(selectedChapterData.id, { immediateTrigger: e.target.value })}
                          rows={2}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-green-300 dark:border-green-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-green-500 focus:ring-2 focus:ring-green-500/20 transition-all resize-none"
                          placeholder="What immediately triggers this scene or chapter..."
                          maxLength={300}
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Subjectives - Character Psychology */}
                <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700">
                  <button
                    onClick={() => toggleSection('psychology')}
                    className="w-full flex items-center justify-between text-left mb-4"
                  >
                    <h4 className="text-xl font-bold text-purple-800 dark:text-purple-200 flex items-center gap-2">
                      <Brain className="w-6 h-6" />
                      Subjectives - Character Psychology
                    </h4>
                    {expandedSections.psychology ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                  </button>

                  {expandedSections.psychology && (
                    <div className="space-y-4">
                      {/* Mental Model */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-purple-700 dark:text-purple-300 flex items-center gap-2">
                          <Lightbulb className="w-4 h-4" />
                          Mental Model
                        </h5>

                        <div>
                          <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                            Core Values
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.mentalModel.coreValues}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              mentalModel: { ...selectedChapterData.mentalModel, coreValues: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                            placeholder="e.g., Honesty, Freedom, Family..."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                            Thinking Mode
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.mentalModel.thinkingMode}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              mentalModel: { ...selectedChapterData.mentalModel, thinkingMode: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                            placeholder="e.g., Analytical, Intuitive, Emotional..."
                          />
                        </div>
                      </div>

                      {/* Emotional Baseline */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-purple-700 dark:text-purple-300 flex items-center gap-2">
                          <Heart className="w-4 h-4" />
                          Emotional Baseline
                        </h5>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                              Displayed Emotion
                            </label>
                            <input
                              type="text"
                              value={selectedChapterData.emotionalBaseline.displayedEmotion}
                              onChange={(e) => updateChapter(selectedChapterData.id, {
                                emotionalBaseline: { ...selectedChapterData.emotionalBaseline, displayedEmotion: e.target.value }
                              })}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                              placeholder="e.g., Calm, Excited, Nervous..."
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                              Hidden Emotion
                            </label>
                            <input
                              type="text"
                              value={selectedChapterData.emotionalBaseline.hiddenEmotion}
                              onChange={(e) => updateChapter(selectedChapterData.id, {
                                emotionalBaseline: { ...selectedChapterData.emotionalBaseline, hiddenEmotion: e.target.value }
                              })}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-purple-300 dark:border-purple-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                              placeholder="e.g., Anxious, Hopeful, Doubtful..."
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">
                            Emotional Intensity: {selectedChapterData.emotionalBaseline.emotionalIntensity}%
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={selectedChapterData.emotionalBaseline.emotionalIntensity}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              emotionalBaseline: { ...selectedChapterData.emotionalBaseline, emotionalIntensity: parseInt(e.target.value) }
                            })}
                            className="w-full h-2 bg-purple-200 rounded-lg appearance-none cursor-pointer dark:bg-purple-700"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Subjectives - Interaction Dynamics */}
                <div className="bg-gradient-to-br from-rose-50 to-orange-50 dark:from-rose-900/20 dark:to-orange-900/20 rounded-xl p-6 border border-rose-200 dark:border-rose-700">
                  <button
                    onClick={() => toggleSection('interaction')}
                    className="w-full flex items-center justify-between text-left mb-4"
                  >
                    <h4 className="text-xl font-bold text-rose-800 dark:text-rose-200 flex items-center gap-2">
                      <Users className="w-6 h-6" />
                      Subjectives - Interaction Dynamics
                    </h4>
                    {expandedSections.interaction ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                  </button>

                  {expandedSections.interaction && (
                    <div className="space-y-4">
                      {/* Dialogue Strategy */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-rose-700 dark:text-rose-300 flex items-center gap-2">
                          <MessageCircle className="w-4 h-4" />
                          Dialogue Strategy
                        </h5>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              Initiative: {selectedChapterData.dialogueStrategy.initiative}%
                            </label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={selectedChapterData.dialogueStrategy.initiative}
                              onChange={(e) => updateChapter(selectedChapterData.id, {
                                dialogueStrategy: { ...selectedChapterData.dialogueStrategy, initiative: parseInt(e.target.value) }
                              })}
                              className="w-full h-2 bg-rose-200 rounded-lg appearance-none cursor-pointer dark:bg-rose-700"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              Listening Ratio: {selectedChapterData.dialogueStrategy.listeningRatio}%
                            </label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={selectedChapterData.dialogueStrategy.listeningRatio}
                              onChange={(e) => updateChapter(selectedChapterData.id, {
                                dialogueStrategy: { ...selectedChapterData.dialogueStrategy, listeningRatio: parseInt(e.target.value) }
                              })}
                              className="w-full h-2 bg-rose-200 rounded-lg appearance-none cursor-pointer dark:bg-rose-700"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                            Questioning Style
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.dialogueStrategy.questioningStyle}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              dialogueStrategy: { ...selectedChapterData.dialogueStrategy, questioningStyle: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                            placeholder="e.g., Direct, Subtle, Probing..."
                          />
                        </div>
                      </div>

                      {/* Relationship Dynamics */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-rose-700 dark:text-rose-300 flex items-center gap-2">
                          <Heart className="w-4 h-4" />
                          Relationship Dynamics
                        </h5>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              Initial Goodwill: {selectedChapterData.relationshipDynamics.initialGoodwill}%
                            </label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={selectedChapterData.relationshipDynamics.initialGoodwill}
                              onChange={(e) => updateChapter(selectedChapterData.id, {
                                relationshipDynamics: { ...selectedChapterData.relationshipDynamics, initialGoodwill: parseInt(e.target.value) }
                              })}
                              className="w-full h-2 bg-rose-200 rounded-lg appearance-none cursor-pointer dark:bg-rose-700"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              Trust Level: {selectedChapterData.relationshipDynamics.trustLevel}%
                            </label>
                            <input
                              type="range"
                              min="0"
                              max="100"
                              value={selectedChapterData.relationshipDynamics.trustLevel}
                              onChange={(e) => updateChapter(selectedChapterData.id, {
                                relationshipDynamics: { ...selectedChapterData.relationshipDynamics, trustLevel: parseInt(e.target.value) }
                              })}
                              className="w-full h-2 bg-rose-200 rounded-lg appearance-none cursor-pointer dark:bg-rose-700"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                            Intimacy Level
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.relationshipDynamics.intimacyLevel}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              relationshipDynamics: { ...selectedChapterData.relationshipDynamics, intimacyLevel: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                            placeholder="e.g., Strangers, Acquaintances, Close friends..."
                          />
                        </div>
                      </div>

                      {/* Goal Orientation */}
                      <div className="space-y-3">
                        <h5 className="font-semibold text-rose-700 dark:text-rose-300 flex items-center gap-2">
                          <Target className="w-4 h-4" />
                          Goal Orientation
                        </h5>

                        <div>
                          <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                            Scene Goal
                          </label>
                          <input
                            type="text"
                            value={selectedChapterData.goalOrientation.sceneGoal}
                            onChange={(e) => updateChapter(selectedChapterData.id, {
                              goalOrientation: { ...selectedChapterData.goalOrientation, sceneGoal: e.target.value }
                            })}
                            className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                            placeholder="What the character wants to achieve in this scene..."
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              Displayed Intent
                            </label>
                            <input
                              type="text"
                              value={selectedChapterData.goalOrientation.displayedIntent}
                              onChange={(e) => updateChapter(selectedChapterData.id, {
                                goalOrientation: { ...selectedChapterData.goalOrientation, displayedIntent: e.target.value }
                              })}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                              placeholder="What they appear to want..."
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-rose-700 dark:text-rose-300 mb-2">
                              Hidden Intent
                            </label>
                            <input
                              type="text"
                              value={selectedChapterData.goalOrientation.hiddenIntent}
                              onChange={(e) => updateChapter(selectedChapterData.id, {
                                goalOrientation: { ...selectedChapterData.goalOrientation, hiddenIntent: e.target.value }
                              })}
                              className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-rose-300 dark:border-rose-700 rounded-lg text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-rose-500 focus:ring-2 focus:ring-rose-500/20 transition-all"
                              placeholder="What they really want..."
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedSimplifiedStoryFlow;
