'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslation } from '@/app/i18n/client';
import { Character } from '@/lib/mock-data';

interface CharacterInfoDisplayProps {
  character: Character;
  lang: string;
}

const CharacterInfoDisplay: React.FC<CharacterInfoDisplayProps> = ({ character, lang }) => {
  const { t } = useTranslation(lang, 'translation');
  
  const formatFollowers = (count: number) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 shadow-xl">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }} />
      
      <div className="relative z-10 p-4 md:p-6 flex items-center gap-4 md:gap-6 w-full">
        {/* Character Avatar */}
        <div className="relative">
          <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-32 md:h-32 lg:w-40 lg:h-40 rounded-full overflow-hidden flex-shrink-0">
            <Image
              src={character.character_avatar}
              alt={character.name}
              width={160}
              height={160}
              className="w-full h-full object-cover"
              unoptimized
            />
          </div>
        </div>

        {/* Character Info */}
        <div className="text-white flex-1 min-w-0">
          {/* Character Name */}
          <div className="flex items-center gap-2 md:gap-4 mb-2">
            <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold truncate">
              {character.name}
            </h2>
          </div>

          {/* Creator Info and Description */}
          <div className="flex items-start gap-2 mb-3 md:mb-4">
            <div className="flex-1">
              <p className="text-xs sm:text-sm opacity-90 mb-2">
                {t('storyCreation.characterInfo.creator')}: {character.creator_uid ? (
                  <Link
                    href={`/${lang}/creator-profile/${character.creator_uid}`}
                    className="text-indigo-200 hover:text-indigo-100 font-medium"
                  >
                    @{character.creator_name || 'Sophia'}
                  </Link>
                ) : (
                  <span className="text-indigo-200 font-medium">@Sophia</span>
                )}
              </p>
              <p className="text-xs sm:text-sm opacity-90 line-clamp-2 md:line-clamp-3 max-w-md">
                {character.description || t('storyCreation.characterInfo.defaultDescription')}
              </p>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 sm:grid-cols-6 gap-3 md:gap-4 text-center">
            <div>
              <div className="text-lg sm:text-xl md:text-2xl font-bold text-white">
                {formatFollowers(10000)}
              </div>
              <div className="text-xs text-white/80">{t('storyCreation.characterInfo.followers')}</div>
            </div>
            <div>
              <div className="text-lg sm:text-xl md:text-2xl font-bold text-white">
                500K
              </div>
              <div className="text-xs text-white/80">{t('storyCreation.characterInfo.chats')}</div>
            </div>
            <div>
              <div className="text-lg sm:text-xl md:text-2xl font-bold text-white">
                #3
              </div>
              <div className="text-xs text-white/80">{t('storyCreation.characterInfo.weeklyRank')}</div>
            </div>
            <div>
              <div className="text-lg sm:text-xl md:text-2xl font-bold text-white">
                127
              </div>
              <div className="text-xs text-white/80">{t('storyCreation.characterInfo.stories')}</div>
            </div>
            <div>
              <div className="text-lg sm:text-xl md:text-2xl font-bold text-white">
                89%
              </div>
              <div className="text-xs text-white/80">{t('storyCreation.characterInfo.satisfaction')}</div>
            </div>
            <div>
              <div className="text-lg sm:text-xl md:text-2xl font-bold text-white">
                4.8
              </div>
              <div className="text-xs text-white/80">{t('storyCreation.characterInfo.rating')}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CharacterInfoDisplay;
