// 故事线相关类型定义

export interface Story {
  id: string;
  title: string;
  description: string;
  openingMessage: string;
  coverImage: string;
  characterId: string;
  creatorUid: string;
  creatorName: string;
  difficulty: 'easy' | 'normal' | 'hard';
  estimatedDuration: string;
  tags: string[];
  stats: {
    plays: number;
    likes: number;
    rating: number;
    shares: number;
  };
  progress: {
    currentChapter: number;
    totalChapters: number;
    completedChapters: number[];
  };
  status: 'active' | 'draft' | 'archived';
  createdAt: string;
  updatedAt: string;
}

export interface StoryChapter {
  id: number;
  title: string;
  description: string;
  status: 'completed' | 'current' | 'locked';
  unlockCondition?: {
    type: 'previous_chapter' | 'bond_level' | 'item' | 'achievement';
    value?: string | number;
    description: string;
  };
}

export interface StoryChoice {
  id: string;
  title: string;
  description: string;
  icon: string;
  consequences?: string;
}

export interface StoryReward {
  type: 'alphane_dust' | 'endora_crystal' | 'bond_exp' | 'puzzle_piece' | 'item';
  amount: number;
  name: string;
  icon: string;
  rarity?: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface StoryAchievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  rarity: 'bronze' | 'silver' | 'gold' | 'platinum';
  progress?: {
    current: number;
    total: number;
  };
  unlocked: boolean;
}

export interface StoryCharacter {
  characterId: string;
  name: string;
  avatar: string;
  role: 'main' | 'supporting' | 'minor';
  bondLevel?: number;
  unlocked: boolean;
}

// 故事详情页面的完整数据结构
export interface StoryDetailData {
  story: Story;
  chapters: StoryChapter[];
  choices: StoryChoice[];
  rewards: StoryReward[];
  achievements: StoryAchievement[];
  characters: StoryCharacter[];
  unlockConditions: {
    bondLevel: number;
    tutorialCompleted: boolean;
    hasMonthlyPass: boolean;
  };
}
