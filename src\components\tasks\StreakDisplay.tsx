'use client';

import React from 'react';
import { Flame, Target, Award } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface StreakDisplayProps {
  streakData: {
    currentStreak: number;
    longestStreak: number;
    nextMilestone: number;
    daysUntilMilestone: number;
  };
  lang: string;
}

const StreakDisplay: React.FC<StreakDisplayProps> = ({ streakData, lang }) => {
  const { t } = useTranslation(lang, 'translation');

  const { currentStreak, longestStreak, nextMilestone, daysUntilMilestone } = streakData;
  const progressToMilestone = ((currentStreak % nextMilestone) / nextMilestone) * 100;

  const getMilestoneReward = (milestone: number) => {
    const milestones: { [key: number]: string } = {
      3: t('tasks.streakInfo.milestones.3'),
      7: t('tasks.streakInfo.milestones.7'),
      15: t('tasks.streakInfo.milestones.15'),
      30: t('tasks.streakInfo.milestones.30'),
      60: t('tasks.streakInfo.milestones.60'),
      100: t('tasks.streakInfo.milestones.100'),
      365: t('tasks.streakInfo.milestones.365')
    };
    
    return milestones[milestone] || `${milestone} Day Milestone`;
  };

  return (
    <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-6 theme-transition">
      <div className="flex items-center gap-3 mb-6">
        <Flame className="w-6 h-6 text-orange-500" />
        <h2 className="text-xl font-semibold text-foreground">
          {t('tasks.streakInfo.title')}
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Current Streak */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-2">
            <div className="text-3xl font-bold text-orange-500 mb-1">
              {currentStreak}
            </div>
            <div className="text-sm text-foreground/70">
              {t('tasks.streakInfo.currentStreak')}
            </div>
          </div>
          <div className="flex items-center justify-center gap-1">
            <Flame className="w-4 h-4 text-orange-400" />
            <span className="text-sm text-foreground/80">
              {t('tasks.overview.streakDays', { days: currentStreak })}
            </span>
          </div>
        </div>

        {/* Longest Streak */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-2">
            <div className="text-3xl font-bold text-purple-500 mb-1">
              {longestStreak}
            </div>
            <div className="text-sm text-foreground/70">
              {t('tasks.streakInfo.longestStreak')}
            </div>
          </div>
          <div className="flex items-center justify-center gap-1">
            <Award className="w-4 h-4 text-purple-400" />
            <span className="text-sm text-foreground/80">Personal Best</span>
          </div>
        </div>

        {/* Next Milestone */}
        <div className="text-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-2">
            <div className="text-3xl font-bold text-blue-500 mb-1">
              {nextMilestone}
            </div>
            <div className="text-sm text-foreground/70">
              {t('tasks.streakInfo.nextMilestone')}
            </div>
          </div>
          <div className="flex items-center justify-center gap-1">
            <Target className="w-4 h-4 text-blue-400" />
            <span className="text-sm text-foreground/80">
              {t('tasks.overview.nextMilestone', { days: daysUntilMilestone })}
            </span>
          </div>
        </div>
      </div>

      {/* Progress to Next Milestone */}
      <div className="mt-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-foreground">
            Progress to {nextMilestone}-day milestone
          </span>
          <span className="text-sm text-foreground/70">
            {currentStreak}/{nextMilestone}
          </span>
        </div>
        <div className="w-full bg-orange-100 dark:bg-orange-900/30 rounded-full h-3">
          <div 
            className="bg-gradient-to-r from-orange-400 to-red-400 h-3 rounded-full transition-all duration-500"
            style={{ width: `${Math.min(progressToMilestone, 100)}%` }}
          />
        </div>
        <div className="mt-2 text-sm text-foreground/60">
          {getMilestoneReward(nextMilestone)}
        </div>
      </div>

      {/* Streak Tips */}
      <div className="mt-6 bg-white/50 dark:bg-gray-800/50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-foreground mb-2">
          💡 Tips to maintain your streak:
        </h4>
        <ul className="text-sm text-foreground/70 space-y-1">
          <li>• Complete at least one meaningful conversation daily</li>
          <li>• Save important moments to your memory capsule</li>
          <li>• Use Streak Freeze cards when you need a break</li>
          <li>• Check in during your favorite time of day</li>
        </ul>
      </div>
    </div>
  );
};

export default StreakDisplay; 