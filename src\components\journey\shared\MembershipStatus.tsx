import React from 'react';
import { <PERSON>, <PERSON>rk<PERSON>, Settings, Gem, Zap, Globe } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

export type MembershipTier = 'standard' | 'pass' | 'diamond' | 'metaverse';

interface MembershipStatusProps {
  membershipTier: MembershipTier;
  showUpgradeButton?: boolean;
  onUpgradeClick?: () => void;
  onSettingsClick?: () => void;
  lang: string;
  compact?: boolean;
}

interface MembershipBenefitsDisplayProps {
  membershipTier: MembershipTier;
  lang: string;
  compact?: boolean;
}

export const MembershipStatus: React.FC<MembershipStatusProps> = ({
  membershipTier,
  showUpgradeButton = true,
  onUpgradeClick,
  onSettingsClick,
  lang,
  compact = false
}) => {
  const { t } = useTranslation(lang, 'translation');

  const getMembershipConfig = (tier: MembershipTier) => {
    switch (tier) {
      case 'standard':
        return {
          name: t('journey.membership.freeVersion'),
          color: 'text-gray-400',
          bgGradient: 'from-gray-400 to-gray-600',
          icon: Crown,
          buttonStyle: 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 text-blue-400 animate-pulse'
        };
      case 'pass':
        return {
          name: t('journey.membership.passMember'),
          color: 'text-blue-400',
          bgGradient: 'from-blue-400 to-blue-600',
          icon: Crown,
          buttonStyle: 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30 text-blue-400'
        };
      case 'diamond':
        return {
          name: t('journey.membership.diamondMember'),
          color: 'text-cyan-400',
          bgGradient: 'from-cyan-400 to-cyan-600',
          icon: Gem,
          buttonStyle: 'bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border border-cyan-500/30 text-cyan-400'
        };
      case 'metaverse':
        return {
          name: t('journey.membership.metaverseMember'),
          color: 'text-purple-400',
          bgGradient: 'from-purple-400 to-purple-600',
          icon: Globe,
          buttonStyle: 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 text-purple-400'
        };
    }
  };

  const config = getMembershipConfig(membershipTier);
  const IconComponent = config.icon;

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <div className={`w-8 h-8 bg-gradient-to-br ${config.bgGradient} rounded-lg flex items-center justify-center ${membershipTier !== 'standard' ? 'animate-pulse-scale' : ''}`}>
          <IconComponent className="w-4 h-4 text-white" />
        </div>
        <span className={`font-medium ${config.color}`}>
          {config.name}
        </span>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className={`w-12 h-12 bg-gradient-to-br ${config.bgGradient} rounded-xl flex items-center justify-center shadow-lg ${membershipTier !== 'standard' ? 'animate-pulse-scale' : ''}`}>
          <IconComponent className="w-6 h-6 text-white" />
        </div>
        <div>
          <div className="text-lg font-bold text-foreground">
            {config.name}
          </div>
          {membershipTier !== 'standard' && (
            <div className={`text-sm ${config.color}`}>{t('journey.membership.prioritySupport')}</div>
          )}
        </div>
      </div>

      <div className="flex gap-2">
        {showUpgradeButton && (
          <button
            onClick={onUpgradeClick}
            className={`px-4 py-2 rounded-xl hover:scale-105 transition-all font-medium ${config.buttonStyle}`}
          >
            <IconComponent className="w-4 h-4 inline mr-1" />
            {membershipTier === 'standard' ? '升级会员' : config.name}
          </button>
        )}
        {onSettingsClick && (
          <button 
            onClick={onSettingsClick}
            className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl transition-all"
          >
            <Settings className="w-5 h-5 text-foreground/70" />
          </button>
        )}
      </div>
    </div>
  );
};

export const MembershipBenefitsDisplay: React.FC<MembershipBenefitsDisplayProps> = ({
  membershipTier,
  lang,
  compact = false
}) => {
  const { t } = useTranslation(lang, 'translation');

  if (membershipTier === 'standard') return null;

  const membershipBenefits = {
    pass: ['无限对话', '优先支持', '基础分析'],
    diamond: ['钻石特权', '高级功能', '专属内容', '优先队列'],
    metaverse: ['元宇宙体验', '全部功能', '专属顾问', '限量内容', '最高优先级']
  };

  const benefits = membershipBenefits[membershipTier as keyof typeof membershipBenefits] || [];

  const getGradientStyle = () => {
    switch (membershipTier) {
      case 'pass':
        return 'from-blue-500/10 to-cyan-500/10 border-blue-500/30';
      case 'diamond':
        return 'from-cyan-500/10 to-blue-500/10 border-cyan-500/30';
      case 'metaverse':
        return 'from-purple-500/10 to-pink-500/10 border-purple-500/30';
      default:
        return 'from-blue-500/10 to-purple-500/10 border-blue-500/30';
    }
  };

  const getTextColor = () => {
    switch (membershipTier) {
      case 'pass':
        return 'text-blue-400';
      case 'diamond':
        return 'text-cyan-400';
      case 'metaverse':
        return 'text-purple-400';
      default:
        return 'text-blue-400';
    }
  };

  return (
    <div className={`p-4 bg-gradient-to-r ${getGradientStyle()} rounded-xl ${compact ? 'mb-4' : 'mb-6'}`}>
      <div className="flex items-center gap-2 mb-2">
        <Sparkles className={`w-5 h-5 ${getTextColor()}`} />
        <span className={`${getTextColor()} font-medium`}>会员特权</span>
      </div>
      <div className={`flex ${compact ? 'flex-col gap-1' : 'flex-wrap gap-2'}`}>
        {benefits.map((benefit, index) => (
          <span 
            key={index} 
            className={`px-3 py-1 bg-white/10 border border-white/20 rounded-full ${getTextColor()} ${compact ? 'text-xs' : 'text-sm'}`}
          >
            {benefit}
          </span>
        ))}
      </div>
    </div>
  );
}; 