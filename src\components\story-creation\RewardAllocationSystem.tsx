'use client';

import React, { useState } from 'react';
import { Plus, Minus, RotateCcw, Trophy } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { 
  StoryRewardAllocation, 
  RewardAllocation, 
  RewardType
} from '@/types/story-creation';
import { REWARD_TYPES } from '@/types/story-creation';

interface RewardAllocationSystemProps {
  allocation: StoryRewardAllocation;
  onAllocationChange: (allocation: StoryRewardAllocation) => void;
  lang: string;
}

const RewardAllocationSystem: React.FC<RewardAllocationSystemProps> = ({
  allocation,
  onAllocationChange,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  
  const remainingBudget = allocation.totalBudget - allocation.usedBudget;

  // 检查是否能添加新的奖励类型
  const canAddNewRewardType = (rewardTypeId: string): boolean => {
    const currentAllocations = allocation.allocations;
    const hasExisting = currentAllocations.some(a => a.type.id === rewardTypeId);
    
    if (hasExisting) return true; // 已存在的可以修改
    
    // 检查最多3种限制
    if (currentAllocations.length >= 3) return false;
    
    // 检查互斥规则
    const hasBondLevelUp = currentAllocations.some(a => a.type.id === 'bond_level_up');
    const hasBondPoints = currentAllocations.some(a => a.type.id === 'bond_points');
    
    if (rewardTypeId === 'bond_level_up' && hasBondPoints) return false;
    if (rewardTypeId === 'bond_points' && hasBondLevelUp) return false;
    
    // 检查货币类型限制（最多选2种）
    const currencyTypes = ['alphane', 'endora', 'puzzle'];
    const currentCurrencyCount = currentAllocations.filter(a => 
      currencyTypes.includes(a.type.id)
    ).length;
    
    if (currencyTypes.includes(rewardTypeId) && currentCurrencyCount >= 2) return false;
    
    return true;
  };

  // 更新分配数量
  const updateAllocation = (rewardTypeId: string, amount: number, customText?: string) => {
    const rewardType = REWARD_TYPES.find(rt => rt.id === rewardTypeId);
    if (!rewardType) return;

    const newAllocations = [...allocation.allocations];
    const existingIndex = newAllocations.findIndex(a => a.type.id === rewardTypeId);
    
    if (amount <= 0) {
      // 移除分配
      if (existingIndex >= 0) {
        newAllocations.splice(existingIndex, 1);
      }
    } else {
      // 检查是否能添加新类型
      if (!canAddNewRewardType(rewardTypeId)) {
        return;
      }

      // 检查是否超出预算
      const totalCost = amount * rewardType.cost;
      const otherAllocations = newAllocations.filter(a => a.type.id !== rewardTypeId);
      const otherCost = otherAllocations.reduce((sum, a) => sum + a.totalCost, 0);
      
      if (otherCost + totalCost > allocation.totalBudget) {
        return; // 超出预算，不更新
      }

      // 检查最大数量限制
      if (rewardType.maxAmount && amount > rewardType.maxAmount) {
        amount = rewardType.maxAmount;
      }

      const existingAllocation = existingIndex >= 0 ? newAllocations[existingIndex] : null;
      const newAllocation: RewardAllocation = {
        id: existingAllocation?.id || `${rewardTypeId}-${Date.now()}`,
        type: rewardType,
        amount,
        cost: rewardType.cost,
        totalCost: amount * rewardType.cost,
        customText: customText !== undefined ? customText : existingAllocation?.customText
      };

      if (existingIndex >= 0) {
        newAllocations[existingIndex] = newAllocation;
      } else {
        newAllocations.push(newAllocation);
      }
    }

    const newUsedBudget = newAllocations.reduce((sum, a) => sum + a.totalCost, 0);
    
    onAllocationChange({
      ...allocation,
      allocations: newAllocations,
      usedBudget: newUsedBudget
    });
  };

  // 更新自定义文字
  const updateCustomText = (rewardTypeId: string, customText: string) => {
    const existingAllocation = allocation.allocations.find(a => a.type.id === rewardTypeId);
    if (existingAllocation) {
      updateAllocation(rewardTypeId, existingAllocation.amount, customText);
    }
  };

  // 获取当前分配数量
  const getCurrentAmount = (rewardTypeId: string): number => {
    const existing = allocation.allocations.find(a => a.type.id === rewardTypeId);
    return existing ? existing.amount : 0;
  };

  // 获取当前自定义文字
  const getCurrentCustomText = (rewardTypeId: string): string => {
    const existing = allocation.allocations.find(a => a.type.id === rewardTypeId);
    return existing?.customText || '';
  };

  // 获取最大可分配数量
  const getMaxAmount = (rewardType: RewardType): number => {
    const currentAmount = getCurrentAmount(rewardType.id);
    const otherAllocations = allocation.allocations.filter(a => a.type.id !== rewardType.id);
    const otherCost = otherAllocations.reduce((sum, a) => sum + a.totalCost, 0);
    const availableBudget = allocation.totalBudget - otherCost;
    const maxByBudget = Math.floor(availableBudget / rewardType.cost);
    
    if (rewardType.maxAmount) {
      return Math.min(maxByBudget, rewardType.maxAmount);
    }
    return maxByBudget;
  };

  // 一键拉满功能（仅限前四种便宜的奖励）
  const maxOutReward = (rewardTypeId: string) => {
    const rewardType = REWARD_TYPES.find(rt => rt.id === rewardTypeId);
    if (!rewardType) return;
    
    const maxAmount = getMaxAmount(rewardType);
    if (maxAmount > 0) {
      updateAllocation(rewardTypeId, maxAmount);
    }
  };

  // 一键清空功能（仅限前四种便宜的奖励）
  const clearReward = (rewardTypeId: string) => {
    updateAllocation(rewardTypeId, 0);
  };

  // 检查是否被限制
  const isRewardTypeBlocked = (rewardTypeId: string): boolean => {
    return !canAddNewRewardType(rewardTypeId) && getCurrentAmount(rewardTypeId) === 0;
  };

  // 获取限制原因
  const getBlockReason = (rewardTypeId: string): string => {
    if (canAddNewRewardType(rewardTypeId)) return '';
    
    const currentAllocations = allocation.allocations;
    
    if (currentAllocations.length >= 3) {
      return t('storyCreation.rewardAllocation.maxTypesReached');
    }
    
    const hasBondLevelUp = currentAllocations.some(a => a.type.id === 'bond_level_up');
    const hasBondPoints = currentAllocations.some(a => a.type.id === 'bond_points');
    
    if (rewardTypeId === 'bond_level_up' && hasBondPoints) {
      return t('storyCreation.rewardAllocation.bondTypesExclusive');
    }
    if (rewardTypeId === 'bond_points' && hasBondLevelUp) {
      return t('storyCreation.rewardAllocation.bondTypesExclusive');
    }
    
    const currencyTypes = ['alphane', 'endora', 'puzzle'];
    const currentCurrencyCount = currentAllocations.filter(a => 
      currencyTypes.includes(a.type.id)
    ).length;
    
    if (currencyTypes.includes(rewardTypeId) && currentCurrencyCount >= 2) {
      return t('storyCreation.rewardAllocation.maxCurrencyTypes');
    }
    
    return '';
  };

  // 重置所有分配
  const resetAllAllocations = () => {
    onAllocationChange({
      ...allocation,
      allocations: [],
      usedBudget: 0
    });
  };

  return (
    <div className="space-y-4">
      {/* 预算显示 */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
            <Trophy className="text-purple-500" size={20} />
            {t('storyCreation.rewardAllocation.title')}
          </h3>
          <button
            onClick={resetAllAllocations}
            className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <RotateCcw size={14} />
            {t('storyCreation.rewardAllocation.reset')}
          </button>
        </div>
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {t('storyCreation.rewardAllocation.budget')}
          </div>
          <div className="text-xl font-bold text-purple-600 dark:text-purple-400">
            {allocation.usedBudget} / {allocation.totalBudget}
          </div>
        </div>
        <div className="mt-2 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(allocation.usedBudget / allocation.totalBudget) * 100}%` }}
          />
        </div>
        <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {t('storyCreation.rewardAllocation.remaining')}: {remainingBudget} {t('storyCreation.rewardAllocation.points')}
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          {t('storyCreation.rewardAllocation.restrictions')}
        </div>
      </div>

      {/* 奖励类型列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {REWARD_TYPES.map(rewardType => {
          const currentAmount = getCurrentAmount(rewardType.id);
          const maxAmount = getMaxAmount(rewardType);
          const isMaxed = currentAmount >= maxAmount;
          const isBlocked = isRewardTypeBlocked(rewardType.id);
          const blockReason = getBlockReason(rewardType.id);
          const canMaxOut = ['alphane', 'bond_points', 'endora', 'puzzle'].includes(rewardType.id);

          return (
            <div
              key={rewardType.id}
              className={`border rounded-lg p-3 transition-all ${
                isBlocked 
                  ? 'bg-gray-100 dark:bg-gray-900 border-gray-200 dark:border-gray-700 opacity-60' 
                  : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:shadow-md'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-xl">{rewardType.icon}</span>
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-gray-200">
                      {rewardType.name}
                    </h4>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    {rewardType.cost} {t('storyCreation.rewardAllocation.pointsEach')}
                  </div>
                  {rewardType.maxAmount && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {t('storyCreation.rewardAllocation.maxAmount')}: {rewardType.maxAmount}
                    </div>
                  )}
                </div>
              </div>

              {/* 限制说明 */}
              {isBlocked && blockReason && (
                <div className="mb-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded text-xs text-yellow-700 dark:text-yellow-300">
                  {blockReason}
                </div>
              )}

              {/* 分配控制 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => updateAllocation(rewardType.id, Math.max(0, currentAmount - 1))}
                      disabled={currentAmount <= 0 || isBlocked}
                      className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <Minus size={14} />
                    </button>
                    <span className="min-w-[2rem] text-center font-medium text-gray-800 dark:text-gray-200">
                      {currentAmount}
                    </span>
                    <button
                      onClick={() => updateAllocation(rewardType.id, currentAmount + 1)}
                      disabled={isMaxed || isBlocked}
                      className="w-8 h-8 flex items-center justify-center rounded-full border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <Plus size={14} />
                    </button>
                  </div>

                  {/* 成本显示 */}
                  {currentAmount > 0 && (
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${rewardType.color}`}>
                      {currentAmount * rewardType.cost} {t('storyCreation.rewardAllocation.points')}
                    </div>
                  )}
                </div>

                {/* 快捷按钮组 */}
                {canMaxOut && !isBlocked && (maxAmount > currentAmount || currentAmount > 0) && (
                  <div className="flex gap-1">
                    {maxAmount > currentAmount && (
                      <button
                        onClick={() => maxOutReward(rewardType.id)}
                        className="px-2 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
                      >
                        {t('storyCreation.rewardAllocation.maxOut')}
                      </button>
                    )}
                    {currentAmount > 0 && (
                      <button
                        onClick={() => clearReward(rewardType.id)}
                        className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                      >
                        {t('storyCreation.rewardAllocation.clear')}
                      </button>
                    )}
                  </div>
                )}
              </div>

              {/* 自定义文字输入（仅限 custom_badge） */}
              {rewardType.id === 'custom_badge' && currentAmount > 0 && (
                <div className="mt-2">
                  <input
                    type="text"
                    value={getCurrentCustomText(rewardType.id)}
                    onChange={(e) => updateCustomText(rewardType.id, e.target.value)}
                    placeholder={t('storyCreation.rewardAllocation.badgeTextPlaceholder')}
                    className="w-full px-3 py-2 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-purple-500/20 focus:border-purple-500 transition-all"
                  />
                </div>
              )}

              {/* 进度条 */}
              {maxAmount > 0 && (
                <div className="mt-2">
                  <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                    <div
                      className="bg-gradient-to-r from-purple-500 to-pink-500 h-1 rounded-full transition-all duration-300"
                      style={{ width: `${(currentAmount / maxAmount) * 100}%` }}
                    />
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {currentAmount} / {maxAmount}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* 分配总结 */}
      {allocation.allocations.length > 0 && (
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">
            {t('storyCreation.rewardAllocation.summary')}
          </h4>
          <div className="space-y-2">
            {allocation.allocations.map(alloc => (
              <div key={alloc.id}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span>{alloc.type.icon}</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {alloc.type.name} × {alloc.amount}
                    </span>
                  </div>
                  <div className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    {alloc.totalCost} {t('storyCreation.rewardAllocation.points')}
                  </div>
                </div>
                {alloc.customText && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 ml-6 mt-1">
                    "{alloc.customText}"
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default RewardAllocationSystem; 