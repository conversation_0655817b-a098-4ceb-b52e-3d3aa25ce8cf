/**
 * Test for Chapter Reorder Logic
 * This test verifies that when a main chapter is dragged, all its branch chapters move with it
 */

import type { StoryChapter } from '@/types/story-creation';

// Mock the chapter reorder logic
function handleChapterReorder(
  chapters: StoryChapter[], 
  chapterId: string, 
  newOrder: number
): StoryChapter[] {
  const updatedChapters = [...chapters];
  const chapterIndex = updatedChapters.findIndex(c => c.id === chapterId);

  if (chapterIndex === -1) return chapters;

  const chapter = updatedChapters[chapterIndex];
  const oldOrder = chapter.order;

  // If dragging a main chapter, we need to move all its branches with it
  if (chapter.chapterType === 'main') {
    // Find all branch chapters that belong to this main chapter
    const branchChapters = updatedChapters.filter(c => 
      c.chapterType === 'branch' && c.parentChapter === chapter.id
    );

    // Update the main chapter's order
    updatedChapters[chapterIndex] = { ...chapter, order: newOrder };

    // Update all branch chapters to have the same new order
    branchChapters.forEach(branchChapter => {
      const branchIndex = updatedChapters.findIndex(c => c.id === branchChapter.id);
      if (branchIndex !== -1) {
        updatedChapters[branchIndex] = { ...branchChapter, order: newOrder };
      }
    });

    // Update other main chapters' orders (and their branches)
    updatedChapters.forEach((c, index) => {
      if (index !== chapterIndex && c.chapterType === 'main' && c.id !== chapter.id) {
        let shouldUpdateOrder = false;
        let newOrderForThisChapter = c.order;

        if (oldOrder < newOrder && c.order > oldOrder && c.order <= newOrder) {
          newOrderForThisChapter = c.order - 1;
          shouldUpdateOrder = true;
        } else if (oldOrder > newOrder && c.order >= newOrder && c.order < oldOrder) {
          newOrderForThisChapter = c.order + 1;
          shouldUpdateOrder = true;
        }

        if (shouldUpdateOrder) {
          // Update the main chapter
          updatedChapters[index] = { ...c, order: newOrderForThisChapter };
          
          // Update all branches of this main chapter
          const thisMainBranches = updatedChapters.filter(branch => 
            branch.chapterType === 'branch' && branch.parentChapter === c.id
          );
          thisMainBranches.forEach(branchChapter => {
            const branchIndex = updatedChapters.findIndex(ch => ch.id === branchChapter.id);
            if (branchIndex !== -1) {
              updatedChapters[branchIndex] = { ...branchChapter, order: newOrderForThisChapter };
            }
          });
        }
      }
    });
  }

  return updatedChapters;
}

// Test data
const createTestChapters = (): StoryChapter[] => [
  {
    id: 'main-1',
    title: 'Chapter 1',
    description: '',
    content: '',
    order: 1,
    chapterType: 'main',
    backgroundSetting: '',
    completionEffects: {
      bondPointsChange: 0,
      greetingChange: '',
      characterMoodChange: '',
      customEffects: ''
    },
    choices: [],
    nextChapters: []
  },
  {
    id: 'branch-1a',
    title: 'Chapter 1A',
    description: '',
    content: '',
    order: 1,
    chapterType: 'branch',
    parentChapter: 'main-1',
    branchLetter: 'A',
    backgroundSetting: '',
    completionEffects: {
      bondPointsChange: 0,
      greetingChange: '',
      characterMoodChange: '',
      customEffects: ''
    },
    choices: [],
    nextChapters: []
  },
  {
    id: 'branch-1b',
    title: 'Chapter 1B',
    description: '',
    content: '',
    order: 1,
    chapterType: 'branch',
    parentChapter: 'main-1',
    branchLetter: 'B',
    backgroundSetting: '',
    completionEffects: {
      bondPointsChange: 0,
      greetingChange: '',
      characterMoodChange: '',
      customEffects: ''
    },
    choices: [],
    nextChapters: []
  },
  {
    id: 'main-2',
    title: 'Chapter 2',
    description: '',
    content: '',
    order: 2,
    chapterType: 'main',
    backgroundSetting: '',
    completionEffects: {
      bondPointsChange: 0,
      greetingChange: '',
      characterMoodChange: '',
      customEffects: ''
    },
    choices: [],
    nextChapters: []
  },
  {
    id: 'main-3',
    title: 'Chapter 3',
    description: '',
    content: '',
    order: 3,
    chapterType: 'main',
    backgroundSetting: '',
    completionEffects: {
      bondPointsChange: 0,
      greetingChange: '',
      characterMoodChange: '',
      customEffects: ''
    },
    choices: [],
    nextChapters: []
  }
];

// Test function
function testChapterReorderLogic() {
  console.log('🧪 Testing Chapter Reorder Logic...\n');
  
  const chapters = createTestChapters();
  
  console.log('📋 Initial state:');
  chapters.forEach(c => {
    console.log(`  ${c.chapterType === 'main' ? '📖' : '  📄'} ${c.title} (order: ${c.order})`);
  });
  
  console.log('\n🔄 Moving Chapter 1 (with branches 1A, 1B) from order 1 to order 3...\n');
  
  const reorderedChapters = handleChapterReorder(chapters, 'main-1', 3);
  
  console.log('📋 After reorder:');
  const sortedResult = reorderedChapters.sort((a, b) => {
    if (a.order !== b.order) return a.order - b.order;
    if (a.chapterType === 'main' && b.chapterType === 'branch') return -1;
    if (a.chapterType === 'branch' && b.chapterType === 'main') return 1;
    return (a.branchLetter || '').localeCompare(b.branchLetter || '');
  });
  
  sortedResult.forEach(c => {
    console.log(`  ${c.chapterType === 'main' ? '📖' : '  📄'} ${c.title} (order: ${c.order})`);
  });
  
  // Verify the result
  const main1 = reorderedChapters.find(c => c.id === 'main-1');
  const branch1a = reorderedChapters.find(c => c.id === 'branch-1a');
  const branch1b = reorderedChapters.find(c => c.id === 'branch-1b');
  const main2 = reorderedChapters.find(c => c.id === 'main-2');
  const main3 = reorderedChapters.find(c => c.id === 'main-3');
  
  console.log('\n✅ Verification:');
  console.log(`  Main Chapter 1 order: ${main1?.order} (expected: 3)`);
  console.log(`  Branch 1A order: ${branch1a?.order} (expected: 3)`);
  console.log(`  Branch 1B order: ${branch1b?.order} (expected: 3)`);
  console.log(`  Main Chapter 2 order: ${main2?.order} (expected: 1)`);
  console.log(`  Main Chapter 3 order: ${main3?.order} (expected: 2)`);
  
  const success = main1?.order === 3 && 
                  branch1a?.order === 3 && 
                  branch1b?.order === 3 && 
                  main2?.order === 1 && 
                  main3?.order === 2;
  
  console.log(`\n${success ? '🎉 TEST PASSED' : '❌ TEST FAILED'}: Branch chapters ${success ? 'correctly' : 'incorrectly'} follow their main chapter!`);
  
  return success;
}

// Export for potential use in actual tests
export { handleChapterReorder, testChapterReorderLogic };

// Run test if this file is executed directly
if (typeof window === 'undefined') {
  testChapterReorderLogic();
}
