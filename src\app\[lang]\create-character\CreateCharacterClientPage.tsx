'use client';

import { FC, useState } from 'react';
import { User, MessageCircle, Settings } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';

// Import new components
import FunctionSelector from '@/components/character-creation/FunctionSelector';
import FlashCreation from '@/components/character-creation/FlashCreation';
import CustomizeCreation from '@/components/character-creation/CustomizeCreation';
import ImportCreation from '@/components/character-creation/ImportCreation';

// Import hooks
import { useCharacterForm } from '@/hooks/character-creation/useCharacterForm';
import { useImageUpload } from '@/hooks/character-creation/useImageUpload';
import { useCharacterImport } from '@/hooks/character-creation/useCharacterImport';

// Import types
import type { 
  CreateCharacterClientPageProps, 
  SelectedFunction, 
  Step, 
  StepConfig 
} from '@/types/character-creation';

const CreateCharacterClientPage: FC<CreateCharacterClientPageProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  
  // State for function selection
  const [selectedFunction, setSelectedFunction] = useState<SelectedFunction>('flash');
  const [currentStep, setCurrentStep] = useState<Step>('basics');
  const [createMode, setCreateMode] = useState<'simple' | 'detailed'>('simple');

  // Use custom hooks
  const characterForm = useCharacterForm(lang);
  const imageUpload = useImageUpload(characterForm.formData, characterForm.setFormData);
  const characterImport = useCharacterImport(
    characterForm.setFormData,
    setCreateMode,
    setCurrentStep,
    imageUpload.setIsAiGenerated
  );

  // 包装提交函数，确保它返回Promise<string>
  const handleSubmit = async (): Promise<string> => {
    return await characterForm.handleSubmit();
  };

  // Step configuration
  const steps: readonly StepConfig[] = [
    { id: 'basics', label: t('characterCreation.steps.basics.title'), icon: User, description: t('characterCreation.steps.basics.description') },
    { id: 'personality', label: t('characterCreation.steps.personality.title'), icon: MessageCircle, description: t('characterCreation.steps.personality.description') },
    { id: 'advanced', label: t('characterCreation.steps.advanced.title'), icon: Settings, description: t('characterCreation.steps.advanced.description') },
  ] as const;

  // Handle function selection content rendering
  const renderSelectedFunctionContent = () => {
    switch (selectedFunction) {
      case 'flash':
        return <FlashCreation lang={lang} />;
      case 'customize':
        return (
          <CustomizeCreation
            currentStep={currentStep}
            formData={characterForm.formData}
            setFormData={characterForm.setFormData}
            onStepChange={setCurrentStep}
            onSubmit={handleSubmit}
            isStepComplete={characterForm.isStepComplete}
            steps={steps}
            lang={lang}
          />
        );
      case 'import':
        return (
          <ImportCreation
            importFile={characterImport.importFile}
            setImportFile={characterImport.setImportFile}
            importedCharacters={characterImport.importedCharacters}
            setImportedCharacters={characterImport.setImportedCharacters}
            onImportFiles={characterImport.handleImportFiles}
            onSelectCharacter={characterImport.handleSelectCharacter}
            scriptFile={characterImport.scriptFile}
            onScriptFileUpload={characterImport.handleScriptFileUpload}
            lang={lang}
          />
        );
      default:
        return null;
    }
  };

  // Main content renderer
  const renderMainContent = () => (
    <div className="space-y-8">
      {/* Function Selection Buttons */}
      <FunctionSelector
        selectedFunction={selectedFunction}
        onFunctionSelect={setSelectedFunction}
        lang={lang}
      />

      {/* Selected Function Content */}
      <div className="animate-in slide-in-from-bottom-4 duration-500">
        {renderSelectedFunctionContent()}
      </div>
    </div>
  );

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200/20 dark:bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-200/20 dark:bg-pink-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Main Content */}
          {renderMainContent()}
        </div>
      </div>
    </MainAppLayout>
  );
};

export default CreateCharacterClientPage;
