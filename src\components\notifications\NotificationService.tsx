'use client';

import React, { useState, useMemo } from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Settings, UserPlus, User, CreditCard } from 'lucide-react';
import type { NotificationData } from './NotificationItem';
import type { NotificationTab } from './NotificationTabs';

interface NotificationServiceProps {
  lang: string;
  children: (props: {
    notifications: NotificationData[];
    activeTab: NotificationTab;
    unreadCounts: Record<NotificationTab, number>;
    onTabChange: (tab: NotificationTab) => void;
    onMarkAsRead: (id: string) => void;
    onMarkAllAsRead: () => void;
    onDelete: (id: string) => void;
  }) => React.ReactNode;
}

const NotificationService: React.FC<NotificationServiceProps> = ({
  lang,
  children
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [activeTab, setActiveTab] = useState<NotificationTab>('all');

  // 模拟通知数据
  const [allNotifications, setAllNotifications] = useState<NotificationData[]>([
    {
      id: 'sys1',
      type: 'system',
      icon: <Settings className='w-5 h-5 text-primary' />,
      title: 'System Update v2.4.1 Available',
      description: 'New features and security improvements are now available. Update recommended.',
      timestamp: '2 hours ago',
      isUnread: true,
      category: 'System',
      link: '/system/updates',
    },
    {
      id: 'soc1',
      type: 'social',
      icon: <UserPlus className='w-5 h-5 text-primary' />,
      title: 'You have 5 new followers this week',
      description: 'Your recent characters gained attention from @sarahchen, @alexkim, and 3 others.',
      timestamp: '1 day ago',
      isUnread: true,
      category: 'Social',
      link: '/profile/followers',
    },
    {
      id: 'prof1',
      type: 'profile',
      icon: <User className='w-5 h-5 text-primary' />,
      title: 'Profile Updated Successfully',
      description: 'Your bio and specialties have been updated and are now visible to other users.',
      timestamp: '3 days ago',
      isUnread: false,
      category: 'Profile',
      link: '/profile/edit',
    },
    {
      id: 'sub1',
      type: 'subscription',
      icon: <CreditCard className='w-5 h-5 text-primary' />,
      title: 'Pro subscription expires in 7 days',
      description: 'Your Pro plan will expire on December 22, 2024. Renew now to avoid interruption.',
      timestamp: '1 hour ago',
      isUnread: true,
      category: 'Subscription',
      link: '/store',
    },
    {
      id: 'sys2',
      type: 'system',
      icon: <Settings className='w-5 h-5 text-primary' />,
      title: 'Scheduled Maintenance Complete',
      description: 'System maintenance has been completed. All services are now fully operational.',
      timestamp: '2 days ago',
      isUnread: false,
      category: 'System',
      link: '#',
    },
    {
      id: 'soc2',
      type: 'social',
      icon: <UserPlus className='w-5 h-5 text-primary' />,
      title: 'Dr. Sarah Chen started following you',
      description: 'A verified AI researcher with 12.5k followers is now following your work.',
      timestamp: '2 days ago',
      isUnread: false,
      category: 'Social',
      link: '/users/sarahchen',
    },
    {
      id: 'prof2',
      type: 'profile',
      icon: <User className='w-5 h-5 text-primary' />,
      title: 'Avatar Changed',
      description: 'Your profile picture has been successfully updated across the platform.',
      timestamp: '1 week ago',
      isUnread: false,
      category: 'Profile',
      link: '/profile/edit',
    },
    {
      id: 'sub2',
      type: 'subscription',
      icon: <CreditCard className='w-5 h-5 text-primary' />,
      title: 'Payment Method Updated',
      description: 'Your payment method has been successfully updated for automatic renewal.',
      timestamp: '1 week ago',
      isUnread: false,
      category: 'Subscription',
      link: '/store',
    },
  ]);

  // 根据当前标签过滤通知
  const filteredNotifications = useMemo(() => {
    if (activeTab === 'all') {
      return allNotifications;
    }
    return allNotifications.filter(notification => notification.type === activeTab);
  }, [allNotifications, activeTab]);

  // 计算未读通知数量
  const unreadCounts = useMemo(() => {
    const counts: Record<NotificationTab, number> = {
      all: 0,
      system: 0,
      social: 0,
      profile: 0,
      subscription: 0
    };

    allNotifications.forEach(notification => {
      if (notification.isUnread) {
        counts.all++;
        counts[notification.type]++;
      }
    });

    return counts;
  }, [allNotifications]);

  // 标记单个通知为已读
  const handleMarkAsRead = (id: string) => {
    setAllNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, isUnread: false }
          : notification
      )
    );
  };

  // 标记当前标签下的所有通知为已读
  const handleMarkAllAsRead = () => {
    setAllNotifications(prev => 
      prev.map(notification => {
        if (activeTab === 'all') {
          return { ...notification, isUnread: false };
        }
        return notification.type === activeTab
          ? { ...notification, isUnread: false }
          : notification;
      })
    );
  };

  // 删除通知
  const handleDelete = (id: string) => {
    setAllNotifications(prev => 
      prev.filter(notification => notification.id !== id)
    );
  };

  return (
    <>
      {children({
        notifications: filteredNotifications,
        activeTab,
        unreadCounts,
        onTabChange: setActiveTab,
        onMarkAsRead: handleMarkAsRead,
        onMarkAllAsRead: handleMarkAllAsRead,
        onDelete: handleDelete
      })}
    </>
  );
};

export default NotificationService; 