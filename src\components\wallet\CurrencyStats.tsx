import React from 'react';
import { TrendingUp, Calendar, Target, Award } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface CurrencyBalance {
  type: 'alphane' | 'endora' | 'serotile' | 'oxytol';
  amount: number;
  dailyEarned: number;
  weeklyEarned: number;
  totalEarned: number;
}

interface CurrencyStatsProps {
  lang: string;
  currencyBalances: CurrencyBalance[];
}

const CurrencyStats: React.FC<CurrencyStatsProps> = ({ 
  lang, 
  currencyBalances 
}) => {
  const { t } = useTranslation(lang, 'translation');

  // Calculate total stats
  const totalBalance = currencyBalances.reduce((sum, currency) => sum + currency.amount, 0);
  const totalDailyEarned = currencyBalances.reduce((sum, currency) => sum + currency.dailyEarned, 0);
  const totalWeeklyEarned = currencyBalances.reduce((sum, currency) => sum + currency.weeklyEarned, 0);
  const totalAllTimeEarned = currencyBalances.reduce((sum, currency) => sum + currency.totalEarned, 0);

  // Calculate weekly growth rate
  const weeklyGrowthRate = totalBalance > 0 ? ((totalWeeklyEarned / (totalBalance - totalWeeklyEarned)) * 100) : 0;

  // Find most earned currency today
  const mostEarnedToday = currencyBalances.reduce((max, currency) => 
    currency.dailyEarned > max.dailyEarned ? currency : max
  );

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString();
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Portfolio Value */}
      <div className="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
            <TrendingUp className="w-6 h-6" />
          </div>
          <div className="text-right">
            <div className="text-xs opacity-80">{t('wallet.portfolioValue')}</div>
            <div className="text-2xl font-bold">{formatNumber(totalBalance)}</div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4 opacity-80" />
          <span className="text-sm opacity-90">
            +{weeklyGrowthRate.toFixed(1)}% {t('wallet.thisWeek')}
          </span>
        </div>
      </div>

      {/* Daily Earnings */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
            <Calendar className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <div className="text-right">
            <div className="text-xs text-gray-500 dark:text-gray-400">{t('wallet.todayTotal')}</div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {formatNumber(totalDailyEarned)}
            </div>
          </div>
        </div>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {t('wallet.acrossAllCurrencies')}
        </div>
      </div>

      {/* Weekly Progress */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
            <Target className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="text-right">
            <div className="text-xs text-gray-500 dark:text-gray-400">{t('wallet.weeklyEarnings')}</div>
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {formatNumber(totalWeeklyEarned)}
            </div>
          </div>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="h-2 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full transition-all duration-500"
            style={{ width: `${Math.min((totalWeeklyEarned / (totalWeeklyEarned + 100)) * 100, 100)}%` }}
          ></div>
        </div>
      </div>

      {/* Best Performer */}
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-xl flex items-center justify-center">
            <Award className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div className="text-right">
            <div className="text-xs text-gray-500 dark:text-gray-400">{t('wallet.bestPerformer')}</div>
            <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {t(`tokens.${mostEarnedToday.type}`)}
            </div>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">{t('wallet.todayEarned')}</span>
          <span className="text-lg font-semibold text-green-600 dark:text-green-400">
            +{mostEarnedToday.dailyEarned.toLocaleString()}
          </span>
        </div>
      </div>

      {/* Detailed Breakdown */}
      <div className="lg:col-span-4 bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          {t('wallet.currencyBreakdown')}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {currencyBalances.map((currency) => {
            const percentage = (currency.amount / totalBalance) * 100;
            return (
              <div key={currency.type} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t(`tokens.${currency.type}`)}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {percentage.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-500 ${
                      currency.type === 'alphane' ? 'bg-gradient-to-r from-yellow-400 to-orange-500' :
                      currency.type === 'endora' ? 'bg-gradient-to-r from-pink-400 to-rose-500' :
                      currency.type === 'serotile' ? 'bg-gradient-to-r from-blue-400 to-cyan-500' :
                      'bg-gradient-to-r from-green-400 to-emerald-500'
                    }`}
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {formatNumber(currency.amount)} {t('wallet.tokens')}
                </div>
              </div>
            );
          })}
        </div>

        {/* All Time Stats */}
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(totalAllTimeEarned)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">{t('wallet.allTimeEarned')}</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(totalWeeklyEarned)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">{t('wallet.thisWeekEarned')}</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(totalDailyEarned)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">{t('wallet.todayEarned')}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CurrencyStats; 