'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Map<PERSON><PERSON>, <PERSON>, <PERSON>, Heart, Target, Users } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface StoryFlashResult {
  title: string;
  description: string;
  chapters: {
    id: string;
    title: string;
    // Scene Layer
    environment: string;
    timeElements: {
      season: string;
      timeOfDay: string;
      duration: string;
    };
    spatialElements: {
      location: string;
      atmosphere: string;
    };
    environmentalElements: {
      weather: string;
      lighting: string;
      sounds: string;
    };
    // Antecedent Layer
    macroHistory: string;
    characterPast: string;
    immediateTrigger: string;
    // Character Psychology
    mentalModel: {
      coreValues: string;
      thinkingMode: string;
    };
    emotionalBaseline: {
      displayedEmotion: string;
      hiddenEmotion: string;
      emotionalIntensity: number;
    };
    // Interaction Dynamics
    dialogueStrategy: {
      initiative: number;
      questioningStyle: string;
    };
    relationshipDynamics: {
      initialGoodwill: number;
      intimacyLevel: string;
    };
    goalOrientation: {
      sceneGoal: string;
      displayedIntent: string;
      hiddenIntent: string;
    };
  }[];
}

interface StoryFlashGeneratorProps {
  characterName: string;
  onAccept: (result: StoryFlashResult) => void;
  onRegenerate: () => void;
  isGenerating: boolean;
  result: StoryFlashResult | null;
  regenerateCount: number;
  maxRegenerations: number;
  lang: string;
}

const StoryFlashGenerator: React.FC<StoryFlashGeneratorProps> = ({
  characterName,
  onAccept,
  onRegenerate,
  isGenerating,
  result,
  regenerateCount,
  maxRegenerations,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [copiedText, setCopiedText] = useState<string>('');

  const handleCopyText = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      setTimeout(() => setCopiedText(''), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  return (
    <div className="space-y-6">
      {/* Generation Header */}
      <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-700">
        <div className="text-center">
          <h3 className="text-2xl font-bold text-purple-800 dark:text-purple-200 mb-2 flex items-center justify-center gap-2">
            <Sparkles className="w-6 h-6" />
            {t('storyCreation.flashGenerator.title')}
          </h3>
          <p className="text-purple-600 dark:text-purple-300 mb-4">
            {t('storyCreation.flashGenerator.subtitle', { characterName })}
          </p>
          <div className="space-y-2">
            <div className="flex justify-center gap-2 flex-wrap">
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/50 dark:to-cyan-900/50 text-blue-700 dark:text-blue-300 rounded-full text-sm font-medium border border-blue-200 dark:border-blue-700">
                {t('storyCreation.flashGenerator.features.sceneSettings')}
              </span>
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 text-green-700 dark:text-green-300 rounded-full text-sm font-medium border border-green-200 dark:border-green-700">
                {t('storyCreation.flashGenerator.features.backgroundContext')}
              </span>
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/50 dark:to-pink-900/50 text-purple-700 dark:text-purple-300 rounded-full text-sm font-medium border border-purple-200 dark:border-purple-700">
                {t('storyCreation.flashGenerator.features.characterPsychology')}
              </span>
            </div>
            <div className="flex justify-center gap-2 flex-wrap">
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/50 dark:to-red-900/50 text-orange-700 dark:text-orange-300 rounded-full text-sm font-medium border border-orange-200 dark:border-orange-700">
                {t('storyCreation.flashGenerator.features.dialogueDynamics')}
              </span>
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-rose-100 to-pink-100 dark:from-rose-900/50 dark:to-pink-900/50 text-rose-700 dark:text-rose-300 rounded-full text-sm font-medium border border-rose-200 dark:border-rose-700">
                {t('storyCreation.flashGenerator.features.relationshipBuilding')}
              </span>
              <span className="inline-block px-3 py-2 bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/50 dark:to-purple-900/50 text-indigo-700 dark:text-indigo-300 rounded-full text-sm font-medium border border-indigo-200 dark:border-indigo-700">
                {t('storyCreation.flashGenerator.features.storyGoals')}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Generation Result Preview */}
      {result && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h4 className="text-xl font-bold text-purple-800 dark:text-purple-200 flex items-center gap-2">
              {t('storyCreation.flashGenerator.preview.title')}
            </h4>
            <div className="bg-white dark:bg-gray-800 px-4 py-2 rounded-full border border-purple-200 dark:border-purple-700">
              <span className="text-sm text-purple-600 dark:text-purple-400 font-medium">
                {t('storyCreation.flashGenerator.preview.regenerationsLeft', { count: maxRegenerations - regenerateCount })}
              </span>
            </div>
          </div>

          {/* Story Title */}
          <div className="text-center">
            <h5 className="text-3xl font-bold text-purple-800 dark:text-purple-200">
              ✨ {result.title || t('storyCreation.title')}
            </h5>
            <p className="text-lg text-purple-600 dark:text-purple-300 mt-2">
              {result.description || t('storyCreation.subtitle')}
            </p>
          </div>

          {/* Story Chapters Bento Grid */}
          <div className="space-y-4">
            <h6 className="text-lg font-semibold text-purple-700 dark:text-purple-300 text-center">
              {t('storyCreation.flashGenerator.chapters.title')}
            </h6>
            
            {/* Bento Grid Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {result.chapters.map((chapter, index) => (
                <div key={chapter.id} className="space-y-4">
                  {/* Chapter Header */}
                  <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-purple-200 dark:border-purple-700 shadow-sm">
                    <div className="flex items-center justify-between mb-3">
                      <h6 className="font-bold text-purple-700 dark:text-purple-300 flex items-center gap-2">
                        📚 {t('storyCreation.flashGenerator.chapters.chapterTitle', { index: index + 1 })}: {chapter.title}
                      </h6>
                      <button
                        onClick={() => handleCopyText(chapter.title, t('storyCreation.flashGenerator.copy.chapterTitle', { index: index + 1 }))}
                        className="text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 p-2 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/50 transition-all"
                        title={t('storyCreation.flashGenerator.copy.chapterTitleTooltip', { index: index + 1 })}
                      >
                        📋
                      </button>
                    </div>
                  </div>

                  {/* Scene Layer */}
                  <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-700 shadow-sm">
                    <h6 className="font-semibold text-blue-700 dark:text-blue-300 mb-3 flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      {t('storyCreation.flashGenerator.sceneSetting.title')}
                    </h6>
                    <div className="space-y-2 text-sm">
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.sceneSetting.environment')}:</span> {chapter.environment}</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.sceneSetting.location')}:</span> {chapter.spatialElements.location}</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.sceneSetting.time')}:</span> {chapter.timeElements.timeOfDay}, {chapter.timeElements.season}</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.sceneSetting.weather')}:</span> {chapter.environmentalElements.weather}</p>
                    </div>
                  </div>

                  {/* Character Psychology */}
                  <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-4 border border-purple-200 dark:border-purple-700 shadow-sm">
                    <h6 className="font-semibold text-purple-700 dark:text-purple-300 mb-3 flex items-center gap-2">
                      <Brain className="w-4 h-4" />
                      {t('storyCreation.flashGenerator.characterPsychology.title')}
                    </h6>
                    <div className="space-y-2 text-sm">
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.characterPsychology.coreValues')}:</span> {chapter.mentalModel.coreValues}</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.characterPsychology.displayedEmotion')}:</span> {chapter.emotionalBaseline.displayedEmotion}</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.characterPsychology.hiddenEmotion')}:</span> {chapter.emotionalBaseline.hiddenEmotion}</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.characterPsychology.thinkingMode')}:</span> {chapter.mentalModel.thinkingMode}</p>
                    </div>
                  </div>

                  {/* Interaction Dynamics */}
                  <div className="bg-gradient-to-br from-rose-50 to-orange-50 dark:from-rose-900/20 dark:to-orange-900/20 rounded-xl p-4 border border-rose-200 dark:border-rose-700 shadow-sm">
                    <h6 className="font-semibold text-rose-700 dark:text-rose-300 mb-3 flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      {t('storyCreation.flashGenerator.interactionDynamics.title')}
                    </h6>
                    <div className="space-y-2 text-sm">
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.interactionDynamics.sceneGoal')}:</span> {chapter.goalOrientation.sceneGoal}</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.interactionDynamics.intimacyLevel')}:</span> {chapter.relationshipDynamics.intimacyLevel}</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.interactionDynamics.initiative')}:</span> {chapter.dialogueStrategy.initiative}%</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.interactionDynamics.goodwill')}:</span> {chapter.relationshipDynamics.initialGoodwill}%</p>
                    </div>
                  </div>

                  {/* Background Context */}
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-4 border border-green-200 dark:border-green-700 shadow-sm">
                    <h6 className="font-semibold text-green-700 dark:text-green-300 mb-3 flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      {t('storyCreation.flashGenerator.backgroundContext.title')}
                    </h6>
                    <div className="space-y-2 text-sm">
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.backgroundContext.immediateTrigger')}:</span> {chapter.immediateTrigger}</p>
                      <p><span className="font-medium">{t('storyCreation.flashGenerator.backgroundContext.characterPast')}:</span> {chapter.characterPast}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 mt-6">
            <button
              onClick={() => onAccept(result)}
              className="flex-1 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all shadow-lg hover:shadow-xl font-semibold flex items-center justify-center gap-2 transform hover:scale-[1.02]"
            >
              {t('storyCreation.flashGenerator.acceptButton')}
            </button>

            {regenerateCount < maxRegenerations && (
              <button
                onClick={onRegenerate}
                disabled={isGenerating}
                className="px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl hover:from-orange-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all shadow-lg hover:shadow-xl font-semibold flex items-center justify-center gap-2 transform hover:scale-[1.02]"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                    <span>{t('storyCreation.flashGenerator.regenerateButton.generating')}</span>
                  </>
                ) : (
                  <>
                    {t('storyCreation.flashGenerator.regenerateButton.text', { count: maxRegenerations - regenerateCount })}
                  </>
                )}
              </button>
            )}
          </div>

          <p className="text-sm text-purple-600 dark:text-purple-400 text-center font-medium">
            {t('storyCreation.flashGenerator.customizeHint')}
          </p>
        </div>
      )}

      {/* Copy Success Message */}
      {copiedText && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 z-50">
          <CheckCircle className="w-4 h-4" />
          <span>{t('storyCreation.flashGenerator.copySuccess', { text: copiedText })}</span>
        </div>
      )}
    </div>
  );
};

export default StoryFlashGenerator;
