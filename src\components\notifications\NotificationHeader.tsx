'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Bell } from 'lucide-react';

interface NotificationHeaderProps {
  lang: string;
}

const NotificationHeader: React.FC<NotificationHeaderProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');

  return (
    <div className="text-center">
      <div className="flex justify-center items-center space-x-4 mb-2">
        <Bell className="text-logo" size={48} />
        <h1 className="text-4xl md:text-5xl font-bold text-foreground leading-tight">
          {t('notifications.title')}
        </h1>
      </div>
      <p className="text-lg text-foreground/70 max-w-3xl mx-auto leading-relaxed">
        {t('notifications.description')}
      </p>
    </div>
  );
};

export default NotificationHeader; 