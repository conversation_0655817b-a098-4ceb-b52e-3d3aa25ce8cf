'use client';

import React from 'react';
import { Zap, Palette } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryFunctionSelectorProps } from '@/types/story-creation';

const StoryFunctionSelector: React.FC<StoryFunctionSelectorProps & { lang: string }> = ({
  selectedFunction,
  onFunctionSelect,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');

  return (
    <div className="sticky top-12 lg:top-16 z-20">
      {/* Transparent wrapper with no padding */}
      <div className="bg-transparent">
        <div className="rounded-xl overflow-hidden bg-gradient-to-r from-pink-500 via-purple-500 to-rose-500 h-20 md:h-24 shadow-xl">
          <div className="grid grid-cols-2 h-full">
            {/* Flash Quick Creation */}
            <button
              className={`cursor-pointer transition-all duration-300 text-white relative overflow-hidden ${
                selectedFunction === 'flash'
                  ? 'bg-black/20 shadow-inner'
                  : 'hover:bg-white/10 hover:shadow-lg'
              }`}
              onClick={() => onFunctionSelect('flash')}
            >
              <div className="flex flex-col items-center justify-center h-full relative z-10">
                <Zap size={20} className="md:w-6 md:h-6 mb-1 drop-shadow-sm" />
                <span className="text-xs md:text-sm font-bold drop-shadow-sm">
                  {t('storyCreation.functions.flash.title')}
                </span>
              </div>
              {/* Subtle hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
            </button>

            {/* Customize Deep Customization */}
            <button
              className={`cursor-pointer transition-all duration-300 text-white relative overflow-hidden border-l border-white/20 ${
                selectedFunction === 'customize'
                  ? 'bg-black/20 shadow-inner'
                  : 'hover:bg-white/10 hover:shadow-lg'
              }`}
              onClick={() => onFunctionSelect('customize')}
            >
              <div className="flex flex-col items-center justify-center h-full relative z-10">
                <Palette size={20} className="md:w-6 md:h-6 mb-1 drop-shadow-sm" />
                <span className="text-xs md:text-sm font-bold drop-shadow-sm">
                  {t('storyCreation.functions.customize.title')}
                </span>
              </div>
              {/* Subtle hover effect overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoryFunctionSelector; 