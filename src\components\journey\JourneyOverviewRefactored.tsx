'use client';

import React, { useState } from 'react';
import { useTranslation } from '@/app/i18n/client';
import { useJourneyData } from '../../hooks/journey/useJourneyData';
import { IntegratedOverviewCard } from './overview/IntegratedOverviewCard';
import { RecommendationsCard } from './overview/RecommendationsCard';
import { SeasonLeaderboardCard } from './season/SeasonLeaderboardCard';

interface JourneyOverviewRefactoredProps {
  lang: string;
}

const JourneyOverviewRefactored: React.FC<JourneyOverviewRefactoredProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const {
    data,
    updateMembershipTier,
    progressPercentage,
    dailyProgress,
    getLevelName
  } = useJourneyData();

  // 推荐数据
  const personalizedRecommendations = [
    {
      type: 'challenge' as const,
      title: t('journey.recommendations.socialChallenge'),
      description: t('journey.recommendations.socialChallengeDesc'),
      reward: t('journey.recommendations.trophyReward', { amount: 200 }),
      difficulty: 'medium' as const,
      matchScore: 95
    },
    {
      type: 'feature' as const,
      title: t('journey.recommendations.newFeature'),
      description: t('journey.recommendations.newFeatureDesc'),
      reward: t('journey.recommendations.exclusiveBadge'),
      difficulty: 'easy' as const,
      matchScore: 92
    }
  ];

  const handleRecommendationClick = (recommendation: any) => {
    console.log('Recommendation clicked:', recommendation);
  };

  // 模拟季节排行榜数据
  const seasonRankers = [
    { rank: 1, name: 'SeasonMaster', trophies: 8420, weeklyGain: 420, avatar: '🏆' },
    { rank: 2, name: 'TrophyHunter', trophies: 7850, weeklyGain: 380, avatar: '⭐' },
    { rank: 3, name: 'ChampionHeart', trophies: 7200, weeklyGain: 350, avatar: '💎' },
    { rank: 4, name: 'EliteJourney', trophies: 6950, weeklyGain: 320, avatar: '🌟' },
    { rank: 5, name: 'ProAdventurer', trophies: 6700, weeklyGain: 290, avatar: '🔥' }
  ];

  return (
    <div className="space-y-4 px-2 sm:px-4">
      {/* 用户本赛季信息 */}
      <IntegratedOverviewCard
        currentLevel={data.currentLevel}
        membershipTier={data.membershipTier}
        progressPercentage={progressPercentage}
        getLevelName={getLevelName}
        currentTrophies={data.currentTrophies}
        currentRank={data.seasonStats.rank}
        dailyProgress={dailyProgress}
        currentStreak={8}
        lang={lang}
      />

      {/* 推荐任务和排名 - 移动端垂直排列，桌面端水平排列 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* 个性化推荐卡片 */}
        <RecommendationsCard
          recommendations={personalizedRecommendations}
          onRecommendationClick={handleRecommendationClick}
          lang={lang}
        />

        {/* 季节排行榜卡片 */}
        <SeasonLeaderboardCard
          seasonRankers={seasonRankers}
          lang={lang}
        />
      </div>
    </div>
  );
};

export default JourneyOverviewRefactored; 