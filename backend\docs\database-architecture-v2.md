# Alphane AI Database Architecture v2.0

## Overview

This document describes the comprehensive PostgreSQL database architecture for the Alphane AI platform, version 2.0. This major upgrade introduces multi-language support, enhanced gamification, a complete store system, and advanced social features to support all frontend Card components.

## Architecture Principles

### 1. **ISTJ Design Philosophy**
- **Systematic**: Organized in logical layers with clear separation of concerns
- **Thorough**: Comprehensive coverage of all frontend requirements
- **Reliable**: Robust data integrity and consistency mechanisms
- **Methodical**: Step-by-step implementation with proper migration paths

### 2. **Performance-First Approach**
- Materialized views for Card component data aggregation
- Strategic indexing including GIN indexes for JSONB columns
- Partitioning for high-volume tables (activity logs)
- Concurrent refresh strategies for real-time data

### 3. **Scalability Considerations**
- UUID primary keys for distributed systems
- JSONB for flexible schema evolution
- Read replica support through materialized views
- Horizontal scaling preparation

## Schema Architecture Layers

### Layer 1: Core Identity System
```
users ──┬── user_profiles
        ├── user_currencies  
        ├── user_statistics
        ├── user_subscriptions
        └── user_achievements
```

**Purpose**: Foundation user management with enhanced profile and gamification support.

**Key Features**:
- Extended user profiles with social links and privacy settings
- Multi-currency system (star_diamonds, joy_crystals, glimmering_dust, memory_puzzles)
- Comprehensive user statistics for analytics
- Subscription tier management

### Layer 2: Content Creation System
```
characters ──┬── character_localizations
             ├── character_appearance_details
             ├── character_personality_profiles
             └── character_stats (materialized view)

stories ──┬── story_localizations
          ├── scene_hierarchical_info
          ├── story_progression
          └── story_stats (materialized view)
```

**Purpose**: Multi-language content creation with complex data structures from data_examples.

**Key Features**:
- Full i18n support for Chinese, Japanese, and English
- Hierarchical appearance data (3-layer structure)
- Cognitive personality models with MBTI integration
- Complex story structures with 4-layer scene information
- User progress tracking through stories

### Layer 3: Social Interaction System
```
moments ──┬── social_interactions
          ├── comments
          ├── content_moderation
          └── moment_stats (materialized view)

social_interactions ──┬── trending_content
                      ├── hashtags
                      ├── content_hashtags
                      └── user_social_stats
```

**Purpose**: Rich social features and user-generated content management.

**Key Features**:
- Comprehensive interaction tracking (likes, shares, comments, bookmarks)
- Content moderation and safety systems
- Trending algorithm support
- Hashtag system for content discovery
- Social statistics aggregation

### Layer 4: Gamification System
```
user_achievements ──┬── daily_missions
                    ├── user_daily_mission_progress
                    ├── memorial_events
                    ├── leaderboards
                    └── user_leaderboard_entries

user_journey ──┬── achievement_progress (materialized view)
               ├── memorial_events_view (materialized view)
               └── daily_mission_progress_view (materialized view)
```

**Purpose**: Advanced gamification with daily missions, achievements, and memorial events.

**Key Features**:
- Multi-track progression (free, pass, diamond)
- Daily/weekly/monthly mission system
- Anniversary and milestone celebration
- Leaderboard and ranking systems
- Memorial events for character relationships

### Layer 5: Commerce System
```
store_products ──┬── subscription_plans
                 ├── purchase_history
                 ├── promotional_offers
                 ├── user_promotional_claims
                 └── store_stats (materialized view)
```

**Purpose**: Complete e-commerce platform with subscriptions and promotions.

**Key Features**:
- Flexible product catalog with multiple categories
- Subscription tier management (standard, pass, diamond, metaverse)
- Promotional campaign system
- Detailed transaction tracking
- First-time purchase offers

## Multi-Language Support Implementation

### Character Localization Strategy
```sql
-- Example: Character data in multiple languages
character_localizations:
├── zh-CN: 艾莉娅·萨拉 (Chinese character data)
├── ja-JP: アリア・サラ (Japanese character data)  
└── en-US: Aria Sara (English character data)
```

**Data Structure**:
- `name`: Full name and name origin explanation
- `background`: Complete character backstory
- `personality`: Detailed personality description
- `appear_hierarchical_info`: 3-layer appearance hierarchy
- `ext_hierarchical_info`: Extended cognitive and meta-rule data

### Story Localization Strategy
```sql
-- Example: Story structure with multi-language support
story_localizations:
├── story_core_world: Core concept, synopsis, constraints, goals
├── story_narrative_style: Structure, pacing, character arcs
└── story_beats: Scene-by-scene progression data
```

**Scene Hierarchy** (4-layer structure):
1. **Worldview**: Time period, geography, culture, society
2. **Scene**: Physical setting, atmosphere, sensory details
3. **Antecedent**: Prior events, character state, emotions
4. **Character**: Goals, conflicts, emotional arc, behaviors

## Card Component Data Support

### CharacterCard Requirements
```typescript
interface CharacterCardData {
  character: Character;
  stats: { likes: number; friends: number; shares: number; };
  aspectRatio: number;
}
```

**Supported by**: `character_stats` materialized view
- Real-time engagement metrics
- Creator information
- Quality ratings
- Recent activity timestamps

### MomentCard Requirements  
```typescript
interface MomentCardData {
  character: Character;
  moment: { id: string; title: string; image: string; };
  stats: { likes: number; shares: number; };
  publishedAt: string;
}
```

**Supported by**: `moment_stats` materialized view
- Social interaction aggregation
- User and character context
- Engagement calculations
- Media URL management

### TrophyCard Requirements
```typescript
interface Achievement {
  rarity: 'bronze' | 'silver' | 'gold' | 'platinum';
  progress: { current: number; total: number; };
  category: string;
  unlocked: boolean;
}
```

**Supported by**: `achievement_progress` materialized view
- Progress tracking with percentages
- Rarity-based ranking
- Category organization
- Unlock status management

### MemorialCard Requirements
```typescript
interface MemorialItem {
  characterName: string;
  memorialType: 'first_meeting' | 'perfect_intimacy' | 'perfect_storyline';
  anniversaryDate: Date;
  rewards: Array<{type: string; name: string; value: string;}>;
  timeRemaining: number;
}
```

**Supported by**: `memorial_events_view` materialized view
- Anniversary calculations
- Reward management
- Time-based status tracking
- Character relationship milestones

### CurrencyCard Requirements
```typescript
interface CurrencyBalance {
  type: 'star_diamonds' | 'joy_crystals' | 'glimmering_dust' | 'memory_puzzles';
  amount: number;
  spent24h: number;
  earned24h: number;
}
```

**Supported by**: `user_currencies` table with `currency_stats` JSONB
- Real-time balance tracking
- 24-hour activity statistics
- Transaction history
- Daily bonus management

### FeaturedCard Requirements
```typescript
interface FeaturedCardItem {
  diamondPrice: number;
  discountPercentage: 30 | 50;
  tag: 'quarterly_special' | 'monthly_special' | 'ip_collab';
  validityDays: number;
  purchaseLimit: 1 | 2;
}
```

**Supported by**: `store_stats` materialized view
- Product performance metrics
- Purchase statistics
- Availability tracking
- Promotional data

## Performance Optimization Strategy

### Materialized Views Refresh Schedule
```sql
-- Refresh every 15 minutes for real-time Card data
SELECT cron.schedule('refresh-card-views', '*/15 * * * *', 
  'SELECT refresh_all_card_views();');
```

**Views and Refresh Frequency**:
- `character_stats`: 15 minutes (high engagement)
- `moment_stats`: 15 minutes (social activity)
- `user_dashboard_data`: 30 minutes (profile data)
- `achievement_progress`: 1 hour (gamification)
- `memorial_events_view`: 1 hour (anniversary tracking)
- `store_stats`: 1 hour (commerce analytics)

### Indexing Strategy

**Primary Indexes**:
- UUID primary keys with B-tree indexes
- Foreign key relationships with cascade options
- Composite indexes for common query patterns

**JSONB Optimization**:
```sql
-- GIN indexes for flexible JSONB queries
CREATE INDEX idx_characters_personality_gin 
ON characters USING GIN (personality_traits);

CREATE INDEX idx_character_localizations_appear_gin 
ON character_localizations USING GIN (appear_hierarchical_info);
```

**Performance Considerations**:
- Partial indexes for boolean flags (`WHERE is_active = true`)
- Expression indexes for computed values
- Covering indexes for read-heavy queries

### Partitioning Strategy

**Activity Logs Partitioning**:
```sql
-- Monthly partitions for user activity logs
CREATE TABLE user_activity_logs_y2024m01 PARTITION OF user_activity_logs
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

**Benefits**:
- Improved query performance on time-based data
- Easier maintenance and archival
- Parallel query execution
- Reduced index size per partition

## Data Integrity and Consistency

### Constraint Strategy
- Foreign key constraints with appropriate CASCADE/SET NULL
- Check constraints for enum-like values
- UNIQUE constraints for business logic requirements
- NOT NULL constraints for required fields

### Transaction Management
- ACID compliance for all operations
- Optimistic locking for concurrent updates
- Deadlock detection and retry mechanisms
- Connection pooling for high concurrency

### Backup and Recovery
- Point-in-time recovery capability
- Automated daily backups
- Cross-region replication for disaster recovery
- Schema migration rollback procedures

## Migration and Deployment

### Migration Strategy
1. **Phase 1**: Core table extensions (characters, stories, users)
2. **Phase 2**: Localization tables and multi-language support
3. **Phase 3**: Store and gamification systems
4. **Phase 4**: Social features and UGC support
5. **Phase 5**: Performance optimization (views, indexes)

### Deployment Checklist
- [ ] Database backup before migration
- [ ] Schema validation in staging environment
- [ ] Performance testing with production data volume
- [ ] API endpoint testing with new schema
- [ ] Rollback procedure verification
- [ ] Monitoring and alerting setup

### Monitoring and Maintenance
- Query performance monitoring
- Index usage analysis
- Materialized view refresh monitoring
- Storage growth tracking
- Connection pool health checks

---

**Document Version**: 2.0.0  
**Last Updated**: 2024-01-31  
**Next Review**: 2024-04-30
