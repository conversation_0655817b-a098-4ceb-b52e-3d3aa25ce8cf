'use client';

import React from 'react';
import { DollarSign, ShoppingBag, TrendingUp, CreditCard, Users, Target } from 'lucide-react';

interface PurchaseAnalyticsProps {
  lang: string;
}

const PurchaseAnalytics: React.FC<PurchaseAnalyticsProps> = ({ lang }) => {
  const revenueMetrics = [
    { title: 'Total Revenue', value: '$45,231', change: '+12.3%', icon: DollarSign, color: 'from-green-500 to-emerald-600' },
    { title: 'Avg Order Value', value: '$13.47', change: '+8.9%', icon: CreditCard, color: 'from-blue-500 to-indigo-600' },
    { title: 'Conversion Rate', value: '3.2%', change: '+0.8%', icon: Target, color: 'from-purple-500 to-pink-600' },
    { title: 'Paying Users', value: '2,847', change: '+15.6%', icon: Users, color: 'from-orange-500 to-red-600' }
  ];

  const topProducts = [
    { name: 'MetaVerse Pass', revenue: '$15,600', units: 156, category: 'Subscription' },
    { name: 'Vampire Collection', revenue: '$8,940', units: 894, category: 'Character Arts' },
    { name: 'CEO Business Pack', revenue: '$6,780', units: 678, category: 'Character Series' },
    { name: 'Premium Memory Pack', revenue: '$4,560', units: 456, category: 'Memory Arts' },
    { name: 'Romance Story Bundle', revenue: '$3,240', units: 324, category: 'Story Arts' }
  ];

  return (
    <div className="space-y-8">
      {/* Revenue Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {revenueMetrics.map((metric, index) => {
          const IconComponent = metric.icon;
          return (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${metric.color} rounded-lg flex items-center justify-center`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <span className="text-sm font-medium text-green-600 dark:text-green-400">{metric.change}</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">{metric.value}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">{metric.title}</p>
            </div>
          );
        })}
      </div>

      {/* Top Products */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
          <ShoppingBag className="w-5 h-5 text-purple-500" />
          Top Selling Products
        </h3>
        <div className="space-y-4">
          {topProducts.map((product, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {index + 1}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">{product.name}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{product.category}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-semibold text-gray-900 dark:text-gray-100">{product.revenue}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">{product.units} units</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Revenue Trends Chart Placeholder */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-green-500" />
          Revenue Trends
        </h3>
        <div className="h-64 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
          <p className="text-gray-500 dark:text-gray-400">Revenue chart visualization would go here</p>
        </div>
      </div>
    </div>
  );
};

export default PurchaseAnalytics;
