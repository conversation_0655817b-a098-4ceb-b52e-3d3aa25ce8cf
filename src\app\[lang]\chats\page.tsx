import { characters } from '@/lib/mock-data';
import ChatsClientPage from './ChatsClientPage';

export default async function ChatsPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  // Directly use the centralized characters list
  const uniqueCharacters = characters; 
  
  // Create mock chat data for each character
  const chatListData = uniqueCharacters.map((character, index) => ({
    character,
    lastMessage: {
      text: index % 2 === 0 ? "Hey, I was just thinking about our last adventure. Ready for another one?" : "Did you see the sunset today? It was beautiful, reminded me of you.",
      timestamp: `${(index + 1) * 5}m ago`,
    }
  }));

  return <ChatsClientPage lang={lang} initialChats={chatListData} />;
} 