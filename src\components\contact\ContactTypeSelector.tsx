'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';

interface ContactTypeSelectorProps {
  selectedType: string;
  onTypeChange: (type: string) => void;
  lang: string;
}

const ContactTypeSelector: React.FC<ContactTypeSelectorProps> = ({
  selectedType,
  onTypeChange,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');

  const contactTypes = [
    'wishlist',
    'reportBug',
    'reportAbuse',
    'suggestions',
    'join',
    'invest',
    'other'
  ];

  return (
    <div>
      <label className="block text-sm font-medium mb-3 text-foreground">
        {t('contact.form.contactType.label')}
      </label>
      <div className="space-y-2">
        {contactTypes.map((type) => (
          <label 
            key={type} 
            className="flex items-center cursor-pointer hover:bg-accent/50 p-2 rounded-md transition-colors"
          >
            <input
              type="radio"
              name="contactType"
              value={type}
              checked={selectedType === type}
              onChange={(e) => onTypeChange(e.target.value)}
              className="mr-3 w-4 h-4 text-primary focus:ring-primary focus:ring-2 border-border"
            />
            <span className="text-foreground">
              {t(`contact.form.contactType.options.${type}`)}
            </span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default ContactTypeSelector; 