import React from 'react';

interface ProgressRingProps {
  progress: number; // 0-100
  size?: 'sm' | 'md' | 'lg';
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showLabel?: boolean;
  label?: string;
  vipGlow?: boolean;
  animated?: boolean;
}

export const ProgressRing: React.FC<ProgressRingProps> = ({
  progress,
  size = 'md',
  strokeWidth,
  color = 'stroke-purple-500',
  backgroundColor = 'stroke-gray-300 dark:stroke-gray-600',
  showLabel = true,
  label,
  vipGlow = false,
  animated = true
}) => {
  const sizes = {
    sm: { width: 60, height: 60, radius: 24, stroke: strokeWidth || 4 },
    md: { width: 80, height: 80, radius: 32, stroke: strokeWidth || 6 },
    lg: { width: 120, height: 120, radius: 48, stroke: strokeWidth || 8 }
  };

  const { width, height, radius, stroke } = sizes[size];
  const normalizedRadius = radius - stroke * 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className="relative flex items-center justify-center">
      {/* VIP发光效果 */}
      {vipGlow && (
        <div className="absolute inset-0 rounded-full blur-md bg-gradient-to-r from-yellow-400/40 to-orange-500/40 animate-pulse"></div>
      )}
      
      <svg
        width={width}
        height={height}
        className={`transform -rotate-90 ${animated ? 'transition-all duration-1000' : ''}`}
      >
        {/* 背景圆环 */}
        <circle
          cx={width / 2}
          cy={height / 2}
          r={normalizedRadius}
          fill="transparent"
          className={backgroundColor}
          strokeWidth={stroke}
        />
        
        {/* 进度圆环 */}
        <circle
          cx={width / 2}
          cy={height / 2}
          r={normalizedRadius}
          fill="transparent"
          className={vipGlow ? 'stroke-yellow-400' : color}
          strokeWidth={stroke}
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          style={{ 
            transition: animated ? 'stroke-dashoffset 1s ease-in-out' : 'none',
            filter: vipGlow ? 'drop-shadow(0 0 6px rgba(251, 191, 36, 0.5))' : 'none'
          }}
        />
      </svg>
      
      {/* 中心标签 */}
      {showLabel && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className={`font-bold ${
              size === 'sm' ? 'text-sm' : 
              size === 'md' ? 'text-lg' : 'text-2xl'
            } ${vipGlow ? 'text-yellow-400' : 'text-foreground'}`}>
              {label || `${Math.round(progress)}%`}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 