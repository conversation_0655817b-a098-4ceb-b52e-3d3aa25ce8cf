// API配置
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1';

// 通用API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: any[];
}

// 用户信息类型
export interface User {
  id: string;
  username: string;
  email: string;
  display_name?: string;
  avatar_url?: string;
  bio?: string;
  language_preference: string;
  timezone: string;
  is_creator: boolean;
  is_verified: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  profile?: UserProfile;
  currencies?: UserCurrencies;
  stats?: {
    followers_count?: number;
    following_count?: number;
  };
  is_following?: boolean;
}

export interface UserProfile {
  location?: string;
  website?: string;
  birth_date?: string;
  gender?: string;
  interests?: string[];
  social_links?: Record<string, string>;
  privacy_settings?: Record<string, any>;
}

export interface UserCurrencies {
  coins: number;
  gems: number;
  tokens: number;
  hearts: number;
  star_diamonds: number;
  joy_crystals: number;
  glimmering_dust: number;
  memory_puzzles: number;
  daily_bonus_available: boolean;
  next_daily_bonus: string;
  currency_stats?: Record<string, any>;
}

// 登录响应数据类型
export interface LoginResponseData {
  user: User;
  token: string;
  refreshToken: string;
}

// 通用请求函数
export async function apiRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  const response = await fetch(url, {
    ...options,
    headers: defaultHeaders,
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.message || `HTTP error! status: ${response.status}`);
  }

  return data;
}

// 带认证的请求函数
export async function authenticatedRequest<T = any>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  // 在客户端获取token
  let token = '';
  
  if (typeof window !== 'undefined') {
    token = localStorage.getItem('token') || '';
  }
  
  const headers = {
    ...options.headers,
    ...(token && { Authorization: `Bearer ${token}` }),
  };

  return apiRequest<T>(endpoint, {
    ...options,
    headers,
  });
}

// 认证相关API
export const authAPI = {
  // 登录/注册
  authenticate: (email: string, password: string, loginMethod: 'password' | 'otp' = 'password') =>
    apiRequest<LoginResponseData>('/auth', {
      method: 'POST',
      body: JSON.stringify({ email, password, loginMethod }),
    }),

  // 发送OTP
  sendOTP: (email: string) =>
    apiRequest('/auth/send-otp', {
      method: 'POST',
      body: JSON.stringify({ email }),
    }),

  // 验证OTP
  verifyOTP: (email: string, otp: string) =>
    apiRequest<LoginResponseData>('/auth/verify-otp', {
      method: 'POST',
      body: JSON.stringify({ email, otp }),
    }),

  // 刷新token
  refreshToken: (refreshToken: string) =>
    apiRequest<{ token: string; refreshToken: string }>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    }),

  // 登出
  logout: (refreshToken?: string) =>
    authenticatedRequest('/auth/logout', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    }),

  // 获取当前用户信息
  getCurrentUser: () =>
    authenticatedRequest<{ user: User }>('/auth/me'),
};

// 用户相关API
export const userAPI = {
  // 获取当前用户资料
  getProfile: () =>
    authenticatedRequest<{ user: User }>('/users/profile'),

  // 更新用户资料
  updateProfile: (data: Partial<User>) =>
    authenticatedRequest<{ user: User }>('/users/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // 获取用户信息（通过UID）
  getUser: (uid: string) =>
    apiRequest<{ user: User }>(`/users/${uid}`),

  // 获取用户关注列表
  getUserFollowing: (uid: string, page?: number, limit?: number) =>
    apiRequest<{ users: User[]; pagination: any }>(`/users/${uid}/following?page=${page || 1}&limit=${limit || 20}`),

  // 获取用户粉丝列表
  getUserFollowers: (uid: string, page?: number, limit?: number) =>
    apiRequest<{ users: User[]; pagination: any }>(`/users/${uid}/followers?page=${page || 1}&limit=${limit || 20}`),

  // 关注用户
  followUser: (userId: string) =>
    authenticatedRequest('/users/follow', {
      method: 'POST',
      body: JSON.stringify({ userId }),
    }),

  // 取消关注用户
  unfollowUser: (userId: string) =>
    authenticatedRequest('/users/unfollow', {
      method: 'POST',
      body: JSON.stringify({ userId }),
    }),

  // 获取用户货币
  getCurrencies: () =>
    authenticatedRequest<{ currencies: UserCurrencies }>('/users/currencies'),

  // 领取每日奖励
  claimDailyBonus: () =>
    authenticatedRequest<{ bonus: any; currencies: UserCurrencies }>('/users/currencies/daily-bonus', {
      method: 'POST',
    }),
};