'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Send, Lightbulb } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryFlashCreationProps } from '@/types/story-creation';
import StoryFlashGenerator from './StoryFlashGenerator';

interface StoryFlashResult {
  title: string;
  description: string;
  chapters: any[];
}

const FlashCreation: React.FC<StoryFlashCreationProps & { lang: string; characterName?: string }> = ({
  onSubmit,
  isLoading = false,
  lang,
  characterName = 'Character'
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [concept, setConcept] = useState('');
  const [generatedResult, setGeneratedResult] = useState<StoryFlashResult | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [regenerateCount, setRegenerateCount] = useState(0);
  const maxRegenerations = 3;

  const handleSubmit = async () => {
    if (concept.trim()) {
      setIsGenerating(true);

      // Simulate AI generation with realistic data
      setTimeout(() => {
        const mockResult: StoryFlashResult = {
          title: `${characterName}'s Adventure`,
          description: `An immersive story experience featuring ${characterName}`,
          chapters: [
            {
              id: 'chapter-1',
              title: 'First Encounter',
              environment: 'A cozy coffee shop with warm lighting and soft jazz music',
              timeElements: {
                season: 'Autumn',
                timeOfDay: 'Late afternoon',
                duration: '30 minutes'
              },
              spatialElements: {
                location: 'Corner table by the window',
                atmosphere: 'Intimate and welcoming'
              },
              environmentalElements: {
                weather: 'Light rain outside',
                lighting: 'Warm amber glow',
                sounds: 'Gentle jazz and coffee machine hums'
              },
              macroHistory: 'A chance meeting that could change everything',
              characterPast: 'Recently moved to the city, feeling lonely',
              immediateTrigger: 'Accidentally bumped into each other',
              mentalModel: {
                coreValues: 'Kindness, authenticity, curiosity',
                thinkingMode: 'Intuitive and empathetic'
              },
              emotionalBaseline: {
                displayedEmotion: 'Friendly curiosity',
                hiddenEmotion: 'Nervous excitement',
                emotionalIntensity: 65
              },
              dialogueStrategy: {
                initiative: 70,
                questioningStyle: 'Gentle and interested'
              },
              relationshipDynamics: {
                initialGoodwill: 75,
                intimacyLevel: 'Strangers with potential'
              },
              goalOrientation: {
                sceneGoal: 'Make a genuine connection',
                displayedIntent: 'Casual conversation',
                hiddenIntent: 'Hope for friendship or more'
              }
            },
            {
              id: 'chapter-2',
              title: 'Growing Closer',
              environment: 'A peaceful park with cherry blossoms in bloom',
              timeElements: {
                season: 'Spring',
                timeOfDay: 'Morning',
                duration: '1 hour'
              },
              spatialElements: {
                location: 'Walking path under blooming trees',
                atmosphere: 'Romantic and serene'
              },
              environmentalElements: {
                weather: 'Perfect sunny day',
                lighting: 'Soft morning sunlight',
                sounds: 'Birds singing and leaves rustling'
              },
              macroHistory: 'Second meeting after exchanging numbers',
              characterPast: 'Feeling more confident after first meeting',
              immediateTrigger: 'Planned walk together',
              mentalModel: {
                coreValues: 'Trust, openness, shared experiences',
                thinkingMode: 'More relaxed and genuine'
              },
              emotionalBaseline: {
                displayedEmotion: 'Happy and comfortable',
                hiddenEmotion: 'Growing affection',
                emotionalIntensity: 80
              },
              dialogueStrategy: {
                initiative: 85,
                questioningStyle: 'Personal and meaningful'
              },
              relationshipDynamics: {
                initialGoodwill: 90,
                intimacyLevel: 'Close friends'
              },
              goalOrientation: {
                sceneGoal: 'Deepen the connection',
                displayedIntent: 'Enjoy each other\'s company',
                hiddenIntent: 'Explore romantic possibilities'
              }
            },
            {
              id: 'chapter-3',
              title: 'Heart to Heart',
              environment: 'A quiet rooftop garden under the stars',
              timeElements: {
                season: 'Summer',
                timeOfDay: 'Late evening',
                duration: '2 hours'
              },
              spatialElements: {
                location: 'Secluded rooftop with city view',
                atmosphere: 'Intimate and magical'
              },
              environmentalElements: {
                weather: 'Clear starry night',
                lighting: 'Moonlight and city lights',
                sounds: 'Distant city hum and gentle breeze'
              },
              macroHistory: 'Months of growing friendship and trust',
              characterPast: 'Ready to be vulnerable and open',
              immediateTrigger: 'A moment of emotional honesty',
              mentalModel: {
                coreValues: 'Vulnerability, deep connection, love',
                thinkingMode: 'Emotionally open and honest'
              },
              emotionalBaseline: {
                displayedEmotion: 'Tender and sincere',
                hiddenEmotion: 'Deep love and commitment',
                emotionalIntensity: 95
              },
              dialogueStrategy: {
                initiative: 60,
                questioningStyle: 'Deep and meaningful'
              },
              relationshipDynamics: {
                initialGoodwill: 95,
                intimacyLevel: 'Romantic partners'
              },
              goalOrientation: {
                sceneGoal: 'Express true feelings',
                displayedIntent: 'Share deepest thoughts',
                hiddenIntent: 'Commit to the relationship'
              }
            }
          ]
        };

        setGeneratedResult(mockResult);
        setIsGenerating(false);
      }, 2000);
    }
  };

  const handleAccept = (result: StoryFlashResult) => {
    onSubmit(concept);
  };

  const handleRegenerate = () => {
    if (regenerateCount < maxRegenerations) {
      setRegenerateCount(prev => prev + 1);
      handleSubmit();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && concept.trim()) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const exampleConcepts = [
    t('storyCreation.functions.flash.example'),
    "Lost in a magical forest with a talking cat",
    "A detective story in Victorian London",
    "Space adventure with alien encounters",
    "A time-traveling romance across centuries"
  ];

  // Show StoryFlashGenerator if we have a result
  if (generatedResult) {
    return (
      <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-pink-200/50 dark:border-pink-700/50 rounded-2xl p-6 sm:p-8 shadow-xl">
        <StoryFlashGenerator
          characterName={characterName}
          onAccept={handleAccept}
          onRegenerate={handleRegenerate}
          isGenerating={isGenerating}
          result={generatedResult}
          regenerateCount={regenerateCount}
          maxRegenerations={maxRegenerations}
          lang={lang}
        />
      </div>
    );
  }

  return (
    <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-pink-200/50 dark:border-pink-700/50 rounded-2xl p-6 sm:p-8 shadow-xl">
      <div className="text-center mb-8">
        <div className="flex justify-center mb-4">
          <div className="bg-gradient-to-r from-pink-500 to-rose-500 p-3 rounded-full shadow-lg">
            <Sparkles className="w-8 h-8 text-white" />
          </div>
        </div>
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-gray-100 mb-2">
          {t('storyCreation.functions.flash.title')} {t('storyCreation.title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-300 max-w-md mx-auto">
          {t('storyCreation.functions.flash.description')} {characterName}
        </p>
      </div>

      <div className="space-y-6">
        {/* Concept Input */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
            {t('storyCreation.functions.flash.placeholder').replace('...', '')}
          </label>
          <div className="relative">
            <textarea
              value={concept}
              onChange={(e) => setConcept(e.target.value)}
              onKeyPress={handleKeyPress}
              rows={4}
              className="w-full px-4 py-3 bg-white/90 dark:bg-gray-700/90 border border-pink-200 dark:border-pink-700 rounded-xl text-gray-700 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-pink-500/20 focus:border-pink-500 transition-all resize-none"
              placeholder={t('storyCreation.functions.flash.placeholder')}
              disabled={isLoading}
            />
            {concept && (
              <div className="absolute bottom-3 right-3 text-xs text-gray-500 dark:text-gray-400">
                {concept.length}/500
              </div>
            )}
          </div>
        </div>

        {/* Example Concepts */}
        <div>
          <div className="flex items-center gap-2 mb-3">
            <Lightbulb className="w-4 h-4 text-yellow-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('storyCreation.functions.flash.needInspiration')}
            </span>
          </div>
          <div className="flex flex-wrap gap-2">
            {exampleConcepts.map((example, index) => (
              <button
                key={index}
                onClick={() => setConcept(example)}
                disabled={isLoading}
                className="px-3 py-1.5 bg-pink-50 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300 rounded-full text-xs hover:bg-pink-100 dark:hover:bg-pink-900/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed border border-pink-200 dark:border-pink-700"
              >
                {example}
              </button>
            ))}
          </div>
        </div>

        {/* Generate Button */}
        <div className="flex justify-center pt-4">
          <button
            onClick={handleSubmit}
            disabled={!concept.trim() || isGenerating}
            className="flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-xl hover:from-pink-600 hover:to-rose-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>{t('storyCreation.functions.flash.generating')}</span>
              </>
            ) : (
              <>
                <Send className="w-5 h-5" />
                <span>{t('storyCreation.functions.flash.generate')}</span>
              </>
            )}
          </button>
        </div>

        {/* Helper Text */}
        <div className="bg-pink-50 dark:bg-pink-900/20 border border-pink-200 dark:border-pink-700 rounded-lg p-4">
          <p className="text-sm text-pink-700 dark:text-pink-300 text-center">
            {t('storyCreation.functions.flash.helpText')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default FlashCreation; 