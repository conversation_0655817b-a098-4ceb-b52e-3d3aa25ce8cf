import React from 'react';
import { TrendingUp, Medal } from 'lucide-react';
import { JourneyCard, JourneyCardHeader } from '../shared/JourneyCard';
import { useTranslation } from '@/app/i18n/client';

interface SeasonRanker {
  rank: number;
  name: string;
  trophies: number;
  weeklyGain: number;
  avatar: string;
}

interface SeasonLeaderboardCardProps {
  seasonRankers: SeasonRanker[];
  lang: string;
}

export const SeasonLeaderboardCard: React.FC<SeasonLeaderboardCardProps> = ({
  seasonRankers,
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');

  const getRankingStyle = (index: number) => {
    switch (index) {
      case 0:
        return 'bg-gradient-to-r from-yellow-500/20 to-amber-500/20 border border-yellow-500/30';
      case 1:
        return 'bg-gradient-to-r from-gray-500/20 to-slate-500/20 border border-gray-500/30';
      case 2:
        return 'bg-gradient-to-r from-orange-500/20 to-red-500/20 border border-orange-500/30';
      default:
        return 'bg-white/10 border border-white/10';
    }
  };

  const getRankBadgeStyle = (index: number) => {
    switch (index) {
      case 0:
        return 'bg-gradient-to-r from-yellow-400 to-amber-500 text-white';
      case 1:
        return 'bg-gradient-to-r from-gray-400 to-slate-500 text-white';
      case 2:
        return 'bg-gradient-to-r from-orange-400 to-red-500 text-white';
      default:
        return 'bg-gradient-to-r from-blue-400 to-indigo-500 text-white';
    }
  };

  return (
    <JourneyCard gradient="from-yellow-500/10 via-orange-500/10 to-yellow-500/10">
      <JourneyCardHeader
        icon={TrendingUp}
        iconGradient="from-yellow-500 to-orange-500"
        title={t('journey.season.seasonRanking')}
        subtitle={t('journey.season.topPerformers')}
        actions={
          <div className="flex items-center gap-1">
            <Medal className="w-4 h-4 text-yellow-400" />
            <span className="text-yellow-400 text-sm font-medium">{t('journey.season.leaderboard')}</span>
          </div>
        }
      />

      <div className="space-y-2">
        {seasonRankers.map((ranker, index) => (
          <div
            key={ranker.rank}
            className={`flex items-center gap-3 p-2.5 rounded-lg transition-all duration-300 hover:scale-[1.01] ${getRankingStyle(index)}`}
          >
            {/* 排名徽章 - 更小 */}
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${getRankBadgeStyle(index)} shrink-0`}>
              {ranker.rank}
            </div>
            
            {/* 玩家信息 - 紧凑布局 */}
            <div className="flex-1 min-w-0">
              <div className="font-semibold text-foreground flex items-center gap-2 text-sm">
                <span className="truncate">{ranker.name}</span>
                {index < 3 && (
                  <span className="text-base shrink-0">{index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}</span>
                )}
              </div>
              <div className="text-xs text-foreground/60">
                +{ranker.weeklyGain} {t('journey.season.thisWeek')}
              </div>
            </div>
            
            {/* 奖杯数和头像 - 紧凑布局 */}
            <div className="flex items-center gap-2 shrink-0">
              <div className="text-right">
                <div className="font-bold text-foreground text-sm">{ranker.trophies.toLocaleString()}</div>
                <div className="text-xs text-foreground/60">{t('journey.season.seasonTrophies')}</div>
              </div>
              <div className="text-xl">{ranker.avatar}</div>
            </div>
          </div>
        ))}
      </div>

      {/* 查看完整排行榜按钮 - 更紧凑 */}
      <div className="mt-4 text-center">
        <button className="px-4 py-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg text-yellow-400 font-medium hover:scale-105 transition-all text-sm">
          <TrendingUp className="w-4 h-4 inline mr-2" />
          {t('journey.season.viewFullLeaderboard')}
        </button>
      </div>
    </JourneyCard>
  );
}; 