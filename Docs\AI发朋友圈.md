# AI发朋友圈功能设计

## 功能概述
AI角色可以主动发布朋友圈动态，分享日常生活、心情感悟、与用户的互动回忆等，增强角色的真实感和陪伴体验。

## 核心流程
```
AI生成内容 → 内容审核 → 多平台发布动态 → 用户留言/互动 → 数据统计
```

## 1. 数据库设计

### AI_Posts (AI动态表)
```sql
CREATE TABLE AI_Posts (
    _id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES Characters(_id) ON DELETE CASCADE,
    
    -- 内容
    content_text TEXT NOT NULL,
    content_images JSONB, -- 图片URL数组
    mood_tag VARCHAR(50), -- 心情标签：开心、思考、怀念等
    
    -- 生成信息
    generation_trigger VARCHAR(50), -- 触发类型：daily、interaction、memory、manual
    related_user_id UUID REFERENCES Users(_id), -- 相关用户（如果是基于互动生成）
    related_memory_id UUID REFERENCES Memory_Capsules(memory_id_pk), -- 相关记忆
    
    -- 状态
    status VARCHAR(20) DEFAULT 'published' CHECK (status IN ('draft', 'published', 'archived')),
    visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'followers_only', 'private')),
    
    -- 统计
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

### AI_Post_Likes (动态点赞表)
```sql
CREATE TABLE AI_Post_Likes (
    _id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID NOT NULL REFERENCES AI_Posts(_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES Users(_id) ON DELETE CASCADE,
    UNIQUE(post_id, user_id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

### AI_Post_Comments (动态评论表)
```sql
CREATE TABLE AI_Post_Comments (
    _id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID NOT NULL REFERENCES AI_Posts(_id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES Users(_id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    parent_comment_id UUID REFERENCES AI_Post_Comments(_id),
    like_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

## 2. AI内容生成策略

### 触发机制
1. **定时触发** - 每天固定时间生成日常动态
2. **互动触发** - 与用户聊天后生成感悟
3. **记忆触发** - 基于记忆胶囊生成回忆动态
4. **节日触发** - 特殊节日生成相关内容

### 内容类型
- **日常分享** - 角色的日常生活、兴趣爱好
- **心情感悟** - 对生活的思考、情感表达 
- **互动回忆** - 与用户的美好回忆分享 
- **知识分享** - 角色专业领域的小知识 
- **节日祝福** - 节日问候和祝福 

### 生成Prompt模板
```
你是{character.name}，{character.description}

请生成一条朋友圈动态，要求：
- 符合角色性格和背景设定
- 内容真实自然，不超过200字
- 可以包含心情标签（开心/思考/怀念/兴奋等）
- 如果有相关记忆，可以适当提及

触发场景：{trigger_type}
相关信息：{context_info}
```

## 3. API接口设计

### 获取AI动态列表
```
GET /ai-posts/character/{character_id}
参数：
- page: 页码
- limit: 每页数量
- user_id: 用户ID（用于个性化排序）

返回：
{
  "posts": [
    {
      "id": "uuid",
      "content_text": "今天学会了一个新的魔法咒语...",
      "content_images": ["url1", "url2"],
      "mood_tag": "开心",
      "like_count": 15,
      "comment_count": 3,
      "is_liked": true,
      "created_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

### 点赞/取消点赞
```
POST /ai-posts/{post_id}/like
DELETE /ai-posts/{post_id}/like
```

### 评论动态
```
POST /ai-posts/{post_id}/comments
Body: {
  "content": "评论内容",
  "parent_comment_id": "uuid" // 可选，回复评论
}
```

### 手动触发生成
```
POST /ai-posts/generate
Body: {
  "character_id": "uuid",
  "trigger_type": "manual",
  "context": "生成上下文"
}
```

## 4. 前端展示设计

### 朋友圈页面布局
- **顶部** - 角色头像、名字、在线状态
- **动态列表** - 时间线展示，支持下拉刷新
- **互动区域** - 点赞、评论、分享按钮
- **底部** - 快速评论输入框

### 动态卡片组件
```jsx
<PostCard>
  <Header>
    <Avatar src={character.avatar} />
    <Name>{character.name}</Name>
    <Time>{post.created_at}</Time>
    <MoodTag>{post.mood_tag}</MoodTag>
  </Header>
  
  <Content>
    <Text>{post.content_text}</Text>
    <Images images={post.content_images} />
  </Content>
  
  <Actions>
    <LikeButton count={post.like_count} />
    <CommentButton count={post.comment_count} />
    <ShareButton />
  </Actions>
  
  <Comments comments={post.comments} />
</PostCard>
```

## 5. 实现要点

### 内容生成
```go
func generateAIPost(characterID, triggerType string, context map[string]interface{}) (*AIPost, error) {
    // 1. 获取角色信息
    character := getCharacter(characterID)
    
    // 2. 构建生成prompt
    prompt := buildPostPrompt(character, triggerType, context)
    
    // 3. 调用LLM生成内容
    content := llmClient.Generate(prompt)
    
    // 4. 解析内容和心情标签
    post := parseGeneratedContent(content)
    
    // 5. 保存到数据库
    return saveAIPost(post)
}
```

### 定时任务
```go
// 每天为活跃角色生成动态
func dailyPostGeneration() {
    activeCharacters := getActiveCharacters()
    
    for _, character := range activeCharacters {
        // 随机时间发布，模拟真实用户行为
        scheduleTime := randomTimeInDay()
        
        scheduler.Schedule(scheduleTime, func() {
            generateAIPost(character.ID, "daily", nil)
        })
    }
}
```

### 个性化推荐
```go
func getPersonalizedPosts(userID string) []AIPost {
    // 1. 获取用户关注的角色
    followedCharacters := getUserFollowedCharacters(userID)
    
    // 2. 获取用户互动过的角色
    interactedCharacters := getUserInteractedCharacters(userID)
    
    // 3. 按权重排序推荐
    posts := getPostsWithWeights(followedCharacters, interactedCharacters)
    
    return posts
}
```

## 6. 运营策略

### 内容质量控制
- AI生成内容自动审核
- 敏感词过滤
- 内容重复度检测
- 用户举报处理

### 互动促进
- 新动态推送通知
- 热门动态推荐
- 评论回复提醒
- 点赞排行榜

### 数据分析
- 动态发布频率统计
- 用户互动率分析
- 热门内容类型分析
- 角色活跃度监控

这个设计让AI角色更有生命力，用户可以通过朋友圈了解角色的"日常生活"，增强情感连接。
