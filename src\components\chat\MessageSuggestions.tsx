'use client';

import { FC, useState, useEffect } from 'react';
import { Lightbulb, RefreshCw } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface MessageSuggestionsProps {
  characterName: string;
  onSuggestionClick: (suggestion: string) => void;
  isVisible: boolean;
  lang?: string;
}

const MessageSuggestions: FC<MessageSuggestionsProps> = ({
  characterName,
  onSuggestionClick,
  isVisible,
  lang = 'en'
}) => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation(lang, 'translation');

  // Mock suggestions based on character
  const generateSuggestions = async () => {
    setIsLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockSuggestions = [
      `Tell me about your day, ${characterName}`,
      "What's your favorite memory?",
      "I'd love to hear your thoughts on...",
      "What makes you happy?",
      "Can you share a story with me?"
    ];
    
    // Randomize suggestions
    const shuffled = mockSuggestions.sort(() => 0.5 - Math.random());
    setSuggestions(shuffled.slice(0, 3));
    setIsLoading(false);
  };

  useEffect(() => {
    if (isVisible && suggestions.length === 0) {
      generateSuggestions();
    }
  }, [isVisible]);

  const handleRefresh = () => {
    generateSuggestions();
  };

  if (!isVisible) return null;

  return (
    <div className="bg-gray-800/90 backdrop-blur-sm rounded-lg p-3 mb-2 border border-gray-700">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Lightbulb size={16} className="text-yellow-400" />
          <span className="text-sm text-gray-300 font-medium">{t('chat.suggestions.conversationStarters')}</span>
        </div>
        <button
          onClick={handleRefresh}
          disabled={isLoading}
          className="p-1 text-gray-400 hover:text-white transition-colors rounded"
          title={t('chat.suggestions.refreshSuggestions')}
        >
          <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />
        </button>
      </div>
      
      <div className="space-y-2">
        {isLoading ? (
          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-8 bg-gray-700/50 rounded animate-pulse" />
            ))}
          </div>
        ) : (
          suggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => onSuggestionClick(suggestion)}
              className="w-full text-left p-2 text-sm text-gray-300 bg-gray-700/50 hover:bg-gray-600/50 rounded transition-colors"
            >
              {suggestion}
            </button>
          ))
        )}
      </div>
    </div>
  );
};

export default MessageSuggestions;
