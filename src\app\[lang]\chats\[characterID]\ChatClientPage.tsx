'use client';

import { FC, useState, useRef, useCallback, useEffect } from 'react';
import Image from 'next/image';
import { GripVertical } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useMediaQuery } from 'react-responsive';
import UserBubble from '@/components/bubbles/UserBubble';
import CharBubble from '@/components/bubbles/CharBubble';
import { useAuthContext } from '@/components/AuthProvider';
import MessageSuggestions from '@/components/chat/MessageSuggestions';
import ChatHeader from '@/components/chat/ChatHeader';
import ChatInput from '@/components/chat/ChatInput';

// --- Reusable Child Components ---

const MessageList = ({ messages, characterData, chatEndRef, user }: any) => (
  <div className="flex-1 overflow-y-auto p-4 flex flex-col-reverse space-y-4 space-y-reverse min-h-0">
    <div ref={chatEndRef} />
    {[...(messages || [])].map((msg: any, index: number) => (
      <div key={index}>
        {msg.side === 'right' ? (
          <UserBubble message={msg} user={user} />
        ) : (
          <CharBubble message={msg} character={characterData} />
        )}
      </div>
    ))}
  </div>
);



// --- Main Client Component ---

interface ChatClientPageProps {
  lang: string;
  characterData: any;
  initialMessages: any[];
}

const ChatClientPage: FC<ChatClientPageProps> = ({ lang, characterData, initialMessages }) => {
  const isDesktop = useMediaQuery({ query: '(min-width: 1024px)' });
  const [messages, setMessages] = useState(initialMessages);
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isVoiceMode, setIsVoiceMode] = useState(false);

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      setMessages([{ id: Date.now(), text: inputValue, side: 'right' as const }, ...messages]);
      setInputValue('');
      setShowSuggestions(false); // Hide suggestions after sending
    }
  };

  const handleSuggestionToggle = () => {
    setShowSuggestions(!showSuggestions);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    setShowSuggestions(false);
  };

  const handleVoiceModeToggle = () => {
    setIsVoiceMode(!isVoiceMode);
    setShowSuggestions(false); // Hide suggestions when switching modes
  };

  const handleMenuToggle = (isOpen: boolean) => {
    setIsMenuOpen(isOpen);
  };
  
  const [handleY, setHandleY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false); // 新增：菜单状态
  
  const chatEndRef = useRef<HTMLDivElement>(null);
  const handleRef = useRef<HTMLDivElement>(null);
  const rightColumnRef = useRef<HTMLDivElement>(null);
  
  // 获取真实的认证用户信息
  const { user } = useAuthContext();

  useEffect(() => { setIsClient(true); }, []);

  useEffect(() => {
    if (!isClient || isDesktop) return;
    const INPUT_BAR_HEIGHT = 72;
    const containerHeight = window.innerHeight;
    if (!containerHeight) return;
    const chatAreaHeight = containerHeight * 0.45;
    const initialBottomEdge = containerHeight - INPUT_BAR_HEIGHT;
    setHandleY(initialBottomEdge - (chatAreaHeight / 2));
  }, [isDesktop, isClient]);

  const handleDragStart = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    setIsDragging(true);
    document.body.style.cursor = 'row-resize';
  }, []);

  const handleDragMove = useCallback((e: MouseEvent | TouchEvent) => {
    if (!isDragging) return;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;
    const containerTop = rightColumnRef.current?.getBoundingClientRect().top || 0;
    const relativeY = clientY - containerTop;
    const containerHeight = isDesktop ? rightColumnRef.current?.offsetHeight : window.innerHeight;
    if (!containerHeight) return;
    const chatAreaHeight = containerHeight * 0.45;
    const halfChatHeight = chatAreaHeight / 2;
    const HEADER_HEIGHT = isDesktop ? 0 : 80;
    const INPUT_BAR_HEIGHT = 72;
    const menuOffset = isMenuOpen ? 160 : 0;
    // 补偿菜单偏移，因为拉杆显示位置是 handleY - 32 - menuOffset
    const adjustedRelativeY = relativeY + menuOffset;
    // 考虑菜单偏移和顶部限制（3rem = 48px）
    const minHandleY = Math.max(HEADER_HEIGHT + halfChatHeight, 48 + halfChatHeight + menuOffset);
    const maxHandleY = containerHeight - INPUT_BAR_HEIGHT - halfChatHeight;
    const clampedY = Math.max(minHandleY, Math.min(adjustedRelativeY, maxHandleY));
    setHandleY(clampedY);
  }, [isDragging, isDesktop, isMenuOpen]);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
    document.body.style.cursor = 'default';
  }, []);

  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleDragMove);
      window.addEventListener('touchmove', handleDragMove, { passive: false });
      window.addEventListener('mouseup', handleDragEnd);
      window.addEventListener('touchend', handleDragEnd);
    } else {
      window.removeEventListener('mousemove', handleDragMove);
      window.removeEventListener('touchmove', handleDragMove);
      window.removeEventListener('mouseup', handleDragEnd);
      window.removeEventListener('touchend', handleDragEnd);
    }
    return () => {
      window.removeEventListener('mousemove', handleDragMove);
      window.removeEventListener('touchmove', handleDragMove);
      window.removeEventListener('mouseup', handleDragEnd);
      window.removeEventListener('touchend', handleDragEnd);
    };
  }, [isDragging, handleDragMove, handleDragEnd]);
  
  useEffect(() => {
    if (isClient) { chatEndRef.current?.scrollIntoView({ behavior: 'smooth' }); }
  }, [messages, isClient]);
  
  if (!isClient) { return null; }
  
  const containerHeight = (isDesktop ? rightColumnRef.current?.offsetHeight : window.innerHeight) || 0;
  const chatAreaHeight = containerHeight * 0.45;
  // 当菜单打开时，聊天区域需要向上移动，减去菜单高度（160px = h-40）
  const menuOffset = isMenuOpen ? 160 : 0;
  const rawChatAreaTop = handleY > 0 ? handleY - chatAreaHeight / 2 - menuOffset : -chatAreaHeight;
  // 确保聊天区域不超过header（3.5rem = 56px）
  const chatAreaTop = Math.max(56, rawChatAreaTop);

  // Mobile immersive view
  if (!isDesktop) {
    return (
      <div className="h-screen w-screen overflow-hidden relative bg-black select-none">
        <div className="absolute inset-0 z-0">
          {characterData.character_bg_image && (
            <Image
              src={characterData.character_bg_image.replace(/'/g, "")}
              alt={characterData.name}
              fill
              className="object-cover"
              unoptimized
              priority
            />
          )}
          <div className="absolute inset-0 bg-black/30" />
        </div>
        <ChatHeader characterData={characterData} lang={lang} />
        <div
          className="absolute left-0 right-0 z-30 flex flex-col bg-transparent"
          style={{
            top: `${chatAreaTop}px`,
            height: `${chatAreaHeight}px`,
            maskImage: 'linear-gradient(to top, black 85%, transparent 100%)'
          }}
        >
          <MessageList messages={messages} characterData={characterData} chatEndRef={chatEndRef} user={user} />
        </div>
        <div
          ref={handleRef}
          onMouseDown={handleDragStart}
          onTouchStart={handleDragStart}
          className="absolute right-0 w-8 h-16 flex items-center justify-center cursor-row-resize z-50 group touch-none"
          style={{
            top: handleY > 0 ? `${handleY - 32 - menuOffset}px` : '-100px',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            borderRadius: '8px 0 0 8px'
          }}
        >
          <GripVertical className="w-5 h-8 text-white/70" />
        </div>
        <div className="absolute bottom-0 left-0 right-0 z-40 space-y-0">
          <div className="px-4">
            <MessageSuggestions
              characterName={characterData.name}
              onSuggestionClick={handleSuggestionClick}
              isVisible={showSuggestions && !isVoiceMode}
              lang={lang}
            />
          </div>
          <ChatInput
            value={inputValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInputValue(e.target.value)}
            onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Enter' && handleSendMessage()}
            onSend={handleSendMessage}
            onSuggestionToggle={handleSuggestionToggle}
            showSuggestions={showSuggestions}
            isVoiceMode={isVoiceMode}
            onVoiceModeToggle={handleVoiceModeToggle}
            onMenuToggle={handleMenuToggle}
            lang={lang}
          />
        </div>
      </div>
    );
  }

  // Desktop 62.5% / 37.5% split view
  return (
    <div className="h-screen w-screen bg-black flex text-white select-none">
      <div className="w-[62.5%] h-full relative">
        <Image
          src={characterData.character_bg_image.replace(/'/g, "")}
          alt={characterData.name}
          fill
          className="object-cover"
          unoptimized
          priority
        />
        <ChatHeader characterData={characterData} lang={lang} />
      </div>

      <div ref={rightColumnRef} className="w-[37.5%] h-full relative flex flex-col">
        <div className="absolute inset-0 z-0">
          <Image
            src={characterData.character_bg_image.replace(/'/g, "")}
            alt=""
            fill
            className="object-cover"
            unoptimized
          />
          <div className="absolute inset-0 bg-black/50 backdrop-blur-lg" />
        </div>
        <div
          className="relative z-10 flex-1 flex flex-col min-h-0"
          style={{
            maskImage: 'linear-gradient(to top, black 85%, transparent 100%)',
            transition: 'all 0.3s ease-in-out',
            transform: isMenuOpen ? 'translateY(-160px)' : 'translateY(0)'
          }}
        >
          <MessageList messages={messages} characterData={characterData} chatEndRef={chatEndRef} user={user} />
        </div>
        <div className="relative z-10 flex-shrink-0 space-y-0">
          <div className="px-4">
            <MessageSuggestions
              characterName={characterData.name}
              onSuggestionClick={handleSuggestionClick}
              isVisible={showSuggestions && !isVoiceMode}
              lang={lang}
            />
          </div>
          <ChatInput
            value={inputValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setInputValue(e.target.value)}
            onKeyPress={(e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Enter' && handleSendMessage()}
            onSend={handleSendMessage}
            onSuggestionToggle={handleSuggestionToggle}
            showSuggestions={showSuggestions}
            isVoiceMode={isVoiceMode}
            onVoiceModeToggle={handleVoiceModeToggle}
            onMenuToggle={handleMenuToggle}
            lang={lang}
          />
        </div>
      </div>
    </div>
  );
}

export default ChatClientPage; 