'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON>Right, Zap, Check, Loader2, Diamond } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

export interface FeaturedCardItem {
  id: string;
  name: string;
  description: string;
  endoraPrice: number;
  discountPercentage: 30 | 50; // Only -30% or -50%
  tag?: 'quarterly_special' | 'monthly_special' | 'weekly_special' | 'ip_collab' | 'new_feature'; // Various tag types
  ipCollabName?: string; // For IP Collab items
  validityDays: number; // How many days the offer is valid
  purchaseLimit: 1 | 2; // Can only buy 1 or 2 times
  isPurchased?: boolean; // Whether user has already purchased
  purchaseCount?: number; // How many times user has purchased (0-2)
}

interface FeaturedCardProps {
  item: FeaturedCardItem;
  onPurchase: (item: FeaturedCardItem) => void;
  isLoading?: boolean;
  variant?: 'mobile' | 'desktop' | 'responsive';
  lang: string;
}

const FeaturedCard: React.FC<FeaturedCardProps> = ({
  item,
  onPurchase,
  isLoading = false,
  variant = 'responsive',
  lang
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const originalPrice = Math.round(item.endoraPrice / (1 - item.discountPercentage / 100));
  const canPurchase = !item.isPurchased && (item.purchaseCount || 0) < item.purchaseLimit;
  const remainingPurchases = item.purchaseLimit - (item.purchaseCount || 0);

  const handleGetNowClick = () => {
    if (canPurchase) {
      setShowConfirmModal(true);
    }
  };

  // Responsive card classes based on variant
  const getCardClasses = () => {
    const baseClasses = "bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 group";

    if (variant === 'mobile') {
      // Legacy mobile horizontal scroll variant
      return `flex-shrink-0 w-64 xs:w-72 sm:w-80 p-4 sm:p-6 ${baseClasses}`;
    }

    if (variant === 'desktop') {
      // Legacy desktop variant
      return `w-full p-4 sm:p-6 ${baseClasses}`;
    }

    // New responsive variant - adapts to grid container
    return `w-full p-4 md:p-6 min-h-[360px] flex flex-col ${baseClasses}`;
  };

  const handleConfirmPurchase = () => {
    setShowConfirmModal(false);
    onPurchase(item);
  };

  const getTagDisplay = () => {
    switch (item.tag) {
      case 'quarterly_special':
        return (
          <div className="inline-flex items-center gap-1 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200 px-2 py-1 rounded-full text-xs font-medium">
            <Star className="w-3 h-3" />
            {t('store.featuredCard.tags.quarterlySpecial')}
          </div>
        );
      case 'monthly_special':
        return (
          <div className="inline-flex items-center gap-1 bg-blue-100 dark:bg-blue-900/50 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full text-xs font-medium">
            <Clock className="w-3 h-3" />
            {t('store.featuredCard.tags.monthlySpecial')}
          </div>
        );
      case 'weekly_special':
        return (
          <div className="inline-flex items-center gap-1 bg-orange-100 dark:bg-orange-900/50 text-orange-800 dark:text-orange-200 px-2 py-1 rounded-full text-xs font-medium">
            <Clock className="w-3 h-3" />
            {t('store.featuredCard.tags.weeklySpecial')}
          </div>
        );
      case 'ip_collab':
        return (
          <div className="inline-flex items-center gap-1 bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-200 px-2 py-1 rounded-full text-xs font-medium">
            <Star className="w-3 h-3" />
            {t('store.featuredCard.tags.ipCollab')}
          </div>
        );
      case 'new_feature':
        return (
          <div className="inline-flex items-center gap-1 bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
            <Zap className="w-3 h-3" />
            {t('store.featuredCard.tags.newFeature')}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className={getCardClasses()}>
        {/* Header with tag and discount */}
        <div className="flex items-start justify-between mb-3 sm:mb-4">
          <div className="flex flex-wrap items-center gap-1 sm:gap-2 flex-1 mr-2">
            {getTagDisplay()}
          </div>
          <div className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold flex-shrink-0">
            -{item.discountPercentage}%
          </div>
        </div>

        {/* Content - Flexible */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1">
            <h3 className="text-base sm:text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary transition-colors line-clamp-2">
              {item.name}
            </h3>
            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-3 mb-2">
              {item.description}
            </p>
            {/* IP Collaboration info */}
            {item.tag === 'ip_collab' && item.ipCollabName && (
              <div className="flex items-center gap-1 text-pink-600 dark:text-pink-400 text-xs font-medium mb-3">
                <Star className="w-3 h-3" />
                {t('store.featuredCard.collaboration', { name: item.ipCollabName })}
              </div>
            )}
          </div>

          {/* Fixed Bottom Section */}
          <div className="mt-auto space-y-3">
            {/* Pricing - Diamond Only */}
            <div>
              <div className="flex items-center justify-center gap-2 mb-1">
                <span className="text-lg sm:text-xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-1">
                  <Diamond className="w-4 h-4 text-blue-500" fill="currentColor" />
                  {item.endoraPrice}
                </span>
                <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 line-through flex items-center gap-1">
                  <Diamond className="w-3 h-3 text-gray-400" fill="currentColor" />
                  {originalPrice}
                </span>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {item.purchaseLimit > 1
                    ? t('store.featuredCard.purchaseLimitPlural', { limit: item.purchaseLimit })
                    : t('store.featuredCard.purchaseLimit', { limit: item.purchaseLimit })
                  }
                </div>
              </div>
            </div>

            {/* Validity and purchase info */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1 text-orange-600 dark:text-orange-400 text-xs font-medium">
                <Clock className="w-3 h-3" />
                {t('store.featuredCard.daysLeft', { days: item.validityDays })}
              </div>
              {!canPurchase && (
                <div className="text-xs text-gray-500">
                  {item.isPurchased
                    ? t('store.featuredCard.purchased')
                    : t('store.featuredCard.remainingPurchases', { count: remainingPurchases })
                  }
                </div>
              )}
            </div>

            {/* Action button with diamond emphasis */}
            <button
              onClick={handleGetNowClick}
              disabled={!canPurchase || isLoading}
              className={`w-full py-2.5 px-4 rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center gap-1 ${
                canPurchase && !isLoading
                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:shadow-lg hover:shadow-blue-500/30 group-hover:scale-105'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-3 h-3 animate-spin" />
                  {t('store.featuredCard.processing')}
                </>
              ) : canPurchase ? (
                <>
                  <Diamond className="w-3 h-3" fill="currentColor" />
                  {t('store.featuredCard.getWithEndora')}
                  <ArrowRight className="w-3 h-3" />
                </>
              ) : (
                <>
                  <Check className="w-3 h-3" />
                  {item.isPurchased ? t('store.featuredCard.purchased') : t('store.featuredCard.limitReached')}
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Purchase Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 z-[70] flex items-center justify-center">
          <div className="absolute inset-0 bg-black/50" onClick={() => setShowConfirmModal(false)} />
          <div className="relative bg-white dark:bg-gray-800 rounded-2xl p-6 mx-4 max-w-sm w-full shadow-2xl">
            <div className="text-center">
              {/* Diamond icon */}
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Diamond className="w-8 h-8 text-white" fill="currentColor" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {t('store.featuredCard.confirmPurchase')}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {item.name}
              </p>

              {/* Diamond cost confirmation */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-4">
                <div className="flex items-center justify-center gap-2 text-lg font-bold text-blue-700 dark:text-blue-300">
                  <Diamond className="w-5 h-5 text-blue-500" fill="currentColor" />
                  {item.endoraPrice} Endora
                </div>
                <div className="flex items-center justify-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
                  <span className="line-through flex items-center gap-1">
                    <Diamond className="w-3 h-3" fill="currentColor" />
                    {originalPrice}
                  </span>
                  <span className="text-green-600 dark:text-green-400 font-medium">
                    {t('store.featuredCard.save', { percentage: item.discountPercentage })}
                  </span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">
                  {t('store.featuredCard.validFor', { days: item.validityDays })}
                </p>
              </div>
              {/* Buttons */}
              <div className="flex gap-3">
                <button
                  onClick={() => setShowConfirmModal(false)}
                  className="flex-1 py-3 px-4 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium"
                >
                  {t('store.featuredCard.cancel')}
                </button>
                <button
                  onClick={handleConfirmPurchase}
                  className="flex-1 py-3 px-4 bg-gradient-to-r from-primary to-primary/80 text-white rounded-xl font-medium hover:scale-105 transition-transform"
                >
                  {t('store.featuredCard.ok')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FeaturedCard;
