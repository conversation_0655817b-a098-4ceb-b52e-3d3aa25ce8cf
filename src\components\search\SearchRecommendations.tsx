'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { 
  TrendingUp, 
  <PERSON>rk<PERSON>, 
  Heart, 
  Eye, 
  Star, 
  Users, 
  BookOpen,
  ArrowRight,
  Clock,
  Zap
} from 'lucide-react';

interface SearchRecommendationsProps {
  lang: string;
  searchQuery?: string;
  onSearchClick: (query: string) => void;
}

const SearchRecommendations: React.FC<SearchRecommendationsProps> = ({ 
  lang, 
  searchQuery = '', 
  onSearchClick 
}) => {
  const { t } = useTranslation(lang, 'translation');

  // Mock trending content
  const trendingCharacters = [
    {
      id: '1',
      name: 'Aria',
      description: 'Mystical elf with healing powers',
      followers: 2800,
      rating: 4.9,
      tags: ['fantasy', 'magic'],
      avatar: '/api/placeholder/48/48'
    },
    {
      id: '2', 
      name: '<PERSON><PERSON>',
      description: 'Cyberpunk hacker extraordinaire',
      followers: 2100,
      rating: 4.7,
      tags: ['cyberpunk', 'tech'],
      avatar: '/api/placeholder/48/48'
    },
    {
      id: '3',
      name: '<PERSON>',
      description: 'Charming vampire prince',
      followers: 3200,
      rating: 4.8,
      tags: ['vampire', 'romance'],
      avatar: '/api/placeholder/48/48'
    }
  ];

  const trendingStories = [
    {
      id: '1',
      title: 'The Last Sanctuary',
      description: 'A thrilling post-apocalyptic adventure',
      reads: 12000,
      rating: 4.8,
      tags: ['adventure', 'dystopian']
    },
    {
      id: '2',
      title: 'Moonlight Academy',
      description: 'Romance and mystery in a magical school',
      reads: 8500,
      rating: 4.6,
      tags: ['romance', 'magic']
    }
  ];

  const suggestedSearches = [
    {
      query: 'anime characters',
      category: 'Characters',
      icon: Users,
      trend: '+15%'
    },
    {
      query: 'fantasy stories',
      category: 'Stories',
      icon: BookOpen,
      trend: '+22%'
    },
    {
      query: 'romantic adventures',
      category: 'Stories',
      icon: Heart,
      trend: '+8%'
    },
    {
      query: 'cyberpunk themes',
      category: 'All',
      icon: Sparkles,
      trend: '+31%'
    }
  ];

  const relatedSearches = searchQuery ? [
    `${searchQuery} characters`,
    `${searchQuery} stories`,
    `${searchQuery} romance`,
    `${searchQuery} adventure`,
    `best ${searchQuery}`,
    `popular ${searchQuery}`
  ] : [];

  return (
    <div className="space-y-6">
      {/* Related Searches (if there's a search query) */}
      {searchQuery && relatedSearches.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4">
          <h3 className="text-sm font-semibold text-blue-900 dark:text-blue-300 mb-3 flex items-center gap-2">
            <Sparkles className="w-4 h-4" />
            {t('search.relatedSearches')}
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {relatedSearches.map((query, index) => (
              <button
                key={index}
                onClick={() => onSearchClick(query)}
                className="text-left p-2 bg-white dark:bg-gray-800 rounded-lg text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors border border-blue-200 dark:border-blue-800"
              >
                {query}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Trending Searches */}
      <div className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-4">
        <h3 className="text-sm font-semibold text-orange-900 dark:text-orange-300 mb-3 flex items-center gap-2">
          <TrendingUp className="w-4 h-4" />
          {t('search.trendingSearches')}
        </h3>
        <div className="space-y-2">
          {suggestedSearches.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => onSearchClick(suggestion.query)}
              className="w-full flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg hover:bg-orange-50 dark:hover:bg-orange-900/30 transition-all duration-200 border border-orange-200 dark:border-orange-800"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/50 rounded-lg flex items-center justify-center">
                  <suggestion.icon className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="text-left">
                  <div className="font-medium text-gray-900 dark:text-white text-sm">
                    {suggestion.query}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {suggestion.category}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-green-600 dark:text-green-400">
                  {suggestion.trend}
                </span>
                <ArrowRight className="w-4 h-4 text-gray-400" />
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Trending Characters */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-4">
        <h3 className="text-sm font-semibold text-purple-900 dark:text-purple-300 mb-3 flex items-center gap-2">
          <Zap className="w-4 h-4" />
          {t('search.trendingCharacters')}
        </h3>
        <div className="space-y-3">
          {trendingCharacters.map((character, index) => (
            <div
              key={character.id}
              className="flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg hover:bg-purple-50 dark:hover:bg-purple-900/30 transition-all duration-200 cursor-pointer border border-purple-200 dark:border-purple-800"
              onClick={() => onSearchClick(character.name)}
            >
              <div className="relative">
                <img
                  src={character.avatar}
                  alt={character.name}
                  className="w-10 h-10 rounded-full object-cover border-2 border-purple-200 dark:border-purple-700"
                />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-purple-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">#{index + 1}</span>
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 dark:text-white text-sm">
                  {character.name}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400 line-clamp-1">
                  {character.description}
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Heart className="w-3 h-3" />
                    {character.followers}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Star className="w-3 h-3" />
                    {character.rating}
                  </span>
                </div>
              </div>
              <ArrowRight className="w-4 h-4 text-gray-400" />
            </div>
          ))}
        </div>
      </div>

      {/* Trending Stories */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-4">
        <h3 className="text-sm font-semibold text-green-900 dark:text-green-300 mb-3 flex items-center gap-2">
          <BookOpen className="w-4 h-4" />
          {t('search.trendingStories')}
        </h3>
        <div className="space-y-3">
          {trendingStories.map((story, index) => (
            <div
              key={story.id}
              className="flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/30 transition-all duration-200 cursor-pointer border border-green-200 dark:border-green-800"
              onClick={() => onSearchClick(story.title)}
            >
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/50 rounded-lg flex items-center justify-center">
                <span className="text-green-600 dark:text-green-400 text-sm font-bold">#{index + 1}</span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 dark:text-white text-sm">
                  {story.title}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400 line-clamp-1">
                  {story.description}
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    {story.reads}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                    <Star className="w-3 h-3" />
                    {story.rating}
                  </span>
                </div>
              </div>
              <ArrowRight className="w-4 h-4 text-gray-400" />
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-xl p-4">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-300 mb-3 flex items-center gap-2">
          <Clock className="w-4 h-4" />
          {t('search.quickActions')}
        </h3>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => onSearchClick('new characters')}
            className="p-3 bg-white dark:bg-gray-800 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700"
          >
            {t('search.quickFilters.new')}
          </button>
          <button
            onClick={() => onSearchClick('popular this week')}
            className="p-3 bg-white dark:bg-gray-800 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700"
          >
            {t('search.quickFilters.popular')}
          </button>
          <button
            onClick={() => onSearchClick('highly rated')}
            className="p-3 bg-white dark:bg-gray-800 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700"
          >
            {t('search.quickFilters.topRated')}
          </button>
          <button
            onClick={() => onSearchClick('recently updated')}
            className="p-3 bg-white dark:bg-gray-800 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-700"
          >
            {t('search.quickFilters.updated')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SearchRecommendations; 