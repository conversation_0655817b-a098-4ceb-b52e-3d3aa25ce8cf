'use client';

import React, { useState } from 'react';
import { Compass, Users, Palette, BookOpen, Map, Search, Globe, Eye } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { EnhancedAchievement } from '@/types/achievements';
import TrophyCard from '../TrophyCard';

interface TrophyExplorationsTabProps {
  achievements: EnhancedAchievement[];
  lang: string;
  onAchievementClick: (achievement: EnhancedAchievement) => void;
  onClaimReward: (achievementId: string) => void;
}

const TrophyExplorationsTab: React.FC<TrophyExplorationsTabProps> = ({
  achievements,
  lang,
  onAchievementClick,
  onClaimReward
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [activeFilter, setActiveFilter] = useState<'all' | 'characters' | 'creation' | 'discovery' | 'collection'>('all');

  // Filter achievements for the Explorers player type (discovery & breadth)
  const explorerAchievements = achievements.filter(a => 
    a.category === 'collection' || a.category === 'creation'
  );

  const explorationCategories = [
    {
      id: 'all',
      label: t('trophies.filters.explorations.all'),
      icon: Compass,
      description: t('trophies.filters.explorations.allDesc')
    },
    {
      id: 'characters',
      label: t('trophies.filters.explorations.characters'),
      icon: Users,
      description: t('trophies.filters.explorations.charactersDesc')
    },
    {
      id: 'creation',
      label: t('trophies.filters.explorations.creation'),
      icon: Palette,
      description: t('trophies.filters.explorations.creationDesc')
    },
    {
      id: 'discovery',
      label: t('trophies.filters.explorations.discovery'),
      icon: Search,
      description: t('trophies.filters.explorations.discoveryDesc')
    },
    {
      id: 'collection',
      label: t('trophies.filters.explorations.collection'),
      icon: BookOpen,
      description: t('trophies.filters.explorations.collectionDesc')
    }
  ];

  // Mock categorization based on achievement content
  const categorizeAchievement = (achievement: EnhancedAchievement): string => {
    const name = achievement.name.toLowerCase();
    const desc = achievement.description.toLowerCase();
    
    if (name.includes('creator') || name.includes('storyteller') || desc.includes('create') || desc.includes('publish')) {
      return 'creation';
    }
    if (name.includes('explorer') || name.includes('character') || desc.includes('different characters') || desc.includes('chat with')) {
      return 'characters';
    }
    if (name.includes('memory') || name.includes('keeper') || desc.includes('collect') || desc.includes('capsule')) {
      return 'collection';
    }
    if (desc.includes('discover') || desc.includes('find') || desc.includes('explore')) {
      return 'discovery';
    }
    return 'discovery';
  };

  const filteredAchievements = activeFilter === 'all' 
    ? explorerAchievements
    : explorerAchievements.filter(a => categorizeAchievement(a) === activeFilter);

  const stats = {
    total: explorerAchievements.length,
    completed: explorerAchievements.filter(a => a.status === 'completed').length,
    inProgress: explorerAchievements.filter(a => a.status === 'inProgress').length,
    points: explorerAchievements.filter(a => a.status === 'completed').reduce((sum, a) => sum + a.points, 0)
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-emerald-500/10 via-teal-500/10 to-cyan-500/10 border border-emerald-200/20 dark:border-emerald-800/20 p-8">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-teal-500/5 to-cyan-500/5 backdrop-blur-3xl"></div>
        
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-emerald-500 to-cyan-500 text-white shadow-lg">
              <Compass className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-cyan-600 bg-clip-text text-transparent">
                {t('trophies.explorationsTab.title')}
              </h1>
              <p className="text-muted-foreground">{t('trophies.explorationsTab.subtitle')}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">{stats.completed}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.discovered')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">{stats.inProgress}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.exploring')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-cyan-600 dark:text-cyan-400">{stats.total}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.total')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{stats.points}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.points')}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2">
        {explorationCategories.map((category) => {
          const Icon = category.icon;
          const isActive = activeFilter === category.id;
          const categoryCount = category.id === 'all' 
            ? explorerAchievements.length 
            : explorerAchievements.filter(a => categorizeAchievement(a) === category.id).length;

          return (
            <button
              key={category.id}
              onClick={() => setActiveFilter(category.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                isActive
                  ? 'bg-gradient-to-r from-emerald-500 to-cyan-500 text-white shadow-lg'
                  : 'bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="font-medium">{category.label}</span>
              <span className={`text-xs px-2 py-0.5 rounded-full ${
                isActive 
                  ? 'bg-white/20 text-white' 
                  : 'bg-muted-foreground/20 text-muted-foreground'
              }`}>
                {categoryCount}
              </span>
            </button>
          );
        })}
      </div>

      {/* Exploration Progress */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="rounded-2xl bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 border border-emerald-200/20 dark:border-emerald-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-500 to-teal-500 text-white">
              <Users className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.explorationsTab.characterExplorer.title')}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t('trophies.explorationsTab.characterExplorer.description')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('trophies.explorationsTab.characterExplorer.charactersMet')}</span>
              <span className="font-medium">5 / 25</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="h-2 rounded-full bg-gradient-to-r from-emerald-500 to-teal-500" style={{ width: '20%' }}></div>
            </div>
          </div>
        </div>

        <div className="rounded-2xl bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 border border-teal-200/20 dark:border-teal-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-teal-500 to-cyan-500 text-white">
              <Palette className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.explorationsTab.creativeExplorer.title')}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t('trophies.explorationsTab.creativeExplorer.description')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('trophies.explorationsTab.creativeExplorer.createdCharacters')}</span>
              <span className="font-medium">1</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{t('trophies.explorationsTab.creativeExplorer.publishedStories')}</span>
              <span className="font-medium">0 / 5</span>
            </div>
          </div>
        </div>

        <div className="rounded-2xl bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 border border-cyan-200/20 dark:border-cyan-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-500 text-white">
              <BookOpen className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.explorationsTab.memoryCollector.title')}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t('trophies.explorationsTab.memoryCollector.description')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('trophies.explorationsTab.memoryCollector.memoryCapsules')}</span>
              <span className="font-medium">23 / 50</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="h-2 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500" style={{ width: '46%' }}></div>
            </div>
          </div>
        </div>
      </div>

      {/* Discovery Map */}
      <div className="rounded-2xl bg-card border border-border p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-500 to-cyan-500 text-white">
            <Map className="w-5 h-5" />
          </div>
          <h2 className="text-xl font-bold">{t('trophies.explorationsTab.discoveryMap.title')}</h2>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 rounded-xl bg-gradient-to-br from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 border border-emerald-200/20 dark:border-emerald-800/20">
            <div className="p-3 rounded-lg bg-gradient-to-br from-emerald-500 to-teal-500 text-white mx-auto mb-3 w-fit">
              <Globe className="w-6 h-6" />
            </div>
            <div className="text-lg font-bold">12</div>
            <div className="text-sm text-muted-foreground">{t('trophies.explorationsTab.discoveryMap.worldsExplored')}</div>
          </div>

          <div className="text-center p-4 rounded-xl bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 border border-teal-200/20 dark:border-teal-800/20">
            <div className="p-3 rounded-lg bg-gradient-to-br from-teal-500 to-cyan-500 text-white mx-auto mb-3 w-fit">
              <Eye className="w-6 h-6" />
            </div>
            <div className="text-lg font-bold">47</div>
            <div className="text-sm text-muted-foreground">{t('trophies.explorationsTab.discoveryMap.secretsFound')}</div>
          </div>

          <div className="text-center p-4 rounded-xl bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 border border-cyan-200/20 dark:border-cyan-800/20">
            <div className="p-3 rounded-lg bg-gradient-to-br from-cyan-500 to-blue-500 text-white mx-auto mb-3 w-fit">
              <BookOpen className="w-6 h-6" />
            </div>
            <div className="text-lg font-bold">156</div>
            <div className="text-sm text-muted-foreground">{t('trophies.explorationsTab.discoveryMap.storiesDiscovered')}</div>
          </div>

          <div className="text-center p-4 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200/20 dark:border-blue-800/20">
            <div className="p-3 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-500 text-white mx-auto mb-3 w-fit">
              <Users className="w-6 h-6" />
            </div>
            <div className="text-lg font-bold">89</div>
            <div className="text-sm text-muted-foreground">{t('trophies.explorationsTab.discoveryMap.charactersMet')}</div>
          </div>
        </div>
      </div>

      {/* Achievements Grid */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {activeFilter === 'all' ? t('trophies.common.allExplorations') : explorationCategories.find(c => c.id === activeFilter)?.label}
          </h2>
          <div className="text-sm text-muted-foreground">
            {filteredAchievements.length} {t('trophies.common.achievements')}
          </div>
        </div>

        {filteredAchievements.length > 0 ? (
          <>
            {/* Mobile Layout */}
            <div className="block sm:hidden">
              <div className="columns-2 gap-4" style={{ columnFill: 'balance' }}>
                {filteredAchievements.map((achievement) => (
                  <div key={achievement.id} className="break-inside-avoid mb-4">
                    <TrophyCard
                      achievement={achievement}
                      onClick={() => onAchievementClick(achievement)}
                      onClaimReward={onClaimReward}
                      lang={lang}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Desktop Layout */}
            <div className="hidden sm:block">
              <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-5 lg:gap-6">
                {filteredAchievements.map((achievement) => (
                  <TrophyCard
                    key={achievement.id}
                    achievement={achievement}
                    onClick={() => onAchievementClick(achievement)}
                    onClaimReward={onClaimReward}
                    lang={lang}
                  />
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <div className="p-4 rounded-2xl bg-muted/50 inline-block mb-4">
              <Compass className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">{t('trophies.emptyStates.explorations.title')}</h3>
            <p className="text-muted-foreground">
              {t('trophies.emptyStates.explorations.description')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrophyExplorationsTab;
