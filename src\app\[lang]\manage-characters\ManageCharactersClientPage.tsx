'use client';

import React, { useState, useMemo } from 'react';
import { useTranslation } from '@/app/i18n/client';
import MainAppLayout from '@/components/MainAppLayout';
import { Users, TrendingUp, Eye, Plus, Search, ChevronDown, User, Settings, Flame, X, Tag } from 'lucide-react';
import Link from 'next/link';
import CharacterManageRole from '@/components/CharacterManageRole';
import { getManagedCharacters, type ManagedCharacter } from '@/lib/mock-data';

interface ManageCharactersClientPageProps {
  lang: string;
}

const ManageCharactersClientPage: React.FC<ManageCharactersClientPageProps> = ({ lang }) => {
  const { t: _ } = useTranslation(lang, 'translation');
  const [managedCharacters] = useState<ManagedCharacter[]>(getManagedCharacters());
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'followers' | 'heatScore' | 'trendPercentage' | 'lastEditedAt'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filterStatus, setFilterStatus] = useState<'all' | 'verified' | 'unverified'>('all');
  const [filterGender, setFilterGender] = useState<'all' | 'male' | 'female' | 'non-binary' | 'other'>('all');
  const [filterPOV, setFilterPOV] = useState<'all' | 'first-person' | 'second-person' | 'third-person'>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');



  // Filter and sort character data
  const filteredAndSortedCharacters = useMemo(() => {
    let filtered = managedCharacters.filter(character => {
      const matchesSearch = character.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (character.description?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);
      const matchesVerified = filterStatus === 'all' ||
                           (filterStatus === 'verified' && character.isVerified) ||
                           (filterStatus === 'unverified' && !character.isVerified);
      const matchesGender = filterGender === 'all' || character.gender === filterGender;
      const matchesPOV = filterPOV === 'all' || character.pov === filterPOV;
      const matchesTags = selectedTags.length === 0 ||
                         selectedTags.every(tag => character.tags.includes(tag));

      return matchesSearch && matchesVerified && matchesGender && matchesPOV && matchesTags;
    });

    filtered.sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      if (sortBy === 'lastEditedAt') {
        // Convert relative time to comparable number for sorting
        const getTimeValue = (timeStr: string) => {
          if (timeStr.includes('day')) return parseInt(timeStr);
          if (timeStr.includes('week')) return parseInt(timeStr) * 7;
          return 0;
        };
        aValue = getTimeValue(aValue);
        bValue = getTimeValue(bValue);
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [managedCharacters, searchTerm, sortBy, sortOrder, filterStatus, filterGender, filterPOV, selectedTags]);

  // Calculate overall statistics
  const totalCharacters = filteredAndSortedCharacters.length;
  const totalFollowers = filteredAndSortedCharacters.reduce((sum, char) => sum + char.followers, 0);
  const totalHeat = filteredAndSortedCharacters.reduce((sum, char) => sum + char.heatScore, 0);

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const addTag = () => {
    if (newTag.trim() && !selectedTags.includes(newTag.trim())) {
      setSelectedTags([...selectedTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setSelectedTags(selectedTags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addTag();
    }
  };

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4">

          {/* Sticky Header - Statistics, Filters, and Table Header */}
          <div className="sticky top-12 lg:top-16 z-10 bg-gray-50 dark:bg-gray-900">
            {/* Statistics Row */}
            <div className="grid grid-cols-3 divide-x divide-gray-200 dark:divide-gray-700 py-2">
              {/* Total Characters */}
              <div className="flex items-center gap-3 px-4 lg:px-6 py-1.5">
                <div className="p-1.5 lg:p-3 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                  <Users className="w-4 h-4 lg:w-6 lg:h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400">
                    <span className="hidden sm:inline">Total </span>Characters
                  </p>
                  <p className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white">{totalCharacters}</p>
                </div>
              </div>

              {/* Total Followers */}
              <div className="flex items-center gap-3 px-4 lg:px-6 py-1.5">
                <div className="p-1.5 lg:p-3 bg-green-100 dark:bg-green-900/50 rounded-lg">
                  <Eye className="w-4 h-4 lg:w-6 lg:h-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400">
                    <span className="hidden sm:inline">Total </span>Followers
                  </p>
                  <p className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white">
                    <span className="sm:hidden">{Math.round(totalFollowers / 1000)}k</span>
                    <span className="hidden sm:inline">{totalFollowers.toLocaleString()}</span>
                  </p>
                </div>
              </div>

              {/* Total Heat */}
              <div className="flex items-center gap-3 px-4 lg:px-6 py-1.5">
                <div className="p-1.5 lg:p-3 bg-orange-100 dark:bg-orange-900/50 rounded-lg">
                  <TrendingUp className="w-4 h-4 lg:w-6 lg:h-6 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <p className="text-xs lg:text-sm font-medium text-gray-600 dark:text-gray-400">
                    <span className="hidden sm:inline">Total </span>Heat
                  </p>
                  <p className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white">
                    <span className="sm:hidden">{Math.round(totalHeat / 1000)}k</span>
                    <span className="hidden sm:inline">{totalHeat.toLocaleString()}</span>
                  </p>
                </div>
              </div>
            </div>

            {/* Filters Row */}
            <div className="mt-2 space-y-3">
              {/* Search Box with Create New Button */}
              <div className="flex gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                  <input
                    type="text"
                    placeholder="Search characters..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                </div>
                <Link
                  href={`/${lang}/create-character`}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors whitespace-nowrap"
                >
                  <Plus size={16} />
                  Create New
                </Link>
              </div>

              {/* Three Filters in Same Row */}
              <div className="grid grid-cols-3 gap-3">
                {/* Verified Filter */}
                <div className="relative">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value as any)}
                    className="appearance-none w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-8 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
                  >
                    <option value="all">By Verified</option>
                    <option value="verified">Verified</option>
                    <option value="unverified">Unverified</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={14} />
                </div>

                {/* Gender Filter */}
                <div className="relative">
                  <select
                    value={filterGender}
                    onChange={(e) => setFilterGender(e.target.value as any)}
                    className="appearance-none w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-8 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
                  >
                    <option value="all">By Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="non-binary">Non-binary</option>
                    <option value="other">Other</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={14} />
                </div>

                {/* POV Filter */}
                <div className="relative">
                  <select
                    value={filterPOV}
                    onChange={(e) => setFilterPOV(e.target.value as any)}
                    className="appearance-none w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 pr-8 text-gray-900 dark:text-white focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm"
                  >
                    <option value="all">By POV</option>
                    <option value="first-person">First Person</option>
                    <option value="second-person">Second Person</option>
                    <option value="third-person">Third Person</option>
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" size={14} />
                </div>
              </div>

              {/* Add Tag Input */}
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                  <input
                    type="text"
                    placeholder="Add tag to filter..."
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyDown={handleKeyPress}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  />
                </div>
                <button
                  onClick={addTag}
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                >
                  Add
                </button>
              </div>

              {/* Selected Tags */}
              {selectedTags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {selectedTags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-800 dark:text-indigo-200 text-sm rounded-full"
                    >
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        className="hover:text-indigo-600 dark:hover:text-indigo-300"
                      >
                        <X size={14} />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>

            {/* Table Header */}
            <div className="bg-white dark:bg-gray-800 rounded-t-lg mt-2 overflow-x-auto">
              <table className="w-full table-fixed">
                <colgroup>
                  {/* Name column - always visible, compact layout on mobile */}
                  <col className="w-[100px] sm:w-[140px] lg:w-[160px]" />
                  {/* Actions column - always visible, compact on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Trend column - visible on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Heat column - visible on mobile */}
                  <col className="w-[50px] sm:w-[70px] lg:w-[100px]" />
                  {/* Fans column - visible on mobile */}
                  <col className="w-[50px] sm:w-[70px] lg:w-[100px]" />
                </colgroup>
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    {/* Name column - always visible */}
                    <th
                      className="px-2 sm:px-3 lg:px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center gap-1">
                        <User size={14} />
                        <span className="hidden md:inline">Name</span>
                        {sortBy === 'name' && (
                          <span className="text-indigo-500 text-xs">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>

                    {/* Actions column - always visible */}
                    <th className="px-1 sm:px-2 lg:px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <div className="flex items-center justify-center gap-1">
                        <Settings size={14} />
                        <span className="hidden md:inline">Actions</span>
                      </div>
                    </th>

                    {/* Trend column - visible on mobile */}
                    <th
                      className="px-1 sm:px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => handleSort('trendPercentage')}
                    >
                      <div className="flex items-center justify-center gap-1">
                        <TrendingUp size={14} />
                        <span className="hidden md:inline">Trend</span>
                        {sortBy === 'trendPercentage' && (
                          <span className="text-indigo-500 text-xs">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>

                    {/* Heat column - visible on mobile, using flame icon */}
                    <th
                      className="px-1 sm:px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => handleSort('heatScore')}
                    >
                      <div className="flex items-center justify-center gap-1">
                        <Flame size={14} />
                        <span className="hidden md:inline">Heat</span>
                        {sortBy === 'heatScore' && (
                          <span className="text-indigo-500 text-xs">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>

                    {/* Fans column - visible on mobile */}
                    <th
                      className="px-1 sm:px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      onClick={() => handleSort('followers')}
                    >
                      <div className="flex items-center justify-center gap-1">
                        <Users size={14} />
                        <span className="hidden md:inline">Fans</span>
                        {sortBy === 'followers' && (
                          <span className="text-indigo-500 text-xs">
                            {sortOrder === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>
                  </tr>
                </thead>
              </table>
            </div>
          </div>

          {/* Scrollable Table Body */}
          <div className="bg-white dark:bg-gray-800 rounded-b-lg shadow-sm max-h-[calc(100vh-300px)] overflow-y-auto">
            <div className="overflow-x-auto">
              <table className="w-full table-fixed">
                <colgroup>
                  {/* Name column - always visible, compact layout on mobile */}
                  <col className="w-[100px] sm:w-[140px] lg:w-[160px]" />
                  {/* Actions column - always visible, compact on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Trend column - visible on mobile */}
                  <col className="w-[60px] sm:w-[80px] lg:w-[100px]" />
                  {/* Heat column - visible on mobile */}
                  <col className="w-[50px] sm:w-[70px] lg:w-[100px]" />
                  {/* Fans column - visible on mobile */}
                  <col className="w-[50px] sm:w-[70px] lg:w-[100px]" />
                </colgroup>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredAndSortedCharacters.map((character) => (
                    <CharacterManageRole
                      key={character.id}
                      lang={lang}
                      character={character}
                    />
                  ))}
                </tbody>
              </table>
            </div>

            {filteredAndSortedCharacters.length === 0 && managedCharacters.length > 0 && (
              <div className="text-center py-12">
                <Search className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No matching characters</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            )}

            {managedCharacters.length === 0 && (
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No characters</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Get started by creating your first AI character.
                </p>
                <div className="mt-6">
                  <Link
                    href={`/${lang}/create-character`}
                    className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                  >
                    <Plus size={16} />
                    Create Character
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
};

export default ManageCharactersClientPage;
