'use client';

import React, { useState } from 'react';
import { Target, MessageCircle, Heart, Flame, Clock, Star, Award, Zap, Crown } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { EnhancedAchievement } from '@/types/achievements';
import TrophyCard from '../TrophyCard';

interface TrophyAchievementsTabProps {
  achievements: EnhancedAchievement[];
  lang: string;
  onAchievementClick: (achievement: EnhancedAchievement) => void;
  onClaimReward: (achievementId: string) => void;
}

const TrophyAchievementsTab: React.FC<TrophyAchievementsTabProps> = ({
  achievements,
  lang,
  onAchievementClick,
  onClaimReward
}) => {
  const { t } = useTranslation(lang, 'translation');
  const [activeFilter, setActiveFilter] = useState<'all' | 'conversation' | 'depth' | 'quality' | 'consistency'>('all');

  // Filter achievements for the Achievers player type (chat depth & quality)
  const achieverAchievements = achievements.filter(a => 
    a.category === 'interaction' || a.category === 'beginner'
  );

  const achievementCategories = [
    {
      id: 'all',
      label: t('trophies.filters.achievements.all'),
      icon: Target,
      description: t('trophies.filters.achievements.allDesc')
    },
    {
      id: 'conversation',
      label: t('trophies.filters.achievements.conversation'),
      icon: MessageCircle,
      description: t('trophies.filters.achievements.conversationDesc')
    },
    {
      id: 'depth',
      label: t('trophies.filters.achievements.depth'),
      icon: Heart,
      description: t('trophies.filters.achievements.depthDesc')
    },
    {
      id: 'quality',
      label: t('trophies.filters.achievements.quality'),
      icon: Star,
      description: t('trophies.filters.achievements.qualityDesc')
    },
    {
      id: 'consistency',
      label: t('trophies.filters.achievements.consistency'),
      icon: Flame,
      description: t('trophies.filters.achievements.consistencyDesc')
    }
  ];

  // Mock categorization based on achievement content
  const categorizeAchievement = (achievement: EnhancedAchievement): string => {
    const name = achievement.name.toLowerCase();
    const desc = achievement.description.toLowerCase();
    
    if (name.includes('streak') || desc.includes('consecutive') || desc.includes('daily')) {
      return 'consistency';
    }
    if (name.includes('memory') || name.includes('bond') || desc.includes('deep') || desc.includes('emotional')) {
      return 'depth';
    }
    if (name.includes('conversation') || name.includes('message') || desc.includes('chat')) {
      return 'conversation';
    }
    if (achievement.rarity === 'gold' || achievement.rarity === 'platinum' || achievement.rarity === 'diamond') {
      return 'quality';
    }
    return 'conversation';
  };

  const filteredAchievements = activeFilter === 'all' 
    ? achieverAchievements
    : achieverAchievements.filter(a => categorizeAchievement(a) === activeFilter);

  const stats = {
    total: achieverAchievements.length,
    completed: achieverAchievements.filter(a => a.status === 'completed').length,
    inProgress: achieverAchievements.filter(a => a.status === 'inProgress').length,
    points: achieverAchievements.filter(a => a.status === 'completed').reduce((sum, a) => sum + a.points, 0)
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-amber-500/10 via-orange-500/10 to-red-500/10 border border-amber-200/20 dark:border-amber-800/20 p-8">
        <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 via-orange-500/5 to-red-500/5 backdrop-blur-3xl"></div>
        
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-6">
            <div className="p-4 rounded-2xl bg-gradient-to-br from-amber-500 to-red-500 text-white shadow-lg">
              <Target className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-red-600 bg-clip-text text-transparent">
                {t('trophies.achievementsTab.title')}
              </h1>
              <p className="text-muted-foreground">{t('trophies.achievementsTab.subtitle')}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">{stats.completed}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.completed')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{stats.inProgress}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.inProgress')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">{stats.total}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.total')}</div>
            </div>
            <div className="text-center p-4 rounded-xl bg-white/50 dark:bg-black/20 backdrop-blur-sm">
              <div className="text-2xl font-bold text-pink-600 dark:text-pink-400">{stats.points}</div>
              <div className="text-sm text-muted-foreground">{t('trophies.common.points')}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2">
        {achievementCategories.map((category) => {
          const Icon = category.icon;
          const isActive = activeFilter === category.id;
          const categoryCount = category.id === 'all' 
            ? achieverAchievements.length 
            : achieverAchievements.filter(a => categorizeAchievement(a) === category.id).length;

          return (
            <button
              key={category.id}
              onClick={() => setActiveFilter(category.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                isActive
                  ? 'bg-gradient-to-r from-amber-500 to-red-500 text-white shadow-lg'
                  : 'bg-muted hover:bg-muted/80 text-muted-foreground hover:text-foreground'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="font-medium">{category.label}</span>
              <span className={`text-xs px-2 py-0.5 rounded-full ${
                isActive 
                  ? 'bg-white/20 text-white' 
                  : 'bg-muted-foreground/20 text-muted-foreground'
              }`}>
                {categoryCount}
              </span>
            </button>
          );
        })}
      </div>

      {/* Achievement Milestones */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="rounded-2xl bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200/20 dark:border-amber-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-amber-500 to-orange-500 text-white">
              <MessageCircle className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.achievementsTab.conversationMaster.title')}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t('trophies.achievementsTab.conversationMaster.description')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('trophies.achievementsTab.conversationMaster.messagesSent')}</span>
              <span className="font-medium">2,847</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{t('trophies.achievementsTab.conversationMaster.conversations')}</span>
              <span className="font-medium">156</span>
            </div>
          </div>
        </div>

        <div className="rounded-2xl bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 border border-rose-200/20 dark:border-rose-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-rose-500 to-pink-500 text-white">
              <Heart className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.achievementsTab.emotionalDepth.title')}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t('trophies.achievementsTab.emotionalDepth.description')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('trophies.achievementsTab.emotionalDepth.memoryCapsules')}</span>
              <span className="font-medium">23</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{t('trophies.achievementsTab.emotionalDepth.bondLevel')}</span>
              <span className="font-medium">Level 8</span>
            </div>
          </div>
        </div>

        <div className="rounded-2xl bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200/20 dark:border-orange-800/20 p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-orange-500 to-red-500 text-white">
              <Flame className="w-5 h-5" />
            </div>
            <h3 className="font-bold">{t('trophies.achievementsTab.consistencyStreak.title')}</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-3">
            {t('trophies.achievementsTab.consistencyStreak.description')}
          </p>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{t('trophies.achievementsTab.consistencyStreak.currentStreak')}</span>
              <span className="font-medium">12 days</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>{t('trophies.achievementsTab.consistencyStreak.bestStreak')}</span>
              <span className="font-medium">28 days</span>
            </div>
          </div>
        </div>
      </div>

      {/* Achievements Grid */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold">
            {activeFilter === 'all' ? t('trophies.common.allAchievements') : achievementCategories.find(c => c.id === activeFilter)?.label}
          </h2>
          <div className="text-sm text-muted-foreground">
            {filteredAchievements.length} {t('trophies.common.achievements')}
          </div>
        </div>

        {filteredAchievements.length > 0 ? (
          <>
            {/* Mobile Layout */}
            <div className="block sm:hidden">
              <div className="columns-2 gap-4" style={{ columnFill: 'balance' }}>
                {filteredAchievements.map((achievement) => (
                  <div key={achievement.id} className="break-inside-avoid mb-4">
                    <TrophyCard
                      achievement={achievement}
                      onClick={() => onAchievementClick(achievement)}
                      onClaimReward={onClaimReward}
                      lang={lang}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Desktop Layout */}
            <div className="hidden sm:block">
              <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 sm:gap-5 lg:gap-6">
                {filteredAchievements.map((achievement) => (
                  <TrophyCard
                    key={achievement.id}
                    achievement={achievement}
                    onClick={() => onAchievementClick(achievement)}
                    onClaimReward={onClaimReward}
                    lang={lang}
                  />
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <div className="p-4 rounded-2xl bg-muted/50 inline-block mb-4">
              <Target className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">{t('trophies.emptyStates.achievements.title')}</h3>
            <p className="text-muted-foreground">
              {t('trophies.emptyStates.achievements.description')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrophyAchievementsTab;
