'use client';

import React from 'react';
import { MessageCircle, BookOpen } from 'lucide-react';
import type { CharacterFormData } from '@/types/character-creation';

interface BehaviorStepProps {
  formData: CharacterFormData;
  setFormData: (data: CharacterFormData | ((prev: CharacterFormData) => CharacterFormData)) => void;
}

const BehaviorStep: React.FC<BehaviorStepProps> = ({
  formData,
  setFormData,
}) => {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-bold text-purple-800 dark:text-purple-200 mb-2">Step 2: Behavior</h3>
        <p className="text-muted-foreground text-sm">Define how your character acts and what they know</p>
      </div>

      {/* Greeting Message Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-3 flex items-center gap-2">
          <MessageCircle size={18} />
          Greeting Message
        </h4>
        <textarea
          value={formData.greetingMessage}
          onChange={(e) => setFormData(prev => ({ ...prev, greetingMessage: e.target.value }))}
          rows={3}
          className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
          placeholder="How does your character greet people? This will be their first message..."
          maxLength={300}
        />
        <div className="flex justify-end items-center mt-2">
          <span className={`character-counter ${formData.greetingMessage.length > 50 ? '' : 'over-limit'}`}>
            {formData.greetingMessage.length}/300
          </span>
        </div>
      </div>

      {/* World Setting Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-3 flex items-center gap-2">
          <BookOpen size={18} />
          World Setting
        </h4>
        <textarea
          value={formData.setting}
          onChange={(e) => setFormData(prev => ({ ...prev, setting: e.target.value }))}
          rows={4}
          className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
          placeholder="Describe the world or environment where your character exists..."
          maxLength={800}
        />
        <div className="flex justify-end items-center mt-2">
          <span className={`character-counter ${formData.setting.length > 100 ? '' : 'over-limit'}`}>
            {formData.setting.length}/800
          </span>
        </div>
      </div>

      {/* Background Story Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-3">Background Story (Optional)</h4>
        <textarea
          value={formData.backgroundStory}
          onChange={(e) => setFormData(prev => ({ ...prev, backgroundStory: e.target.value }))}
          rows={4}
          className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
          placeholder="Tell us more about your character's history and background..."
          maxLength={1000}
        />
        <div className="flex justify-end items-center mt-2">
          <span className={`character-counter ${formData.backgroundStory.length > 200 ? '' : 'over-limit'}`}>
            {formData.backgroundStory.length}/1000
          </span>
        </div>
      </div>



      {/* Content Tags Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-3">Content Tags</h4>
        <input
          type="text"
          value={formData.interactionStyleTags}
          onChange={(e) => setFormData(prev => ({ ...prev, interactionStyleTags: e.target.value }))}
          className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
          placeholder="Add tags separated by commas (e.g., fantasy, adventure, friendly)"
          maxLength={200}
        />
        <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
          🏷️ Help users discover your character with relevant tags
        </p>
      </div>

      {/* Visibility Settings */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-3">Visibility</h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          {[
            { value: 'public', label: 'Public', desc: 'Everyone can see and chat' },
            { value: 'unlisted', label: 'Unlisted', desc: 'Only with direct link' },
            { value: 'private', label: 'Private', desc: 'Only you can access' }
          ].map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, visibility: option.value as any }))}
              className={`p-3 rounded-lg border-2 transition-all text-left ${
                formData.visibility === option.value
                  ? 'border-purple-500 bg-purple-100 dark:bg-purple-900/50'
                  : 'border-purple-200 dark:border-purple-700 hover:border-purple-400 dark:hover:border-purple-500'
              }`}
            >
              <div className="font-medium text-purple-800 dark:text-purple-200">{option.label}</div>
              <div className="text-xs text-purple-600 dark:text-purple-400">{option.desc}</div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BehaviorStep;
