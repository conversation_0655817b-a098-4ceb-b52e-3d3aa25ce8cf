'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Heart,
  Share2,
  Star,
  ThumbsUp,
  MessageCircle,
  BookOpen,
  Users,
  CheckCircle,
  Lock,
  RotateCcw,
  Trophy,
  Gift,
  Sparkles,
  ArrowLeft
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import type { StoryDetailData } from '@/types/story';
import BottomNavBar from '@/components/BottomNavBar';
import { useTranslation } from '@/app/i18n/client';


interface StoryDetailClientPageProps {
  storyData: StoryDetailData;
  lang: string;
}

const StoryDetailClientPage: React.FC<StoryDetailClientPageProps> = ({ storyData, lang }) => {
  const [isLiked, setIsLiked] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [selectedChoice, setSelectedChoice] = useState<string | null>(null);
  const router = useRouter();
  const { t } = useTranslation(lang, 'translation');

  const { story, chapters, choices, rewards, achievements, characters } = storyData;

  const handleLike = () => {
    setIsLiked(!isLiked);
  };

  const handleFavorite = () => {
    setIsFavorited(!isFavorited);
  };

  const handleChoiceSelect = (choiceId: string) => {
    setSelectedChoice(choiceId);
  };

  const getDifficultyStars = (difficulty: string) => {
    const starCount = difficulty === 'easy' ? 1 : difficulty === 'normal' ? 2 : 3;
    return '⭐'.repeat(starCount);
  };

  const getChapterIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5" />;
      case 'current':
        return <RotateCcw className="w-5 h-5 animate-spin" />;
      case 'locked':
        return <Lock className="w-5 h-5" />;
      default:
        return <BookOpen className="w-5 h-5" />;
    }
  };

  const getChapterStatusClass = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-md';
      case 'current':
        return 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-md chapter-current';
      case 'locked':
        return 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500 border-2 border-dashed border-gray-300 dark:border-gray-600';
      default:
        return 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border border-gray-200 dark:border-gray-700';
    }
  };

  const getCharacterRoleText = (role: string) => {
    switch (role) {
      case 'main':
        return t('story.characters.mainRole');
      case 'supporting':
        return t('story.characters.supportingRole');
      default:
        return t('story.characters.minorRole');
    }
  };

  const getCharacterDescription = (role: string) => {
    switch (role) {
      case 'main':
        return t('story.characters.mainDescription');
      case 'supporting':
        return t('story.characters.supportingDescription');
      default:
        return '';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-gray-900 dark:via-slate-800 dark:to-gray-900">
      <main className="max-w-7xl mx-auto px-4 py-8 pb-24 sm:px-6 lg:px-8">
        {/* Back button */}
        <div className="mb-6">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <ArrowLeft size={20} />
            <span>{t('story.header.back')}</span>
          </button>
        </div>

        {/* Responsive layout */}
        <div className="lg:grid lg:grid-cols-3 lg:gap-8 space-y-6 lg:space-y-0">

          {/* Main content - 2 columns on desktop, full width on mobile */}
          <div className="lg:col-span-2 space-y-6">
            {/* Story cover and progress */}
            <div className="relative overflow-hidden rounded-2xl shadow-xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50">
              <div className="relative h-80 sm:h-96 md:h-[28rem]">
                <Image
                  src={story.coverImage}
                  alt={story.title}
                  fill
                  className="object-cover"
                  unoptimized
                />
                <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-gray-900/30 to-transparent"></div>
                
                {/* Story title and difficulty */}
                <div className="absolute bottom-0 left-0 p-6 sm:p-8">
                  <div className="flex items-center gap-3 mb-3">
                    <h1 className="text-3xl font-bold text-white drop-shadow-2xl sm:text-4xl md:text-5xl">
                      {story.title}
                    </h1>
                  </div>
                  <p className="text-sm text-white/90 sm:text-base drop-shadow-lg">
                    {t('story.info.starring')} <Link href={`/${lang}/character/${story.characterId}`} className="hover:underline font-medium text-pink-300 hover:text-pink-200 transition-colors">
                      {characters.find(c => c.characterId === story.characterId)?.name}
                    </Link> {t('story.info.starring2')}
                    · {t('story.info.estimatedDuration')}{story.estimatedDuration}
                  </p>
                  
                  {/* Story progress bar */}
                  <div className="mt-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm text-white/90 font-medium drop-shadow">{t('story.info.progress')}</span>
                      <span className="text-sm text-white font-bold drop-shadow">
                        {story.progress.completedChapters.length + (story.progress.currentChapter ? 1 : 0)}/{story.progress.totalChapters} {t('story.info.chapters')}
                      </span>
                    </div>
                    <div className="h-4 rounded-full bg-white/20 backdrop-blur-sm overflow-hidden shadow-inner">
                      <div
                        className="h-full bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 transition-all duration-700 ease-out shadow-lg"
                        style={{
                          width: `${((story.progress.completedChapters.length) / story.progress.totalChapters) * 100}%`
                        }}
                      ></div>
                    </div>
                  </div>
                </div>

                {/* Continue Story Button - positioned in bottom right */}
                <div className="absolute bottom-6 right-6">
                  <Link
                    href={`/${lang}/chats/${story.characterId}`}
                    className="bg-white/95 backdrop-blur-sm text-blue-600 font-bold py-3 px-6 rounded-xl hover:bg-white transition-all duration-300 shadow-xl hover:shadow-2xl flex items-center gap-2 transform hover:scale-105 border border-white/20"
                  >
                    <BookOpen className="w-5 h-5" />
                    {t('story.header.continueStory')}
                  </Link>
                </div>
              </div>
            </div>

            {/* Story info and actions */}
            <div className="rounded-2xl bg-white/90 dark:bg-gray-800/90 p-6 shadow-xl backdrop-blur-sm sm:p-8 border border-gray-200/50 dark:border-gray-700/50">
              <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {t('story.info.creator')} <Link href="#" className="font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:underline transition-colors">
                      {story.creatorName}
                    </Link>
                  </p>
                  <div className="mt-2 flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <span className="flex items-center gap-1.5 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                      <MessageCircle className="w-4 h-4 text-blue-500" />
                      {story.stats.plays.toLocaleString()}{t('story.info.plays')}
                    </span>
                    <span className="flex items-center gap-1.5 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                      <ThumbsUp className="w-4 h-4 text-green-500" />
                      {story.stats.likes}{t('story.info.likes')}
                    </span>
                    <span className="flex items-center gap-1.5 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                      <Star className="w-4 h-4 text-yellow-500" />
                      {story.stats.rating}{t('story.info.rating')}
                    </span>
                  </div>
                </div>
                
                <div className="flex shrink-0 gap-3">
                  <button
                    onClick={handleLike}
                    className={`flex h-11 items-center justify-center gap-2 rounded-lg px-4 text-sm font-semibold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 ${
                      isLiked
                        ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white'
                        : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'
                    }`}
                  >
                    <ThumbsUp className="w-4 h-4" />
                    <span className="hidden sm:inline">{t('story.actions.like')}</span>
                  </button>
                  <button className="flex h-11 items-center justify-center gap-2 rounded-lg bg-white dark:bg-gray-700 px-4 text-sm font-semibold text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 border border-gray-200 dark:border-gray-600">
                    <Share2 className="w-4 h-4" />
                    <span className="hidden sm:inline">{t('story.actions.share')}</span>
                  </button>
                  <button
                    onClick={handleFavorite}
                    className={`flex h-11 items-center justify-center gap-2 rounded-xl px-4 text-sm font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 ${
                      isFavorited
                        ? 'bg-gradient-to-r from-yellow-400 to-orange-400 text-white'
                        : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'
                    }`}
                  >
                    <Star className="w-4 h-4" />
                    <span className="hidden sm:inline">{t('story.actions.favorite')}</span>
                  </button>
                </div>
              </div>

              <hr className="my-8 border-gray-200 dark:border-gray-700" />

              {/* Story description */}
              <div>
                <h2 className="mb-4 text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <BookOpen className="w-5 h-5 text-purple-500" />
                  {t('story.sections.description')}
                </h2>
                <p className="text-base leading-relaxed text-gray-700 dark:text-gray-300 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-4 rounded-xl border border-purple-100 dark:border-purple-800">
                  {story.description}
                </p>
              </div>

              {/* Story tags */}
              <div className="mt-8">
                <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <Sparkles className="w-5 h-5 text-pink-500" />
                  {t('story.sections.tags')}
                </h3>
                <div className="flex gap-3 flex-wrap">
                  {story.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-800 dark:to-indigo-800 text-blue-700 dark:text-blue-200 px-4 py-2 rounded-full text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>

              {/* Opening message */}
              <div className="mt-8">
                <h2 className="mb-4 text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <MessageCircle className="w-5 h-5 text-indigo-500" />
                  {t('story.sections.openingMessage')}
                </h2>
                <blockquote className="border-l-4 border-blue-500 bg-gradient-to-r from-blue-50 via-indigo-50 to-slate-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-slate-900/30 p-6 italic text-gray-700 dark:text-gray-300 rounded-r-xl shadow-lg">
                  {story.openingMessage}
                </blockquote>
              </div>
            </div>

            {/* Chapter progress */}
            <div className="rounded-2xl bg-white/90 dark:bg-gray-800/90 p-6 shadow-xl backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50">
              <h2 className="mb-6 text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <BookOpen className="w-6 h-6 text-blue-500" />
                {t('story.sections.chapterProgress')}
              </h2>

              <div className="space-y-4">
                {chapters.map((chapter) => (
                  <div
                    key={chapter.id}
                    className={`p-5 rounded-xl flex items-center gap-4 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-xl ${getChapterStatusClass(chapter.status)} ${
                      chapter.status !== 'locked' ? 'hover:scale-[1.02] hover:-translate-y-1' : 'opacity-60'
                    }`}
                    onClick={() => {
                      if (chapter.status !== 'locked') {
                        console.log(t('story.chapters.enterChapter'), chapter.title);
                      }
                    }}
                  >
                    <div className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center font-bold text-lg shadow-inner">
                      {chapter.id}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-bold text-lg mb-1">{chapter.title}</h3>
                      <p className="text-sm opacity-90 mb-1">{chapter.description}</p>
                      {chapter.unlockCondition && (
                        <p className="text-xs opacity-80 bg-white/20 px-2 py-1 rounded-full inline-block">{chapter.unlockCondition.description}</p>
                      )}
                    </div>
                    <div className="w-8 h-8 flex items-center justify-center">
                      {getChapterIcon(chapter.status)}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Key choices */}
            {choices.length > 0 && (
              <div className="rounded-3xl bg-card/80 p-6 shadow-romantic-lg backdrop-blur-lg border border-border">
                <h2 className="mb-4 text-xl font-semibold text-foreground flex items-center gap-2">
                  <Users className="w-5 h-5 text-accent" />
                  {t('story.sections.keyChoices')}
                </h2>

                <p className="text-sm text-muted-foreground mb-4">
                  {t('story.choices.description', { chapter: story.progress.currentChapter })}
                </p>

                <div className="space-y-3">
                  {choices.map((choice) => (
                    <button
                      key={choice.id}
                      onClick={() => handleChoiceSelect(choice.id)}
                      className={`w-full p-4 rounded-lg text-left transition-all ${
                        selectedChoice === choice.id
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-secondary/50 text-foreground hover:bg-primary/10 hover:translate-x-2'
                      }`}
                    >
                      <h3 className="font-semibold flex items-center gap-2">
                        <span>{choice.icon}</span>
                        {choice.title}
                      </h3>
                      <p className="text-sm mt-1 opacity-80">{choice.description}</p>
                      {choice.consequences && (
                        <p className="text-xs mt-2 opacity-70">{t('story.choices.result')} {choice.consequences}</p>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Mobile: Quick rewards preview */}
            <div className="lg:hidden">
              <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-gray-200/50 dark:border-gray-700/50">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
                  <Trophy className="w-5 h-5 text-yellow-500" />
                  {t('story.sections.completionRewards')}
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  {rewards.slice(0, 4).map((reward, index) => (
                    <div key={index} className="p-3 rounded-lg bg-gradient-to-r from-amber-50 via-yellow-50 to-orange-50 dark:from-amber-900/30 dark:via-yellow-900/30 dark:to-orange-900/30 border border-amber-200 dark:border-amber-700">
                      <div className="flex items-center justify-between">
                        <span className="text-xs flex items-center gap-2 font-medium text-gray-700 dark:text-gray-200">
                          <span className="text-lg">{reward.icon}</span>
                          {reward.name}
                        </span>
                        <span className="font-bold text-sm text-amber-600 dark:text-amber-400 bg-amber-100 dark:bg-amber-900/50 px-2 py-1 rounded-full">
                          +{reward.amount}
                        </span>
                      </div>
                    </div>
                  ))}
                  {rewards.length > 4 && (
                    <div className="col-span-2 text-center">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {t('story.rewards.moreRewards', { count: rewards.length - 4 })}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Desktop sidebar - third column */}
          <div className="hidden lg:block space-y-6">
            <div className="sticky top-8 space-y-6">
            {/* Completion rewards preview */}
            <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-gray-200/50 dark:border-gray-700/50">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center gap-2">
                <Trophy className="w-6 h-6 text-yellow-500" />
                {t('story.sections.completionRewards')}
              </h3>
              
              <div className="space-y-4">
                {rewards.map((reward, index) => (
                  <div
                    key={index}
                    className="p-4 rounded-xl bg-gradient-to-r from-amber-50 via-yellow-50 to-orange-50 dark:from-amber-900/30 dark:via-yellow-900/30 dark:to-orange-900/30 border border-amber-200 dark:border-amber-700 transition-all duration-300 hover:scale-105 hover:shadow-lg shadow-md"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm flex items-center gap-3 font-medium text-gray-700 dark:text-gray-200">
                        <span className="text-2xl">{reward.icon}</span>
                        {reward.name}
                      </span>
                      <span className="font-bold text-lg text-amber-600 dark:text-amber-400 bg-amber-100 dark:bg-amber-900/50 px-3 py-1 rounded-full">
                        +{reward.amount}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Unlockable achievements */}
            <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-gray-200/50 dark:border-gray-700/50">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-blue-500" />
                {t('story.sections.unlockableAchievements')}
              </h3>
              
              <div className="space-y-3">
                {achievements.map((achievement) => (
                  <div 
                    key={achievement.id}
                    className={`p-3 rounded-lg border transition-all ${
                      achievement.rarity === 'gold' 
                        ? 'bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 border-yellow-300 dark:border-yellow-700'
                        : achievement.rarity === 'silver'
                        ? 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-300 dark:border-blue-700'
                        : 'bg-gradient-to-r from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20 border-pink-300 dark:border-pink-700'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{achievement.icon}</span>
                      <div>
                        <p className="font-semibold text-gray-900 dark:text-gray-100">{achievement.title}</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">{achievement.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Participating characters */}
            <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-gray-200/50 dark:border-gray-700/50">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('story.sections.participatingCharacters')}</h3>

              <div className="space-y-3">
                {characters.map((character) => (
                  <div
                    key={character.characterId}
                    className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all"
                  >
                    {character.avatar ? (
                      <Image
                        src={character.avatar}
                        alt={character.name}
                        width={48}
                        height={48}
                        className="w-12 h-12 rounded-full border-2 border-primary/30"
                        unoptimized
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-gray-500 dark:text-gray-400">
                        <Users className="w-6 h-6" />
                      </div>
                    )}
                    <div className="flex-1">
                      <Link
                        href={`/${lang}/character/${character.characterId}`}
                        className="font-semibold text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        {character.name}
                      </Link>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {getCharacterRoleText(character.role)}{getCharacterDescription(character.role)}
                      </p>
                      <div className="flex items-center gap-1 mt-1">
                        {character.unlocked ? (
                          character.bondLevel ? (
                            <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full">
                              {t('story.characters.bondLevel', { level: character.bondLevel })}
                            </span>
                          ) : (
                            <span className="text-xs bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded-full">
                              {t('story.characters.unlocked')}
                            </span>
                          )
                        ) : (
                          <span className="text-xs bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full">
                            {t('story.characters.locked')}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Unlock conditions */}
            <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-gray-200/50 dark:border-gray-700/50">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">{t('story.sections.unlockConditions')}</h3>

              <div className="space-y-3 text-sm">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-900 dark:text-gray-100">
                    {t('story.unlock.bondLevel', { 
                      character: characters.find(c => c.characterId === story.characterId)?.name,
                      level: storyData.unlockConditions.bondLevel 
                    })}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  <span className="text-gray-900 dark:text-gray-100">{t('story.unlock.tutorialComplete')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 rounded-full border-2 border-gray-400 dark:border-gray-500"></div>
                  <span className="text-gray-600 dark:text-gray-400">{t('story.unlock.monthlyPass')}</span>
                </div>
              </div>
            </div>


            </div>
          </div>
        </div>
      </main>
      <BottomNavBar lang={lang} />
    </div>
  );
};

export default StoryDetailClientPage;
