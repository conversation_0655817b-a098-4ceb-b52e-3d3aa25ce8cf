# 后端核心流程 Mermaid 图示例

## 1. AI记忆胶囊 - 用户从对话中存入记忆

```mermaid
sequenceDiagram
    participant UserApp as 用户APP
    participant BackendAPI as 后端API
    participant AIMemoryService as AI记忆服务
    participant LLM as 大语言模型

    UserApp->>BackendAPI: POST /ai-memory/character/{char_id}/add-from-dialogue (session_id, message_ids)
    BackendAPI->>BackendAPI: 验证用户权限和参数
    BackendAPI->>LLM: (可选) 提取/总结 message_ids 内容
    LLM-->>BackendAPI: 结构化记忆文本
    BackendAPI->>AIMemoryService: 存储记忆 (角色ID, 结构化文本, 用户备注)
    AIMemoryService-->>BackendAPI: 存储成功 (返回entry_id)
    BackendAPI-->>UserApp: 成功响应 (包含entry_id)
```

## 2. 记忆碎片画图 (许愿抽卡)

```mermaid
sequenceDiagram
    participant UserApp as 用户APP
    participant BackendAPI as 后端API
    participant ImageGachaService as 图片抽卡服务
    participant ImageService as 图片生成服务
    participant UserBalanceService as 用户余额服务

    UserApp->>BackendAPI: POST /gacha/memory-image/wish (char_id, context_desc, currency_spent)
    BackendAPI->>UserBalanceService: 校验并扣除用户代币
    UserBalanceService-->>BackendAPI: 扣款成功/失败
    alt 扣款成功
        BackendAPI->>ImageGachaService: 触发图片生成 (char_id, context_desc, user_id)
        ImageGachaService->>ImageService: POST /image/generate (prompt based on context_desc)
        ImageService-->>ImageGachaService: 返回 image_task_id
        ImageGachaService-->>BackendAPI: 返回 image_task_id
        BackendAPI-->>UserApp: 成功提交 (返回 image_task_id)
        UserApp->>BackendAPI: GET /image/query/{image_task_id} (轮询)
        BackendAPI->>ImageService: 查询状态
        ImageService-->>BackendAPI: 状态 (processing/completed/failed)
        BackendAPI-->>UserApp: 返回状态 (若完成则含image_url)
    else 扣款失败
        BackendAPI-->>UserApp: 错误响应 (余额不足)
    end
```

## 3. 聊天补全接口流程 (`/completion/chat`)

```mermaid
sequenceDiagram
    participant UserApp as 用户APP
    participant BackendAPI as 后端API网关
    participant RequestClassifier as 请求分类器 (Fast/Slow)
    participant SubconsciousProcessor as 潜意识处理模块
    participant AistudioGemini2_0FlashForSub as Aistudio Gemini 2.0 Flash (潜意识)
    participant AistudioGemini2_5FlashForSub as Aistudio Gemini 2.5 Flash (潜意识)
    participant ComplexityAnalyzer as 输入复杂度分析器
    participant MainChatProcessor as 主聊天处理模块
    participant AistudioGemini2_5FlashPool as Aistudio Gemini 2.5 Flash 池
    participant VertexGemini2_5ProPool as Vertex Gemini 2.5 Pro 池
    participant SlowReqQueue as 慢速请求队列
    participant APILoadMonitor as API池负载监控
    participant BillingService as 计费服务
    participant UserBalanceService as 用户余额服务

    UserApp->>BackendAPI: POST /completion/chat (story_id, ..., request_type_preference?)
    note right of UserApp: 用户可指定偏好快速或慢速(可选)
    BackendAPI->>BackendAPI: 1. 验证参数与权限

    BackendAPI->>RequestClassifier: 1.1. 判断请求类型 (Fast/Slow)
    activate RequestClassifier
    RequestClassifier-->>BackendAPI: 返回: 请求类型 (Fast/Slow)
    deactivate RequestClassifier

    BackendAPI->>SubconsciousProcessor: 2. 潜意识处理 (Fast Req Path)\n(潜意识prompt, char_prompt, story_prompt, history, input)
    activate SubconsciousProcessor
    SubconsciousProcessor->>SubconsciousProcessor: 2.1. 判断上下文长度
    alt 上下文较短
        SubconsciousProcessor->>AistudioGemini2_0FlashForSub: 调用 Gemini 2.0 Flash (池)
        activate AistudioGemini2_0FlashForSub
        AistudioGemini2_0FlashForSub-->>SubconsciousProcessor: 潜意识结果
        deactivate AistudioGemini2_0FlashForSub
    else 上下文较长
        SubconsciousProcessor->>AistudioGemini2_5FlashForSub: 调用 Gemini 2.5 Flash (池)
        activate AistudioGemini2_5FlashForSub
        AistudioGemini2_5FlashForSub-->>SubconsciousProcessor: 潜意识结果
        deactivate AistudioGemini2_5FlashForSub
    end
    SubconsciousProcessor-->>BackendAPI: 返回: 多巴胺等、情绪模板
    deactivate SubconsciousProcessor
    BackendAPI->>BackendAPI: 2.2. 存储/更新潜意识影响

    BackendAPI->>ComplexityAnalyzer: 3. 分析用户输入复杂度 (user_input)
    activate ComplexityAnalyzer
    ComplexityAnalyzer-->>BackendAPI: 返回: 复杂度等级
    deactivate ComplexityAnalyzer

    alt 请求类型为 Fast Req 或 Slow Req 且 API 负载低
        BackendAPI->>MainChatProcessor: 4. 生成主回复 (prompts, history, input, 潜意识结果)
        activate MainChatProcessor
        alt 复杂度较低或中等 (Fast Req 优先 Aistudio)
            MainChatProcessor->>AistudioGemini2_5FlashPool: 调用 Aistudio Gemini 2.5 Flash (池)
            activate AistudioGemini2_5FlashPool
            AistudioGemini2_5FlashPool-->>MainChatProcessor: (流式) AI回复块
            deactivate AistudioGemini2_5FlashPool
        else 复杂度较高 (Fast Req 优先 Vertex)
            MainChatProcessor->>VertexGemini2_5ProPool: 调用 Vertex Gemini 2.5 Pro (池)
            activate VertexGemini2_5ProPool
            VertexGemini2_5ProPool-->>MainChatProcessor: (流式) AI回复块
            deactivate VertexGemini2_5ProPool
        end
        MainChatProcessor-->>BackendAPI: (流式) AI回复块, API消耗信息
        deactivate MainChatProcessor
        
        BackendAPI-->>UserApp: 5. 流式输出 (text/event-stream)
        
        BackendAPI->>BillingService: 6. 计算Credit (根据API消耗)
        activate BillingService
        BillingService-->>BackendAPI: 返回: 需扣除的Credit量
        deactivate BillingService

        BackendAPI->>UserBalanceService: 7. 扣除用户余额 (Credit)
        activate UserBalanceService
        UserBalanceService-->>BackendAPI: 扣款成功/失败
        deactivate UserBalanceService

    else 请求类型为 Slow Req 且 API 负载高
        BackendAPI->>APILoadMonitor: 查询主API池负载
        activate APILoadMonitor
        APILoadMonitor-->>BackendAPI: 返回: 当前负载 (e.g., 70%)
        deactivate APILoadMonitor
        
        BackendAPI->>SlowReqQueue: 4.1. 请求加入慢速队列
        activate SlowReqQueue
        SlowReqQueue-->>BackendAPI: 排队成功 (返回排队ID)
        deactivate SlowReqQueue
        BackendAPI-->>UserApp: 响应: 请求已排队 (含排队ID)
        
        loop 直到API负载 < 50% 且轮到该请求
            APILoadMonitor->>SlowReqQueue: 定期通知API池负载状态
            SlowReqQueue->>SlowReqQueue: 处理队列中的请求
            opt 当条件满足时
                SlowReqQueue->>BackendAPI: 通知: 开始处理慢速请求 (携带请求数据)
                BackendAPI->>MainChatProcessor: 4.2. 生成主回复 (同上)
                activate MainChatProcessor
                alt 复杂度较低或中等
                     MainChatProcessor->>AistudioGemini2_5FlashPool: 调用 Aistudio Gemini 2.5 Flash (池)
                     activate AistudioGemini2_5FlashPool
                     AistudioGemini2_5FlashPool-->>MainChatProcessor: (流式) AI回复块
                     deactivate AistudioGemini2_5FlashPool
                else 复杂度较高
                     MainChatProcessor->>VertexGemini2_5ProPool: 调用 Vertex Gemini 2.5 Pro (池)
                     activate VertexGemini2_5ProPool
                     VertexGemini2_5ProPool-->>MainChatProcessor: (流式) AI回复块
                     deactivate VertexGemini2_5ProPool
                end
                MainChatProcessor-->>BackendAPI: (流式) AI回复块, API消耗信息
                deactivate MainChatProcessor

                BackendAPI-->>UserApp: 5. 流式输出 (通过某种通知机制，如WebSocket或长轮询)
                
                BackendAPI->>BillingService: 6. 计算Credit
                activate BillingService
                BillingService-->>BackendAPI: 返回: Credit量
                deactivate BillingService

                BackendAPI->>UserBalanceService: 7. 扣除用户余额
                activate UserBalanceService
                UserBalanceService-->>BackendAPI: 扣款成功/失败
                deactivate UserBalanceService
            end
        end
    end
