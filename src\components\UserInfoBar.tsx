'use client';

import React from 'react';

interface UserInfoBarProps {
  onLevelClick: () => void;
}

const UserInfoBar: React.FC<UserInfoBarProps> = ({ onLevelClick }) => {
    const level = 104;
    const progress = 75; // Percentage
    const radius = 18;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (progress / 100) * circumference;

    return (
        <div className="relative flex items-center p-0.5 rounded-full bg-fuchsia-200/50 dark:bg-fuchsia-900/50 backdrop-blur-sm shadow-lg w-full max-w-xs md:hidden">
            {/* Level Circle for Bond Dew */}
            <div 
              className="relative flex-shrink-0 w-[44px] h-[44px] cursor-pointer"
              onClick={onLevelClick}
            >
                <svg className="absolute top-0 left-0 w-full h-full" viewBox="0 0 44 44">
                    {/* Background Circle */}
                    <circle
                        cx="22"
                        cy="22"
                        r={radius}
                        className="fill-white dark:fill-slate-700"
                    />
                     {/* Progress Arc */}
                    <circle
                        cx="22"
                        cy="22"
                        r={radius}
                        fill="transparent"
                        strokeWidth="4"
                        stroke="url(#progress-gradient)"
                        strokeLinecap="round"
                        transform="rotate(-90 22 22)"
                        strokeDasharray={circumference}
                        strokeDashoffset={strokeDashoffset}
                    />
                    <defs>
                        <linearGradient id="progress-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stopColor="#f472b6" />
                            <stop offset="100%" stopColor="#a78bfa" />
                        </linearGradient>
                    </defs>
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xl text-slate-700 dark:text-slate-300">{level}</span>
                </div>
            </div>

            {/* User Info & Currencies */}
            <div className="flex-grow flex flex-col ml-[-18px] pl-6 pr-2 overflow-hidden">
                {/* Username */}
                <div className="h-5 flex items-center justify-center">
                    <span className="text-[11px] text-slate-700 dark:text-slate-200 truncate">yipansansha</span>
                </div>
                
                {/* Currencies */}
                <div className="h-6 flex items-center justify-between px-1 -mt-0.5 rounded-b-full">
                    <div className="flex items-center gap-0.5">
                        <span className="text-base">💎</span>
                        <span className="text-[10px] text-slate-700 dark:text-slate-200">5596</span>
                    </div>
                    <div className="flex items-center gap-0.5">
                        <span className="text-base">✨</span>
                        <span className="text-[10px] text-slate-700 dark:text-slate-200">5546</span>
                    </div>
                    <div className="flex items-center gap-0.5">
                        <span className="text-base">🧩</span>
                        <span className="text-[10px] text-slate-700 dark:text-slate-200">15/15</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UserInfoBar; 