'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';

export interface GenreFilters {
  gender: string;
  pov?: string;
}

interface CharacterGenreFilterProps {
  filters: GenreFilters;
  onFiltersChange: (filters: GenreFilters) => void;
  className?: string;
  lang?: string;
}

const CharacterGenreFilter: React.FC<CharacterGenreFilterProps> = ({
  filters,
  onFiltersChange,
  className = '',
  lang = 'en'
}) => {
  const { t } = useTranslation(lang, 'translation');
  
  const genderOptions = [
    { value: '', label: t('characterCreation.filters.any') },
    { value: 'female', label: t('characterCreation.filters.female') },
    { value: 'male', label: t('characterCreation.filters.male') },
    { value: 'other', label: t('characterCreation.filters.other') }
  ];

  const povOptions = [
    { value: '', label: t('characterCreation.filters.any') },
    { value: 'female', label: t('characterCreation.filters.femalePOV') },
    { value: 'male', label: t('characterCreation.filters.malePOV') },
    { value: 'other', label: t('characterCreation.filters.otherPOV') }
  ];

  const handleGenderChange = (gender: string) => {
    onFiltersChange({ ...filters, gender });
  };

  const handlePovChange = (pov: string) => {
    onFiltersChange({ ...filters, pov });
  };

  const FilterSection = ({
    title,
    options,
    selectedValue,
    onChange
  }: {
    title: string;
    options: { value: string; label: string }[];
    selectedValue: string;
    onChange: (value: string) => void;
  }) => (
    <div className="space-y-2">
      <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">{title}</h3>
      <div className="flex gap-1.5 flex-wrap">
        {options.map((option) => (
          <button
            key={option.value}
            onClick={() => onChange(option.value)}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
              selectedValue === option.value
                ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600'
            }`}
          >
            {option.label}
          </button>
        ))}
      </div>
    </div>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      <FilterSection
        title={t('characterCreation.filters.gender')}
        options={genderOptions}
        selectedValue={filters.gender}
        onChange={handleGenderChange}
      />
      <FilterSection
        title={t('characterCreation.filters.pointOfView')}
        options={povOptions}
        selectedValue={filters.pov || ''}
        onChange={handlePovChange}
      />
    </div>
  );
};

export default CharacterGenreFilter;
