import React from 'react';
import { Wallet, TrendingUp, TrendingDown } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';

interface BalanceData {
  amount: number;
  currency: string;
  change: number;
  changeType: 'increase' | 'decrease';
  changeLabel: string;
}

interface BalanceDisplayProps {
  lang: string;
  balance: BalanceData;
  className?: string;
}

const BalanceDisplay: React.FC<BalanceDisplayProps> = ({ 
  lang, 
  balance, 
  className = '' 
}) => {
  const { t } = useTranslation(lang, 'translation');

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat(lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US', {
      style: 'currency',
      currency: currency === 'USD' ? 'USD' : 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatPercentage = (change: number) => {
    return new Intl.NumberFormat(lang === 'zh' ? 'zh-CN' : lang === 'ja' ? 'ja-JP' : 'en-US', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
      signDisplay: 'always'
    }).format(change / 100);
  };

  return (
    <div className={`bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-500 rounded-2xl p-6 text-white shadow-lg ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Wallet className="w-5 h-5" />
          <span className="text-sm font-medium opacity-90">{t('wallet.totalBalance')}</span>
        </div>
        {balance.changeType === 'increase' ? (
          <TrendingUp className="w-5 h-5 opacity-80" />
        ) : (
          <TrendingDown className="w-5 h-5 opacity-80" />
        )}
      </div>
      
      <div className="space-y-2">
        <div className="text-3xl font-bold">
          {formatCurrency(balance.amount, balance.currency)}
        </div>
        <div className="text-sm opacity-80 flex items-center gap-1">
          {balance.changeType === 'increase' ? (
            <TrendingUp className="w-3 h-3" />
          ) : (
            <TrendingDown className="w-3 h-3" />
          )}
          <span>
            {formatPercentage(balance.change)} {balance.changeLabel}
          </span>
        </div>
      </div>
    </div>
  );
};

export default BalanceDisplay; 