'use client';

import React from 'react';
import { ExternalLink } from 'lucide-react';

export interface NotificationData {
  id: string;
  type: 'system' | 'social' | 'profile' | 'subscription';
  icon: React.ReactNode;
  title: string;
  description: string;
  timestamp: string;
  isUnread: boolean;
  category: string;
  link?: string;
}

interface NotificationItemProps {
  notification: NotificationData;
  onMarkAsRead?: (id: string) => void;
  onDelete?: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onDelete
}) => {
  const handleClick = () => {
    if (notification.isUnread && onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
    
    if (notification.link && notification.link !== '#') {
      window.open(notification.link, '_blank');
    }
  };

  return (
    <div 
      className="bg-card text-card-foreground rounded-lg p-4 border border-border hover:border-card-foreground transition-all duration-200 theme-transition cursor-pointer"
      onClick={handleClick}
    >
      <div className="flex items-start space-x-3">
        <div className={`p-2 rounded-lg mt-0.5 ${
          notification.isUnread ? 'bg-primary/20' : 'bg-card-foreground/10'
        }`}>
          {notification.icon}
        </div>
        <div className="flex-grow">
          <div className="flex items-start justify-between">
            <div className="flex-grow">
              <h3 className={`text-sm font-medium ${
                notification.isUnread ? 'text-card-foreground' : 'text-card-foreground/80'
              }`}>
                {notification.title}
              </h3>
              <p className="text-xs text-slate-600 dark:text-slate-400 mt-1">
                {notification.description}
              </p>
              <div className="flex items-center justify-between mt-2">
                <p className="text-xs text-slate-500 dark:text-slate-500">
                  {notification.timestamp}
                </p>
                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                  {notification.category}
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-2 ml-3">
              {notification.isUnread && (
                <div className="w-2 h-2 bg-primary rounded-full" title="Unread"></div>
              )}
              {notification.link && notification.link !== '#' && (
                <ExternalLink 
                  size={14} 
                  className="text-card-foreground/40 hover:text-card-foreground transition-colors" 
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationItem; 