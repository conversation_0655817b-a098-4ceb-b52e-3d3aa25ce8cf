'use client';

import React from 'react';
import { Heart } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { formatCurrency } from '@/lib/utils';

interface User {
  stamina_current?: number;
  stamina_max?: number;
  stamina_recovery_time?: number; // 下次回复时间的时间戳
  membership_tier?: 'standard' | 'pass' | 'diamond' | 'metaverse';
}

interface StaminaDisplayProps {
  user: User | null;
  variant?: 'mobile' | 'sidebar';
  lang: string;
}

const StaminaDisplay: React.FC<StaminaDisplayProps> = ({ 
  user, 
  variant = 'mobile',
  lang 
}) => {
  const router = useRouter();
  
  // 根据会员等级获取体力配置
  const getStaminaConfig = (membershipTier: string = 'standard') => {
    switch (membershipTier) {
      case 'pass':
        return {
          maxStamina: 20,
          recoverySpeed: 2, // 2倍回复速度
          recoveryInterval: 30 * 60 * 1000, // 30分钟回复1点
        };
      case 'diamond':
        return {
          maxStamina: 50,
          recoverySpeed: 5, // 5倍回复速度
          recoveryInterval: 12 * 60 * 1000, // 12分钟回复1点
        };
      case 'metaverse':
        return {
          maxStamina: Infinity,
          recoverySpeed: Infinity,
          recoveryInterval: 0,
        };
      default: // standard
        return {
          maxStamina: 10,
          recoverySpeed: 1,
          recoveryInterval: 60 * 60 * 1000, // 60分钟回复1点
        };
    }
  };

  const config = getStaminaConfig(user?.membership_tier);
  const currentStamina = user?.stamina_current || 8; // 默认值
  const maxStamina = config.maxStamina === Infinity ? '∞' : config.maxStamina;
  const recoveryTime = user?.stamina_recovery_time || Date.now() + 45 * 60 * 1000; // 默认45分钟后回复

  // 计算回复时间显示
  const getRecoveryTimeText = () => {
    if (config.maxStamina === Infinity) return '无限体力';
    if (currentStamina >= config.maxStamina) return '已满';
    
    const now = Date.now();
    const timeLeft = recoveryTime - now;
    
    if (timeLeft <= 0) return '即将回复';
    
    const minutes = Math.floor(timeLeft / (60 * 1000));
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  const handleClick = () => {
    router.push(`/${lang}/stamina`);
  };

  if (variant === 'mobile') {
    // 移动端header样式 - 与货币组件保持一致
    return (
      <div 
        className="flex items-center gap-0.5 sm:gap-1 cursor-pointer hover:opacity-80 transition-opacity"
        onClick={handleClick}
      >
        {/* 心心图标容器 */}
        <div
          className="h-8 bg-red-500 rounded-lg flex items-center justify-center"
          style={{width: '2.8rem'}} // 与货币组件保持一致的宽度
        >
          <Heart className="text-white fill-current" size={14}/>
        </div>
        {/* 体力数值 */}
        <span className="text-xs font-bold text-gray-800 dark:text-gray-100 leading-none whitespace-nowrap">
          {currentStamina}/{maxStamina}
        </span>
      </div>
    );
  }

  // 桌面端sidebar样式 - 与货币组件保持一致
  return (
    <div 
      className="flex flex-col items-center cursor-pointer hover:opacity-80 transition-opacity"
      onClick={handleClick}
    >
      <div className="h-11 bg-red-500 rounded-lg flex items-center justify-center mb-0.5" style={{width: '3.625rem'}}>
        <Heart className="text-white fill-current" size={20}/>
      </div>
      <p className="text-lg font-bold text-gray-800 dark:text-gray-100 text-center">
        {currentStamina}/{maxStamina}
      </p>
      {/* 回复时间显示 - 仅在sidebar中显示 */}
      <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-1">
        {getRecoveryTimeText()}
      </p>
    </div>
  );
};

export default StaminaDisplay;
