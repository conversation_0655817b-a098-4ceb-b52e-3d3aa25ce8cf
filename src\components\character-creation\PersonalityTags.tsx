'use client';

import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';

interface PersonalityTagsProps {
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  maxTags?: number;
  allowCustom?: boolean;
  presetTags?: string[];
  className?: string;
}

const defaultPresetTags = [
  'Gentle', 'Lively', 'Mysterious', 'Calm', 'Humorous', 'Serious',
  'Innocent', 'Mature', 'Cheerful', 'Introverted', 'Curious', 'Kind',
  'Brave', 'Wise', 'Strong', 'Elegant', 'Cute', 'Stern',
  'Optimistic', 'Pessimistic', 'Romantic', 'Realistic', 'Rational', 'Emotional'
];

const PersonalityTags: React.FC<PersonalityTagsProps> = ({
  selectedTags,
  onTagsChange,
  maxTags = 10,
  allowCustom = true,
  presetTags = defaultPresetTags,
  className = ""
}) => {
  const [customTag, setCustomTag] = useState('');

  const handleTagToggle = (tag: string) => {
    if (selectedTags.includes(tag)) {
      // Remove tag
      onTagsChange(selectedTags.filter(t => t !== tag));
    } else {
      // Add tag (check max count limit)
      if (selectedTags.length < maxTags) {
        onTagsChange([...selectedTags, tag]);
      }
    }
  };

  const handleCustomTagAdd = () => {
    const tag = customTag.trim();
    if (tag && !selectedTags.includes(tag) && selectedTags.length < maxTags) {
      onTagsChange([...selectedTags, tag]);
      setCustomTag('');
    }
  };

  const handleCustomTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleCustomTagAdd();
    }
  };

  const removeTag = (tag: string) => {
    onTagsChange(selectedTags.filter(t => t !== tag));
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Selected tags */}
      {selectedTags.length > 0 && (
        <div className="space-y-2">
          <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200">
            Selected Personality Tags ({selectedTags.length}/{maxTags})
          </label>
          <div className="flex flex-wrap gap-2">
            {selectedTags.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center gap-1 px-3 py-1 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-700 dark:text-emerald-300 rounded-full text-sm font-medium border border-emerald-200 dark:border-emerald-700"
              >
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(tag)}
                  className="text-emerald-500 hover:text-emerald-700 dark:hover:text-emerald-200 transition-colors"
                  title="Remove tag"
                >
                  <X size={14} />
                </button>
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Preset tag selection */}
      <div className="space-y-2">
        <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200">
          Select Personality Tags
        </label>
        <div className="flex flex-wrap gap-2">
          {presetTags.map((tag) => (
            <button
              key={tag}
              type="button"
              onClick={() => handleTagToggle(tag)}
              disabled={!selectedTags.includes(tag) && selectedTags.length >= maxTags}
              className={`px-3 py-2 rounded-full text-sm font-medium transition-all border ${
                selectedTags.includes(tag)
                  ? 'bg-emerald-100 dark:bg-emerald-900/50 text-emerald-700 dark:text-emerald-300 border-emerald-300 dark:border-emerald-700'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed'
              }`}
            >
              {tag}
            </button>
          ))}
        </div>
      </div>

      {/* Custom tag input */}
      {allowCustom && selectedTags.length < maxTags && (
        <div className="space-y-2">
          <label className="block text-sm font-semibold text-gray-800 dark:text-gray-200">
            Add Custom Tag
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              value={customTag}
              onChange={(e) => setCustomTag(e.target.value)}
              onKeyDown={handleCustomTagKeyDown}
              placeholder="Enter personality trait..."
              maxLength={10}
              className="flex-1 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 focus:border-emerald-500 focus:ring-2 focus:ring-emerald-500/20 transition-all text-sm"
            />
            <button
              type="button"
              onClick={handleCustomTagAdd}
              disabled={!customTag.trim() || selectedTags.includes(customTag.trim())}
              className="px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all text-sm font-medium flex items-center gap-1"
            >
              <Plus size={14} />
              Add
            </button>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Press Enter or click Add button to add custom tag
          </p>
        </div>
      )}

      {/* Hint messages */}
      {selectedTags.length === 0 && (
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <div className="text-amber-500 mt-0.5">💡</div>
            <div>
              <p className="text-sm text-amber-700 dark:text-amber-300">
                Please select at least 1 personality tag to describe your character traits
              </p>
            </div>
          </div>
        </div>
      )}

      {selectedTags.length >= maxTags && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <div className="text-blue-500 mt-0.5">ℹ️</div>
            <div>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Maximum tag limit reached ({maxTags} tags), please remove other tags before adding new ones
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PersonalityTags; 