'use client';

import React, { useState } from 'react';
import { Crown, Check, Star, Sparkles, ArrowRight, Diamond, Zap } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { EnhancedStoreCard, StoreCardHeader, StoreButton } from './EnhancedStoreCard';

interface Plan {
  name: string;
  price: number;
  period: string;
  status: string;
  features: string[];
}

interface SubscriptionData {
  currentPlans: {
    explorer: { isActive: boolean; expiresAt: string | null };
    pass: { isActive: boolean; expiresAt: string | null };
    diamond: { isActive: boolean; expiresAt: string | null };
    infinity: { isActive: boolean; expiresAt: string | null };
  };
  plans: {
    explorer: Plan;
    pass: Plan;
    diamond: Plan;
    infinity: Plan;
  };
}

interface SubscriptionSectionProps {
  lang: string;
  featured?: boolean;
  subscriptionData?: SubscriptionData;
}

const SubscriptionSection: React.FC<SubscriptionSectionProps> = ({
  lang,
  featured = false,
  subscriptionData
}) => {
  const { t } = useTranslation(lang, 'translation');

  // Default subscription data with SOTA price progression ordering
  const defaultSubscriptionData: SubscriptionData = {
    currentPlans: {
      explorer: { isActive: true, expiresAt: null },
      pass: { isActive: false, expiresAt: null },
      diamond: { isActive: false, expiresAt: null },
      infinity: { isActive: false, expiresAt: null }
    },
    plans: {
      explorer: {
        name: t('store.subscriptions.explorer.name'),
        price: 0,
        period: t('store.subscriptions.explorer.period'),
        status: 'permanent',
        features: [
          t('store.subscriptions.explorer.features.0'),
          t('store.subscriptions.explorer.features.1'),
          t('store.subscriptions.explorer.features.2'),
          t('store.subscriptions.explorer.features.3'),
          t('store.subscriptions.explorer.features.4'),
          t('store.subscriptions.explorer.features.5'),
          t('store.subscriptions.explorer.features.6')
        ]
      },
      pass: {
        name: t('store.subscriptions.pass.name'),
        price: 9.99,
        period: t('store.subscriptions.monthly'),
        status: 'inactive',
        features: [
          t('store.subscriptions.pass.features.0'),
          t('store.subscriptions.pass.features.1'),
          t('store.subscriptions.pass.features.2'),
          t('store.subscriptions.pass.features.3'),
          t('store.subscriptions.pass.features.4'),
          t('store.subscriptions.pass.features.5'),
          t('store.subscriptions.pass.features.6'),
          t('store.subscriptions.pass.features.7'),
          t('store.subscriptions.pass.features.8'),
          t('store.subscriptions.pass.features.9'),
          t('store.subscriptions.pass.features.10'),
          t('store.subscriptions.pass.features.11'),
          t('store.subscriptions.pass.features.12'),
          t('store.subscriptions.pass.features.13')
        ]
      },
      diamond: {
        name: t('store.subscriptions.diamond.name'),
        price: 29.99,
        period: t('store.subscriptions.monthly'),
        status: 'inactive',
        features: [
          t('store.subscriptions.diamond.features.0'),
          t('store.subscriptions.diamond.features.1'),
          t('store.subscriptions.diamond.features.2'),
          t('store.subscriptions.diamond.features.3'),
          t('store.subscriptions.diamond.features.4'),
          t('store.subscriptions.diamond.features.5'),
          t('store.subscriptions.diamond.features.6'),
          t('store.subscriptions.diamond.features.7'),
          t('store.subscriptions.diamond.features.8'),
          t('store.subscriptions.diamond.features.9'),
          t('store.subscriptions.diamond.features.10'),
          t('store.subscriptions.diamond.features.11'),
          t('store.subscriptions.diamond.features.12'),
          t('store.subscriptions.diamond.features.13')
        ]
      },
      infinity: {
        name: t('store.subscriptions.infinity.name'),
        price: 99.99,
        period: t('store.subscriptions.monthly'),
        status: 'inactive',
        features: [
          t('store.subscriptions.infinity.features.0'),
          t('store.subscriptions.infinity.features.1'),
          t('store.subscriptions.infinity.features.2'),
          t('store.subscriptions.infinity.features.3'),
          t('store.subscriptions.infinity.features.4'),
          t('store.subscriptions.infinity.features.5'),
          t('store.subscriptions.infinity.features.6'),
          t('store.subscriptions.infinity.features.7'),
          t('store.subscriptions.infinity.features.8'),
          t('store.subscriptions.infinity.features.9'),
          t('store.subscriptions.infinity.features.10'),
          t('store.subscriptions.infinity.features.11')
        ]
      }
    }
  };

  const data = subscriptionData || defaultSubscriptionData;
  const [planStates, setPlanStates] = useState(data.currentPlans);

  const handleSubscriptionToggle = (planType: 'pass' | 'diamond' | 'infinity') => {
    setPlanStates(prev => ({
      ...prev,
      [planType]: {
        ...prev[planType],
        isActive: !prev[planType].isActive,
        expiresAt: !prev[planType].isActive ? '2024-12-31' : null
      }
    }));
  };

  // If featured mode, show simplified view with SOTA ordering
  if (featured) {
    const plans = [
      {
        id: 'diamondPass',
        name: t('store.subscriptions.diamond.name'),
        shortName: t('store.subscriptions.diamond.shortName'),
        description: t('store.subscriptions.diamond.description'),
        price: '$29.99',
        period: 'month',
        features: [
          t('store.subscriptions.diamond.features.0'),
          t('store.subscriptions.diamond.features.1'),
          t('store.subscriptions.diamond.features.2'),
          t('store.subscriptions.diamond.features.5'),
          t('store.subscriptions.diamond.features.6')
        ],
        popular: true,
        color: 'from-purple-500 to-pink-600',
        icon: Crown,
      },
      {
        id: 'infinityPass',
        name: t('store.subscriptions.infinity.name'),
        shortName: t('store.subscriptions.infinity.shortName'),
        description: t('store.subscriptions.infinity.description'),
        price: '$99.99',
        period: 'month',
        features: [
          t('store.subscriptions.infinity.features.0'),
          t('store.subscriptions.infinity.features.1'),
          t('store.subscriptions.infinity.features.2'),
          t('store.subscriptions.infinity.features.4'),
          t('store.subscriptions.infinity.features.6')
        ],
        popular: false,
        color: 'from-cyan-500 to-blue-600',
        icon: Sparkles,
      },
    ];

    return (
      <div className="space-y-6">

        {/* Enhanced Featured Plans - ISFJ Style with Neon Effects */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-6">
          {plans.map((plan) => {
            const Icon = plan.icon;
            return (
              <EnhancedStoreCard
                key={plan.id}
                gradient={plan.color}
                glowEffect={plan.popular}
                vipGlow={plan.popular}
                neonReflection={true}
                className="relative"
              >
                {/* Popular badge */}
                {plan.popular && (
                  <div className="absolute -top-3 left-4 z-10">
                    <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                      <Star className="w-3 h-3" fill="currentColor" />
                      {t('store.subscriptions.mostPopular')}
                    </div>
                  </div>
                )}

                {/* Enhanced header with ISFJ warmth */}
                <StoreCardHeader
                  icon={Icon}
                  title={plan.name}
                  subtitle={plan.description}
                  iconGradient={plan.color}
                  actions={
                    <div className="text-right">
                      <div className="text-2xl font-bold text-foreground">
                        {plan.price}
                      </div>
                      <div className="text-sm text-foreground/60">
                        /{plan.period}
                      </div>
                    </div>
                  }
                />

                <div className="mb-6">
                  <ul className="space-y-2">
                    {plan.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-4 h-4 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
                        </div>
                        <span className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                          {feature}
                        </span>
                      </li>
                    ))}
                    {plan.features.length > 3 && (
                      <li className="text-center pt-2">
                        <span className="text-gray-500 dark:text-gray-400 text-sm font-medium">
                          +{plan.features.length - 3} {t('store.subscriptions.moreFeatures')}
                        </span>
                      </li>
                    )}
                  </ul>
                </div>

                {/* Enhanced upgrade button */}
                <StoreButton
                  variant="primary"
                  size="lg"
                  neonGlow={true}
                  className="w-full"
                >
                  <Crown className="w-4 h-4 mr-2" />
                  {t('store.subscriptions.upgrade')}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </StoreButton>
              </EnhancedStoreCard>
            );
          })}
        </div>
      </div>
    );
  }

  // Full subscription management view
  return (

      <div className="space-y-8">

      {/* Active Plans Status */}
      {(planStates.pass?.isActive || planStates.diamond?.isActive || planStates.infinity?.isActive) && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <Crown className="text-green-600 dark:text-green-400" size={24} />
            <div>
              <h3 className="font-semibold text-green-800 dark:text-green-200">
                {t('store.subscriptions.activeSubscriptions')}:
              </h3>
              <div className="text-sm text-green-600 dark:text-green-400 space-y-1.5">
                {planStates.pass?.isActive && (
                  <div>{t('store.subscriptions.pass.shortName')} - {t('store.subscriptions.expires')}: {planStates.pass?.expiresAt}</div>
                )}
                {planStates.diamond?.isActive && (
                  <div>{t('store.subscriptions.diamond.shortName')} - {t('store.subscriptions.expires')}: {planStates.diamond?.expiresAt}</div>
                )}
                {planStates.infinity?.isActive && (
                  <div>{t('store.subscriptions.infinity.shortName')} - {t('store.subscriptions.expires')}: {planStates.infinity?.expiresAt}</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* SOTA Simplified Responsive Grid - 2x2 for <1536px, 1x4 for ≥1536px */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-4 gap-6 max-w-7xl mx-auto animate-in fade-in duration-500">
        {Object.entries(data.plans).map(([planKey, plan]) => {
          const typedPlanKey = planKey as keyof typeof planStates;
          const planState = planStates[typedPlanKey];
          const isPremium = planKey !== 'explorer';

          const getBorderStyle = () => {
            switch (planKey) {
              case 'pass':
                return 'border-blue-500 shadow-lg shadow-blue-500/20 hover:shadow-blue-500/30';
              case 'diamond':
                return 'border-purple-500 shadow-lg shadow-purple-500/20 hover:shadow-purple-500/30';
              case 'infinity':
                return 'border-cyan-500 shadow-lg shadow-cyan-500/20 hover:shadow-cyan-500/30';
              default:
                return 'border-orange-500 shadow-lg shadow-orange-500/20 hover:shadow-orange-500/30';
            }
          };

          return (
            <div
              key={planKey}
              className={`relative bg-white dark:bg-gray-800 border rounded-xl p-6 pt-8 transition-all duration-300 h-full flex flex-col overflow-visible hover:scale-105 hover:-translate-y-1 ${getBorderStyle()}`}
            >
              {/* Simplified Premium Badges */}
              {planKey === 'diamond' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg border-2 border-white dark:border-gray-800 hover:scale-110 transition-transform">
                    <span className="flex items-center gap-1.5">
                      <Star className="w-4 h-4" fill="currentColor" />
                      <span>{t('store.subscriptions.mostPopular')}</span>
                    </span>
                  </div>
                </div>
              )}
              {planKey === 'infinity' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-cyan-400 via-blue-500 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg border-2 border-white dark:border-gray-800 hover:scale-110 transition-transform">
                    <span className="flex items-center gap-1.5">
                      <Sparkles className="w-4 h-4" />
                      <span>{t('store.subscriptions.ultimate')}</span>
                    </span>
                  </div>
                </div>
              )}

              <div className={`text-center mb-6 ${(planKey === 'diamond' || planKey === 'infinity') ? 'mt-4' : ''}`}>
                <h3 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 leading-tight">{plan.name}</h3>
                <div className="mb-4">
                  {plan.price === 0 ? (
                    <span className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('store.subscriptions.free')}</span>
                  ) : (
                    <div>
                      <span className="text-3xl font-bold text-gray-900 dark:text-gray-100">${plan.price}</span>
                      <span className="text-gray-500 dark:text-gray-400">/{plan.period}</span>
                    </div>
                  )}
                </div>

                {/* Status indicator */}
                {planKey === 'explorer' && (
                  <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                    {t('store.subscriptions.alwaysActive')}
                  </div>
                )}
                {isPremium && planState?.isActive && (
                  <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                    {t('store.subscriptions.active')}
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {t('store.subscriptions.validUntil')}: {planState?.expiresAt}
                    </div>
                  </div>
                )}
              </div>

              {/* Features section with flex-1 to push button to bottom */}
              <div className="flex-1 mb-6">
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-1.5">
                      <Check size={16} className="text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Button at bottom */}
              <button
                onClick={() => {
                  if (planKey === 'explorer') return;
                  handleSubscriptionToggle(planKey as 'pass' | 'diamond' | 'infinity');
                }}
                disabled={planKey === 'explorer'}
                className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 ${
                  planKey === 'explorer'
                    ? 'bg-green-100 text-green-800 cursor-not-allowed dark:bg-green-900/30 dark:text-green-400'
                    : planState?.isActive
                    ? 'bg-red-50 text-red-700 hover:bg-red-100 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/30'
                    : 'bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:shadow-lg hover:shadow-current/30 transform hover:scale-[1.02] active:scale-[0.98]'
                }`}
              >
                {planKey === 'explorer'
                  ? t('store.subscriptions.currentlyActive')
                  : planState?.isActive
                  ? t('store.subscriptions.cancelAtNextBilling')
                  : t('store.actions.subscribe')
                }
              </button>
            </div>
          );
        })}
      </div>
      </div>
    );
};

export default SubscriptionSection; 