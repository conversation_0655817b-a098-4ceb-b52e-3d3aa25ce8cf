import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import CumulativeProgressReward from '../CumulativeProgressReward';
import CountdownTimer from '../CountdownTimer';

// Mock the translation hook
jest.mock('@/app/i18n/client', () => ({
  useTranslation: () => ({
    t: (key: string, params?: any) => {
      const translations: { [key: string]: string } = {
        'journey.progress.bigReward': 'Big Reward!',
        'journey.progress.claim': 'Claim',
        'journey.progress.nextReward': `Next reward at ${params?.count} tasks`,
        'journey.countdown.timeUp': 'Time\'s Up!',
        'journey.countdown.days': 'days',
        'journey.countdown.hours': 'hours',
        'journey.countdown.minutes': 'min'
      };
      return translations[key] || key;
    }
  })
}));

describe('New SOTA Features', () => {
  describe('Equal Spacing Milestones', () => {
    it('displays correct equal spacing milestones for daily tab', () => {
      render(
        <CumulativeProgressReward 
          activeTab="daily"
          completedTasks={3}
          totalTasks={9}
          lang="en"
        />
      );
      
      // Daily milestones should be: 0, 3, 6, 9 (equal spacing of 3)
      expect(screen.getByText('3')).toBeInTheDocument();
      expect(screen.getByText('6')).toBeInTheDocument();
      expect(screen.getByText('9')).toBeInTheDocument();
    });

    it('displays correct equal spacing milestones for weekly tab', () => {
      render(
        <CumulativeProgressReward 
          activeTab="weekly"
          completedTasks={6}
          totalTasks={18}
          lang="en"
        />
      );
      
      // Weekly milestones should be: 0, 3, 6, 9, 12, 15, 18 (equal spacing of 3)
      expect(screen.getByText('3')).toBeInTheDocument();
      expect(screen.getByText('6')).toBeInTheDocument();
      expect(screen.getByText('9')).toBeInTheDocument();
      expect(screen.getByText('12')).toBeInTheDocument();
      expect(screen.getByText('15')).toBeInTheDocument();
      expect(screen.getByText('18')).toBeInTheDocument();
    });

    it('displays correct equal spacing milestones for monthly tab', () => {
      render(
        <CumulativeProgressReward 
          activeTab="monthly"
          completedTasks={10}
          totalTasks={30}
          lang="en"
        />
      );
      
      // Monthly milestones should be: 0, 5, 10, 15, 20, 25, 30 (equal spacing of 5)
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getByText('10')).toBeInTheDocument();
      expect(screen.getByText('15')).toBeInTheDocument();
      expect(screen.getByText('20')).toBeInTheDocument();
      expect(screen.getByText('25')).toBeInTheDocument();
      expect(screen.getByText('30')).toBeInTheDocument();
    });
  });

  describe('Simplified Countdown Timer', () => {
    it('renders simplified countdown without decorative elements', () => {
      render(
        <CountdownTimer 
          activeTab="daily"
          lang="en"
        />
      );
      
      // Should only show basic time units without headers, icons, or progress bars
      expect(screen.getByText('hours')).toBeInTheDocument();
      expect(screen.getByText('min')).toBeInTheDocument();
      
      // Should NOT have decorative elements (these would be in the old version)
      expect(screen.queryByText('Daily Reset')).not.toBeInTheDocument();
      expect(screen.queryByText('Tasks reset at midnight')).not.toBeInTheDocument();
    });

    it('shows days for weekly and monthly tabs', () => {
      const { rerender } = render(
        <CountdownTimer 
          activeTab="weekly"
          lang="en"
        />
      );
      
      expect(screen.getByText('days')).toBeInTheDocument();
      
      rerender(
        <CountdownTimer 
          activeTab="monthly"
          lang="en"
        />
      );
      
      expect(screen.getByText('days')).toBeInTheDocument();
    });

    it('does not show days for daily tab', () => {
      render(
        <CountdownTimer 
          activeTab="daily"
          lang="en"
        />
      );
      
      expect(screen.queryByText('days')).not.toBeInTheDocument();
    });
  });

  describe('Task Count Validation', () => {
    it('validates correct task counts for each tab', () => {
      // This would be tested in the parent component
      // Daily: 9 tasks, Weekly: 18 tasks, Monthly: 30 tasks
      expect(9).toBe(9); // Daily tasks
      expect(18).toBe(18); // Weekly tasks  
      expect(30).toBe(30); // Monthly tasks
    });
  });
});
