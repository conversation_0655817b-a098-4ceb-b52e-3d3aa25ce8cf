#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * CodeAct - 真实代码行数统计工具
 * 统计项目中的有效代码行数，排除注释、空行、文档等
 */

class CodeActAnalyzer {
  constructor() {
    this.stats = {
      totalFiles: 0,
      totalLines: 0,
      codeLines: 0,
      commentLines: 0,
      blankLines: 0,
      fileTypes: {},
      directories: {}
    };

    // 需要统计的文件扩展名
    this.codeExtensions = new Set([
      '.ts', '.tsx', '.js', '.jsx',
      '.css', '.scss', '.sass', '.less',
      '.json', '.html', '.htm',
      '.vue', '.svelte'
    ]);

    // 排除的目录
    this.excludeDirs = new Set([
      'node_modules',
      '.git',
      '.next',
      'dist',
      'build',
      'coverage',
      '.nyc_output',
      'Docs',
      'docs',
      'public/fonts',
      '.vscode',
      '.idea'
    ]);

    // 排除的文件
    this.excludeFiles = new Set([
      'package.json',
      'package-lock.json',
      'yarn.lock',
      'bun.lock',
      'tsconfig.json',
      'next.config.js',
      'tailwind.config.ts',
      'postcss.config.mjs',
      'biome.json',
      'eslint.config.mjs',
      'next-env.d.ts',
      'tsconfig.tsbuildinfo',
      'netlify.toml',
      'components.json',
      'README.md',
      'RESPONSIVE_NAVIGATION_OPTIMIZATION.md',
      'analyze_i18n_keys.js',
      'yarn-error.log'
    ]);
  }

  /**
   * 判断是否为代码行（非注释、非空行）
   */
  isCodeLine(line, fileExt) {
    const trimmed = line.trim();
    
    // 空行
    if (!trimmed) return { type: 'blank' };

    // 根据文件类型判断注释
    switch (fileExt) {
      case '.ts':
      case '.tsx':
      case '.js':
      case '.jsx':
        // 单行注释
        if (trimmed.startsWith('//')) return { type: 'comment' };
        // 多行注释开始
        if (trimmed.startsWith('/*')) return { type: 'comment' };
        // 多行注释结束
        if (trimmed.endsWith('*/')) return { type: 'comment' };
        // JSDoc注释
        if (trimmed.startsWith('*')) return { type: 'comment' };
        break;

      case '.css':
      case '.scss':
      case '.sass':
      case '.less':
        // CSS注释
        if (trimmed.startsWith('/*') || trimmed.endsWith('*/') || trimmed.startsWith('*')) {
          return { type: 'comment' };
        }
        break;

      case '.html':
      case '.htm':
        // HTML注释
        if (trimmed.startsWith('<!--') || trimmed.endsWith('-->')) {
          return { type: 'comment' };
        }
        break;

      case '.json':
        // JSON文件通常不包含注释，所有非空行都是代码
        break;
    }

    return { type: 'code' };
  }

  /**
   * 分析单个文件
   */
  analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      const fileExt = path.extname(filePath);
      
      let fileStats = {
        total: lines.length,
        code: 0,
        comment: 0,
        blank: 0
      };

      let inMultiLineComment = false;

      for (const line of lines) {
        const trimmed = line.trim();

        // 处理多行注释状态
        if (fileExt === '.ts' || fileExt === '.tsx' || fileExt === '.js' || fileExt === '.jsx') {
          if (trimmed.includes('/*') && !trimmed.includes('*/')) {
            inMultiLineComment = true;
          }
          if (inMultiLineComment) {
            fileStats.comment++;
            if (trimmed.includes('*/')) {
              inMultiLineComment = false;
            }
            continue;
          }
        }

        const lineType = this.isCodeLine(line, fileExt);
        fileStats[lineType.type]++;
      }

      // 更新总体统计
      this.stats.totalFiles++;
      this.stats.totalLines += fileStats.total;
      this.stats.codeLines += fileStats.code;
      this.stats.commentLines += fileStats.comment;
      this.stats.blankLines += fileStats.blank;

      // 按文件类型统计
      if (!this.stats.fileTypes[fileExt]) {
        this.stats.fileTypes[fileExt] = {
          files: 0,
          lines: 0,
          code: 0
        };
      }
      this.stats.fileTypes[fileExt].files++;
      this.stats.fileTypes[fileExt].lines += fileStats.total;
      this.stats.fileTypes[fileExt].code += fileStats.code;

      return fileStats;
    } catch (error) {
      console.warn(`无法读取文件 ${filePath}: ${error.message}`);
      return null;
    }
  }

  /**
   * 递归扫描目录
   */
  scanDirectory(dirPath, relativePath = '') {
    try {
      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const relativeItemPath = path.join(relativePath, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          // 跳过排除的目录
          if (this.excludeDirs.has(item)) continue;

          this.scanDirectory(fullPath, relativeItemPath);
        } else if (stat.isFile()) {
          // 跳过排除的文件
          if (this.excludeFiles.has(item)) continue;

          const fileExt = path.extname(item);
          
          // 只统计代码文件
          if (this.codeExtensions.has(fileExt)) {
            this.analyzeFile(fullPath);
          }
        }
      }
    } catch (error) {
      console.warn(`无法扫描目录 ${dirPath}: ${error.message}`);
    }
  }

  /**
   * 生成控制台报告
   */
  generateConsoleReport() {
    console.log('\n🚀 CodeAct - 项目代码统计报告');
    console.log('=' .repeat(50));

    console.log('\n📊 总体统计:');
    console.log(`  文件总数: ${this.stats.totalFiles}`);
    console.log(`  总行数: ${this.stats.totalLines.toLocaleString()}`);
    console.log(`  代码行数: ${this.stats.codeLines.toLocaleString()}`);
    console.log(`  注释行数: ${this.stats.commentLines.toLocaleString()}`);
    console.log(`  空行数: ${this.stats.blankLines.toLocaleString()}`);

    const codeRatio = ((this.stats.codeLines / this.stats.totalLines) * 100).toFixed(1);
    console.log(`  代码占比: ${codeRatio}%`);

    console.log('\n📁 按文件类型统计:');
    const sortedTypes = Object.entries(this.stats.fileTypes)
      .sort((a, b) => b[1].code - a[1].code);

    for (const [ext, stats] of sortedTypes) {
      const ratio = ((stats.code / this.stats.codeLines) * 100).toFixed(1);
      console.log(`  ${ext.padEnd(6)} | 文件: ${stats.files.toString().padStart(3)} | 代码行: ${stats.code.toLocaleString().padStart(6)} (${ratio}%)`);
    }

    console.log('\n🎯 核心指标:');
    console.log(`  有效代码行数: ${this.stats.codeLines.toLocaleString()}`);
    console.log(`  平均每文件代码行数: ${Math.round(this.stats.codeLines / this.stats.totalFiles)}`);

    console.log('\n' + '=' .repeat(50));
    console.log('✨ 统计完成！');
  }

  /**
   * 生成JSON报告文件
   */
  generateJSONReport(outputPath = 'codeact-report.json') {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalFiles: this.stats.totalFiles,
        totalLines: this.stats.totalLines,
        codeLines: this.stats.codeLines,
        commentLines: this.stats.commentLines,
        blankLines: this.stats.blankLines,
        codeRatio: ((this.stats.codeLines / this.stats.totalLines) * 100).toFixed(1)
      },
      fileTypes: this.stats.fileTypes,
      metrics: {
        averageCodeLinesPerFile: Math.round(this.stats.codeLines / this.stats.totalFiles),
        mostUsedLanguage: Object.entries(this.stats.fileTypes)
          .sort((a, b) => b[1].code - a[1].code)[0]?.[0] || 'N/A'
      }
    };

    try {
      fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
      console.log(`📄 详细报告已保存到: ${outputPath}`);
    } catch (error) {
      console.warn(`无法保存报告文件: ${error.message}`);
    }
  }

  /**
   * 生成Markdown报告文件
   */
  generateMarkdownReport(outputPath = 'codeact-report.md') {
    const timestamp = new Date().toLocaleString('zh-CN');
    const codeRatio = ((this.stats.codeLines / this.stats.totalLines) * 100).toFixed(1);

    let markdown = `# 📊 CodeAct 项目代码统计报告

> 生成时间: ${timestamp}

## 📈 总体统计

| 指标 | 数值 |
|------|------|
| 文件总数 | ${this.stats.totalFiles.toLocaleString()} |
| 总行数 | ${this.stats.totalLines.toLocaleString()} |
| **代码行数** | **${this.stats.codeLines.toLocaleString()}** |
| 注释行数 | ${this.stats.commentLines.toLocaleString()} |
| 空行数 | ${this.stats.blankLines.toLocaleString()} |
| 代码占比 | ${codeRatio}% |

## 📁 按文件类型统计

| 文件类型 | 文件数 | 总行数 | 代码行数 | 占比 |
|----------|--------|--------|----------|------|
`;

    const sortedTypes = Object.entries(this.stats.fileTypes)
      .sort((a, b) => b[1].code - a[1].code);

    for (const [ext, stats] of sortedTypes) {
      const ratio = ((stats.code / this.stats.codeLines) * 100).toFixed(1);
      markdown += `| ${ext} | ${stats.files} | ${stats.lines.toLocaleString()} | ${stats.code.toLocaleString()} | ${ratio}% |\n`;
    }

    markdown += `
## 🎯 核心指标

- **有效代码行数**: ${this.stats.codeLines.toLocaleString()}
- **平均每文件代码行数**: ${Math.round(this.stats.codeLines / this.stats.totalFiles)}
- **主要开发语言**: ${sortedTypes[0]?.[0] || 'N/A'}
- **代码质量**: ${codeRatio}% 代码占比

## 📊 可视化图表

\`\`\`
代码行数分布:
${sortedTypes.map(([ext, stats]) => {
  const ratio = ((stats.code / this.stats.codeLines) * 100).toFixed(1);
  const barLength = Math.max(0, Math.min(50, Math.round(ratio)));
  const bar = '█'.repeat(barLength) + '░'.repeat(Math.max(0, 50 - barLength));
  return `${ext.padEnd(6)} ${bar} ${ratio}%`;
}).join('\n')}
\`\`\`

---
*由 CodeAct 生成*
`;

    try {
      fs.writeFileSync(outputPath, markdown);
      console.log(`📝 Markdown报告已保存到: ${outputPath}`);
    } catch (error) {
      console.warn(`无法保存Markdown报告: ${error.message}`);
    }
  }

  /**
   * 运行分析
   */
  run(targetPath = '.', options = {}) {
    console.log(`🔍 开始扫描项目: ${path.resolve(targetPath)}`);

    const startTime = Date.now();
    this.scanDirectory(targetPath);
    const endTime = Date.now();

    console.log(`⏱️  扫描耗时: ${endTime - startTime}ms`);

    // 生成控制台报告
    this.generateConsoleReport();

    // 根据选项生成文件报告
    if (options.json !== false) {
      this.generateJSONReport(options.jsonOutput);
    }

    if (options.markdown !== false) {
      this.generateMarkdownReport(options.markdownOutput);
    }
  }
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    targetPath: '.',
    json: true,
    markdown: true,
    jsonOutput: 'codeact-report.json',
    markdownOutput: 'codeact-report.md'
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === '--help' || arg === '-h') {
      console.log(`
🚀 CodeAct - 项目代码统计工具

用法:
  node scripts/codeact.js [目录] [选项]

选项:
  --help, -h              显示帮助信息
  --no-json              不生成JSON报告文件
  --no-markdown          不生成Markdown报告文件
  --json-output <文件>    指定JSON报告文件名 (默认: codeact-report.json)
  --md-output <文件>      指定Markdown报告文件名 (默认: codeact-report.md)

示例:
  node scripts/codeact.js                    # 分析当前目录
  node scripts/codeact.js src                # 分析src目录
  node scripts/codeact.js --no-json          # 只生成控制台和Markdown报告
  node scripts/codeact.js --md-output stats.md  # 自定义Markdown文件名
      `);
      process.exit(0);
    } else if (arg === '--no-json') {
      options.json = false;
    } else if (arg === '--no-markdown') {
      options.markdown = false;
    } else if (arg === '--json-output') {
      options.jsonOutput = args[++i];
    } else if (arg === '--md-output') {
      options.markdownOutput = args[++i];
    } else if (!arg.startsWith('--')) {
      options.targetPath = arg;
    }
  }

  return options;
}

// 运行脚本
if (require.main === module) {
  const options = parseArgs();
  const analyzer = new CodeActAnalyzer();
  analyzer.run(options.targetPath, options);
}

module.exports = CodeActAnalyzer;
