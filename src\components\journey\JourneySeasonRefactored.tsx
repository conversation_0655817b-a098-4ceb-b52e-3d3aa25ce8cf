'use client';

import React, { useState } from 'react';
import { Calendar, Crown } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { useJourneyData } from '../../hooks/journey/useJourneyData';

import SeasonPass from './SeasonPass';
import MembershipPlans from './shared/MembershipPlans';

interface JourneySeasonRefactoredProps {
  lang: string;
}

const JourneySeasonRefactored: React.FC<JourneySeasonRefactoredProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const { data, updateMembershipTier } = useJourneyData();

  // 状态管理
  const [showMembershipModal, setShowMembershipModal] = useState(false);

  // 事件处理
  const handleMembershipUpgrade = (tierLevel: 'standard' | 'pass' | 'diamond' | 'metaverse') => {
    updateMembershipTier(tierLevel);
    setShowMembershipModal(false);
  };

  return (
    <div className="space-y-6 -mx-4">
      <div className="mx-2 space-y-6">
        {/* 合并后的Season Pass内容 - 包含通行证 */}
        <SeasonPass
          currentLevel={data.currentLevel}
          isPremiumUser={data.membershipTier !== 'standard'}
          membershipTier={data.membershipTier}
          onMembershipClick={() => setShowMembershipModal(true)}
          lang={lang}
        />

        {/* 会员升级模态框 */}
        {showMembershipModal && (
          <MembershipPlans
            currentMembership={data.membershipTier}
            lang={lang}
            onUpgrade={handleMembershipUpgrade}
            onClose={() => setShowMembershipModal(false)}
          />
        )}
      </div>
    </div>
  );
};

export default JourneySeasonRefactored; 