'use client';

import { FC, useState } from 'react';
import { useRouter } from 'next/navigation';
import { BookOpen, Settings, Layers } from 'lucide-react';
import MainAppLayout from '@/components/MainAppLayout';
import { useTranslation } from '@/app/i18n/client';
import { characters } from '@/lib/mock-data';
import toast from 'react-hot-toast';

// Import new components
import StoryFunctionSelector from '@/components/story-creation/FunctionSelector';
import FlashCreation from '@/components/story-creation/FlashCreation';
import CustomizeCreation from '@/components/story-creation/CustomizeCreation';
import CharacterInfoDisplay from '@/components/story-creation/CharacterInfoDisplay';

// Import hooks
import { useStoryForm } from '@/hooks/story-creation/useStoryForm';

// Import types
import type { 
  CreateStoryClientPageProps, 
  StorySelectedFunction, 
  StoryStep, 
  StoryStepConfig 
} from '@/types/story-creation';

const CreateStoryClientPage: FC<CreateStoryClientPageProps> = ({ lang, characterId }) => {
  const router = useRouter();
  const { t } = useTranslation(lang, 'translation');

  // Get character data if characterId is provided
  const character = characterId ? characters.find(c => c.id === characterId) : null;

  // State for function selection
  const [selectedFunction, setSelectedFunction] = useState<StorySelectedFunction>('flash');
  const [currentStep, setCurrentStep] = useState<StoryStep>('worldSetting');

  // Use custom hooks
  const storyForm = useStoryForm(lang);

  // Step configuration
  const steps: readonly StoryStepConfig[] = [
    { 
      id: 'worldSetting', 
      label: t('storyCreation.functions.customize.steps.worldSetting'), 
      icon: Layers, 
      description: t('storyCreation.steps.worldSetting.description') 
    },
    { 
      id: 'storyFlow', 
      label: t('storyCreation.functions.customize.steps.storyFlow'), 
      icon: BookOpen, 
      description: t('storyCreation.steps.storyFlow.description') 
    },
    { 
      id: 'objectivesSubjectives', 
      label: t('storyCreation.functions.customize.steps.objectivesSubjectives'), 
      icon: Settings, 
      description: t('storyCreation.steps.objectivesSubjectives.description') 
    },
  ] as const;

  // Handle function selection content rendering
  const renderSelectedFunctionContent = () => {
    switch (selectedFunction) {
      case 'flash':
        return (
          <FlashCreation
            lang={lang}
            characterName={character?.name || 'Character'}
            onSubmit={handleFlashSubmit}
            isLoading={storyForm.isLoading}
          />
        );
      case 'customize':
        return (
          <CustomizeCreation
            lang={lang}
            currentStep={currentStep}
            formData={storyForm.formData}
            setFormData={storyForm.setFormData}
            onStepChange={setCurrentStep}
            onSubmit={handleCustomizeSubmit}
            isStepComplete={storyForm.isStepComplete}
            steps={steps}
            characterId={characterId || ''}
          />
        );
      default:
        return null;
    }
  };

  // Handle flash story generation and submission
  const handleFlashSubmit = async (concept: string) => {
    try {
      // Generate story with AI
      await storyForm.generateFlashStory(concept);
      
      // Submit the generated story
      const result = await storyForm.handleSubmit();
      
      if (result.success) {
        toast.success(t('storyCreation.success.storyCreated'));
        router.push(`/${lang}/chats/${characterId}`);
      }
    } catch (error) {
      console.error('Flash story creation failed:', error);
      toast.error(t('storyCreation.error.failedToGenerate'));
    }
  };

  // Handle customize story submission
  const handleCustomizeSubmit = async () => {
    try {
      const result = await storyForm.handleSubmit();
      
      if (result.success) {
        toast.success(t('storyCreation.success.storyCreated'));
        router.push(`/${lang}/chats/${characterId}`);
      }
    } catch (error) {
      console.error('Story creation failed:', error);
      toast.error(t('storyCreation.error.failedToCreate'));
    }
  };

  // Main content renderer
  const renderMainContent = () => (
    <div className="space-y-8">
      {/* Function Selection Buttons */}
      <StoryFunctionSelector
        lang={lang}
        selectedFunction={selectedFunction}
        onFunctionSelect={setSelectedFunction}
      />

      {/* Selected Function Content */}
      <div className="animate-in slide-in-from-bottom-4 duration-500">
        {renderSelectedFunctionContent()}
      </div>
    </div>
  );

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-purple-50/30 to-pink-50/30 dark:from-gray-900 dark:via-purple-900/10 dark:to-pink-900/10">
        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-200/20 dark:bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-pink-200/20 dark:bg-pink-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {character ? (
            // Character-specific layout
            <>
              {/* Character Info Section */}
              <div className="mb-4">
                <CharacterInfoDisplay character={character} lang={lang} />
              </div>

              {/* Main Content - Full width */}
              {renderMainContent()}
            </>
          ) : characterId ? (
            // Character not found
            <div className="text-center py-12">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">{t('storyCreation.error.characterNotFound')}</h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                {t('storyCreation.error.characterNotFoundDescription')}
              </p>
              <button
                onClick={() => router.push(`/${lang}/create-story`)}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                {t('storyCreation.error.backToCharacterSelection')}
              </button>
            </div>
          ) : (
            // Default layout when no character is selected
            <>
              {/* Page Header */}
              <div className="text-center mb-12">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-500 to-rose-500 bg-clip-text text-transparent mb-4">
                  {t('storyCreation.title')}
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                  {t('storyCreation.subtitle')}
                </p>
              </div>

              {/* Main Content */}
              {renderMainContent()}
            </>
          )}
        </div>
      </div>
    </MainAppLayout>
  );
};

export default CreateStoryClientPage; 