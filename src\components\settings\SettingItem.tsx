'use client';

import React from 'react';
import { ChevronRight } from 'lucide-react';

interface SettingItemProps {
  label: string;
  description?: string;
  children?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  isPremium?: boolean;
  showArrow?: boolean;
}

const SettingItem: React.FC<SettingItemProps> = ({
  label,
  description,
  children,
  onClick,
  disabled = false,
  className = '',
  isPremium = false,
  showArrow = false
}) => {
  const isClickable = onClick && !disabled;

  return (
    <div 
      className={`
        flex items-center justify-between py-4 px-2
        ${isClickable ? 'cursor-pointer hover:bg-accent rounded-lg transition-colors' : ''}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
      onClick={isClickable ? onClick : undefined}
    >
      <div className="flex-1 min-w-0 mr-4">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium text-foreground cursor-pointer">
            {label}
          </label>
          {isPremium && (
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
              Premium
            </span>
          )}
        </div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">
            {description}
          </p>
        )}
      </div>
      
      <div className="flex items-center gap-2">
        {children}
        {showArrow && (
          <ChevronRight className="w-4 h-4 text-muted-foreground" />
        )}
      </div>
    </div>
  );
};

export default SettingItem; 