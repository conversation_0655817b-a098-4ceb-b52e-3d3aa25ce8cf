import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { Star, MessageSquare, Spark<PERSON>, Heart, Clock, Eye } from 'lucide-react';
import type { Memory } from '@/app/[lang]/manage-memories/ManageMemoryClientPage';

interface MemoryCardProps {
  memory: Memory;
  onClick: (memory: Memory) => void;
  lang: string;
}

const MemoryCard: React.FC<MemoryCardProps> = ({ memory, onClick, lang }) => {
  const { t: _ } = useTranslation(lang, 'translation');

  const emotionConfig = {
    happy: {
      gradient: 'from-yellow-400/20 via-orange-300/20 to-pink-400/20',
      borderColor: 'border-yellow-300/50',
      shadowColor: 'shadow-yellow-300/25',
      emoji: '😊',
      bgOverlay: 'bg-gradient-to-br from-yellow-50/80 to-orange-50/80 dark:from-yellow-900/20 dark:to-orange-900/20'
    },
    sad: {
      gradient: 'from-blue-400/20 via-indigo-300/20 to-purple-400/20',
      borderColor: 'border-blue-300/50',
      shadowColor: 'shadow-blue-300/25',
      emoji: '🥺',
      bgOverlay: 'bg-gradient-to-br from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-indigo-900/20'
    },
    excited: {
      gradient: 'from-pink-400/20 via-red-300/20 to-orange-400/20',
      borderColor: 'border-pink-300/50',
      shadowColor: 'shadow-pink-300/25',
      emoji: '🎉',
      bgOverlay: 'bg-gradient-to-br from-pink-50/80 to-red-50/80 dark:from-pink-900/20 dark:to-red-900/20'
    },
    thoughtful: {
      gradient: 'from-purple-400/20 via-indigo-300/20 to-blue-400/20',
      borderColor: 'border-purple-300/50',
      shadowColor: 'shadow-purple-300/25',
      emoji: '🤔',
      bgOverlay: 'bg-gradient-to-br from-purple-50/80 to-indigo-50/80 dark:from-purple-900/20 dark:to-indigo-900/20'
    },
    important: {
      gradient: 'from-amber-400/20 via-yellow-300/20 to-orange-400/20',
      borderColor: 'border-amber-300/50',
      shadowColor: 'shadow-amber-300/25',
      emoji: '⭐',
      bgOverlay: 'bg-gradient-to-br from-amber-50/80 to-yellow-50/80 dark:from-amber-900/20 dark:to-yellow-900/20'
    },
  };

  const config = memory.emotion_marker ? emotionConfig[memory.emotion_marker] : emotionConfig.thoughtful;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return _('memory.time.today', 'Today');
    } else if (diffInHours < 48) {
      return _('memory.time.yesterday', 'Yesterday');
    } else if (diffInHours < 168) {
      return _('memory.time.daysAgo', `${Math.floor(diffInHours / 24)} days ago`);
    } else {
      return date.toLocaleDateString(lang === 'zh' ? 'zh-CN' : 'en-US', {
        month: 'short',
        day: 'numeric',
      });
    }
  };

  return (
    <div
      onClick={() => onClick(memory)}
      className={`group relative cursor-pointer transform hover:scale-[1.02] transition-all duration-500 ease-out animate-fadeInUp`}
    >
      {/* Capsule Container with Glass Morphism */}
      <div className={`
        relative backdrop-blur-xl rounded-3xl overflow-hidden
        ${config.bgOverlay}
        border ${config.borderColor}
        shadow-2xl ${config.shadowColor}
        hover:shadow-3xl hover:${config.shadowColor}
        transition-all duration-500
        before:absolute before:inset-0 before:rounded-3xl before:p-px 
        before:bg-gradient-to-r before:${config.gradient} before:-z-10
      `}>
        
        {/* Memory Artwork Background */}
        {memory.artwork_url && (
          <div className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-700">
            <img
              src={memory.artwork_url}
              alt=""
              className="w-full h-full object-cover rounded-3xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-white/90 via-white/50 to-white/30 dark:from-gray-900/90 dark:via-gray-900/50 dark:to-gray-900/30 rounded-3xl"></div>
          </div>
        )}

        {/* Floating Emotion Indicator */}
        <div className="absolute -top-3 -right-3 z-20">
          <div className={`
            w-12 h-12 rounded-full flex items-center justify-center
            backdrop-blur-lg bg-white/80 dark:bg-gray-800/80
            border-2 ${config.borderColor}
            shadow-lg transform group-hover:scale-110 transition-transform duration-300
          `}>
            <span className="text-lg">{config.emoji}</span>
          </div>
        </div>

        {/* Main Content */}
        <div className="relative z-10 p-6">
          {/* Header with Character */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="relative">
                <img
                  src={memory.source_character.avatar}
                  alt={memory.source_character.name}
                  className="w-12 h-12 rounded-full border-3 border-white/60 dark:border-gray-700/60 shadow-xl"
                />
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white dark:border-gray-800"></div>
              </div>
              <div>
                <h3 className="font-bold text-gray-900 dark:text-white text-lg leading-tight">
                  {memory.capsule_name}
                </h3>
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">{memory.source_character.name}</span>
                  <div className="flex items-center gap-1">
                    <Clock size={12} />
                    <span>{formatDate(memory.created_at)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Memory Summary */}
          <div className="mb-4">
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed line-clamp-3 text-sm">
              {memory.summary}
            </p>
          </div>

          {/* Tags Cloud */}
          <div className="flex flex-wrap gap-1.5 mb-4">
            {memory.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className={`
                  px-3 py-1 rounded-full text-xs font-medium
                  bg-gradient-to-r ${config.gradient}
                  border ${config.borderColor}
                  text-gray-700 dark:text-gray-300
                  backdrop-blur-sm
                `}
              >
                #{tag}
              </span>
            ))}
            {memory.tags.length > 3 && (
              <span className="px-3 py-1 text-xs text-gray-500 dark:text-gray-400 bg-gray-100/50 dark:bg-gray-800/50 rounded-full backdrop-blur-sm">
                +{memory.tags.length - 3}
              </span>
            )}
          </div>

          {/* Footer with Stats */}
          <div className="flex items-center justify-between pt-4 border-t border-white/30 dark:border-gray-700/30">
            {/* Importance Stars */}
            <div className="flex items-center gap-1">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star
                  key={i}
                  size={14}
                  className={`transition-all duration-300 ${
                    i < memory.importance_score
                      ? 'text-yellow-400 fill-current transform hover:scale-125'
                      : 'text-gray-300 dark:text-gray-600'
                  }`}
                />
              ))}
            </div>

            {/* Interaction Stats */}
            <div className="flex items-center gap-4 text-xs text-gray-600 dark:text-gray-400">
              {memory.reference_count !== undefined && memory.reference_count > 0 && (
                <div className="flex items-center gap-1">
                  <MessageSquare size={12} />
                  <span>{memory.reference_count}</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <Eye size={12} />
                <span>AI Refs</span>
              </div>
            </div>
          </div>

          {/* Hover Actions */}
          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
            <div className="flex gap-2">
              {memory.artwork_url && (
                <button className="p-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-lg hover:scale-110 transition-transform">
                  <Eye size={14} className="text-purple-600" />
                </button>
              )}
              <button className="p-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-lg hover:scale-110 transition-transform">
                <Sparkles size={14} className="text-pink-600" />
              </button>
              <button className="p-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-lg hover:scale-110 transition-transform">
                <Heart size={14} className="text-red-500" />
              </button>
            </div>
          </div>
        </div>

        {/* Subtle Inner Glow */}
        <div className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${config.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none`}></div>
      </div>

      {/* Floating Particles Effect */}
      <div className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-700">
        <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping"></div>
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-pink-400 rounded-full animate-ping animation-delay-300"></div>
        <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-indigo-400 rounded-full animate-ping animation-delay-700"></div>
      </div>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fadeInUp {
          animation: fadeInUp 0.6s ease-out forwards;
        }
        .animation-delay-300 {
          animation-delay: 300ms;
        }
        .animation-delay-700 {
          animation-delay: 700ms;
        }
      `}</style>
    </div>
  );
};

export default MemoryCard; 