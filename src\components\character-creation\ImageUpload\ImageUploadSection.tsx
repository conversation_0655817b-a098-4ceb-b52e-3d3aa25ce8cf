'use client';

import React from 'react';
import Image from 'next/image';
import { Upload, Palette } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ImageUploadSectionProps {
  characterImagePreview: string;
  avatarPreview: string;
  characterImageInputRef: React.RefObject<HTMLInputElement>;
  handleCharacterImageUpload: (file: File) => void;
  triggerCharacterImageInput: () => void;
  lang?: string;
}

const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({
  characterImagePreview,
  avatarPreview,
  characterImageInputRef,
  handleCharacterImageUpload,
  triggerCharacterImageInput,
  lang,
}) => {
  const { t } = useTranslation();
  return (
    <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl p-6 border border-purple-200/50 dark:border-purple-700/50 shadow-sm">
      <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-6 flex items-center gap-2">
        <Upload size={20} />
        {t('characterCreation.imageUpload.characterImages')}
      </h4>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Character Image Upload */}
        <div>
          <h5 className="font-medium text-purple-700 dark:text-purple-300 mb-4">{t('characterCreation.imageUpload.characterPortrait')}</h5>
          <button
            type="button"
            onClick={triggerCharacterImageInput}
            className="w-full p-6 border-2 border-dashed border-purple-300 dark:border-purple-700 rounded-xl hover:border-purple-400 dark:hover:border-purple-600 transition-all duration-300 bg-white/50 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800 hover:scale-105"
          >
            {characterImagePreview ? (
              <div className="text-purple-700 dark:text-purple-300">
                <Image
                  src={characterImagePreview}
                  alt="Character Preview"
                  width={200}
                  height={240}
                  className="mx-auto mb-2 rounded-lg object-cover"
                />
                <p className="font-semibold">{t('characterCreation.imageUpload.characterImageUploaded')}</p>
                <p className="text-sm">{t('characterCreation.imageUpload.clickToChangeImage')}</p>
              </div>
            ) : (
              <div className="text-purple-600 dark:text-purple-400">
                <Upload className="mx-auto mb-2" size={32} />
                <p className="font-semibold">{t('characterCreation.imageUpload.uploadCharacterImage')}</p>
                <p className="text-sm">{t('characterCreation.imageUpload.clickOrDragToUpload')}</p>
              </div>
            )}
          </button>
          
          <input
            ref={characterImageInputRef}
            type="file"
            accept="image/*"
            onChange={(e) => e.target.files?.[0] && handleCharacterImageUpload(e.target.files[0])}
            className="hidden"
          />
        </div>

        {/* Avatar Preview */}
        <div>
          <h5 className="font-medium text-purple-700 dark:text-purple-300 mb-4">{t('characterCreation.imageUpload.avatarAutoCropped')}</h5>
          <div className="w-full p-6 border-2 border-dashed border-green-300 dark:border-green-700 rounded-xl bg-green-50/50 dark:bg-green-900/20 transition-all duration-300">
            {avatarPreview ? (
              <div className="text-green-700 dark:text-green-300 text-center">
                <Image
                  src={avatarPreview}
                  alt="Avatar Preview"
                  width={128}
                  height={128}
                  className="mx-auto mb-2 rounded-full object-cover"
                />
                <p className="font-semibold">{t('characterCreation.imageUpload.avatarReady')}</p>
                <p className="text-sm">{t('characterCreation.imageUpload.croppedFromCharacterImage')}</p>
              </div>
            ) : (
              <div className="text-gray-400 text-center">
                <Palette className="mx-auto mb-2" size={32} />
                <p>{t('characterCreation.imageUpload.uploadCharacterImageFirst')}</p>
                <p className="text-sm">{t('characterCreation.imageUpload.avatarWillBeAutoCropped')}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Avatar Preview Section */}
      {avatarPreview && (
        <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200/50 dark:border-green-700/50">
          <h4 className="font-medium text-green-700 dark:text-green-300 mb-4">{t('characterCreation.imageUpload.avatarPreview')}</h4>
          <div className="flex items-center gap-6">
            <div className="relative">
              <Image
                src={avatarPreview}
                alt="Avatar Preview"
                width={80}
                height={80}
                className="w-20 h-20 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg"
              />
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
            </div>
            <div className="text-sm text-green-600 dark:text-green-400">
              <p className="font-medium">{t('characterCreation.imageUpload.avatarSetupComplete')}</p>
              <p className="text-green-500 dark:text-green-500">{t('characterCreation.imageUpload.sourceCroppedFromCharacterImage')}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploadSection;
