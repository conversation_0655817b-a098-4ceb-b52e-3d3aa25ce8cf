'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeft, UserPlus, UserMinus, BadgeCheck, Search } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Follower {
  id: string;
  name: string;
  avatar: string;
  username: string;
  isFollowing: boolean;
  followerCount: number;
  bio: string;
  joinedDate: string;
  isVerified: boolean;
}

interface UserInfo {
  name: string;
  avatar: string;
  followerCount: number;
}

interface FollowersClientPageProps {
  lang: string;
  uid: string;
  userInfo: UserInfo;
  followers: Follower[];
}

const FollowersClientPage: React.FC<FollowersClientPageProps> = ({
  lang,
  uid,
  userInfo,
  followers
}) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [followersData, setFollowersData] = useState(followers);

  const filteredFollowers = followersData.filter(follower =>
    follower.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    follower.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleFollowToggle = (followerId: string) => {
    setFollowersData(prev =>
      prev.map(follower =>
        follower.id === followerId
          ? { ...follower, isFollowing: !follower.isFollowing }
          : follower
      )
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center gap-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div className="flex items-center gap-3">
            <Image
              src={userInfo.avatar}
              alt={userInfo.name}
              width={40}
              height={40}
              className="w-10 h-10 rounded-full object-cover"
              unoptimized
            />
            <div>
              <h1 className="text-xl font-bold">{userInfo.name}</h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {userInfo.followerCount} followers
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search followers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>
      </div>

      {/* Followers List */}
      <div className="bg-white dark:bg-gray-800">
        {filteredFollowers.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">
              {searchTerm ? 'No followers found matching your search.' : 'No followers yet.'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredFollowers.map((follower) => (
              <div key={follower.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1">
                    <Link href={`/${lang}/profile/${follower.id}`}>
                      <Image
                        src={follower.avatar}
                        alt={follower.name}
                        width={48}
                        height={48}
                        className="w-12 h-12 rounded-full object-cover"
                        unoptimized
                      />
                    </Link>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <Link
                          href={`/${lang}/profile/${follower.id}`}
                          className="font-semibold text-gray-900 dark:text-gray-100 hover:text-indigo-600 dark:hover:text-indigo-400 truncate"
                        >
                          {follower.name}
                        </Link>
                        {follower.isVerified && (
                          <BadgeCheck size={16} className="text-blue-500 flex-shrink-0" />
                        )}
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                        {follower.username}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mt-1">
                        {follower.bio}
                      </p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <span>{follower.followerCount.toLocaleString()} followers</span>
                        <span>Joined {follower.joinedDate}</span>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => handleFollowToggle(follower.id)}
                    className={`ml-4 px-4 py-2 rounded-lg font-medium transition-colors flex items-center gap-2 ${
                      follower.isFollowing
                        ? 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                        : 'bg-indigo-600 text-white hover:bg-indigo-700'
                    }`}
                  >
                    {follower.isFollowing ? (
                      <>
                        <UserMinus size={16} />
                        Following
                      </>
                    ) : (
                      <>
                        <UserPlus size={16} />
                        Follow
                      </>
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FollowersClientPage;
