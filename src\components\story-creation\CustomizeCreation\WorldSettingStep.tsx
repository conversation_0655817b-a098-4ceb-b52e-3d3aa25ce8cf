'use client';

import React, { useCallback } from 'react';
import { ArrowRight, Globe } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import type { StoryFormData, WorldSetting } from '@/types/story-creation';
import WorldSettingComponent from './WorldSetting';

interface WorldSettingStepProps {
  formData: StoryFormData;
  setFormData: (data: StoryFormData | ((prev: StoryFormData) => StoryFormData)) => void;
  lang: string;
  onStepChange?: (step: 'worldSetting' | 'storyFlow' | 'objectivesSubjectives') => void;
  onSubmit?: () => void;
}

const WorldSettingStep: React.FC<WorldSettingStepProps> = ({
  formData,
  setFormData,
  lang,
  onStepChange,
  onSubmit
}) => {
  const { t } = useTranslation(lang, 'translation');

  // 更新世界设定
  const handleWorldSettingChange = useCallback((updates: Partial<WorldSetting>) => {
    setFormData(prev => ({
      ...prev,
      worldSetting: { ...prev.worldSetting, ...updates }
    }));
  }, [setFormData]);

  // 继续到下一步
  const handleContinueToStoryFlow = () => {
    if (onStepChange) {
      onStepChange('storyFlow');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2 flex items-center justify-center gap-2">
          <Globe className="w-6 h-6 text-purple-500" />
          {t('storyCreation.steps.worldSetting.title')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {t('storyCreation.steps.worldSetting.description')}
        </p>
      </div>

      {/* World Setting Component */}
      <WorldSettingComponent
        worldSetting={formData.worldSetting}
        onWorldSettingChange={handleWorldSettingChange}
        formData={formData}
        setFormData={setFormData}
        lang={lang}
      />

      {/* Navigation */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {t('storyCreation.navigation.stepOf', { current: 1, total: 3 })}: {t('storyCreation.steps.worldSetting.description')}
        </div>
        
        <button
          onClick={handleContinueToStoryFlow}
          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          {t('storyCreation.storyFlow.navigation.continueToObjectives')}
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export default WorldSettingStep;
