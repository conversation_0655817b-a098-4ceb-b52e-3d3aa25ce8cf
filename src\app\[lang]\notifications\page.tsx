'use client';

import { useParams } from 'next/navigation';
import MainAppLayout from '@/components/MainAppLayout';
import {
  NotificationService,
  NotificationHeader,
  NotificationTabs,
  NotificationList
} from '@/components/notifications';

export default function NotificationsPage() {
  const { lang } = useParams() as { lang: string };

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-background theme-transition">
        <div className="max-w-4xl mx-auto p-4 sm:p-6 space-y-6">
          
          <NotificationHeader lang={lang} />

          <NotificationService lang={lang}>
            {({ 
              notifications, 
              activeTab, 
              unreadCounts, 
              onTabChange, 
              onMarkAsRead, 
              onMarkAllAsRead, 
              onDelete 
            }) => (
              <>
                <NotificationTabs
                  lang={lang}
                  activeTab={activeTab}
                  onTabChange={onTabChange}
                  unreadCounts={unreadCounts}
                />

                <NotificationList
                  lang={lang}
                  notifications={notifications}
                  activeTab={activeTab}
                  onMarkAsRead={onMarkAsRead}
                  onMarkAllAsRead={onMarkAllAsRead}
                  onDelete={onDelete}
                />
              </>
            )}
          </NotificationService>
        </div>
      </div>
    </MainAppLayout>
  );
}
