'use client';

import React, { useState } from 'react';
import { MessageCircle, Brain, Star, Plus, X, Minus, BookOpen, Upload, FileText, Trash2, Eye } from 'lucide-react';
import type { CharacterFormData } from '@/types/character-creation';
import { useTranslation } from '@/app/i18n/client';

interface PersonalityStepProps {
  formData: CharacterFormData;
  setFormData: (data: CharacterFormData | ((prev: CharacterFormData) => CharacterFormData)) => void;
  lang?: string;
}

const PersonalityStep: React.FC<PersonalityStepProps> = ({
  formData,
  setFormData,
  lang = 'en'
}) => {
  const { t } = useTranslation(lang, 'translation');
  // 直接使用bodyLanguage字段，不再从interactionStyleTags中提取
  const updateBodyLanguage = (value: string) => {
    setFormData(prev => ({ ...prev, bodyLanguage: value }));
  };

  // State for good at and bad at inputs
  const [goodAtInput, setGoodAtInput] = useState('');
  const [badAtInput, setBadAtInput] = useState('');
  const handleKnowledgeFileUpload = (files: FileList) => {
    const validFiles: File[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const extension = file.name.toLowerCase().split('.').pop();
      if (extension === 'txt' || extension === 'md' || extension === 'json') {
        validFiles.push(file);
      }
    }
    if (validFiles.length > 0) {
      setFormData(prev => ({
        ...prev,
        knowledgeFiles: [...prev.knowledgeFiles, ...validFiles]
      }));
    }
  };

  const removeKnowledgeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      knowledgeFiles: prev.knowledgeFiles.filter((_, i) => i !== index)
    }));
  };

  // Good at handlers
  const handleAddGoodAt = () => {
    const skill = goodAtInput.trim();
    if (skill && !formData.goodAt.includes(skill)) {
      setFormData(prev => ({
        ...prev,
        goodAt: [...prev.goodAt, skill]
      }));
      setGoodAtInput('');
    }
  };

  const handleGoodAtKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddGoodAt();
    }
  };

  const removeGoodAt = (indexToRemove: number) => {
    setFormData(prev => ({
      ...prev,
      goodAt: prev.goodAt.filter((_, index) => index !== indexToRemove)
    }));
  };

  // Bad at handlers
  const handleAddBadAt = () => {
    const skill = badAtInput.trim();
    if (skill && !formData.badAt.includes(skill)) {
      setFormData(prev => ({
        ...prev,
        badAt: [...prev.badAt, skill]
      }));
      setBadAtInput('');
    }
  };

  const handleBadAtKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddBadAt();
    }
  };

  const removeBadAt = (indexToRemove: number) => {
    setFormData(prev => ({
      ...prev,
      badAt: prev.badAt.filter((_, index) => index !== indexToRemove)
    }));
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-purple-800 dark:text-purple-200 mb-2">{t('characterCreation.personality.title')}</h3>
        <p className="text-gray-600 dark:text-gray-400">{t('characterCreation.personality.subtitle')}</p>
      </div>

      {/* Behaviour Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-1.5 flex items-center gap-1.5">
          <MessageCircle size={18} />
          {t('characterCreation.personality.behaviour')}
        </h4>

        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">{t('characterCreation.personality.defaultGreeting')}</label>
            <textarea
              value={formData.greetingMessage}
              onChange={(e) => setFormData(prev => ({ ...prev, greetingMessage: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
              placeholder={t('characterCreation.personality.greetingPlaceholder')}
              maxLength={500}
            />
            <div className="flex justify-end items-center mt-1">
              <span className={`character-counter ${formData.greetingMessage.length > 50 ? '' : 'over-limit'}`}>
                {formData.greetingMessage.length}/500
              </span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">{t('characterCreation.personality.speechStyle')}</label>
            <textarea
              value={formData.customPromptPrefix}
              onChange={(e) => setFormData(prev => ({ ...prev, customPromptPrefix: e.target.value }))}
              rows={2}
              className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
              placeholder={t('characterCreation.personality.speechStylePlaceholder')}
              maxLength={300}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">{t('characterCreation.personality.facialExpressions')}</label>
            <textarea
              value={formData.facialExpressions}
              onChange={(e) => setFormData(prev => ({ ...prev, facialExpressions: e.target.value }))}
              rows={2}
              className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
              placeholder={t('characterCreation.personality.facialExpressionsPlaceholder')}
              maxLength={300}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">{t('characterCreation.personality.bodyLanguage')}</label>
            <textarea
              value={formData.bodyLanguage}
              onChange={(e) => updateBodyLanguage(e.target.value)}
              rows={2}
              className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
              placeholder={t('characterCreation.personality.bodyLanguagePlaceholder')}
              maxLength={300}
            />
          </div>
        </div>
      </div>


      {/* Knowledge Section */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-1.5 flex items-center gap-1.5">
          <Brain size={18} />
          {t('characterCreation.personality.knowledge')}
        </h4>

        <div className="space-y-4">
          {/* Knowledge Base */}
          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">{t('characterCreation.personality.knowledgeBase')}</label>
            <textarea
              value={formData.initialMemoriesText}
              onChange={(e) => setFormData(prev => ({ ...prev, initialMemoriesText: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
              placeholder={t('characterCreation.personality.knowledgePlaceholder')}
              maxLength={2000}
            />
            <div className="flex justify-end items-center mt-1">
              <span className={`character-counter ${formData.initialMemoriesText.length > 100 ? '' : 'over-limit'}`}>
                {formData.initialMemoriesText.length}/2000
              </span>
            </div>
          </div>

          {/* Knowledge File Upload */}
          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">{t('characterCreation.personality.uploadKnowledgeFiles')}</label>
            <div
              className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center hover:border-gray-400 dark:hover:border-gray-500 transition-all cursor-pointer bg-white dark:bg-gray-800"
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.txt,.md,.json';
                input.multiple = true;
                input.onchange = (e) => {
                  const files = (e.target as HTMLInputElement).files;
                  if (files) handleKnowledgeFileUpload(files);
                };
                input.click();
              }}
            >
              <Upload className="mx-auto mb-2 text-gray-500" size={24} />
              <p className="font-medium text-gray-700 dark:text-gray-300 mb-1">{t('characterCreation.personality.uploadKnowledgeButton')}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {t('characterCreation.personality.uploadKnowledgeDesc')}
              </p>
            </div>

            {/* Uploaded Files List */}
            {formData.knowledgeFiles.length > 0 && (
              <div className="mt-3 space-y-2">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('characterCreation.personality.uploadedFiles')}</p>
                {formData.knowledgeFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
                  >
                    <div className="flex items-center gap-2">
                      <FileText size={16} className="text-gray-600" />
                      <span className="text-sm text-gray-700 dark:text-gray-300 truncate max-w-xs">
                        {file.name}
                      </span>
                      <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                        {(file.size / 1024).toFixed(1)} KB
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeKnowledgeFile(index)}
                      className="text-red-500 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-100 dark:hover:bg-red-900/30 p-1 rounded transition-all"
                      title={t('characterCreation.personality.deleteFile')}
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Good At Section */}
          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">{t('characterCreation.personality.goodAt')}</label>
            <div className="space-y-3">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={goodAtInput}
                  onChange={(e) => setGoodAtInput(e.target.value)}
                  onKeyDown={handleGoodAtKeyDown}
                  placeholder={t('characterCreation.personality.goodAtPlaceholder')}
                  className="flex-1 px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                />
                <button
                  type="button"
                  onClick={handleAddGoodAt}
                  disabled={!goodAtInput.trim() || formData.goodAt.includes(goodAtInput.trim())}
                  className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium flex items-center gap-1 shadow-md hover:shadow-lg"
                >
                  <Plus size={16} />
                  {t('characterCreation.personality.add')}
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.goodAt.map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/50 dark:to-emerald-900/50 text-green-700 dark:text-green-300 rounded-full text-sm font-medium border border-green-200 dark:border-green-700 shadow-sm"
                  >
                    <Star size={12} className="mr-1" />
                    {skill}
                    <button
                      type="button"
                      onClick={() => removeGoodAt(index)}
                      className="ml-2 w-4 h-4 flex items-center justify-center text-green-500 hover:text-green-700 dark:hover:text-green-200 hover:bg-green-200 dark:hover:bg-green-800 rounded-full transition-all duration-200"
                    >
                      <X size={12} />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Bad At Section */}
          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-2">{t('characterCreation.personality.badAt')}</label>
            <div className="space-y-3">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={badAtInput}
                  onChange={(e) => setBadAtInput(e.target.value)}
                  onKeyDown={handleBadAtKeyDown}
                  placeholder={t('characterCreation.personality.badAtPlaceholder')}
                  className="flex-1 px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
                />
                <button
                  type="button"
                  onClick={handleAddBadAt}
                  disabled={!badAtInput.trim() || formData.badAt.includes(badAtInput.trim())}
                  className="px-4 py-2 bg-gradient-to-r from-red-500 to-rose-500 text-white rounded-lg hover:from-red-600 hover:to-rose-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all font-medium flex items-center gap-1 shadow-md hover:shadow-lg"
                >
                  <Plus size={16} />
                  {t('characterCreation.personality.add')}
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.badAt.map((skill, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-red-100 to-rose-100 dark:from-red-900/50 dark:to-rose-900/50 text-red-700 dark:text-red-300 rounded-full text-sm font-medium border border-red-200 dark:border-red-700 shadow-sm"
                  >
                    <Minus size={12} className="mr-1" />
                    {skill}
                    <button
                      type="button"
                      onClick={() => removeBadAt(index)}
                      className="ml-2 w-4 h-4 flex items-center justify-center text-red-500 hover:text-red-700 dark:hover:text-red-200 hover:bg-red-200 dark:hover:bg-red-800 rounded-full transition-all duration-200"
                    >
                      <X size={12} />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Background Story */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-1.5 flex items-center gap-1.5">
          <BookOpen size={18} />
          {t('characterCreation.personality.backgroundStory')}
        </h4>

        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">{t('characterCreation.personality.relationshipWithPlayer')}</label>
            <textarea
              value={formData.relationshipWithPlayer}
              onChange={(e) => setFormData(prev => ({ ...prev, relationshipWithPlayer: e.target.value }))}
              rows={2}
              className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
              placeholder={t('characterCreation.personality.relationshipPlaceholder')}
              maxLength={500}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">World Setting</label>
            <textarea
              value={formData.setting}
              onChange={(e) => setFormData(prev => ({ ...prev, setting: e.target.value }))}
              rows={2}
              className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
              placeholder="Describe the world or setting where the character lives..."
              maxLength={500}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">{t('characterCreation.personality.importantExperiences')}</label>
            <textarea
              value={formData.backgroundStory}
              onChange={(e) => setFormData(prev => ({ ...prev, backgroundStory: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 bg-background border border-purple-300 dark:border-purple-700 rounded-lg text-foreground placeholder-muted-foreground focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
              placeholder={t('characterCreation.personality.experiencesPlaceholder')}
              maxLength={1000}
            />
            <div className="flex justify-between items-center mt-1">
              <span className={`character-counter ${formData.backgroundStory.length > 200 ? '' : 'over-limit'}`}>
                {formData.backgroundStory.length}/1000
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Visibility Settings */}
      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 border border-purple-200 dark:border-purple-700">
        <h4 className="font-semibold text-purple-800 dark:text-purple-200 mb-1.5 flex items-center gap-1.5">
          <Eye size={18} />
          {t('characterCreation.personality.visibility')}
        </h4>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
          {[
            { value: 'public', label: t('characterCreation.personality.public'), desc: t('characterCreation.personality.publicDesc') },
            { value: 'unlisted', label: t('characterCreation.personality.unlisted'), desc: t('characterCreation.personality.unlistedDesc') },
            { value: 'private', label: t('characterCreation.personality.private'), desc: t('characterCreation.personality.privateDesc') }
          ].map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, visibility: option.value as any }))}
              className={`p-3 rounded-lg border-2 transition-all text-left ${
                formData.visibility === option.value
                  ? 'border-purple-500 bg-purple-100 dark:bg-purple-900/50 text-purple-800 dark:text-purple-200'
                  : 'border-purple-200 dark:border-purple-700 hover:border-purple-400 dark:hover:border-purple-500 text-purple-600 dark:text-purple-400'
              }`}
            >
              <div className="font-medium text-sm">{option.label}</div>
              <div className="text-xs opacity-75">{option.desc}</div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PersonalityStep;
