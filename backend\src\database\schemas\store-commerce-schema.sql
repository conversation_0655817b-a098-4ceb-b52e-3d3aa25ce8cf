-- =====================================================
-- Store and Commerce System Data Storage Schema
-- Supporting complete e-commerce functionality
-- =====================================================

-- 1. Store Products Table
-- Comprehensive product catalog
CREATE TABLE store_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sku VARCHAR(100) UNIQUE NOT NULL,
    
    -- Product Basic Information
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN (
        'featured', 'memberships', 'welcome', 'arts', 'memorial', 
        'currency', 'mindfuel', 'character', 'cosmetic', 'subscription'
    )),
    subcategory VARCHAR(50),
    
    -- Product Type and Classification
    product_type VARCHAR(30) NOT NULL CHECK (product_type IN (
        'consumable', 'subscription', 'character', 'cosmetic', 'currency_pack',
        'starter_pack', 'character_bundle', 'premium_subscription', 'memorial_item'
    )),
    
    -- Pricing Structure
    price_data JSONB NOT NULL DEFAULT '{}',
    -- Structure: {
    --   base_price: number,
    --   currency: 'USD' | 'star_diamonds' | 'joy_crystals' | 'glimmering_dust' | 'memory_puzzles',
    --   discount_price?: number,
    --   discount_percentage?: number,
    --   regional_pricing?: {country_code: price}
    -- }
    
    -- Rewards and Content
    rewards JSONB DEFAULT '[]',
    -- Array of: {
    --   type: 'currency' | 'character' | 'subscription' | 'cosmetic' | 'badge',
    --   name: string,
    --   amount?: number,
    --   currency_type?: string,
    --   item_id?: string,
    --   duration_days?: number
    -- }
    
    -- Availability and Limits
    is_available BOOLEAN DEFAULT true,
    purchase_limit INTEGER, -- null = unlimited
    per_user_limit INTEGER, -- null = unlimited per user
    stock_quantity INTEGER, -- null = unlimited stock
    
    -- Special Offer Configuration
    is_featured BOOLEAN DEFAULT false,
    is_first_time_only BOOLEAN DEFAULT false,
    is_limited_time BOOLEAN DEFAULT false,
    valid_from TIMESTAMP,
    valid_until TIMESTAMP,
    
    -- Tags and Metadata
    tags TEXT[] DEFAULT '{}',
    special_tag VARCHAR(50) CHECK (special_tag IN (
        'quarterly_special', 'monthly_special', 'weekly_special', 
        'ip_collab', 'new_feature', 'bestseller', 'limited_edition'
    )),
    ip_collab_name VARCHAR(100),
    
    -- Media and Display
    image_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    media_urls JSONB DEFAULT '[]',
    display_order INTEGER DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    
    -- Constraints
    CONSTRAINT valid_price_data CHECK (price_data ? 'base_price' AND price_data ? 'currency'),
    CONSTRAINT valid_dates CHECK (valid_from IS NULL OR valid_until IS NULL OR valid_from < valid_until)
);

-- 2. Subscription Plans Table
-- Detailed subscription tier management
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plan_code VARCHAR(50) UNIQUE NOT NULL,
    
    -- Plan Information
    name VARCHAR(100) NOT NULL,
    description TEXT,
    tier VARCHAR(20) NOT NULL CHECK (tier IN ('standard', 'pass', 'diamond', 'metaverse')),
    
    -- Pricing
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(10) DEFAULT 'USD',
    billing_period VARCHAR(20) NOT NULL CHECK (billing_period IN ('monthly', 'yearly', 'lifetime', 'forever')),
    
    -- Features and Benefits
    features JSONB NOT NULL DEFAULT '[]',
    -- Array of feature strings
    
    benefits JSONB NOT NULL DEFAULT '{}',
    -- Structure: {
    --   mind_fuel_bonus?: string,
    --   recovery_speed?: string,
    --   special_features?: string[],
    --   ai_conversations?: string,
    --   character_access?: string,
    --   avatar_quality?: string,
    --   support_level?: string
    -- }
    
    -- Limits and Quotas
    daily_interaction_quota JSONB DEFAULT '{}',
    -- Structure: {
    --   fast_req_total: number,
    --   slow_req_total: number,
    --   unlimited: boolean
    -- }
    
    stamina_config JSONB DEFAULT '{}',
    -- Structure: {
    --   max_stamina: number,
    --   recovery_rate: number, // minutes per point
    --   bonus_multiplier: number
    -- }
    
    -- Status and Availability
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Enhanced User Currencies Table Extension
-- Extend existing user_currencies table
ALTER TABLE user_currencies 
ADD COLUMN IF NOT EXISTS star_diamonds INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS joy_crystals INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS glimmering_dust INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS memory_puzzles INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS daily_bonus_available BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS next_daily_bonus TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS currency_stats JSONB DEFAULT '{}';
-- currency_stats structure: {
--   star_diamonds: {spent_24h: number, earned_24h: number, total_earned: number, total_spent: number},
--   joy_crystals: {spent_24h: number, earned_24h: number, total_earned: number, total_spent: number},
--   glimmering_dust: {spent_24h: number, earned_24h: number, total_earned: number, total_spent: number},
--   memory_puzzles: {spent_24h: number, earned_24h: number, total_earned: number, total_spent: number}
-- }

-- 4. User Subscriptions Table
-- Track user subscription status
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_plan_id UUID NOT NULL REFERENCES subscription_plans(id),
    
    -- Subscription Status
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN (
        'active', 'cancelled', 'expired', 'paused', 'pending'
    )),
    
    -- Timing
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    cancelled_at TIMESTAMP,
    auto_renew BOOLEAN DEFAULT true,
    
    -- Payment Information
    payment_method VARCHAR(50),
    last_payment_date TIMESTAMP,
    next_payment_date TIMESTAMP,
    payment_amount DECIMAL(10,2),
    payment_currency VARCHAR(10),
    
    -- Usage Tracking
    usage_stats JSONB DEFAULT '{}',
    -- Structure: {
    --   conversations_used: number,
    --   features_accessed: string[],
    --   last_activity: timestamp
    -- }
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, subscription_plan_id, started_at)
);

-- 5. Purchase History Table
-- Detailed transaction records
CREATE TABLE purchase_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES store_products(id),
    subscription_plan_id UUID REFERENCES subscription_plans(id),
    
    -- Transaction Details
    transaction_type VARCHAR(30) NOT NULL CHECK (transaction_type IN (
        'product_purchase', 'subscription_purchase', 'currency_purchase', 
        'gift_claim', 'reward_grant', 'refund'
    )),
    
    -- Financial Information
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    payment_method VARCHAR(50),
    payment_provider VARCHAR(50),
    external_transaction_id VARCHAR(200),
    
    -- Purchase Details
    quantity INTEGER DEFAULT 1,
    rewards_granted JSONB DEFAULT '[]',
    -- Same structure as store_products.rewards
    
    -- Status and Processing
    status VARCHAR(20) NOT NULL DEFAULT 'completed' CHECK (status IN (
        'pending', 'completed', 'failed', 'cancelled', 'refunded'
    )),
    
    -- Metadata
    purchase_metadata JSONB DEFAULT '{}',
    -- Structure: {
    --   is_first_purchase: boolean,
    --   discount_applied: number,
    --   promo_code?: string,
    --   ip_address?: string,
    --   user_agent?: string
    -- }
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. Promotional Offers Table
-- Marketing campaigns and special offers
CREATE TABLE promotional_offers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Offer Information
    name VARCHAR(200) NOT NULL,
    description TEXT,
    offer_type VARCHAR(30) NOT NULL CHECK (offer_type IN (
        'welcome_gift', 'first_time_purchase', 'seasonal', 'loyalty', 
        'referral', 'comeback', 'milestone'
    )),
    
    -- Targeting
    target_audience JSONB DEFAULT '{}',
    -- Structure: {
    --   user_types?: string[], // 'new', 'returning', 'premium'
    --   membership_tiers?: string[],
    --   registration_date_range?: {from: date, to: date},
    --   purchase_history?: {min_purchases: number, max_purchases: number}
    -- }
    
    -- Offer Configuration
    rewards JSONB NOT NULL DEFAULT '[]',
    -- Same structure as store_products.rewards
    
    conditions JSONB DEFAULT '{}',
    -- Structure: {
    --   min_purchase_amount?: number,
    --   required_products?: string[],
    --   max_claims_per_user?: number,
    --   max_total_claims?: number
    -- }
    
    -- Timing and Availability
    is_active BOOLEAN DEFAULT true,
    starts_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ends_at TIMESTAMP,
    
    -- Usage Tracking
    total_claims INTEGER DEFAULT 0,
    total_budget_used DECIMAL(10,2) DEFAULT 0.00,
    max_budget DECIMAL(10,2),
    
    -- Metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id)
);

-- 7. User Promotional Claims Table
-- Track user claims of promotional offers
CREATE TABLE user_promotional_claims (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    promotional_offer_id UUID NOT NULL REFERENCES promotional_offers(id) ON DELETE CASCADE,
    
    -- Claim Details
    rewards_received JSONB NOT NULL DEFAULT '[]',
    claim_value DECIMAL(10,2) DEFAULT 0.00,
    
    -- Status
    status VARCHAR(20) NOT NULL DEFAULT 'claimed' CHECK (status IN (
        'claimed', 'pending', 'expired', 'revoked'
    )),
    
    -- Metadata
    claim_metadata JSONB DEFAULT '{}',
    claimed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, promotional_offer_id)
);

-- 8. Indexes for Performance Optimization
CREATE INDEX idx_store_products_category ON store_products(category);
CREATE INDEX idx_store_products_type ON store_products(product_type);
CREATE INDEX idx_store_products_featured ON store_products(is_featured) WHERE is_featured = true;
CREATE INDEX idx_store_products_available ON store_products(is_available) WHERE is_available = true;
CREATE INDEX idx_store_products_valid_dates ON store_products(valid_from, valid_until);

CREATE INDEX idx_subscription_plans_tier ON subscription_plans(tier);
CREATE INDEX idx_subscription_plans_active ON subscription_plans(is_active) WHERE is_active = true;

CREATE INDEX idx_user_subscriptions_user ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX idx_user_subscriptions_expires ON user_subscriptions(expires_at);

CREATE INDEX idx_purchase_history_user ON purchase_history(user_id);
CREATE INDEX idx_purchase_history_type ON purchase_history(transaction_type);
CREATE INDEX idx_purchase_history_date ON purchase_history(created_at);
CREATE INDEX idx_purchase_history_status ON purchase_history(status);

CREATE INDEX idx_promotional_offers_type ON promotional_offers(offer_type);
CREATE INDEX idx_promotional_offers_active ON promotional_offers(is_active) WHERE is_active = true;
CREATE INDEX idx_promotional_offers_dates ON promotional_offers(starts_at, ends_at);

CREATE INDEX idx_user_promotional_claims_user ON user_promotional_claims(user_id);
CREATE INDEX idx_user_promotional_claims_offer ON user_promotional_claims(promotional_offer_id);

-- GIN indexes for JSONB columns
CREATE INDEX idx_store_products_price_gin ON store_products USING GIN (price_data);
CREATE INDEX idx_store_products_rewards_gin ON store_products USING GIN (rewards);
CREATE INDEX idx_subscription_plans_features_gin ON subscription_plans USING GIN (features);
CREATE INDEX idx_subscription_plans_benefits_gin ON subscription_plans USING GIN (benefits);
CREATE INDEX idx_user_currencies_stats_gin ON user_currencies USING GIN (currency_stats);
CREATE INDEX idx_promotional_offers_target_gin ON promotional_offers USING GIN (target_audience);

-- 9. Triggers for Updated Timestamps
CREATE TRIGGER store_products_updated_at 
BEFORE UPDATE ON store_products 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER subscription_plans_updated_at 
BEFORE UPDATE ON subscription_plans 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER user_subscriptions_updated_at 
BEFORE UPDATE ON user_subscriptions 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER purchase_history_updated_at 
BEFORE UPDATE ON purchase_history 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER promotional_offers_updated_at 
BEFORE UPDATE ON promotional_offers 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. Store Statistics Materialized View
-- Aggregated stats for store Card components
CREATE MATERIALIZED VIEW store_stats AS
SELECT
    sp.id,
    sp.name,
    sp.category,
    sp.product_type,
    COUNT(DISTINCT ph.user_id) as purchase_count,
    SUM(ph.amount) as total_revenue,
    AVG(ph.amount) as avg_purchase_value,
    COUNT(DISTINCT ph.id) as transaction_count,
    sp.is_featured,
    sp.created_at
FROM store_products sp
LEFT JOIN purchase_history ph ON sp.id = ph.product_id
WHERE sp.is_available = true
GROUP BY sp.id, sp.name, sp.category, sp.product_type, sp.is_featured, sp.created_at;

-- 11. Functions for Store Management
CREATE OR REPLACE FUNCTION update_currency_stats(
    p_user_id UUID,
    p_currency_type VARCHAR(50),
    p_amount INTEGER,
    p_transaction_type VARCHAR(20) -- 'earn' or 'spend'
)
RETURNS void AS $$
DECLARE
    current_stats JSONB;
    updated_stats JSONB;
BEGIN
    -- Get current stats
    SELECT currency_stats INTO current_stats
    FROM user_currencies
    WHERE user_id = p_user_id;

    -- Initialize if null
    IF current_stats IS NULL THEN
        current_stats := '{}'::jsonb;
    END IF;

    -- Update stats based on transaction type
    IF p_transaction_type = 'earn' THEN
        updated_stats := jsonb_set(
            current_stats,
            ARRAY[p_currency_type, 'earned_24h'],
            (COALESCE((current_stats->p_currency_type->>'earned_24h')::integer, 0) + p_amount)::text::jsonb
        );
        updated_stats := jsonb_set(
            updated_stats,
            ARRAY[p_currency_type, 'total_earned'],
            (COALESCE((updated_stats->p_currency_type->>'total_earned')::integer, 0) + p_amount)::text::jsonb
        );
    ELSIF p_transaction_type = 'spend' THEN
        updated_stats := jsonb_set(
            current_stats,
            ARRAY[p_currency_type, 'spent_24h'],
            (COALESCE((current_stats->p_currency_type->>'spent_24h')::integer, 0) + p_amount)::text::jsonb
        );
        updated_stats := jsonb_set(
            updated_stats,
            ARRAY[p_currency_type, 'total_spent'],
            (COALESCE((updated_stats->p_currency_type->>'total_spent')::integer, 0) + p_amount)::text::jsonb
        );
    END IF;

    -- Update the record
    UPDATE user_currencies
    SET currency_stats = updated_stats
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- 12. Refresh Function for Materialized View
CREATE OR REPLACE FUNCTION refresh_store_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY store_stats;
END;
$$ LANGUAGE plpgsql;

COMMENT ON TABLE store_products IS 'Comprehensive product catalog for store system';
COMMENT ON TABLE subscription_plans IS 'Subscription tier management with detailed features';
COMMENT ON TABLE user_subscriptions IS 'User subscription status and tracking';
COMMENT ON TABLE purchase_history IS 'Detailed transaction and purchase records';
COMMENT ON TABLE promotional_offers IS 'Marketing campaigns and special offers';
COMMENT ON TABLE user_promotional_claims IS 'User claims of promotional offers';
COMMENT ON MATERIALIZED VIEW store_stats IS 'Aggregated store statistics for analytics and Card components';
