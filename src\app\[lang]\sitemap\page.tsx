'use client';

import { useParams } from 'next/navigation';
import Link from 'next/link';
import MainAppLayout from '@/components/MainAppLayout';

export default function SitemapPage() {
  const { lang } = useParams() as { lang: string };

  const sitePages = [
    {
      category: 'Main Pages',
      pages: [
        { name: 'Home', href: `/${lang}` },
        { name: 'Characters', href: `/${lang}/characters` },
        { name: 'Moments', href: `/${lang}/moments` },
        { name: 'Memories', href: `/${lang}/memories` },
        { name: 'Square', href: `/${lang}/square` },
      ]
    },
    {
      category: 'User Features',
      pages: [
        { name: 'Create Character', href: `/${lang}/create-character` },
        { name: 'Profile', href: `/${lang}/profile` },
        { name: 'Creator Profile', href: `/${lang}/creator-profile` },
        { name: 'Settings', href: `/${lang}/settings` },
        { name: 'Gift Center', href: `/${lang}/gift` },
      ]
    },
    {
      category: 'Account',
      pages: [
        { name: 'Sign In', href: `/${lang}/signin` },
        { name: 'Sign Up', href: `/${lang}/signup` },
        { name: 'Store', href: `/${lang}/store` },
      ]
    },
    {
      category: 'Legal & Support',
      pages: [
        { name: 'Terms of Use', href: `/${lang}/terms` },
        { name: 'Privacy Policy', href: `/${lang}/privacy` },
        { name: 'Contact Us', href: `/${lang}/contact` },
        { name: 'Sitemap', href: `/${lang}/sitemap` },
      ]
    }
  ];

  return (
    <MainAppLayout lang={lang}>
      <div className="min-h-screen bg-white dark:bg-black text-black dark:text-white">
        <div className="max-w-4xl mx-auto p-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-2">Sitemap</h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">Navigate through all pages on Alphane AI</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {sitePages.map((section, index) => (
              <div key={index} className="space-y-4">
                <h2 className="text-xl font-bold border-b border-gray-300 dark:border-gray-700 pb-2">
                  {section.category}
                </h2>
                <ul className="space-y-2">
                  {section.pages.map((page, pageIndex) => (
                    <li key={pageIndex}>
                      <Link 
                        href={page.href}
                        className="text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        {page.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </MainAppLayout>
  );
}
