'use client';

import React from 'react';
import { useTranslation } from '@/app/i18n/client';
import { 
  Users, 
  BookOpen, 
  User, 
  Brain, 
  Heart, 
  Star, 
  Eye, 
  MessageCircle, 
  Calendar, 
  Verified, 
  Crown 
} from 'lucide-react';

interface SearchResultsProps {
  lang: string;
  results: {
    characters?: any[];
    stories?: any[];
    users?: any[];
    memories?: any[];
  };
  activeTab: 'all' | 'characters' | 'stories' | 'users' | 'memories';
}

const SearchResults: React.FC<SearchResultsProps> = ({ 
  lang, 
  results, 
  activeTab 
}) => {
  const { t } = useTranslation(lang, 'translation');

  const filteredResults = activeTab === 'all' ? results : { [activeTab]: results[activeTab] || [] };

  return (
    <div className="space-y-6">
      {/* Characters */}
      {(activeTab === 'all' || activeTab === 'characters') && filteredResults.characters?.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <Users className="w-5 h-5" />
            {t('search.characters.title')}
            <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
              ({filteredResults.characters.length})
            </span>
          </h2>
          <div className="space-y-3">
            {filteredResults.characters.map((character: any) => (
              <div
                key={character.id}
                className="group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-300 cursor-pointer hover:shadow-md"
              >
                <div className="flex items-start gap-4">
                  <div className="relative">
                    <img
                      src={character.avatar}
                      alt={character.name}
                      className="w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-700"
                    />
                    {(character.verified || character.premium) && (
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center">
                        {character.verified && <Verified className="w-3 h-3 text-blue-500" />}
                        {character.premium && <Crown className="w-3 h-3 text-yellow-500" />}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900 dark:text-white text-base truncate">
                        {character.name}
                      </h3>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        by {character.creator}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                      {character.description}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <span className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        {character.followers}
                      </span>
                      <span className="flex items-center gap-1">
                        <MessageCircle className="w-4 h-4" />
                        {character.chats}
                      </span>
                      <span className="flex items-center gap-1">
                        <Star className="w-4 h-4" />
                        {character.rating}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {character.tags?.map((tag: string, index: number) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <button className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg text-sm font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-sm">
                      {t('search.actions.chat')}
                    </button>
                    <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200">
                      {t('search.actions.view')}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Stories */}
      {(activeTab === 'all' || activeTab === 'stories') && filteredResults.stories?.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <BookOpen className="w-5 h-5" />
            {t('search.stories.title')}
            <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
              ({filteredResults.stories.length})
            </span>
          </h2>
          <div className="space-y-3">
            {filteredResults.stories.map((story: any) => (
              <div
                key={story.id}
                className="group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-300 cursor-pointer hover:shadow-md"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-gray-900 dark:text-white text-base">
                        {story.title}
                      </h3>
                      <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                        story.status === 'completed' 
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                          : 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                      }`}>
                        {story.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                      {story.description}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-2">
                      <span>by {story.creator}</span>
                      <span>{story.chapters} chapters</span>
                      <span className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        {story.likes}
                      </span>
                      <span className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        {story.reads}
                      </span>
                      <span className="flex items-center gap-1">
                        <Star className="w-4 h-4" />
                        {story.rating}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {story.tags?.map((tag: string, index: number) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs font-medium"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2 ml-4">
                    <button className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg text-sm font-medium hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-sm">
                      {t('search.actions.read')}
                    </button>
                    <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200">
                      {t('search.actions.save')}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Users */}
      {(activeTab === 'all' || activeTab === 'users') && filteredResults.users?.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <User className="w-5 h-5" />
            {t('search.users.title')}
            <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
              ({filteredResults.users.length})
            </span>
          </h2>
          <div className="space-y-3">
            {filteredResults.users.map((user: any) => (
              <div
                key={user.id}
                className="group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-300 cursor-pointer hover:shadow-md"
              >
                <div className="flex items-start gap-4">
                  <div className="relative">
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-700"
                    />
                    {(user.verified || user.premium) && (
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-white dark:bg-gray-800 rounded-full flex items-center justify-center">
                        {user.verified && <Verified className="w-3 h-3 text-blue-500" />}
                        {user.premium && <Crown className="w-3 h-3 text-yellow-500" />}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900 dark:text-white text-base">
                        {user.name}
                      </h3>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-3">
                      {user.bio}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <span className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        {user.followers} followers
                      </span>
                      <span className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        {user.characters} characters
                      </span>
                      <span className="flex items-center gap-1">
                        <BookOpen className="w-4 h-4" />
                        {user.stories} stories
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <button className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-lg text-sm font-medium hover:from-purple-600 hover:to-pink-700 transition-all duration-200 shadow-sm">
                      {t('search.actions.follow')}
                    </button>
                    <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200">
                      {t('search.actions.profile')}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Memories */}
      {(activeTab === 'all' || activeTab === 'memories') && filteredResults.memories?.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <Brain className="w-5 h-5" />
            {t('search.memories.title')}
            <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
              ({filteredResults.memories.length})
            </span>
          </h2>
          <div className="space-y-3">
            {filteredResults.memories.map((memory: any) => (
              <div
                key={memory.id}
                className="group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-300 cursor-pointer hover:shadow-md"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-gray-900 dark:text-white text-base">
                        {memory.title}
                      </h3>
                      <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                        memory.private 
                          ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                          : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                      }`}>
                        {memory.private ? 'Private' : 'Public'}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
                      <span>Character: {memory.character}</span>
                      <span>Emotion: {memory.emotion}</span>
                      <span>Importance: {memory.importance}/10</span>
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {memory.created}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {memory.tags?.map((tag: string, index: number) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 rounded-full text-xs font-medium"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2 ml-4">
                    <button className="px-4 py-2 bg-gradient-to-r from-amber-500 to-orange-600 text-white rounded-lg text-sm font-medium hover:from-amber-600 hover:to-orange-700 transition-all duration-200 shadow-sm">
                      {t('search.actions.view')}
                    </button>
                    <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200">
                      {t('search.actions.share')}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchResults; 