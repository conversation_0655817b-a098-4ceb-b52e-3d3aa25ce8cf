import { FC } from 'react';

interface StoryPageProps {
  params: Promise<{
    lng: string;
    storyID: string;
  }>;
}

const StoryPage: FC<StoryPageProps> = async ({ params }) => {
  const { lng, storyID } = await params;
  
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="w-full max-w-4xl p-4 md:p-8">
        <h1 className="text-2xl md:text-4xl font-bold mb-4">
          Story Template: {storyID}
        </h1>
        <p className="text-lg text-gray-700 dark:text-gray-300">
          Language: {lng}
        </p>
        {/* Story template with 2-3 character interactions will be implemented here */}
      </div>
    </div>
  );
};

export default StoryPage; 